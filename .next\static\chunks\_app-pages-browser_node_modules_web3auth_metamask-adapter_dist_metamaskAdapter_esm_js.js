"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_web3auth_metamask-adapter_dist_metamaskAdapter_esm_js"],{

/***/ "(app-pages-browser)/./node_modules/@metamask/detect-provider/dist/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@metamask/detect-provider/dist/index.js ***!
  \**************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n/**\n * Returns a Promise that resolves to the value of window.ethereum if it is\n * set within the given timeout, or null.\n * The Promise will not reject, but an error will be thrown if invalid options\n * are provided.\n *\n * @param options - Options bag.\n * @param options.mustBeMetaMask - Whether to only look for MetaMask providers.\n * Default: false\n * @param options.silent - Whether to silence console errors. Does not affect\n * thrown errors. Default: false\n * @param options.timeout - Milliseconds to wait for 'ethereum#initialized' to\n * be dispatched. Default: 3000\n * @returns A Promise that resolves with the Provider if it is detected within\n * given timeout, otherwise null.\n */\nfunction detectEthereumProvider({ mustBeMetaMask = false, silent = false, timeout = 3000, } = {}) {\n    _validateInputs();\n    let handled = false;\n    return new Promise((resolve) => {\n        if (window.ethereum) {\n            handleEthereum();\n        }\n        else {\n            window.addEventListener('ethereum#initialized', handleEthereum, { once: true });\n            setTimeout(() => {\n                handleEthereum();\n            }, timeout);\n        }\n        function handleEthereum() {\n            if (handled) {\n                return;\n            }\n            handled = true;\n            window.removeEventListener('ethereum#initialized', handleEthereum);\n            const { ethereum } = window;\n            if (ethereum && (!mustBeMetaMask || ethereum.isMetaMask)) {\n                resolve(ethereum);\n            }\n            else {\n                const message = mustBeMetaMask && ethereum\n                    ? 'Non-MetaMask window.ethereum detected.'\n                    : 'Unable to detect window.ethereum.';\n                !silent && console.error('@metamask/detect-provider:', message);\n                resolve(null);\n            }\n        }\n    });\n    function _validateInputs() {\n        if (typeof mustBeMetaMask !== 'boolean') {\n            throw new Error(`@metamask/detect-provider: Expected option 'mustBeMetaMask' to be a boolean.`);\n        }\n        if (typeof silent !== 'boolean') {\n            throw new Error(`@metamask/detect-provider: Expected option 'silent' to be a boolean.`);\n        }\n        if (typeof timeout !== 'number') {\n            throw new Error(`@metamask/detect-provider: Expected option 'timeout' to be a number.`);\n        }\n    }\n}\nmodule.exports = detectEthereumProvider;\n//# sourceMappingURL=data:application/json;base64,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//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AbWV0YW1hc2svZGV0ZWN0LXByb3ZpZGVyL2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQywwREFBMEQsSUFBSTtBQUNoRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhFQUE4RSxZQUFZO0FBQzFGO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsV0FBVztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AbWV0YW1hc2svZGV0ZWN0LXByb3ZpZGVyL2Rpc3QvaW5kZXguanM/MGNkYyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qKlxuICogUmV0dXJucyBhIFByb21pc2UgdGhhdCByZXNvbHZlcyB0byB0aGUgdmFsdWUgb2Ygd2luZG93LmV0aGVyZXVtIGlmIGl0IGlzXG4gKiBzZXQgd2l0aGluIHRoZSBnaXZlbiB0aW1lb3V0LCBvciBudWxsLlxuICogVGhlIFByb21pc2Ugd2lsbCBub3QgcmVqZWN0LCBidXQgYW4gZXJyb3Igd2lsbCBiZSB0aHJvd24gaWYgaW52YWxpZCBvcHRpb25zXG4gKiBhcmUgcHJvdmlkZWQuXG4gKlxuICogQHBhcmFtIG9wdGlvbnMgLSBPcHRpb25zIGJhZy5cbiAqIEBwYXJhbSBvcHRpb25zLm11c3RCZU1ldGFNYXNrIC0gV2hldGhlciB0byBvbmx5IGxvb2sgZm9yIE1ldGFNYXNrIHByb3ZpZGVycy5cbiAqIERlZmF1bHQ6IGZhbHNlXG4gKiBAcGFyYW0gb3B0aW9ucy5zaWxlbnQgLSBXaGV0aGVyIHRvIHNpbGVuY2UgY29uc29sZSBlcnJvcnMuIERvZXMgbm90IGFmZmVjdFxuICogdGhyb3duIGVycm9ycy4gRGVmYXVsdDogZmFsc2VcbiAqIEBwYXJhbSBvcHRpb25zLnRpbWVvdXQgLSBNaWxsaXNlY29uZHMgdG8gd2FpdCBmb3IgJ2V0aGVyZXVtI2luaXRpYWxpemVkJyB0b1xuICogYmUgZGlzcGF0Y2hlZC4gRGVmYXVsdDogMzAwMFxuICogQHJldHVybnMgQSBQcm9taXNlIHRoYXQgcmVzb2x2ZXMgd2l0aCB0aGUgUHJvdmlkZXIgaWYgaXQgaXMgZGV0ZWN0ZWQgd2l0aGluXG4gKiBnaXZlbiB0aW1lb3V0LCBvdGhlcndpc2UgbnVsbC5cbiAqL1xuZnVuY3Rpb24gZGV0ZWN0RXRoZXJldW1Qcm92aWRlcih7IG11c3RCZU1ldGFNYXNrID0gZmFsc2UsIHNpbGVudCA9IGZhbHNlLCB0aW1lb3V0ID0gMzAwMCwgfSA9IHt9KSB7XG4gICAgX3ZhbGlkYXRlSW5wdXRzKCk7XG4gICAgbGV0IGhhbmRsZWQgPSBmYWxzZTtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHtcbiAgICAgICAgaWYgKHdpbmRvdy5ldGhlcmV1bSkge1xuICAgICAgICAgICAgaGFuZGxlRXRoZXJldW0oKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdldGhlcmV1bSNpbml0aWFsaXplZCcsIGhhbmRsZUV0aGVyZXVtLCB7IG9uY2U6IHRydWUgfSk7XG4gICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgICAgICBoYW5kbGVFdGhlcmV1bSgpO1xuICAgICAgICAgICAgfSwgdGltZW91dCk7XG4gICAgICAgIH1cbiAgICAgICAgZnVuY3Rpb24gaGFuZGxlRXRoZXJldW0oKSB7XG4gICAgICAgICAgICBpZiAoaGFuZGxlZCkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGhhbmRsZWQgPSB0cnVlO1xuICAgICAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2V0aGVyZXVtI2luaXRpYWxpemVkJywgaGFuZGxlRXRoZXJldW0pO1xuICAgICAgICAgICAgY29uc3QgeyBldGhlcmV1bSB9ID0gd2luZG93O1xuICAgICAgICAgICAgaWYgKGV0aGVyZXVtICYmICghbXVzdEJlTWV0YU1hc2sgfHwgZXRoZXJldW0uaXNNZXRhTWFzaykpIHtcbiAgICAgICAgICAgICAgICByZXNvbHZlKGV0aGVyZXVtKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IG1lc3NhZ2UgPSBtdXN0QmVNZXRhTWFzayAmJiBldGhlcmV1bVxuICAgICAgICAgICAgICAgICAgICA/ICdOb24tTWV0YU1hc2sgd2luZG93LmV0aGVyZXVtIGRldGVjdGVkLidcbiAgICAgICAgICAgICAgICAgICAgOiAnVW5hYmxlIHRvIGRldGVjdCB3aW5kb3cuZXRoZXJldW0uJztcbiAgICAgICAgICAgICAgICAhc2lsZW50ICYmIGNvbnNvbGUuZXJyb3IoJ0BtZXRhbWFzay9kZXRlY3QtcHJvdmlkZXI6JywgbWVzc2FnZSk7XG4gICAgICAgICAgICAgICAgcmVzb2x2ZShudWxsKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH0pO1xuICAgIGZ1bmN0aW9uIF92YWxpZGF0ZUlucHV0cygpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBtdXN0QmVNZXRhTWFzayAhPT0gJ2Jvb2xlYW4nKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEBtZXRhbWFzay9kZXRlY3QtcHJvdmlkZXI6IEV4cGVjdGVkIG9wdGlvbiAnbXVzdEJlTWV0YU1hc2snIHRvIGJlIGEgYm9vbGVhbi5gKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNpbGVudCAhPT0gJ2Jvb2xlYW4nKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEBtZXRhbWFzay9kZXRlY3QtcHJvdmlkZXI6IEV4cGVjdGVkIG9wdGlvbiAnc2lsZW50JyB0byBiZSBhIGJvb2xlYW4uYCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB0aW1lb3V0ICE9PSAnbnVtYmVyJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBAbWV0YW1hc2svZGV0ZWN0LXByb3ZpZGVyOiBFeHBlY3RlZCBvcHRpb24gJ3RpbWVvdXQnIHRvIGJlIGEgbnVtYmVyLmApO1xuICAgICAgICB9XG4gICAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBkZXRlY3RFdGhlcmV1bVByb3ZpZGVyO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2Jhc2U2NCxleUoyWlhKemFXOXVJam96TENKbWFXeGxJam9pYVc1a1pYZ3Vhbk1pTENKemIzVnlZMlZTYjI5MElqb2lJaXdpYzI5MWNtTmxjeUk2V3lJdUxpOXpjbU12YVc1a1pYZ3VkSE1pWFN3aWJtRnRaWE1pT2x0ZExDSnRZWEJ3YVc1bmN5STZJanRCUVdkQ1FUczdPenM3T3pzN096czdPenM3TzBkQlpVYzdRVUZEU0N4VFFVRlRMSE5DUVVGelFpeERRVUVyUWl4RlFVTTFSQ3hqUVVGakxFZEJRVWNzUzBGQlN5eEZRVU4wUWl4TlFVRk5MRWRCUVVjc1MwRkJTeXhGUVVOa0xFOUJRVThzUjBGQlJ5eEpRVUZKTEVkQlEyWXNSMEZCUnl4RlFVRkZPMGxCUlVvc1pVRkJaU3hGUVVGRkxFTkJRVU03U1VGRmJFSXNTVUZCU1N4UFFVRlBMRWRCUVVjc1MwRkJTeXhEUVVGRE8wbEJSWEJDTEU5QlFVOHNTVUZCU1N4UFFVRlBMRU5CUVVNc1EwRkJReXhQUVVGUExFVkJRVVVzUlVGQlJUdFJRVU0zUWl4SlFVRkxMRTFCUVdsQ0xFTkJRVU1zVVVGQlVTeEZRVUZGTzFsQlJTOUNMR05CUVdNc1JVRkJSU3hEUVVGRE8xTkJSV3hDTzJGQlFVMDdXVUZGVEN4TlFVRk5MRU5CUVVNc1owSkJRV2RDTEVOQlEzSkNMSE5DUVVGelFpeEZRVU4wUWl4alFVRmpMRVZCUTJRc1JVRkJSU3hKUVVGSkxFVkJRVVVzU1VGQlNTeEZRVUZGTEVOQlEyWXNRMEZCUXp0WlFVVkdMRlZCUVZVc1EwRkJReXhIUVVGSExFVkJRVVU3WjBKQlEyUXNZMEZCWXl4RlFVRkZMRU5CUVVNN1dVRkRia0lzUTBGQlF5eEZRVUZGTEU5QlFVOHNRMEZCUXl4RFFVRkRPMU5CUTJJN1VVRkZSQ3hUUVVGVExHTkJRV003V1VGRmNrSXNTVUZCU1N4UFFVRlBMRVZCUVVVN1owSkJRMWdzVDBGQlR6dGhRVU5TTzFsQlEwUXNUMEZCVHl4SFFVRkhMRWxCUVVrc1EwRkJRenRaUVVWbUxFMUJRVTBzUTBGQlF5eHRRa0ZCYlVJc1EwRkJReXh6UWtGQmMwSXNSVUZCUlN4alFVRmpMRU5CUVVNc1EwRkJRenRaUVVWdVJTeE5RVUZOTEVWQlFVVXNVVUZCVVN4RlFVRkZMRWRCUVVjc1RVRkJaMElzUTBGQlF6dFpRVVYwUXl4SlFVRkpMRkZCUVZFc1NVRkJTU3hEUVVGRExFTkJRVU1zWTBGQll5eEpRVUZKTEZGQlFWRXNRMEZCUXl4VlFVRlZMRU5CUVVNc1JVRkJSVHRuUWtGRGVFUXNUMEZCVHl4RFFVRkRMRkZCUVhkQ0xFTkJRVU1zUTBGQlF6dGhRVU51UXp0cFFrRkJUVHRuUWtGRlRDeE5RVUZOTEU5QlFVOHNSMEZCUnl4alFVRmpMRWxCUVVrc1VVRkJVVHR2UWtGRGVFTXNRMEZCUXl4RFFVRkRMSGREUVVGM1F6dHZRa0ZETVVNc1EwRkJReXhEUVVGRExHMURRVUZ0UXl4RFFVRkRPMmRDUVVWNFF5eERRVUZETEUxQlFVMHNTVUZCU1N4UFFVRlBMRU5CUVVNc1MwRkJTeXhEUVVGRExEUkNRVUUwUWl4RlFVRkZMRTlCUVU4c1EwRkJReXhEUVVGRE8yZENRVU5vUlN4UFFVRlBMRU5CUVVNc1NVRkJTU3hEUVVGRExFTkJRVU03WVVGRFpqdFJRVU5JTEVOQlFVTTdTVUZEU0N4RFFVRkRMRU5CUVVNc1EwRkJRenRKUVVWSUxGTkJRVk1zWlVGQlpUdFJRVU4wUWl4SlFVRkpMRTlCUVU4c1kwRkJZeXhMUVVGTExGTkJRVk1zUlVGQlJUdFpRVU4yUXl4TlFVRk5MRWxCUVVrc1MwRkJTeXhEUVVGRExEaEZRVUU0UlN4RFFVRkRMRU5CUVVNN1UwRkRha2M3VVVGRFJDeEpRVUZKTEU5QlFVOHNUVUZCVFN4TFFVRkxMRk5CUVZNc1JVRkJSVHRaUVVNdlFpeE5RVUZOTEVsQlFVa3NTMEZCU3l4RFFVRkRMSE5GUVVGelJTeERRVUZETEVOQlFVTTdVMEZEZWtZN1VVRkRSQ3hKUVVGSkxFOUJRVThzVDBGQlR5eExRVUZMTEZGQlFWRXNSVUZCUlR0WlFVTXZRaXhOUVVGTkxFbEJRVWtzUzBGQlN5eERRVUZETEhORlFVRnpSU3hEUVVGRExFTkJRVU03VTBGRGVrWTdTVUZEU0N4RFFVRkRPMEZCUTBnc1EwRkJRenRCUVd4R1JDeHBRa0ZCVXl4elFrRkJjMElzUTBGQlF5SXNJbk52ZFhKalpYTkRiMjUwWlc1MElqcGJJbWx1ZEdWeVptRmpaU0JOWlhSaFRXRnphMFYwYUdWeVpYVnRVSEp2ZG1sa1pYSWdlMXh1SUNCcGMwMWxkR0ZOWVhOclB6b2dZbTl2YkdWaGJqdGNiaUFnYjI1alpTaGxkbVZ1ZEU1aGJXVTZJSE4wY21sdVp5QjhJSE41YldKdmJDd2diR2x6ZEdWdVpYSTZJQ2d1TGk1aGNtZHpPaUJoYm5sYlhTa2dQVDRnZG05cFpDazZJSFJvYVhNN1hHNGdJRzl1S0dWMlpXNTBUbUZ0WlRvZ2MzUnlhVzVuSUh3Z2MzbHRZbTlzTENCc2FYTjBaVzVsY2pvZ0tDNHVMbUZ5WjNNNklHRnVlVnRkS1NBOVBpQjJiMmxrS1RvZ2RHaHBjenRjYmlBZ2IyWm1LR1YyWlc1MFRtRnRaVG9nYzNSeWFXNW5JSHdnYzNsdFltOXNMQ0JzYVhOMFpXNWxjam9nS0M0dUxtRnlaM002SUdGdWVWdGRLU0E5UGlCMmIybGtLVG9nZEdocGN6dGNiaUFnWVdSa1RHbHpkR1Z1WlhJb1pYWmxiblJPWVcxbE9pQnpkSEpwYm1jZ2ZDQnplVzFpYjJ3c0lHeHBjM1JsYm1WeU9pQW9MaTR1WVhKbmN6b2dZVzU1VzEwcElEMCtJSFp2YVdRcE9pQjBhR2x6TzF4dUlDQnlaVzF2ZG1WTWFYTjBaVzVsY2lobGRtVnVkRTVoYldVNklITjBjbWx1WnlCOElITjViV0p2YkN3Z2JHbHpkR1Z1WlhJNklDZ3VMaTVoY21kek9pQmhibmxiWFNrZ1BUNGdkbTlwWkNrNklIUm9hWE03WEc0Z0lISmxiVzkyWlVGc2JFeHBjM1JsYm1WeWN5aGxkbVZ1ZEQ4NklITjBjbWx1WnlCOElITjViV0p2YkNrNklIUm9hWE03WEc1OVhHNWNibWx1ZEdWeVptRmpaU0JYYVc1a2IzY2dlMXh1SUNCbGRHaGxjbVYxYlQ4NklFMWxkR0ZOWVhOclJYUm9aWEpsZFcxUWNtOTJhV1JsY2p0Y2JuMWNibHh1Wlhod2IzSjBJRDBnWkdWMFpXTjBSWFJvWlhKbGRXMVFjbTkyYVdSbGNqdGNibHh1THlvcVhHNGdLaUJTWlhSMWNtNXpJR0VnVUhKdmJXbHpaU0IwYUdGMElISmxjMjlzZG1WeklIUnZJSFJvWlNCMllXeDFaU0J2WmlCM2FXNWtiM2N1WlhSb1pYSmxkVzBnYVdZZ2FYUWdhWE5jYmlBcUlITmxkQ0IzYVhSb2FXNGdkR2hsSUdkcGRtVnVJSFJwYldWdmRYUXNJRzl5SUc1MWJHd3VYRzRnS2lCVWFHVWdVSEp2YldselpTQjNhV3hzSUc1dmRDQnlaV3BsWTNRc0lHSjFkQ0JoYmlCbGNuSnZjaUIzYVd4c0lHSmxJSFJvY205M2JpQnBaaUJwYm5aaGJHbGtJRzl3ZEdsdmJuTmNiaUFxSUdGeVpTQndjbTkyYVdSbFpDNWNiaUFxWEc0Z0tpQkFjR0Z5WVcwZ2IzQjBhVzl1Y3lBdElFOXdkR2x2Ym5NZ1ltRm5MbHh1SUNvZ1FIQmhjbUZ0SUc5d2RHbHZibk11YlhWemRFSmxUV1YwWVUxaGMyc2dMU0JYYUdWMGFHVnlJSFJ2SUc5dWJIa2diRzl2YXlCbWIzSWdUV1YwWVUxaGMyc2djSEp2ZG1sa1pYSnpMbHh1SUNvZ1JHVm1ZWFZzZERvZ1ptRnNjMlZjYmlBcUlFQndZWEpoYlNCdmNIUnBiMjV6TG5OcGJHVnVkQ0F0SUZkb1pYUm9aWElnZEc4Z2MybHNaVzVqWlNCamIyNXpiMnhsSUdWeWNtOXljeTRnUkc5bGN5QnViM1FnWVdabVpXTjBYRzRnS2lCMGFISnZkMjRnWlhKeWIzSnpMaUJFWldaaGRXeDBPaUJtWVd4elpWeHVJQ29nUUhCaGNtRnRJRzl3ZEdsdmJuTXVkR2x0Wlc5MWRDQXRJRTFwYkd4cGMyVmpiMjVrY3lCMGJ5QjNZV2wwSUdadmNpQW5aWFJvWlhKbGRXMGphVzVwZEdsaGJHbDZaV1FuSUhSdlhHNGdLaUJpWlNCa2FYTndZWFJqYUdWa0xpQkVaV1poZFd4ME9pQXpNREF3WEc0Z0tpQkFjbVYwZFhKdWN5QkJJRkJ5YjIxcGMyVWdkR2hoZENCeVpYTnZiSFpsY3lCM2FYUm9JSFJvWlNCUWNtOTJhV1JsY2lCcFppQnBkQ0JwY3lCa1pYUmxZM1JsWkNCM2FYUm9hVzVjYmlBcUlHZHBkbVZ1SUhScGJXVnZkWFFzSUc5MGFHVnlkMmx6WlNCdWRXeHNMbHh1SUNvdlhHNW1kVzVqZEdsdmJpQmtaWFJsWTNSRmRHaGxjbVYxYlZCeWIzWnBaR1Z5UEZRZ1BTQk5aWFJoVFdGemEwVjBhR1Z5WlhWdFVISnZkbWxrWlhJK0tIdGNiaUFnYlhWemRFSmxUV1YwWVUxaGMyc2dQU0JtWVd4elpTeGNiaUFnYzJsc1pXNTBJRDBnWm1Gc2MyVXNYRzRnSUhScGJXVnZkWFFnUFNBek1EQXdMRnh1ZlNBOUlIdDlLVG9nVUhKdmJXbHpaVHhVSUh3Z2JuVnNiRDRnZTF4dVhHNGdJRjkyWVd4cFpHRjBaVWx1Y0hWMGN5Z3BPMXh1WEc0Z0lHeGxkQ0JvWVc1a2JHVmtJRDBnWm1Gc2MyVTdYRzVjYmlBZ2NtVjBkWEp1SUc1bGR5QlFjbTl0YVhObEtDaHlaWE52YkhabEtTQTlQaUI3WEc0Z0lDQWdhV1lnS0NoM2FXNWtiM2NnWVhNZ1YybHVaRzkzS1M1bGRHaGxjbVYxYlNrZ2UxeHVYRzRnSUNBZ0lDQm9ZVzVrYkdWRmRHaGxjbVYxYlNncE8xeHVYRzRnSUNBZ2ZTQmxiSE5sSUh0Y2JseHVJQ0FnSUNBZ2QybHVaRzkzTG1Ga1pFVjJaVzUwVEdsemRHVnVaWElvWEc0Z0lDQWdJQ0FnSUNkbGRHaGxjbVYxYlNOcGJtbDBhV0ZzYVhwbFpDY3NYRzRnSUNBZ0lDQWdJR2hoYm1Sc1pVVjBhR1Z5WlhWdExGeHVJQ0FnSUNBZ0lDQjdJRzl1WTJVNklIUnlkV1VnZlN4Y2JpQWdJQ0FnSUNrN1hHNWNiaUFnSUNBZ0lITmxkRlJwYldWdmRYUW9LQ2tnUFQ0Z2UxeHVJQ0FnSUNBZ0lDQm9ZVzVrYkdWRmRHaGxjbVYxYlNncE8xeHVJQ0FnSUNBZ2ZTd2dkR2x0Wlc5MWRDazdYRzRnSUNBZ2ZWeHVYRzRnSUNBZ1puVnVZM1JwYjI0Z2FHRnVaR3hsUlhSb1pYSmxkVzBvS1NCN1hHNWNiaUFnSUNBZ0lHbG1JQ2hvWVc1a2JHVmtLU0I3WEc0Z0lDQWdJQ0FnSUhKbGRIVnlianRjYmlBZ0lDQWdJSDFjYmlBZ0lDQWdJR2hoYm1Sc1pXUWdQU0IwY25WbE8xeHVYRzRnSUNBZ0lDQjNhVzVrYjNjdWNtVnRiM1psUlhabGJuUk1hWE4wWlc1bGNpZ25aWFJvWlhKbGRXMGphVzVwZEdsaGJHbDZaV1FuTENCb1lXNWtiR1ZGZEdobGNtVjFiU2s3WEc1Y2JpQWdJQ0FnSUdOdmJuTjBJSHNnWlhSb1pYSmxkVzBnZlNBOUlIZHBibVJ2ZHlCaGN5QlhhVzVrYjNjN1hHNWNiaUFnSUNBZ0lHbG1JQ2hsZEdobGNtVjFiU0FtSmlBb0lXMTFjM1JDWlUxbGRHRk5ZWE5ySUh4OElHVjBhR1Z5WlhWdExtbHpUV1YwWVUxaGMyc3BLU0I3WEc0Z0lDQWdJQ0FnSUhKbGMyOXNkbVVvWlhSb1pYSmxkVzBnWVhNZ2RXNXJibTkzYmlCaGN5QlVLVHRjYmlBZ0lDQWdJSDBnWld4elpTQjdYRzVjYmlBZ0lDQWdJQ0FnWTI5dWMzUWdiV1Z6YzJGblpTQTlJRzExYzNSQ1pVMWxkR0ZOWVhOcklDWW1JR1YwYUdWeVpYVnRYRzRnSUNBZ0lDQWdJQ0FnUHlBblRtOXVMVTFsZEdGTllYTnJJSGRwYm1SdmR5NWxkR2hsY21WMWJTQmtaWFJsWTNSbFpDNG5YRzRnSUNBZ0lDQWdJQ0FnT2lBblZXNWhZbXhsSUhSdklHUmxkR1ZqZENCM2FXNWtiM2N1WlhSb1pYSmxkVzB1Snp0Y2JseHVJQ0FnSUNBZ0lDQWhjMmxzWlc1MElDWW1JR052Ym5OdmJHVXVaWEp5YjNJb0owQnRaWFJoYldGemF5OWtaWFJsWTNRdGNISnZkbWxrWlhJNkp5d2diV1Z6YzJGblpTazdYRzRnSUNBZ0lDQWdJSEpsYzI5c2RtVW9iblZzYkNrN1hHNGdJQ0FnSUNCOVhHNGdJQ0FnZlZ4dUlDQjlLVHRjYmx4dUlDQm1kVzVqZEdsdmJpQmZkbUZzYVdSaGRHVkpibkIxZEhNb0tTQjdYRzRnSUNBZ2FXWWdLSFI1Y0dWdlppQnRkWE4wUW1WTlpYUmhUV0Z6YXlBaFBUMGdKMkp2YjJ4bFlXNG5LU0I3WEc0Z0lDQWdJQ0IwYUhKdmR5QnVaWGNnUlhKeWIzSW9ZRUJ0WlhSaGJXRnpheTlrWlhSbFkzUXRjSEp2ZG1sa1pYSTZJRVY0Y0dWamRHVmtJRzl3ZEdsdmJpQW5iWFZ6ZEVKbFRXVjBZVTFoYzJzbklIUnZJR0psSUdFZ1ltOXZiR1ZoYmk1Z0tUdGNiaUFnSUNCOVhHNGdJQ0FnYVdZZ0tIUjVjR1Z2WmlCemFXeGxiblFnSVQwOUlDZGliMjlzWldGdUp5a2dlMXh1SUNBZ0lDQWdkR2h5YjNjZ2JtVjNJRVZ5Y205eUtHQkFiV1YwWVcxaGMyc3ZaR1YwWldOMExYQnliM1pwWkdWeU9pQkZlSEJsWTNSbFpDQnZjSFJwYjI0Z0ozTnBiR1Z1ZENjZ2RHOGdZbVVnWVNCaWIyOXNaV0Z1TG1BcE8xeHVJQ0FnSUgxY2JpQWdJQ0JwWmlBb2RIbHdaVzltSUhScGJXVnZkWFFnSVQwOUlDZHVkVzFpWlhJbktTQjdYRzRnSUNBZ0lDQjBhSEp2ZHlCdVpYY2dSWEp5YjNJb1lFQnRaWFJoYldGemF5OWtaWFJsWTNRdGNISnZkbWxrWlhJNklFVjRjR1ZqZEdWa0lHOXdkR2x2YmlBbmRHbHRaVzkxZENjZ2RHOGdZbVVnWVNCdWRXMWlaWEl1WUNrN1hHNGdJQ0FnZlZ4dUlDQjlYRzU5WEc0aVhYMD0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@metamask/detect-provider/dist/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@web3auth/base-evm-adapter/dist/baseEvmAdapter.esm.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@web3auth/base-evm-adapter/dist/baseEvmAdapter.esm.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseEvmAdapter: function() { return /* binding */ BaseEvmAdapter; }\n/* harmony export */ });\n/* harmony import */ var _web3auth_base__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @web3auth/base */ \"(app-pages-browser)/./node_modules/@web3auth/base/dist/base.esm.js\");\n\n\nclass BaseEvmAdapter extends _web3auth_base__WEBPACK_IMPORTED_MODULE_0__.BaseAdapter {\n  async init(_) {\n    if (!this.chainConfig) this.chainConfig = (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.getChainConfig)(_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.CHAIN_NAMESPACES.EIP155, 1);\n  }\n  async authenticateUser() {\n    if (!this.provider || this.status !== _web3auth_base__WEBPACK_IMPORTED_MODULE_0__.ADAPTER_STATUS.CONNECTED) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_0__.WalletLoginError.notConnectedError();\n    const {\n      chainNamespace,\n      chainId\n    } = this.chainConfig;\n    const accounts = await this.provider.request({\n      method: \"eth_accounts\"\n    });\n    if (accounts && accounts.length > 0) {\n      const existingToken = (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.getSavedToken)(accounts[0], this.name);\n      if (existingToken) {\n        const isExpired = (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.checkIfTokenIsExpired)(existingToken);\n        if (!isExpired) {\n          return {\n            idToken: existingToken\n          };\n        }\n      }\n      const payload = {\n        domain: window.location.origin,\n        uri: window.location.href,\n        address: accounts[0],\n        chainId: parseInt(chainId, 16),\n        version: \"1\",\n        nonce: Math.random().toString(36).slice(2),\n        issuedAt: new Date().toISOString()\n      };\n      const challenge = await (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.signChallenge)(payload, chainNamespace);\n      const signedMessage = await this.provider.request({\n        method: \"personal_sign\",\n        params: [challenge, accounts[0]]\n      });\n      const idToken = await (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.verifySignedChallenge)(chainNamespace, signedMessage, challenge, this.name, this.sessionTime, this.clientId, this.web3AuthNetwork);\n      (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.saveToken)(accounts[0], this.name, idToken);\n      return {\n        idToken\n      };\n    }\n    throw _web3auth_base__WEBPACK_IMPORTED_MODULE_0__.WalletLoginError.notConnectedError(\"Not connected with wallet, Please login/connect first\");\n  }\n  async disconnectSession() {\n    super.checkDisconnectionRequirements();\n    const accounts = await this.provider.request({\n      method: \"eth_accounts\"\n    });\n    if (accounts && accounts.length > 0) {\n      (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.clearToken)(accounts[0], this.name);\n    }\n  }\n  async disconnect() {\n    this.rehydrated = false;\n    this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.ADAPTER_EVENTS.DISCONNECTED);\n  }\n}\n\n\n//# sourceMappingURL=baseEvmAdapter.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/base-evm-adapter/dist/baseEvmAdapter.esm.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@web3auth/metamask-adapter/dist/metamaskAdapter.esm.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@web3auth/metamask-adapter/dist/metamaskAdapter.esm.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MetamaskAdapter: function() { return /* binding */ MetamaskAdapter; }\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _metamask_detect_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @metamask/detect-provider */ \"(app-pages-browser)/./node_modules/@metamask/detect-provider/dist/index.js\");\n/* harmony import */ var _metamask_detect_provider__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_metamask_detect_provider__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _web3auth_base__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @web3auth/base */ \"(app-pages-browser)/./node_modules/@web3auth/base/dist/base.esm.js\");\n/* harmony import */ var _web3auth_base_evm_adapter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @web3auth/base-evm-adapter */ \"(app-pages-browser)/./node_modules/@web3auth/base-evm-adapter/dist/baseEvmAdapter.esm.js\");\n\n\n\n\n\nclass MetamaskAdapter extends _web3auth_base_evm_adapter__WEBPACK_IMPORTED_MODULE_3__.BaseEvmAdapter {\n  constructor() {\n    super(...arguments);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"adapterNamespace\", _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_NAMESPACES.EIP155);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"currentChainNamespace\", _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.CHAIN_NAMESPACES.EIP155);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"type\", _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_CATEGORY.EXTERNAL);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"name\", _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.WALLET_ADAPTERS.METAMASK);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"status\", _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_STATUS.NOT_READY);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"metamaskProvider\", null);\n  }\n  get provider() {\n    if (this.status !== _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_STATUS.NOT_READY && this.metamaskProvider) {\n      return this.metamaskProvider;\n    }\n    return null;\n  }\n  set provider(_) {\n    throw new Error(\"Not implemented\");\n  }\n  async init() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    await super.init(options);\n    super.checkInitializationRequirements();\n    this.metamaskProvider = await _metamask_detect_provider__WEBPACK_IMPORTED_MODULE_1___default()({\n      mustBeMetaMask: true,\n      silent: true,\n      timeout: 1000\n    });\n    if (!this.metamaskProvider) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.WalletInitializationError.notInstalled(\"Metamask extension is not installed\");\n    this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_STATUS.READY;\n    this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_EVENTS.READY, _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.WALLET_ADAPTERS.METAMASK);\n    try {\n      _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.log.debug(\"initializing metamask adapter\");\n      if (options.autoConnect) {\n        this.rehydrated = true;\n        await this.connect();\n      }\n    } catch (error) {\n      this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_EVENTS.ERRORED, error);\n    }\n  }\n  async connect() {\n    super.checkConnectionRequirements();\n    if (!this.metamaskProvider) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.WalletLoginError.notConnectedError(\"Not able to connect with metamask\");\n    const {\n      ethereum\n    } = window;\n    const isPhantom = Boolean(\"isPhantom\" in ethereum);\n    // check which is the active provider\n    if (ethereum && ethereum.isMetaMask && isPhantom) {\n      // this means phantom is the active provider.\n      if (ethereum.providers && ethereum.providers.length > 0) {\n        const provider = ethereum.providers.find(p => p.isMetaMask && !p.overrideIsMetaMask);\n        if (provider) {\n          ethereum.setProvider(provider);\n        }\n      }\n    } else if (ethereum && (ethereum.providers || []).length > 0) {\n      // this means that there are another providers than metamask (like coinbase).\n      const provider = ethereum.providers.find(p => p.isMetaMask);\n      if (provider) {\n        ethereum.setSelectedProvider(provider);\n      }\n    }\n    this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_STATUS.CONNECTING;\n    this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_EVENTS.CONNECTING, {\n      adapter: _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.WALLET_ADAPTERS.METAMASK\n    });\n    try {\n      await this.metamaskProvider.request({\n        method: \"eth_requestAccounts\"\n      });\n      const {\n        chainId\n      } = this.metamaskProvider;\n      if (chainId !== this.chainConfig.chainId) {\n        await this.addChain(this.chainConfig, true);\n        await this.switchChain(this.chainConfig, true);\n      }\n      this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_STATUS.CONNECTED;\n      if (!this.provider) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.WalletLoginError.notConnectedError(\"Failed to connect with provider\");\n      const disconnectHandler = () => {\n        var _this$provider;\n        // ready to be connected again\n        this.disconnect();\n        (_this$provider = this.provider) === null || _this$provider === void 0 || _this$provider.removeListener(\"disconnect\", disconnectHandler);\n      };\n      this.provider.on(\"disconnect\", disconnectHandler);\n      this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_EVENTS.CONNECTED, {\n        adapter: _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.WALLET_ADAPTERS.METAMASK,\n        reconnected: this.rehydrated\n      });\n      return this.provider;\n    } catch (error) {\n      // ready again to be connected\n      this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_STATUS.READY;\n      this.rehydrated = false;\n      this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_EVENTS.ERRORED, error);\n      if (error instanceof _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.Web3AuthError) throw error;\n      throw _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.WalletLoginError.connectionError(\"Failed to login with metamask wallet\");\n    }\n  }\n  async disconnect() {\n    var _this$provider2;\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      cleanup: false\n    };\n    await super.disconnectSession();\n    (_this$provider2 = this.provider) === null || _this$provider2 === void 0 || _this$provider2.removeAllListeners();\n    if (options.cleanup) {\n      this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_STATUS.NOT_READY;\n      this.metamaskProvider = null;\n    } else {\n      // ready to be connected again\n      this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_STATUS.READY;\n    }\n    await super.disconnect();\n  }\n  async getUserInfo() {\n    if (this.status !== _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.ADAPTER_STATUS.CONNECTED) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_2__.WalletLoginError.notConnectedError(\"Not connected with wallet, Please login/connect first\");\n    return {};\n  }\n  async addChain(chainConfig) {\n    var _this$metamaskProvide;\n    let init = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    super.checkAddChainRequirements(chainConfig, init);\n    await ((_this$metamaskProvide = this.metamaskProvider) === null || _this$metamaskProvide === void 0 ? void 0 : _this$metamaskProvide.request({\n      method: \"wallet_addEthereumChain\",\n      params: [{\n        chainId: chainConfig.chainId,\n        chainName: chainConfig.displayName,\n        rpcUrls: [chainConfig.rpcTarget],\n        blockExplorerUrls: [chainConfig.blockExplorer],\n        nativeCurrency: {\n          name: chainConfig.tickerName,\n          symbol: chainConfig.ticker,\n          decimals: chainConfig.decimals || 18\n        }\n      }]\n    }));\n    this.addChainConfig(chainConfig);\n  }\n  async switchChain(params) {\n    var _this$metamaskProvide2;\n    let init = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    super.checkSwitchChainRequirements(params, init);\n    await ((_this$metamaskProvide2 = this.metamaskProvider) === null || _this$metamaskProvide2 === void 0 ? void 0 : _this$metamaskProvide2.request({\n      method: \"wallet_switchEthereumChain\",\n      params: [{\n        chainId: params.chainId\n      }]\n    }));\n    this.setAdapterSettings({\n      chainConfig: this.getChainConfig(params.chainId)\n    });\n  }\n}\n\n\n//# sourceMappingURL=metamaskAdapter.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/metamask-adapter/dist/metamaskAdapter.esm.js\n"));

/***/ })

}]);