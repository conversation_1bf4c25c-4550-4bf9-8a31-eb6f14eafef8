"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/multiformats";
exports.ids = ["vendor-chunks/multiformats"];
exports.modules = {

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base.js":
/*!*********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Codec: () => (/* binding */ Codec),\n/* harmony export */   baseX: () => (/* binding */ baseX),\n/* harmony export */   from: () => (/* binding */ from),\n/* harmony export */   or: () => (/* binding */ or),\n/* harmony export */   rfc4648: () => (/* binding */ rfc4648)\n/* harmony export */ });\n/* harmony import */ var _vendor_base_x_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../vendor/base-x.js */ \"(ssr)/./node_modules/multiformats/esm/vendor/base-x.js\");\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n\n\nclass Encoder {\n    constructor(name, prefix, baseEncode){\n        this.name = name;\n        this.prefix = prefix;\n        this.baseEncode = baseEncode;\n    }\n    encode(bytes) {\n        if (bytes instanceof Uint8Array) {\n            return `${this.prefix}${this.baseEncode(bytes)}`;\n        } else {\n            throw Error(\"Unknown type, must be binary type\");\n        }\n    }\n}\nclass Decoder {\n    constructor(name, prefix, baseDecode){\n        this.name = name;\n        this.prefix = prefix;\n        if (prefix.codePointAt(0) === undefined) {\n            throw new Error(\"Invalid prefix character\");\n        }\n        this.prefixCodePoint = prefix.codePointAt(0);\n        this.baseDecode = baseDecode;\n    }\n    decode(text) {\n        if (typeof text === \"string\") {\n            if (text.codePointAt(0) !== this.prefixCodePoint) {\n                throw Error(`Unable to decode multibase string ${JSON.stringify(text)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);\n            }\n            return this.baseDecode(text.slice(this.prefix.length));\n        } else {\n            throw Error(\"Can only multibase decode strings\");\n        }\n    }\n    or(decoder) {\n        return or(this, decoder);\n    }\n}\nclass ComposedDecoder {\n    constructor(decoders){\n        this.decoders = decoders;\n    }\n    or(decoder) {\n        return or(this, decoder);\n    }\n    decode(input) {\n        const prefix = input[0];\n        const decoder = this.decoders[prefix];\n        if (decoder) {\n            return decoder.decode(input);\n        } else {\n            throw RangeError(`Unable to decode multibase string ${JSON.stringify(input)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`);\n        }\n    }\n}\nconst or = (left, right)=>new ComposedDecoder({\n        ...left.decoders || {\n            [left.prefix]: left\n        },\n        ...right.decoders || {\n            [right.prefix]: right\n        }\n    });\nclass Codec {\n    constructor(name, prefix, baseEncode, baseDecode){\n        this.name = name;\n        this.prefix = prefix;\n        this.baseEncode = baseEncode;\n        this.baseDecode = baseDecode;\n        this.encoder = new Encoder(name, prefix, baseEncode);\n        this.decoder = new Decoder(name, prefix, baseDecode);\n    }\n    encode(input) {\n        return this.encoder.encode(input);\n    }\n    decode(input) {\n        return this.decoder.decode(input);\n    }\n}\nconst from = ({ name, prefix, encode, decode })=>new Codec(name, prefix, encode, decode);\nconst baseX = ({ prefix, name, alphabet })=>{\n    const { encode, decode } = (0,_vendor_base_x_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(alphabet, name);\n    return from({\n        prefix,\n        name,\n        encode,\n        decode: (text)=>(0,_bytes_js__WEBPACK_IMPORTED_MODULE_1__.coerce)(decode(text))\n    });\n};\nconst decode = (string, alphabet, bitsPerChar, name)=>{\n    const codes = {};\n    for(let i = 0; i < alphabet.length; ++i){\n        codes[alphabet[i]] = i;\n    }\n    let end = string.length;\n    while(string[end - 1] === \"=\"){\n        --end;\n    }\n    const out = new Uint8Array(end * bitsPerChar / 8 | 0);\n    let bits = 0;\n    let buffer = 0;\n    let written = 0;\n    for(let i = 0; i < end; ++i){\n        const value = codes[string[i]];\n        if (value === undefined) {\n            throw new SyntaxError(`Non-${name} character`);\n        }\n        buffer = buffer << bitsPerChar | value;\n        bits += bitsPerChar;\n        if (bits >= 8) {\n            bits -= 8;\n            out[written++] = 255 & buffer >> bits;\n        }\n    }\n    if (bits >= bitsPerChar || 255 & buffer << 8 - bits) {\n        throw new SyntaxError(\"Unexpected end of data\");\n    }\n    return out;\n};\nconst encode = (data, alphabet, bitsPerChar)=>{\n    const pad = alphabet[alphabet.length - 1] === \"=\";\n    const mask = (1 << bitsPerChar) - 1;\n    let out = \"\";\n    let bits = 0;\n    let buffer = 0;\n    for(let i = 0; i < data.length; ++i){\n        buffer = buffer << 8 | data[i];\n        bits += 8;\n        while(bits > bitsPerChar){\n            bits -= bitsPerChar;\n            out += alphabet[mask & buffer >> bits];\n        }\n    }\n    if (bits) {\n        out += alphabet[mask & buffer << bitsPerChar - bits];\n    }\n    if (pad) {\n        while(out.length * bitsPerChar & 7){\n            out += \"=\";\n        }\n    }\n    return out;\n};\nconst rfc4648 = ({ name, prefix, bitsPerChar, alphabet })=>{\n    return from({\n        prefix,\n        name,\n        encode (input) {\n            return encode(input, alphabet, bitsPerChar);\n        },\n        decode (input) {\n            return decode(input, alphabet, bitsPerChar, name);\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base10.js":
/*!***********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base10.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base10: () => (/* binding */ base10)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base10 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.baseX)({\n    prefix: \"9\",\n    name: \"base10\",\n    alphabet: \"0123456789\"\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTEwLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtDO0FBQzNCLE1BQU1DLFNBQVNELCtDQUFLQSxDQUFDO0lBQzFCRSxRQUFRO0lBQ1JDLE1BQU07SUFDTkMsVUFBVTtBQUNaLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTEwLmpzPzdjNTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYmFzZVggfSBmcm9tICcuL2Jhc2UuanMnO1xuZXhwb3J0IGNvbnN0IGJhc2UxMCA9IGJhc2VYKHtcbiAgcHJlZml4OiAnOScsXG4gIG5hbWU6ICdiYXNlMTAnLFxuICBhbHBoYWJldDogJzAxMjM0NTY3ODknXG59KTsiXSwibmFtZXMiOlsiYmFzZVgiLCJiYXNlMTAiLCJwcmVmaXgiLCJuYW1lIiwiYWxwaGFiZXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base10.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base16.js":
/*!***********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base16.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base16: () => (/* binding */ base16),\n/* harmony export */   base16upper: () => (/* binding */ base16upper)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base16 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"f\",\n    name: \"base16\",\n    alphabet: \"0123456789abcdef\",\n    bitsPerChar: 4\n});\nconst base16upper = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"F\",\n    name: \"base16upper\",\n    alphabet: \"0123456789ABCDEF\",\n    bitsPerChar: 4\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTE2LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvQztBQUM3QixNQUFNQyxTQUFTRCxpREFBT0EsQ0FBQztJQUM1QkUsUUFBUTtJQUNSQyxNQUFNO0lBQ05DLFVBQVU7SUFDVkMsYUFBYTtBQUNmLEdBQUc7QUFDSSxNQUFNQyxjQUFjTixpREFBT0EsQ0FBQztJQUNqQ0UsUUFBUTtJQUNSQyxNQUFNO0lBQ05DLFVBQVU7SUFDVkMsYUFBYTtBQUNmLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTE2LmpzP2QzMDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmZjNDY0OCB9IGZyb20gJy4vYmFzZS5qcyc7XG5leHBvcnQgY29uc3QgYmFzZTE2ID0gcmZjNDY0OCh7XG4gIHByZWZpeDogJ2YnLFxuICBuYW1lOiAnYmFzZTE2JyxcbiAgYWxwaGFiZXQ6ICcwMTIzNDU2Nzg5YWJjZGVmJyxcbiAgYml0c1BlckNoYXI6IDRcbn0pO1xuZXhwb3J0IGNvbnN0IGJhc2UxNnVwcGVyID0gcmZjNDY0OCh7XG4gIHByZWZpeDogJ0YnLFxuICBuYW1lOiAnYmFzZTE2dXBwZXInLFxuICBhbHBoYWJldDogJzAxMjM0NTY3ODlBQkNERUYnLFxuICBiaXRzUGVyQ2hhcjogNFxufSk7Il0sIm5hbWVzIjpbInJmYzQ2NDgiLCJiYXNlMTYiLCJwcmVmaXgiLCJuYW1lIiwiYWxwaGFiZXQiLCJiaXRzUGVyQ2hhciIsImJhc2UxNnVwcGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base16.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base2.js":
/*!**********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base2.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base2: () => (/* binding */ base2)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base2 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"0\",\n    name: \"base2\",\n    alphabet: \"01\",\n    bitsPerChar: 1\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7QUFDN0IsTUFBTUMsUUFBUUQsaURBQU9BLENBQUM7SUFDM0JFLFFBQVE7SUFDUkMsTUFBTTtJQUNOQyxVQUFVO0lBQ1ZDLGFBQWE7QUFDZixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL211bHRpZm9ybWF0cy9lc20vc3JjL2Jhc2VzL2Jhc2UyLmpzPzNjM2IiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmZjNDY0OCB9IGZyb20gJy4vYmFzZS5qcyc7XG5leHBvcnQgY29uc3QgYmFzZTIgPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnMCcsXG4gIG5hbWU6ICdiYXNlMicsXG4gIGFscGhhYmV0OiAnMDEnLFxuICBiaXRzUGVyQ2hhcjogMVxufSk7Il0sIm5hbWVzIjpbInJmYzQ2NDgiLCJiYXNlMiIsInByZWZpeCIsIm5hbWUiLCJhbHBoYWJldCIsImJpdHNQZXJDaGFyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base256emoji.js":
/*!*****************************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base256emoji.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base256emoji: () => (/* binding */ base256emoji)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst alphabet = Array.from(\"\\uD83D\\uDE80\\uD83E\\uDE90☄\\uD83D\\uDEF0\\uD83C\\uDF0C\\uD83C\\uDF11\\uD83C\\uDF12\\uD83C\\uDF13\\uD83C\\uDF14\\uD83C\\uDF15\\uD83C\\uDF16\\uD83C\\uDF17\\uD83C\\uDF18\\uD83C\\uDF0D\\uD83C\\uDF0F\\uD83C\\uDF0E\\uD83D\\uDC09☀\\uD83D\\uDCBB\\uD83D\\uDDA5\\uD83D\\uDCBE\\uD83D\\uDCBF\\uD83D\\uDE02❤\\uD83D\\uDE0D\\uD83E\\uDD23\\uD83D\\uDE0A\\uD83D\\uDE4F\\uD83D\\uDC95\\uD83D\\uDE2D\\uD83D\\uDE18\\uD83D\\uDC4D\\uD83D\\uDE05\\uD83D\\uDC4F\\uD83D\\uDE01\\uD83D\\uDD25\\uD83E\\uDD70\\uD83D\\uDC94\\uD83D\\uDC96\\uD83D\\uDC99\\uD83D\\uDE22\\uD83E\\uDD14\\uD83D\\uDE06\\uD83D\\uDE44\\uD83D\\uDCAA\\uD83D\\uDE09☺\\uD83D\\uDC4C\\uD83E\\uDD17\\uD83D\\uDC9C\\uD83D\\uDE14\\uD83D\\uDE0E\\uD83D\\uDE07\\uD83C\\uDF39\\uD83E\\uDD26\\uD83C\\uDF89\\uD83D\\uDC9E✌✨\\uD83E\\uDD37\\uD83D\\uDE31\\uD83D\\uDE0C\\uD83C\\uDF38\\uD83D\\uDE4C\\uD83D\\uDE0B\\uD83D\\uDC97\\uD83D\\uDC9A\\uD83D\\uDE0F\\uD83D\\uDC9B\\uD83D\\uDE42\\uD83D\\uDC93\\uD83E\\uDD29\\uD83D\\uDE04\\uD83D\\uDE00\\uD83D\\uDDA4\\uD83D\\uDE03\\uD83D\\uDCAF\\uD83D\\uDE48\\uD83D\\uDC47\\uD83C\\uDFB6\\uD83D\\uDE12\\uD83E\\uDD2D❣\\uD83D\\uDE1C\\uD83D\\uDC8B\\uD83D\\uDC40\\uD83D\\uDE2A\\uD83D\\uDE11\\uD83D\\uDCA5\\uD83D\\uDE4B\\uD83D\\uDE1E\\uD83D\\uDE29\\uD83D\\uDE21\\uD83E\\uDD2A\\uD83D\\uDC4A\\uD83E\\uDD73\\uD83D\\uDE25\\uD83E\\uDD24\\uD83D\\uDC49\\uD83D\\uDC83\\uD83D\\uDE33✋\\uD83D\\uDE1A\\uD83D\\uDE1D\\uD83D\\uDE34\\uD83C\\uDF1F\\uD83D\\uDE2C\\uD83D\\uDE43\\uD83C\\uDF40\\uD83C\\uDF37\\uD83D\\uDE3B\\uD83D\\uDE13⭐✅\\uD83E\\uDD7A\\uD83C\\uDF08\\uD83D\\uDE08\\uD83E\\uDD18\\uD83D\\uDCA6✔\\uD83D\\uDE23\\uD83C\\uDFC3\\uD83D\\uDC90☹\\uD83C\\uDF8A\\uD83D\\uDC98\\uD83D\\uDE20☝\\uD83D\\uDE15\\uD83C\\uDF3A\\uD83C\\uDF82\\uD83C\\uDF3B\\uD83D\\uDE10\\uD83D\\uDD95\\uD83D\\uDC9D\\uD83D\\uDE4A\\uD83D\\uDE39\\uD83D\\uDDE3\\uD83D\\uDCAB\\uD83D\\uDC80\\uD83D\\uDC51\\uD83C\\uDFB5\\uD83E\\uDD1E\\uD83D\\uDE1B\\uD83D\\uDD34\\uD83D\\uDE24\\uD83C\\uDF3C\\uD83D\\uDE2B⚽\\uD83E\\uDD19☕\\uD83C\\uDFC6\\uD83E\\uDD2B\\uD83D\\uDC48\\uD83D\\uDE2E\\uD83D\\uDE46\\uD83C\\uDF7B\\uD83C\\uDF43\\uD83D\\uDC36\\uD83D\\uDC81\\uD83D\\uDE32\\uD83C\\uDF3F\\uD83E\\uDDE1\\uD83C\\uDF81⚡\\uD83C\\uDF1E\\uD83C\\uDF88❌✊\\uD83D\\uDC4B\\uD83D\\uDE30\\uD83E\\uDD28\\uD83D\\uDE36\\uD83E\\uDD1D\\uD83D\\uDEB6\\uD83D\\uDCB0\\uD83C\\uDF53\\uD83D\\uDCA2\\uD83E\\uDD1F\\uD83D\\uDE41\\uD83D\\uDEA8\\uD83D\\uDCA8\\uD83E\\uDD2C✈\\uD83C\\uDF80\\uD83C\\uDF7A\\uD83E\\uDD13\\uD83D\\uDE19\\uD83D\\uDC9F\\uD83C\\uDF31\\uD83D\\uDE16\\uD83D\\uDC76\\uD83E\\uDD74▶➡❓\\uD83D\\uDC8E\\uD83D\\uDCB8⬇\\uD83D\\uDE28\\uD83C\\uDF1A\\uD83E\\uDD8B\\uD83D\\uDE37\\uD83D\\uDD7A⚠\\uD83D\\uDE45\\uD83D\\uDE1F\\uD83D\\uDE35\\uD83D\\uDC4E\\uD83E\\uDD32\\uD83E\\uDD20\\uD83E\\uDD27\\uD83D\\uDCCC\\uD83D\\uDD35\\uD83D\\uDC85\\uD83E\\uDDD0\\uD83D\\uDC3E\\uD83C\\uDF52\\uD83D\\uDE17\\uD83E\\uDD11\\uD83C\\uDF0A\\uD83E\\uDD2F\\uD83D\\uDC37☎\\uD83D\\uDCA7\\uD83D\\uDE2F\\uD83D\\uDC86\\uD83D\\uDC46\\uD83C\\uDFA4\\uD83D\\uDE47\\uD83C\\uDF51❄\\uD83C\\uDF34\\uD83D\\uDCA3\\uD83D\\uDC38\\uD83D\\uDC8C\\uD83D\\uDCCD\\uD83E\\uDD40\\uD83E\\uDD22\\uD83D\\uDC45\\uD83D\\uDCA1\\uD83D\\uDCA9\\uD83D\\uDC50\\uD83D\\uDCF8\\uD83D\\uDC7B\\uD83E\\uDD10\\uD83E\\uDD2E\\uD83C\\uDFBC\\uD83E\\uDD75\\uD83D\\uDEA9\\uD83C\\uDF4E\\uD83C\\uDF4A\\uD83D\\uDC7C\\uD83D\\uDC8D\\uD83D\\uDCE3\\uD83E\\uDD42\");\nconst alphabetBytesToChars = alphabet.reduce((p, c, i)=>{\n    p[i] = c;\n    return p;\n}, []);\nconst alphabetCharsToBytes = alphabet.reduce((p, c, i)=>{\n    p[c.codePointAt(0)] = i;\n    return p;\n}, []);\nfunction encode(data) {\n    return data.reduce((p, c)=>{\n        p += alphabetBytesToChars[c];\n        return p;\n    }, \"\");\n}\nfunction decode(str) {\n    const byts = [];\n    for (const char of str){\n        const byt = alphabetCharsToBytes[char.codePointAt(0)];\n        if (byt === undefined) {\n            throw new Error(`Non-base256emoji character: ${char}`);\n        }\n        byts.push(byt);\n    }\n    return new Uint8Array(byts);\n}\nconst base256emoji = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.from)({\n    prefix: \"\\uD83D\\uDE80\",\n    name: \"base256emoji\",\n    encode,\n    decode\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base256emoji.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base32.js":
/*!***********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base32.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base32: () => (/* binding */ base32),\n/* harmony export */   base32hex: () => (/* binding */ base32hex),\n/* harmony export */   base32hexpad: () => (/* binding */ base32hexpad),\n/* harmony export */   base32hexpadupper: () => (/* binding */ base32hexpadupper),\n/* harmony export */   base32hexupper: () => (/* binding */ base32hexupper),\n/* harmony export */   base32pad: () => (/* binding */ base32pad),\n/* harmony export */   base32padupper: () => (/* binding */ base32padupper),\n/* harmony export */   base32upper: () => (/* binding */ base32upper),\n/* harmony export */   base32z: () => (/* binding */ base32z)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base32 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"b\",\n    name: \"base32\",\n    alphabet: \"abcdefghijklmnopqrstuvwxyz234567\",\n    bitsPerChar: 5\n});\nconst base32upper = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"B\",\n    name: \"base32upper\",\n    alphabet: \"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567\",\n    bitsPerChar: 5\n});\nconst base32pad = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"c\",\n    name: \"base32pad\",\n    alphabet: \"abcdefghijklmnopqrstuvwxyz234567=\",\n    bitsPerChar: 5\n});\nconst base32padupper = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"C\",\n    name: \"base32padupper\",\n    alphabet: \"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=\",\n    bitsPerChar: 5\n});\nconst base32hex = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"v\",\n    name: \"base32hex\",\n    alphabet: \"0123456789abcdefghijklmnopqrstuv\",\n    bitsPerChar: 5\n});\nconst base32hexupper = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"V\",\n    name: \"base32hexupper\",\n    alphabet: \"0123456789ABCDEFGHIJKLMNOPQRSTUV\",\n    bitsPerChar: 5\n});\nconst base32hexpad = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"t\",\n    name: \"base32hexpad\",\n    alphabet: \"0123456789abcdefghijklmnopqrstuv=\",\n    bitsPerChar: 5\n});\nconst base32hexpadupper = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"T\",\n    name: \"base32hexpadupper\",\n    alphabet: \"0123456789ABCDEFGHIJKLMNOPQRSTUV=\",\n    bitsPerChar: 5\n});\nconst base32z = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"h\",\n    name: \"base32z\",\n    alphabet: \"ybndrfg8ejkmcpqxot1uwisza345h769\",\n    bitsPerChar: 5\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTMyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBb0M7QUFDN0IsTUFBTUMsU0FBU0QsaURBQU9BLENBQUM7SUFDNUJFLFFBQVE7SUFDUkMsTUFBTTtJQUNOQyxVQUFVO0lBQ1ZDLGFBQWE7QUFDZixHQUFHO0FBQ0ksTUFBTUMsY0FBY04saURBQU9BLENBQUM7SUFDakNFLFFBQVE7SUFDUkMsTUFBTTtJQUNOQyxVQUFVO0lBQ1ZDLGFBQWE7QUFDZixHQUFHO0FBQ0ksTUFBTUUsWUFBWVAsaURBQU9BLENBQUM7SUFDL0JFLFFBQVE7SUFDUkMsTUFBTTtJQUNOQyxVQUFVO0lBQ1ZDLGFBQWE7QUFDZixHQUFHO0FBQ0ksTUFBTUcsaUJBQWlCUixpREFBT0EsQ0FBQztJQUNwQ0UsUUFBUTtJQUNSQyxNQUFNO0lBQ05DLFVBQVU7SUFDVkMsYUFBYTtBQUNmLEdBQUc7QUFDSSxNQUFNSSxZQUFZVCxpREFBT0EsQ0FBQztJQUMvQkUsUUFBUTtJQUNSQyxNQUFNO0lBQ05DLFVBQVU7SUFDVkMsYUFBYTtBQUNmLEdBQUc7QUFDSSxNQUFNSyxpQkFBaUJWLGlEQUFPQSxDQUFDO0lBQ3BDRSxRQUFRO0lBQ1JDLE1BQU07SUFDTkMsVUFBVTtJQUNWQyxhQUFhO0FBQ2YsR0FBRztBQUNJLE1BQU1NLGVBQWVYLGlEQUFPQSxDQUFDO0lBQ2xDRSxRQUFRO0lBQ1JDLE1BQU07SUFDTkMsVUFBVTtJQUNWQyxhQUFhO0FBQ2YsR0FBRztBQUNJLE1BQU1PLG9CQUFvQlosaURBQU9BLENBQUM7SUFDdkNFLFFBQVE7SUFDUkMsTUFBTTtJQUNOQyxVQUFVO0lBQ1ZDLGFBQWE7QUFDZixHQUFHO0FBQ0ksTUFBTVEsVUFBVWIsaURBQU9BLENBQUM7SUFDN0JFLFFBQVE7SUFDUkMsTUFBTTtJQUNOQyxVQUFVO0lBQ1ZDLGFBQWE7QUFDZixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL211bHRpZm9ybWF0cy9lc20vc3JjL2Jhc2VzL2Jhc2UzMi5qcz9kNWFmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJmYzQ2NDggfSBmcm9tICcuL2Jhc2UuanMnO1xuZXhwb3J0IGNvbnN0IGJhc2UzMiA9IHJmYzQ2NDgoe1xuICBwcmVmaXg6ICdiJyxcbiAgbmFtZTogJ2Jhc2UzMicsXG4gIGFscGhhYmV0OiAnYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXoyMzQ1NjcnLFxuICBiaXRzUGVyQ2hhcjogNVxufSk7XG5leHBvcnQgY29uc3QgYmFzZTMydXBwZXIgPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnQicsXG4gIG5hbWU6ICdiYXNlMzJ1cHBlcicsXG4gIGFscGhhYmV0OiAnQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVoyMzQ1NjcnLFxuICBiaXRzUGVyQ2hhcjogNVxufSk7XG5leHBvcnQgY29uc3QgYmFzZTMycGFkID0gcmZjNDY0OCh7XG4gIHByZWZpeDogJ2MnLFxuICBuYW1lOiAnYmFzZTMycGFkJyxcbiAgYWxwaGFiZXQ6ICdhYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ejIzNDU2Nz0nLFxuICBiaXRzUGVyQ2hhcjogNVxufSk7XG5leHBvcnQgY29uc3QgYmFzZTMycGFkdXBwZXIgPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnQycsXG4gIG5hbWU6ICdiYXNlMzJwYWR1cHBlcicsXG4gIGFscGhhYmV0OiAnQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVoyMzQ1Njc9JyxcbiAgYml0c1BlckNoYXI6IDVcbn0pO1xuZXhwb3J0IGNvbnN0IGJhc2UzMmhleCA9IHJmYzQ2NDgoe1xuICBwcmVmaXg6ICd2JyxcbiAgbmFtZTogJ2Jhc2UzMmhleCcsXG4gIGFscGhhYmV0OiAnMDEyMzQ1Njc4OWFiY2RlZmdoaWprbG1ub3BxcnN0dXYnLFxuICBiaXRzUGVyQ2hhcjogNVxufSk7XG5leHBvcnQgY29uc3QgYmFzZTMyaGV4dXBwZXIgPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnVicsXG4gIG5hbWU6ICdiYXNlMzJoZXh1cHBlcicsXG4gIGFscGhhYmV0OiAnMDEyMzQ1Njc4OUFCQ0RFRkdISUpLTE1OT1BRUlNUVVYnLFxuICBiaXRzUGVyQ2hhcjogNVxufSk7XG5leHBvcnQgY29uc3QgYmFzZTMyaGV4cGFkID0gcmZjNDY0OCh7XG4gIHByZWZpeDogJ3QnLFxuICBuYW1lOiAnYmFzZTMyaGV4cGFkJyxcbiAgYWxwaGFiZXQ6ICcwMTIzNDU2Nzg5YWJjZGVmZ2hpamtsbW5vcHFyc3R1dj0nLFxuICBiaXRzUGVyQ2hhcjogNVxufSk7XG5leHBvcnQgY29uc3QgYmFzZTMyaGV4cGFkdXBwZXIgPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnVCcsXG4gIG5hbWU6ICdiYXNlMzJoZXhwYWR1cHBlcicsXG4gIGFscGhhYmV0OiAnMDEyMzQ1Njc4OUFCQ0RFRkdISUpLTE1OT1BRUlNUVVY9JyxcbiAgYml0c1BlckNoYXI6IDVcbn0pO1xuZXhwb3J0IGNvbnN0IGJhc2UzMnogPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnaCcsXG4gIG5hbWU6ICdiYXNlMzJ6JyxcbiAgYWxwaGFiZXQ6ICd5Ym5kcmZnOGVqa21jcHF4b3QxdXdpc3phMzQ1aDc2OScsXG4gIGJpdHNQZXJDaGFyOiA1XG59KTsiXSwibmFtZXMiOlsicmZjNDY0OCIsImJhc2UzMiIsInByZWZpeCIsIm5hbWUiLCJhbHBoYWJldCIsImJpdHNQZXJDaGFyIiwiYmFzZTMydXBwZXIiLCJiYXNlMzJwYWQiLCJiYXNlMzJwYWR1cHBlciIsImJhc2UzMmhleCIsImJhc2UzMmhleHVwcGVyIiwiYmFzZTMyaGV4cGFkIiwiYmFzZTMyaGV4cGFkdXBwZXIiLCJiYXNlMzJ6Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base32.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base36.js":
/*!***********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base36.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base36: () => (/* binding */ base36),\n/* harmony export */   base36upper: () => (/* binding */ base36upper)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base36 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.baseX)({\n    prefix: \"k\",\n    name: \"base36\",\n    alphabet: \"0123456789abcdefghijklmnopqrstuvwxyz\"\n});\nconst base36upper = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.baseX)({\n    prefix: \"K\",\n    name: \"base36upper\",\n    alphabet: \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ\"\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTM2LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrQztBQUMzQixNQUFNQyxTQUFTRCwrQ0FBS0EsQ0FBQztJQUMxQkUsUUFBUTtJQUNSQyxNQUFNO0lBQ05DLFVBQVU7QUFDWixHQUFHO0FBQ0ksTUFBTUMsY0FBY0wsK0NBQUtBLENBQUM7SUFDL0JFLFFBQVE7SUFDUkMsTUFBTTtJQUNOQyxVQUFVO0FBQ1osR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXdlYjMtY3JlYXRvci1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9tdWx0aWZvcm1hdHMvZXNtL3NyYy9iYXNlcy9iYXNlMzYuanM/MWU3YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBiYXNlWCB9IGZyb20gJy4vYmFzZS5qcyc7XG5leHBvcnQgY29uc3QgYmFzZTM2ID0gYmFzZVgoe1xuICBwcmVmaXg6ICdrJyxcbiAgbmFtZTogJ2Jhc2UzNicsXG4gIGFscGhhYmV0OiAnMDEyMzQ1Njc4OWFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6J1xufSk7XG5leHBvcnQgY29uc3QgYmFzZTM2dXBwZXIgPSBiYXNlWCh7XG4gIHByZWZpeDogJ0snLFxuICBuYW1lOiAnYmFzZTM2dXBwZXInLFxuICBhbHBoYWJldDogJzAxMjM0NTY3ODlBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWidcbn0pOyJdLCJuYW1lcyI6WyJiYXNlWCIsImJhc2UzNiIsInByZWZpeCIsIm5hbWUiLCJhbHBoYWJldCIsImJhc2UzNnVwcGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base36.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base58.js":
/*!***********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base58.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base58btc: () => (/* binding */ base58btc),\n/* harmony export */   base58flickr: () => (/* binding */ base58flickr)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base58btc = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.baseX)({\n    name: \"base58btc\",\n    prefix: \"z\",\n    alphabet: \"**********************************************************\"\n});\nconst base58flickr = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.baseX)({\n    name: \"base58flickr\",\n    prefix: \"Z\",\n    alphabet: \"**********************************************************\"\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTU4LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrQztBQUMzQixNQUFNQyxZQUFZRCwrQ0FBS0EsQ0FBQztJQUM3QkUsTUFBTTtJQUNOQyxRQUFRO0lBQ1JDLFVBQVU7QUFDWixHQUFHO0FBQ0ksTUFBTUMsZUFBZUwsK0NBQUtBLENBQUM7SUFDaENFLE1BQU07SUFDTkMsUUFBUTtJQUNSQyxVQUFVO0FBQ1osR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXdlYjMtY3JlYXRvci1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9tdWx0aWZvcm1hdHMvZXNtL3NyYy9iYXNlcy9iYXNlNTguanM/OGQ0OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBiYXNlWCB9IGZyb20gJy4vYmFzZS5qcyc7XG5leHBvcnQgY29uc3QgYmFzZTU4YnRjID0gYmFzZVgoe1xuICBuYW1lOiAnYmFzZTU4YnRjJyxcbiAgcHJlZml4OiAneicsXG4gIGFscGhhYmV0OiAnMTIzNDU2Nzg5QUJDREVGR0hKS0xNTlBRUlNUVVZXWFlaYWJjZGVmZ2hpamttbm9wcXJzdHV2d3h5eidcbn0pO1xuZXhwb3J0IGNvbnN0IGJhc2U1OGZsaWNrciA9IGJhc2VYKHtcbiAgbmFtZTogJ2Jhc2U1OGZsaWNrcicsXG4gIHByZWZpeDogJ1onLFxuICBhbHBoYWJldDogJzEyMzQ1Njc4OWFiY2RlZmdoaWprbW5vcHFyc3R1dnd4eXpBQkNERUZHSEpLTE1OUFFSU1RVVldYWVonXG59KTsiXSwibmFtZXMiOlsiYmFzZVgiLCJiYXNlNThidGMiLCJuYW1lIiwicHJlZml4IiwiYWxwaGFiZXQiLCJiYXNlNThmbGlja3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base58.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base64.js":
/*!***********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base64.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base64: () => (/* binding */ base64),\n/* harmony export */   base64pad: () => (/* binding */ base64pad),\n/* harmony export */   base64url: () => (/* binding */ base64url),\n/* harmony export */   base64urlpad: () => (/* binding */ base64urlpad)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base64 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"m\",\n    name: \"base64\",\n    alphabet: \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",\n    bitsPerChar: 6\n});\nconst base64pad = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"M\",\n    name: \"base64pad\",\n    alphabet: \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\",\n    bitsPerChar: 6\n});\nconst base64url = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"u\",\n    name: \"base64url\",\n    alphabet: \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_\",\n    bitsPerChar: 6\n});\nconst base64urlpad = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"U\",\n    name: \"base64urlpad\",\n    alphabet: \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=\",\n    bitsPerChar: 6\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTY0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW9DO0FBQzdCLE1BQU1DLFNBQVNELGlEQUFPQSxDQUFDO0lBQzVCRSxRQUFRO0lBQ1JDLE1BQU07SUFDTkMsVUFBVTtJQUNWQyxhQUFhO0FBQ2YsR0FBRztBQUNJLE1BQU1DLFlBQVlOLGlEQUFPQSxDQUFDO0lBQy9CRSxRQUFRO0lBQ1JDLE1BQU07SUFDTkMsVUFBVTtJQUNWQyxhQUFhO0FBQ2YsR0FBRztBQUNJLE1BQU1FLFlBQVlQLGlEQUFPQSxDQUFDO0lBQy9CRSxRQUFRO0lBQ1JDLE1BQU07SUFDTkMsVUFBVTtJQUNWQyxhQUFhO0FBQ2YsR0FBRztBQUNJLE1BQU1HLGVBQWVSLGlEQUFPQSxDQUFDO0lBQ2xDRSxRQUFRO0lBQ1JDLE1BQU07SUFDTkMsVUFBVTtJQUNWQyxhQUFhO0FBQ2YsR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXdlYjMtY3JlYXRvci1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9tdWx0aWZvcm1hdHMvZXNtL3NyYy9iYXNlcy9iYXNlNjQuanM/YzFmYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZmM0NjQ4IH0gZnJvbSAnLi9iYXNlLmpzJztcbmV4cG9ydCBjb25zdCBiYXNlNjQgPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnbScsXG4gIG5hbWU6ICdiYXNlNjQnLFxuICBhbHBoYWJldDogJ0FCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5Ky8nLFxuICBiaXRzUGVyQ2hhcjogNlxufSk7XG5leHBvcnQgY29uc3QgYmFzZTY0cGFkID0gcmZjNDY0OCh7XG4gIHByZWZpeDogJ00nLFxuICBuYW1lOiAnYmFzZTY0cGFkJyxcbiAgYWxwaGFiZXQ6ICdBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWmFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6MDEyMzQ1Njc4OSsvPScsXG4gIGJpdHNQZXJDaGFyOiA2XG59KTtcbmV4cG9ydCBjb25zdCBiYXNlNjR1cmwgPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAndScsXG4gIG5hbWU6ICdiYXNlNjR1cmwnLFxuICBhbHBoYWJldDogJ0FCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5LV8nLFxuICBiaXRzUGVyQ2hhcjogNlxufSk7XG5leHBvcnQgY29uc3QgYmFzZTY0dXJscGFkID0gcmZjNDY0OCh7XG4gIHByZWZpeDogJ1UnLFxuICBuYW1lOiAnYmFzZTY0dXJscGFkJyxcbiAgYWxwaGFiZXQ6ICdBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWmFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6MDEyMzQ1Njc4OS1fPScsXG4gIGJpdHNQZXJDaGFyOiA2XG59KTsiXSwibmFtZXMiOlsicmZjNDY0OCIsImJhc2U2NCIsInByZWZpeCIsIm5hbWUiLCJhbHBoYWJldCIsImJpdHNQZXJDaGFyIiwiYmFzZTY0cGFkIiwiYmFzZTY0dXJsIiwiYmFzZTY0dXJscGFkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base8.js":
/*!**********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base8.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base8: () => (/* binding */ base8)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base8 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n    prefix: \"7\",\n    name: \"base8\",\n    alphabet: \"01234567\",\n    bitsPerChar: 3\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7QUFDN0IsTUFBTUMsUUFBUUQsaURBQU9BLENBQUM7SUFDM0JFLFFBQVE7SUFDUkMsTUFBTTtJQUNOQyxVQUFVO0lBQ1ZDLGFBQWE7QUFDZixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL211bHRpZm9ybWF0cy9lc20vc3JjL2Jhc2VzL2Jhc2U4LmpzP2I2MGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmZjNDY0OCB9IGZyb20gJy4vYmFzZS5qcyc7XG5leHBvcnQgY29uc3QgYmFzZTggPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnNycsXG4gIG5hbWU6ICdiYXNlOCcsXG4gIGFscGhhYmV0OiAnMDEyMzQ1NjcnLFxuICBiaXRzUGVyQ2hhcjogM1xufSk7Il0sIm5hbWVzIjpbInJmYzQ2NDgiLCJiYXNlOCIsInByZWZpeCIsIm5hbWUiLCJhbHBoYWJldCIsImJpdHNQZXJDaGFyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base8.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/identity.js":
/*!*************************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/identity.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   identity: () => (/* binding */ identity)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n\n\nconst identity = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.from)({\n    prefix: \"\\x00\",\n    name: \"identity\",\n    encode: (buf)=>(0,_bytes_js__WEBPACK_IMPORTED_MODULE_1__.toString)(buf),\n    decode: (str)=>(0,_bytes_js__WEBPACK_IMPORTED_MODULE_1__.fromString)(str)\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvaWRlbnRpdHkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlDO0FBSVo7QUFDZCxNQUFNRyxXQUFXSCw4Q0FBSUEsQ0FBQztJQUMzQkksUUFBUTtJQUNSQyxNQUFNO0lBQ05DLFFBQVFDLENBQUFBLE1BQU9MLG1EQUFRQSxDQUFDSztJQUN4QkMsUUFBUUMsQ0FBQUEsTUFBT1IscURBQVVBLENBQUNRO0FBQzVCLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvaWRlbnRpdHkuanM/ZmZjYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBmcm9tIH0gZnJvbSAnLi9iYXNlLmpzJztcbmltcG9ydCB7XG4gIGZyb21TdHJpbmcsXG4gIHRvU3RyaW5nXG59IGZyb20gJy4uL2J5dGVzLmpzJztcbmV4cG9ydCBjb25zdCBpZGVudGl0eSA9IGZyb20oe1xuICBwcmVmaXg6ICdcXDAnLFxuICBuYW1lOiAnaWRlbnRpdHknLFxuICBlbmNvZGU6IGJ1ZiA9PiB0b1N0cmluZyhidWYpLFxuICBkZWNvZGU6IHN0ciA9PiBmcm9tU3RyaW5nKHN0cilcbn0pOyJdLCJuYW1lcyI6WyJmcm9tIiwiZnJvbVN0cmluZyIsInRvU3RyaW5nIiwiaWRlbnRpdHkiLCJwcmVmaXgiLCJuYW1lIiwiZW5jb2RlIiwiYnVmIiwiZGVjb2RlIiwic3RyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/basics.js":
/*!*****************************************************!*\
  !*** ./node_modules/multiformats/esm/src/basics.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CID: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_14__.CID),\n/* harmony export */   bases: () => (/* binding */ bases),\n/* harmony export */   bytes: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_14__.bytes),\n/* harmony export */   codecs: () => (/* binding */ codecs),\n/* harmony export */   digest: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_14__.digest),\n/* harmony export */   hasher: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_14__.hasher),\n/* harmony export */   hashes: () => (/* binding */ hashes),\n/* harmony export */   varint: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_14__.varint)\n/* harmony export */ });\n/* harmony import */ var _bases_identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bases/identity.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/identity.js\");\n/* harmony import */ var _bases_base2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./bases/base2.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base2.js\");\n/* harmony import */ var _bases_base8_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bases/base8.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base8.js\");\n/* harmony import */ var _bases_base10_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./bases/base10.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base10.js\");\n/* harmony import */ var _bases_base16_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./bases/base16.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base16.js\");\n/* harmony import */ var _bases_base32_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./bases/base32.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base32.js\");\n/* harmony import */ var _bases_base36_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./bases/base36.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base36.js\");\n/* harmony import */ var _bases_base58_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./bases/base58.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base58.js\");\n/* harmony import */ var _bases_base64_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./bases/base64.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base64.js\");\n/* harmony import */ var _bases_base256emoji_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./bases/base256emoji.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base256emoji.js\");\n/* harmony import */ var _hashes_sha2_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./hashes/sha2.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/sha2.js\");\n/* harmony import */ var _hashes_identity_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hashes/identity.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/identity.js\");\n/* harmony import */ var _codecs_raw_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./codecs/raw.js */ \"(ssr)/./node_modules/multiformats/esm/src/codecs/raw.js\");\n/* harmony import */ var _codecs_json_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./codecs/json.js */ \"(ssr)/./node_modules/multiformats/esm/src/codecs/json.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/multiformats/esm/src/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst bases = {\n    ..._bases_identity_js__WEBPACK_IMPORTED_MODULE_0__,\n    ..._bases_base2_js__WEBPACK_IMPORTED_MODULE_1__,\n    ..._bases_base8_js__WEBPACK_IMPORTED_MODULE_2__,\n    ..._bases_base10_js__WEBPACK_IMPORTED_MODULE_3__,\n    ..._bases_base16_js__WEBPACK_IMPORTED_MODULE_4__,\n    ..._bases_base32_js__WEBPACK_IMPORTED_MODULE_5__,\n    ..._bases_base36_js__WEBPACK_IMPORTED_MODULE_6__,\n    ..._bases_base58_js__WEBPACK_IMPORTED_MODULE_7__,\n    ..._bases_base64_js__WEBPACK_IMPORTED_MODULE_8__,\n    ..._bases_base256emoji_js__WEBPACK_IMPORTED_MODULE_9__\n};\nconst hashes = {\n    ..._hashes_sha2_js__WEBPACK_IMPORTED_MODULE_10__,\n    ..._hashes_identity_js__WEBPACK_IMPORTED_MODULE_11__\n};\nconst codecs = {\n    raw: _codecs_raw_js__WEBPACK_IMPORTED_MODULE_12__,\n    json: _codecs_json_js__WEBPACK_IMPORTED_MODULE_13__\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/basics.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bytes.js":
/*!****************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bytes.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   coerce: () => (/* binding */ coerce),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   equals: () => (/* binding */ equals),\n/* harmony export */   fromHex: () => (/* binding */ fromHex),\n/* harmony export */   fromString: () => (/* binding */ fromString),\n/* harmony export */   isBinary: () => (/* binding */ isBinary),\n/* harmony export */   toHex: () => (/* binding */ toHex),\n/* harmony export */   toString: () => (/* binding */ toString)\n/* harmony export */ });\nconst empty = new Uint8Array(0);\nconst toHex = (d)=>d.reduce((hex, byte)=>hex + byte.toString(16).padStart(2, \"0\"), \"\");\nconst fromHex = (hex)=>{\n    const hexes = hex.match(/../g);\n    return hexes ? new Uint8Array(hexes.map((b)=>parseInt(b, 16))) : empty;\n};\nconst equals = (aa, bb)=>{\n    if (aa === bb) return true;\n    if (aa.byteLength !== bb.byteLength) {\n        return false;\n    }\n    for(let ii = 0; ii < aa.byteLength; ii++){\n        if (aa[ii] !== bb[ii]) {\n            return false;\n        }\n    }\n    return true;\n};\nconst coerce = (o)=>{\n    if (o instanceof Uint8Array && o.constructor.name === \"Uint8Array\") return o;\n    if (o instanceof ArrayBuffer) return new Uint8Array(o);\n    if (ArrayBuffer.isView(o)) {\n        return new Uint8Array(o.buffer, o.byteOffset, o.byteLength);\n    }\n    throw new Error(\"Unknown type, must be binary type\");\n};\nconst isBinary = (o)=>o instanceof ArrayBuffer || ArrayBuffer.isView(o);\nconst fromString = (str)=>new TextEncoder().encode(str);\nconst toString = (b)=>new TextDecoder().decode(b);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/cid.js":
/*!**************************************************!*\
  !*** ./node_modules/multiformats/esm/src/cid.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CID: () => (/* binding */ CID)\n/* harmony export */ });\n/* harmony import */ var _varint_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./varint.js */ \"(ssr)/./node_modules/multiformats/esm/src/varint.js\");\n/* harmony import */ var _hashes_digest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hashes/digest.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/digest.js\");\n/* harmony import */ var _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bases/base58.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base58.js\");\n/* harmony import */ var _bases_base32_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./bases/base32.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base32.js\");\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n\n\n\n\n\nclass CID {\n    constructor(version, code, multihash, bytes){\n        this.code = code;\n        this.version = version;\n        this.multihash = multihash;\n        this.bytes = bytes;\n        this.byteOffset = bytes.byteOffset;\n        this.byteLength = bytes.byteLength;\n        this.asCID = this;\n        this._baseCache = new Map();\n        Object.defineProperties(this, {\n            byteOffset: hidden,\n            byteLength: hidden,\n            code: readonly,\n            version: readonly,\n            multihash: readonly,\n            bytes: readonly,\n            _baseCache: hidden,\n            asCID: hidden\n        });\n    }\n    toV0() {\n        switch(this.version){\n            case 0:\n                {\n                    return this;\n                }\n            default:\n                {\n                    const { code, multihash } = this;\n                    if (code !== DAG_PB_CODE) {\n                        throw new Error(\"Cannot convert a non dag-pb CID to CIDv0\");\n                    }\n                    if (multihash.code !== SHA_256_CODE) {\n                        throw new Error(\"Cannot convert non sha2-256 multihash CID to CIDv0\");\n                    }\n                    return CID.createV0(multihash);\n                }\n        }\n    }\n    toV1() {\n        switch(this.version){\n            case 0:\n                {\n                    const { code, digest } = this.multihash;\n                    const multihash = _hashes_digest_js__WEBPACK_IMPORTED_MODULE_1__.create(code, digest);\n                    return CID.createV1(this.code, multihash);\n                }\n            case 1:\n                {\n                    return this;\n                }\n            default:\n                {\n                    throw Error(`Can not convert CID version ${this.version} to version 0. This is a bug please report`);\n                }\n        }\n    }\n    equals(other) {\n        return other && this.code === other.code && this.version === other.version && _hashes_digest_js__WEBPACK_IMPORTED_MODULE_1__.equals(this.multihash, other.multihash);\n    }\n    toString(base) {\n        const { bytes, version, _baseCache } = this;\n        switch(version){\n            case 0:\n                return toStringV0(bytes, _baseCache, base || _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc.encoder);\n            default:\n                return toStringV1(bytes, _baseCache, base || _bases_base32_js__WEBPACK_IMPORTED_MODULE_3__.base32.encoder);\n        }\n    }\n    toJSON() {\n        return {\n            code: this.code,\n            version: this.version,\n            hash: this.multihash.bytes\n        };\n    }\n    get [Symbol.toStringTag]() {\n        return \"CID\";\n    }\n    [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n        return \"CID(\" + this.toString() + \")\";\n    }\n    static isCID(value) {\n        deprecate(/^0\\.0/, IS_CID_DEPRECATION);\n        return !!(value && (value[cidSymbol] || value.asCID === value));\n    }\n    get toBaseEncodedString() {\n        throw new Error(\"Deprecated, use .toString()\");\n    }\n    get codec() {\n        throw new Error('\"codec\" property is deprecated, use integer \"code\" property instead');\n    }\n    get buffer() {\n        throw new Error(\"Deprecated .buffer property, use .bytes to get Uint8Array instead\");\n    }\n    get multibaseName() {\n        throw new Error('\"multibaseName\" property is deprecated');\n    }\n    get prefix() {\n        throw new Error('\"prefix\" property is deprecated');\n    }\n    static asCID(value) {\n        if (value instanceof CID) {\n            return value;\n        } else if (value != null && value.asCID === value) {\n            const { version, code, multihash, bytes } = value;\n            return new CID(version, code, multihash, bytes || encodeCID(version, code, multihash.bytes));\n        } else if (value != null && value[cidSymbol] === true) {\n            const { version, multihash, code } = value;\n            const digest = _hashes_digest_js__WEBPACK_IMPORTED_MODULE_1__.decode(multihash);\n            return CID.create(version, code, digest);\n        } else {\n            return null;\n        }\n    }\n    static create(version, code, digest) {\n        if (typeof code !== \"number\") {\n            throw new Error(\"String codecs are no longer supported\");\n        }\n        switch(version){\n            case 0:\n                {\n                    if (code !== DAG_PB_CODE) {\n                        throw new Error(`Version 0 CID must use dag-pb (code: ${DAG_PB_CODE}) block encoding`);\n                    } else {\n                        return new CID(version, code, digest, digest.bytes);\n                    }\n                }\n            case 1:\n                {\n                    const bytes = encodeCID(version, code, digest.bytes);\n                    return new CID(version, code, digest, bytes);\n                }\n            default:\n                {\n                    throw new Error(\"Invalid version\");\n                }\n        }\n    }\n    static createV0(digest) {\n        return CID.create(0, DAG_PB_CODE, digest);\n    }\n    static createV1(code, digest) {\n        return CID.create(1, code, digest);\n    }\n    static decode(bytes) {\n        const [cid, remainder] = CID.decodeFirst(bytes);\n        if (remainder.length) {\n            throw new Error(\"Incorrect length\");\n        }\n        return cid;\n    }\n    static decodeFirst(bytes) {\n        const specs = CID.inspectBytes(bytes);\n        const prefixSize = specs.size - specs.multihashSize;\n        const multihashBytes = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_4__.coerce)(bytes.subarray(prefixSize, prefixSize + specs.multihashSize));\n        if (multihashBytes.byteLength !== specs.multihashSize) {\n            throw new Error(\"Incorrect length\");\n        }\n        const digestBytes = multihashBytes.subarray(specs.multihashSize - specs.digestSize);\n        const digest = new _hashes_digest_js__WEBPACK_IMPORTED_MODULE_1__.Digest(specs.multihashCode, specs.digestSize, digestBytes, multihashBytes);\n        const cid = specs.version === 0 ? CID.createV0(digest) : CID.createV1(specs.codec, digest);\n        return [\n            cid,\n            bytes.subarray(specs.size)\n        ];\n    }\n    static inspectBytes(initialBytes) {\n        let offset = 0;\n        const next = ()=>{\n            const [i, length] = _varint_js__WEBPACK_IMPORTED_MODULE_0__.decode(initialBytes.subarray(offset));\n            offset += length;\n            return i;\n        };\n        let version = next();\n        let codec = DAG_PB_CODE;\n        if (version === 18) {\n            version = 0;\n            offset = 0;\n        } else if (version === 1) {\n            codec = next();\n        }\n        if (version !== 0 && version !== 1) {\n            throw new RangeError(`Invalid CID version ${version}`);\n        }\n        const prefixSize = offset;\n        const multihashCode = next();\n        const digestSize = next();\n        const size = offset + digestSize;\n        const multihashSize = size - prefixSize;\n        return {\n            version,\n            codec,\n            multihashCode,\n            digestSize,\n            multihashSize,\n            size\n        };\n    }\n    static parse(source, base) {\n        const [prefix, bytes] = parseCIDtoBytes(source, base);\n        const cid = CID.decode(bytes);\n        cid._baseCache.set(prefix, source);\n        return cid;\n    }\n}\nconst parseCIDtoBytes = (source, base)=>{\n    switch(source[0]){\n        case \"Q\":\n            {\n                const decoder = base || _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc;\n                return [\n                    _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc.prefix,\n                    decoder.decode(`${_bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc.prefix}${source}`)\n                ];\n            }\n        case _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc.prefix:\n            {\n                const decoder = base || _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc;\n                return [\n                    _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc.prefix,\n                    decoder.decode(source)\n                ];\n            }\n        case _bases_base32_js__WEBPACK_IMPORTED_MODULE_3__.base32.prefix:\n            {\n                const decoder = base || _bases_base32_js__WEBPACK_IMPORTED_MODULE_3__.base32;\n                return [\n                    _bases_base32_js__WEBPACK_IMPORTED_MODULE_3__.base32.prefix,\n                    decoder.decode(source)\n                ];\n            }\n        default:\n            {\n                if (base == null) {\n                    throw Error(\"To parse non base32 or base58btc encoded CID multibase decoder must be provided\");\n                }\n                return [\n                    source[0],\n                    base.decode(source)\n                ];\n            }\n    }\n};\nconst toStringV0 = (bytes, cache, base)=>{\n    const { prefix } = base;\n    if (prefix !== _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc.prefix) {\n        throw Error(`Cannot string encode V0 in ${base.name} encoding`);\n    }\n    const cid = cache.get(prefix);\n    if (cid == null) {\n        const cid = base.encode(bytes).slice(1);\n        cache.set(prefix, cid);\n        return cid;\n    } else {\n        return cid;\n    }\n};\nconst toStringV1 = (bytes, cache, base)=>{\n    const { prefix } = base;\n    const cid = cache.get(prefix);\n    if (cid == null) {\n        const cid = base.encode(bytes);\n        cache.set(prefix, cid);\n        return cid;\n    } else {\n        return cid;\n    }\n};\nconst DAG_PB_CODE = 112;\nconst SHA_256_CODE = 18;\nconst encodeCID = (version, code, multihash)=>{\n    const codeOffset = _varint_js__WEBPACK_IMPORTED_MODULE_0__.encodingLength(version);\n    const hashOffset = codeOffset + _varint_js__WEBPACK_IMPORTED_MODULE_0__.encodingLength(code);\n    const bytes = new Uint8Array(hashOffset + multihash.byteLength);\n    _varint_js__WEBPACK_IMPORTED_MODULE_0__.encodeTo(version, bytes, 0);\n    _varint_js__WEBPACK_IMPORTED_MODULE_0__.encodeTo(code, bytes, codeOffset);\n    bytes.set(multihash, hashOffset);\n    return bytes;\n};\nconst cidSymbol = Symbol.for(\"@ipld/js-cid/CID\");\nconst readonly = {\n    writable: false,\n    configurable: false,\n    enumerable: true\n};\nconst hidden = {\n    writable: false,\n    enumerable: false,\n    configurable: false\n};\nconst version = \"0.0.0-dev\";\nconst deprecate = (range, message)=>{\n    if (range.test(version)) {\n        console.warn(message);\n    } else {\n        throw new Error(message);\n    }\n};\nconst IS_CID_DEPRECATION = `CID.isCID(v) is deprecated and will be removed in the next major release.\nFollowing code pattern:\n\nif (CID.isCID(value)) {\n  doSomethingWithCID(value)\n}\n\nIs replaced with:\n\nconst cid = CID.asCID(value)\nif (cid) {\n  // Make sure to use cid instead of value\n  doSomethingWithCID(cid)\n}\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/cid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/codecs/json.js":
/*!**********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/codecs/json.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code),\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   name: () => (/* binding */ name)\n/* harmony export */ });\nconst textEncoder = new TextEncoder();\nconst textDecoder = new TextDecoder();\nconst name = \"json\";\nconst code = 512;\nconst encode = (node)=>textEncoder.encode(JSON.stringify(node));\nconst decode = (data)=>JSON.parse(textDecoder.decode(data));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvY29kZWNzL2pzb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLE1BQU1BLGNBQWMsSUFBSUM7QUFDeEIsTUFBTUMsY0FBYyxJQUFJQztBQUNqQixNQUFNQyxPQUFPLE9BQU87QUFDcEIsTUFBTUMsT0FBTyxJQUFJO0FBQ2pCLE1BQU1DLFNBQVNDLENBQUFBLE9BQVFQLFlBQVlNLE1BQU0sQ0FBQ0UsS0FBS0MsU0FBUyxDQUFDRixPQUFPO0FBQ2hFLE1BQU1HLFNBQVNDLENBQUFBLE9BQVFILEtBQUtJLEtBQUssQ0FBQ1YsWUFBWVEsTUFBTSxDQUFDQyxPQUFPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL211bHRpZm9ybWF0cy9lc20vc3JjL2NvZGVjcy9qc29uLmpzPzM2NWQiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgdGV4dEVuY29kZXIgPSBuZXcgVGV4dEVuY29kZXIoKTtcbmNvbnN0IHRleHREZWNvZGVyID0gbmV3IFRleHREZWNvZGVyKCk7XG5leHBvcnQgY29uc3QgbmFtZSA9ICdqc29uJztcbmV4cG9ydCBjb25zdCBjb2RlID0gNTEyO1xuZXhwb3J0IGNvbnN0IGVuY29kZSA9IG5vZGUgPT4gdGV4dEVuY29kZXIuZW5jb2RlKEpTT04uc3RyaW5naWZ5KG5vZGUpKTtcbmV4cG9ydCBjb25zdCBkZWNvZGUgPSBkYXRhID0+IEpTT04ucGFyc2UodGV4dERlY29kZXIuZGVjb2RlKGRhdGEpKTsiXSwibmFtZXMiOlsidGV4dEVuY29kZXIiLCJUZXh0RW5jb2RlciIsInRleHREZWNvZGVyIiwiVGV4dERlY29kZXIiLCJuYW1lIiwiY29kZSIsImVuY29kZSIsIm5vZGUiLCJKU09OIiwic3RyaW5naWZ5IiwiZGVjb2RlIiwiZGF0YSIsInBhcnNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/codecs/json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/codecs/raw.js":
/*!*********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/codecs/raw.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code),\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   name: () => (/* binding */ name)\n/* harmony export */ });\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n\nconst name = \"raw\";\nconst code = 85;\nconst encode = (node)=>(0,_bytes_js__WEBPACK_IMPORTED_MODULE_0__.coerce)(node);\nconst decode = (data)=>(0,_bytes_js__WEBPACK_IMPORTED_MODULE_0__.coerce)(data);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvY29kZWNzL3Jhdy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFxQztBQUM5QixNQUFNQyxPQUFPLE1BQU07QUFDbkIsTUFBTUMsT0FBTyxHQUFHO0FBQ2hCLE1BQU1DLFNBQVNDLENBQUFBLE9BQVFKLGlEQUFNQSxDQUFDSSxNQUFNO0FBQ3BDLE1BQU1DLFNBQVNDLENBQUFBLE9BQVFOLGlEQUFNQSxDQUFDTSxNQUFNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL211bHRpZm9ybWF0cy9lc20vc3JjL2NvZGVjcy9yYXcuanM/NGE5NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb2VyY2UgfSBmcm9tICcuLi9ieXRlcy5qcyc7XG5leHBvcnQgY29uc3QgbmFtZSA9ICdyYXcnO1xuZXhwb3J0IGNvbnN0IGNvZGUgPSA4NTtcbmV4cG9ydCBjb25zdCBlbmNvZGUgPSBub2RlID0+IGNvZXJjZShub2RlKTtcbmV4cG9ydCBjb25zdCBkZWNvZGUgPSBkYXRhID0+IGNvZXJjZShkYXRhKTsiXSwibmFtZXMiOlsiY29lcmNlIiwibmFtZSIsImNvZGUiLCJlbmNvZGUiLCJub2RlIiwiZGVjb2RlIiwiZGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/codecs/raw.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/hashes/digest.js":
/*!************************************************************!*\
  !*** ./node_modules/multiformats/esm/src/hashes/digest.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Digest: () => (/* binding */ Digest),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   equals: () => (/* binding */ equals)\n/* harmony export */ });\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n/* harmony import */ var _varint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../varint.js */ \"(ssr)/./node_modules/multiformats/esm/src/varint.js\");\n\n\nconst create = (code, digest)=>{\n    const size = digest.byteLength;\n    const sizeOffset = _varint_js__WEBPACK_IMPORTED_MODULE_1__.encodingLength(code);\n    const digestOffset = sizeOffset + _varint_js__WEBPACK_IMPORTED_MODULE_1__.encodingLength(size);\n    const bytes = new Uint8Array(digestOffset + size);\n    _varint_js__WEBPACK_IMPORTED_MODULE_1__.encodeTo(code, bytes, 0);\n    _varint_js__WEBPACK_IMPORTED_MODULE_1__.encodeTo(size, bytes, sizeOffset);\n    bytes.set(digest, digestOffset);\n    return new Digest(code, size, digest, bytes);\n};\nconst decode = (multihash)=>{\n    const bytes = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_0__.coerce)(multihash);\n    const [code, sizeOffset] = _varint_js__WEBPACK_IMPORTED_MODULE_1__.decode(bytes);\n    const [size, digestOffset] = _varint_js__WEBPACK_IMPORTED_MODULE_1__.decode(bytes.subarray(sizeOffset));\n    const digest = bytes.subarray(sizeOffset + digestOffset);\n    if (digest.byteLength !== size) {\n        throw new Error(\"Incorrect length\");\n    }\n    return new Digest(code, size, digest, bytes);\n};\nconst equals = (a, b)=>{\n    if (a === b) {\n        return true;\n    } else {\n        return a.code === b.code && a.size === b.size && (0,_bytes_js__WEBPACK_IMPORTED_MODULE_0__.equals)(a.bytes, b.bytes);\n    }\n};\nclass Digest {\n    constructor(code, size, digest, bytes){\n        this.code = code;\n        this.size = size;\n        this.digest = digest;\n        this.bytes = bytes;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/hashes/digest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/hashes/hasher.js":
/*!************************************************************!*\
  !*** ./node_modules/multiformats/esm/src/hashes/hasher.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hasher: () => (/* binding */ Hasher),\n/* harmony export */   from: () => (/* binding */ from)\n/* harmony export */ });\n/* harmony import */ var _digest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./digest.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/digest.js\");\n\nconst from = ({ name, code, encode })=>new Hasher(name, code, encode);\nclass Hasher {\n    constructor(name, code, encode){\n        this.name = name;\n        this.code = code;\n        this.encode = encode;\n    }\n    digest(input) {\n        if (input instanceof Uint8Array) {\n            const result = this.encode(input);\n            return result instanceof Uint8Array ? _digest_js__WEBPACK_IMPORTED_MODULE_0__.create(this.code, result) : result.then((digest)=>_digest_js__WEBPACK_IMPORTED_MODULE_0__.create(this.code, digest));\n        } else {\n            throw Error(\"Unknown type, must be binary type\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvaGFzaGVzL2hhc2hlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0M7QUFDL0IsTUFBTUMsT0FBTyxDQUFDLEVBQUNDLElBQUksRUFBRUMsSUFBSSxFQUFFQyxNQUFNLEVBQUMsR0FBSyxJQUFJQyxPQUFPSCxNQUFNQyxNQUFNQyxRQUFRO0FBQ3RFLE1BQU1DO0lBQ1hDLFlBQVlKLElBQUksRUFBRUMsSUFBSSxFQUFFQyxNQUFNLENBQUU7UUFDOUIsSUFBSSxDQUFDRixJQUFJLEdBQUdBO1FBQ1osSUFBSSxDQUFDQyxJQUFJLEdBQUdBO1FBQ1osSUFBSSxDQUFDQyxNQUFNLEdBQUdBO0lBQ2hCO0lBQ0FHLE9BQU9DLEtBQUssRUFBRTtRQUNaLElBQUlBLGlCQUFpQkMsWUFBWTtZQUMvQixNQUFNQyxTQUFTLElBQUksQ0FBQ04sTUFBTSxDQUFDSTtZQUMzQixPQUFPRSxrQkFBa0JELGFBQWFULDhDQUFhLENBQUMsSUFBSSxDQUFDRyxJQUFJLEVBQUVPLFVBQVVBLE9BQU9FLElBQUksQ0FBQ0wsQ0FBQUEsU0FBVVAsOENBQWEsQ0FBQyxJQUFJLENBQUNHLElBQUksRUFBRUk7UUFDMUgsT0FBTztZQUNMLE1BQU1NLE1BQU07UUFDZDtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvaGFzaGVzL2hhc2hlci5qcz9lMTYxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIERpZ2VzdCBmcm9tICcuL2RpZ2VzdC5qcyc7XG5leHBvcnQgY29uc3QgZnJvbSA9ICh7bmFtZSwgY29kZSwgZW5jb2RlfSkgPT4gbmV3IEhhc2hlcihuYW1lLCBjb2RlLCBlbmNvZGUpO1xuZXhwb3J0IGNsYXNzIEhhc2hlciB7XG4gIGNvbnN0cnVjdG9yKG5hbWUsIGNvZGUsIGVuY29kZSkge1xuICAgIHRoaXMubmFtZSA9IG5hbWU7XG4gICAgdGhpcy5jb2RlID0gY29kZTtcbiAgICB0aGlzLmVuY29kZSA9IGVuY29kZTtcbiAgfVxuICBkaWdlc3QoaW5wdXQpIHtcbiAgICBpZiAoaW5wdXQgaW5zdGFuY2VvZiBVaW50OEFycmF5KSB7XG4gICAgICBjb25zdCByZXN1bHQgPSB0aGlzLmVuY29kZShpbnB1dCk7XG4gICAgICByZXR1cm4gcmVzdWx0IGluc3RhbmNlb2YgVWludDhBcnJheSA/IERpZ2VzdC5jcmVhdGUodGhpcy5jb2RlLCByZXN1bHQpIDogcmVzdWx0LnRoZW4oZGlnZXN0ID0+IERpZ2VzdC5jcmVhdGUodGhpcy5jb2RlLCBkaWdlc3QpKTtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhyb3cgRXJyb3IoJ1Vua25vd24gdHlwZSwgbXVzdCBiZSBiaW5hcnkgdHlwZScpO1xuICAgIH1cbiAgfVxufSJdLCJuYW1lcyI6WyJEaWdlc3QiLCJmcm9tIiwibmFtZSIsImNvZGUiLCJlbmNvZGUiLCJIYXNoZXIiLCJjb25zdHJ1Y3RvciIsImRpZ2VzdCIsImlucHV0IiwiVWludDhBcnJheSIsInJlc3VsdCIsImNyZWF0ZSIsInRoZW4iLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/hashes/hasher.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/hashes/identity.js":
/*!**************************************************************!*\
  !*** ./node_modules/multiformats/esm/src/hashes/identity.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   identity: () => (/* binding */ identity)\n/* harmony export */ });\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n/* harmony import */ var _digest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./digest.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/digest.js\");\n\n\nconst code = 0;\nconst name = \"identity\";\nconst encode = _bytes_js__WEBPACK_IMPORTED_MODULE_0__.coerce;\nconst digest = (input)=>_digest_js__WEBPACK_IMPORTED_MODULE_1__.create(code, encode(input));\nconst identity = {\n    code,\n    name,\n    encode,\n    digest\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvaGFzaGVzL2lkZW50aXR5LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxQztBQUNDO0FBQ3RDLE1BQU1FLE9BQU87QUFDYixNQUFNQyxPQUFPO0FBQ2IsTUFBTUMsU0FBU0osNkNBQU1BO0FBQ3JCLE1BQU1LLFNBQVNDLENBQUFBLFFBQVNMLDhDQUFhLENBQUNDLE1BQU1FLE9BQU9FO0FBQzVDLE1BQU1FLFdBQVc7SUFDdEJOO0lBQ0FDO0lBQ0FDO0lBQ0FDO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXdlYjMtY3JlYXRvci1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9tdWx0aWZvcm1hdHMvZXNtL3NyYy9oYXNoZXMvaWRlbnRpdHkuanM/NjI3MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb2VyY2UgfSBmcm9tICcuLi9ieXRlcy5qcyc7XG5pbXBvcnQgKiBhcyBEaWdlc3QgZnJvbSAnLi9kaWdlc3QuanMnO1xuY29uc3QgY29kZSA9IDA7XG5jb25zdCBuYW1lID0gJ2lkZW50aXR5JztcbmNvbnN0IGVuY29kZSA9IGNvZXJjZTtcbmNvbnN0IGRpZ2VzdCA9IGlucHV0ID0+IERpZ2VzdC5jcmVhdGUoY29kZSwgZW5jb2RlKGlucHV0KSk7XG5leHBvcnQgY29uc3QgaWRlbnRpdHkgPSB7XG4gIGNvZGUsXG4gIG5hbWUsXG4gIGVuY29kZSxcbiAgZGlnZXN0XG59OyJdLCJuYW1lcyI6WyJjb2VyY2UiLCJEaWdlc3QiLCJjb2RlIiwibmFtZSIsImVuY29kZSIsImRpZ2VzdCIsImlucHV0IiwiY3JlYXRlIiwiaWRlbnRpdHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/hashes/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/hashes/sha2.js":
/*!**********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/hashes/sha2.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sha256: () => (/* binding */ sha256),\n/* harmony export */   sha512: () => (/* binding */ sha512)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var _hasher_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hasher.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/hasher.js\");\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n\n\n\nconst sha256 = (0,_hasher_js__WEBPACK_IMPORTED_MODULE_1__.from)({\n    name: \"sha2-256\",\n    code: 18,\n    encode: (input)=>(0,_bytes_js__WEBPACK_IMPORTED_MODULE_2__.coerce)(crypto__WEBPACK_IMPORTED_MODULE_0__.createHash(\"sha256\").update(input).digest())\n});\nconst sha512 = (0,_hasher_js__WEBPACK_IMPORTED_MODULE_1__.from)({\n    name: \"sha2-512\",\n    code: 19,\n    encode: (input)=>(0,_bytes_js__WEBPACK_IMPORTED_MODULE_2__.coerce)(crypto__WEBPACK_IMPORTED_MODULE_0__.createHash(\"sha512\").update(input).digest())\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvaGFzaGVzL3NoYTIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEI7QUFDTztBQUNFO0FBQzlCLE1BQU1HLFNBQVNGLGdEQUFJQSxDQUFDO0lBQ3pCRyxNQUFNO0lBQ05DLE1BQU07SUFDTkMsUUFBUUMsQ0FBQUEsUUFBU0wsaURBQU1BLENBQUNGLDhDQUFpQixDQUFDLFVBQVVTLE1BQU0sQ0FBQ0YsT0FBT0csTUFBTTtBQUMxRSxHQUFHO0FBQ0ksTUFBTUMsU0FBU1YsZ0RBQUlBLENBQUM7SUFDekJHLE1BQU07SUFDTkMsTUFBTTtJQUNOQyxRQUFRQyxDQUFBQSxRQUFTTCxpREFBTUEsQ0FBQ0YsOENBQWlCLENBQUMsVUFBVVMsTUFBTSxDQUFDRixPQUFPRyxNQUFNO0FBQzFFLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvaGFzaGVzL3NoYTIuanM/NzZlNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3J5cHRvIGZyb20gJ2NyeXB0byc7XG5pbXBvcnQgeyBmcm9tIH0gZnJvbSAnLi9oYXNoZXIuanMnO1xuaW1wb3J0IHsgY29lcmNlIH0gZnJvbSAnLi4vYnl0ZXMuanMnO1xuZXhwb3J0IGNvbnN0IHNoYTI1NiA9IGZyb20oe1xuICBuYW1lOiAnc2hhMi0yNTYnLFxuICBjb2RlOiAxOCxcbiAgZW5jb2RlOiBpbnB1dCA9PiBjb2VyY2UoY3J5cHRvLmNyZWF0ZUhhc2goJ3NoYTI1NicpLnVwZGF0ZShpbnB1dCkuZGlnZXN0KCkpXG59KTtcbmV4cG9ydCBjb25zdCBzaGE1MTIgPSBmcm9tKHtcbiAgbmFtZTogJ3NoYTItNTEyJyxcbiAgY29kZTogMTksXG4gIGVuY29kZTogaW5wdXQgPT4gY29lcmNlKGNyeXB0by5jcmVhdGVIYXNoKCdzaGE1MTInKS51cGRhdGUoaW5wdXQpLmRpZ2VzdCgpKVxufSk7Il0sIm5hbWVzIjpbImNyeXB0byIsImZyb20iLCJjb2VyY2UiLCJzaGEyNTYiLCJuYW1lIiwiY29kZSIsImVuY29kZSIsImlucHV0IiwiY3JlYXRlSGFzaCIsInVwZGF0ZSIsImRpZ2VzdCIsInNoYTUxMiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/hashes/sha2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/index.js":
/*!****************************************************!*\
  !*** ./node_modules/multiformats/esm/src/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CID: () => (/* reexport safe */ _cid_js__WEBPACK_IMPORTED_MODULE_0__.CID),\n/* harmony export */   bytes: () => (/* reexport module object */ _bytes_js__WEBPACK_IMPORTED_MODULE_2__),\n/* harmony export */   digest: () => (/* reexport module object */ _hashes_digest_js__WEBPACK_IMPORTED_MODULE_4__),\n/* harmony export */   hasher: () => (/* reexport module object */ _hashes_hasher_js__WEBPACK_IMPORTED_MODULE_3__),\n/* harmony export */   varint: () => (/* reexport module object */ _varint_js__WEBPACK_IMPORTED_MODULE_1__)\n/* harmony export */ });\n/* harmony import */ var _cid_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cid.js */ \"(ssr)/./node_modules/multiformats/esm/src/cid.js\");\n/* harmony import */ var _varint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./varint.js */ \"(ssr)/./node_modules/multiformats/esm/src/varint.js\");\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n/* harmony import */ var _hashes_hasher_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hashes/hasher.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/hasher.js\");\n/* harmony import */ var _hashes_digest_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./hashes/digest.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/digest.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUErQjtBQUNPO0FBQ0Y7QUFDUztBQUNBO0FBTzNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL211bHRpZm9ybWF0cy9lc20vc3JjL2luZGV4LmpzP2QxZDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ0lEIH0gZnJvbSAnLi9jaWQuanMnO1xuaW1wb3J0ICogYXMgdmFyaW50IGZyb20gJy4vdmFyaW50LmpzJztcbmltcG9ydCAqIGFzIGJ5dGVzIGZyb20gJy4vYnl0ZXMuanMnO1xuaW1wb3J0ICogYXMgaGFzaGVyIGZyb20gJy4vaGFzaGVzL2hhc2hlci5qcyc7XG5pbXBvcnQgKiBhcyBkaWdlc3QgZnJvbSAnLi9oYXNoZXMvZGlnZXN0LmpzJztcbmV4cG9ydCB7XG4gIENJRCxcbiAgaGFzaGVyLFxuICBkaWdlc3QsXG4gIHZhcmludCxcbiAgYnl0ZXNcbn07Il0sIm5hbWVzIjpbIkNJRCIsInZhcmludCIsImJ5dGVzIiwiaGFzaGVyIiwiZGlnZXN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/varint.js":
/*!*****************************************************!*\
  !*** ./node_modules/multiformats/esm/src/varint.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encodeTo: () => (/* binding */ encodeTo),\n/* harmony export */   encodingLength: () => (/* binding */ encodingLength)\n/* harmony export */ });\n/* harmony import */ var _vendor_varint_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../vendor/varint.js */ \"(ssr)/./node_modules/multiformats/esm/vendor/varint.js\");\n\nconst decode = (data, offset = 0)=>{\n    const code = _vendor_varint_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].decode(data, offset);\n    return [\n        code,\n        _vendor_varint_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].decode.bytes\n    ];\n};\nconst encodeTo = (int, target, offset = 0)=>{\n    _vendor_varint_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].encode(int, target, offset);\n    return target;\n};\nconst encodingLength = (int)=>{\n    return _vendor_varint_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].encodingLength(int);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvdmFyaW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBeUM7QUFDbEMsTUFBTUMsU0FBUyxDQUFDQyxNQUFNQyxTQUFTLENBQUM7SUFDckMsTUFBTUMsT0FBT0oseURBQU1BLENBQUNDLE1BQU0sQ0FBQ0MsTUFBTUM7SUFDakMsT0FBTztRQUNMQztRQUNBSix5REFBTUEsQ0FBQ0MsTUFBTSxDQUFDSSxLQUFLO0tBQ3BCO0FBQ0gsRUFBRTtBQUNLLE1BQU1DLFdBQVcsQ0FBQ0MsS0FBS0MsUUFBUUwsU0FBUyxDQUFDO0lBQzlDSCx5REFBTUEsQ0FBQ1MsTUFBTSxDQUFDRixLQUFLQyxRQUFRTDtJQUMzQixPQUFPSztBQUNULEVBQUU7QUFDSyxNQUFNRSxpQkFBaUJILENBQUFBO0lBQzVCLE9BQU9QLHlEQUFNQSxDQUFDVSxjQUFjLENBQUNIO0FBQy9CLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvdmFyaW50LmpzPzllM2IiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHZhcmludCBmcm9tICcuLi92ZW5kb3IvdmFyaW50LmpzJztcbmV4cG9ydCBjb25zdCBkZWNvZGUgPSAoZGF0YSwgb2Zmc2V0ID0gMCkgPT4ge1xuICBjb25zdCBjb2RlID0gdmFyaW50LmRlY29kZShkYXRhLCBvZmZzZXQpO1xuICByZXR1cm4gW1xuICAgIGNvZGUsXG4gICAgdmFyaW50LmRlY29kZS5ieXRlc1xuICBdO1xufTtcbmV4cG9ydCBjb25zdCBlbmNvZGVUbyA9IChpbnQsIHRhcmdldCwgb2Zmc2V0ID0gMCkgPT4ge1xuICB2YXJpbnQuZW5jb2RlKGludCwgdGFyZ2V0LCBvZmZzZXQpO1xuICByZXR1cm4gdGFyZ2V0O1xufTtcbmV4cG9ydCBjb25zdCBlbmNvZGluZ0xlbmd0aCA9IGludCA9PiB7XG4gIHJldHVybiB2YXJpbnQuZW5jb2RpbmdMZW5ndGgoaW50KTtcbn07Il0sIm5hbWVzIjpbInZhcmludCIsImRlY29kZSIsImRhdGEiLCJvZmZzZXQiLCJjb2RlIiwiYnl0ZXMiLCJlbmNvZGVUbyIsImludCIsInRhcmdldCIsImVuY29kZSIsImVuY29kaW5nTGVuZ3RoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/varint.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/vendor/base-x.js":
/*!********************************************************!*\
  !*** ./node_modules/multiformats/esm/vendor/base-x.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction base(ALPHABET, name) {\n    if (ALPHABET.length >= 255) {\n        throw new TypeError(\"Alphabet too long\");\n    }\n    var BASE_MAP = new Uint8Array(256);\n    for(var j = 0; j < BASE_MAP.length; j++){\n        BASE_MAP[j] = 255;\n    }\n    for(var i = 0; i < ALPHABET.length; i++){\n        var x = ALPHABET.charAt(i);\n        var xc = x.charCodeAt(0);\n        if (BASE_MAP[xc] !== 255) {\n            throw new TypeError(x + \" is ambiguous\");\n        }\n        BASE_MAP[xc] = i;\n    }\n    var BASE = ALPHABET.length;\n    var LEADER = ALPHABET.charAt(0);\n    var FACTOR = Math.log(BASE) / Math.log(256);\n    var iFACTOR = Math.log(256) / Math.log(BASE);\n    function encode(source) {\n        if (source instanceof Uint8Array) ;\n        else if (ArrayBuffer.isView(source)) {\n            source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength);\n        } else if (Array.isArray(source)) {\n            source = Uint8Array.from(source);\n        }\n        if (!(source instanceof Uint8Array)) {\n            throw new TypeError(\"Expected Uint8Array\");\n        }\n        if (source.length === 0) {\n            return \"\";\n        }\n        var zeroes = 0;\n        var length = 0;\n        var pbegin = 0;\n        var pend = source.length;\n        while(pbegin !== pend && source[pbegin] === 0){\n            pbegin++;\n            zeroes++;\n        }\n        var size = (pend - pbegin) * iFACTOR + 1 >>> 0;\n        var b58 = new Uint8Array(size);\n        while(pbegin !== pend){\n            var carry = source[pbegin];\n            var i = 0;\n            for(var it1 = size - 1; (carry !== 0 || i < length) && it1 !== -1; it1--, i++){\n                carry += 256 * b58[it1] >>> 0;\n                b58[it1] = carry % BASE >>> 0;\n                carry = carry / BASE >>> 0;\n            }\n            if (carry !== 0) {\n                throw new Error(\"Non-zero carry\");\n            }\n            length = i;\n            pbegin++;\n        }\n        var it2 = size - length;\n        while(it2 !== size && b58[it2] === 0){\n            it2++;\n        }\n        var str = LEADER.repeat(zeroes);\n        for(; it2 < size; ++it2){\n            str += ALPHABET.charAt(b58[it2]);\n        }\n        return str;\n    }\n    function decodeUnsafe(source) {\n        if (typeof source !== \"string\") {\n            throw new TypeError(\"Expected String\");\n        }\n        if (source.length === 0) {\n            return new Uint8Array();\n        }\n        var psz = 0;\n        if (source[psz] === \" \") {\n            return;\n        }\n        var zeroes = 0;\n        var length = 0;\n        while(source[psz] === LEADER){\n            zeroes++;\n            psz++;\n        }\n        var size = (source.length - psz) * FACTOR + 1 >>> 0;\n        var b256 = new Uint8Array(size);\n        while(source[psz]){\n            var carry = BASE_MAP[source.charCodeAt(psz)];\n            if (carry === 255) {\n                return;\n            }\n            var i = 0;\n            for(var it3 = size - 1; (carry !== 0 || i < length) && it3 !== -1; it3--, i++){\n                carry += BASE * b256[it3] >>> 0;\n                b256[it3] = carry % 256 >>> 0;\n                carry = carry / 256 >>> 0;\n            }\n            if (carry !== 0) {\n                throw new Error(\"Non-zero carry\");\n            }\n            length = i;\n            psz++;\n        }\n        if (source[psz] === \" \") {\n            return;\n        }\n        var it4 = size - length;\n        while(it4 !== size && b256[it4] === 0){\n            it4++;\n        }\n        var vch = new Uint8Array(zeroes + (size - it4));\n        var j = zeroes;\n        while(it4 !== size){\n            vch[j++] = b256[it4++];\n        }\n        return vch;\n    }\n    function decode(string) {\n        var buffer = decodeUnsafe(string);\n        if (buffer) {\n            return buffer;\n        }\n        throw new Error(`Non-${name} character`);\n    }\n    return {\n        encode: encode,\n        decodeUnsafe: decodeUnsafe,\n        decode: decode\n    };\n}\nvar src = base;\nvar _brrp__multiformats_scope_baseX = src;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_brrp__multiformats_scope_baseX);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/vendor/base-x.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/vendor/varint.js":
/*!********************************************************!*\
  !*** ./node_modules/multiformats/esm/vendor/varint.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar encode_1 = encode;\nvar MSB = 128, REST = 127, MSBALL = ~REST, INT = Math.pow(2, 31);\nfunction encode(num, out, offset) {\n    out = out || [];\n    offset = offset || 0;\n    var oldOffset = offset;\n    while(num >= INT){\n        out[offset++] = num & 255 | MSB;\n        num /= 128;\n    }\n    while(num & MSBALL){\n        out[offset++] = num & 255 | MSB;\n        num >>>= 7;\n    }\n    out[offset] = num | 0;\n    encode.bytes = offset - oldOffset + 1;\n    return out;\n}\nvar decode = read;\nvar MSB$1 = 128, REST$1 = 127;\nfunction read(buf, offset) {\n    var res = 0, offset = offset || 0, shift = 0, counter = offset, b, l = buf.length;\n    do {\n        if (counter >= l) {\n            read.bytes = 0;\n            throw new RangeError(\"Could not decode varint\");\n        }\n        b = buf[counter++];\n        res += shift < 28 ? (b & REST$1) << shift : (b & REST$1) * Math.pow(2, shift);\n        shift += 7;\n    }while (b >= MSB$1);\n    read.bytes = counter - offset;\n    return res;\n}\nvar N1 = Math.pow(2, 7);\nvar N2 = Math.pow(2, 14);\nvar N3 = Math.pow(2, 21);\nvar N4 = Math.pow(2, 28);\nvar N5 = Math.pow(2, 35);\nvar N6 = Math.pow(2, 42);\nvar N7 = Math.pow(2, 49);\nvar N8 = Math.pow(2, 56);\nvar N9 = Math.pow(2, 63);\nvar length = function(value) {\n    return value < N1 ? 1 : value < N2 ? 2 : value < N3 ? 3 : value < N4 ? 4 : value < N5 ? 5 : value < N6 ? 6 : value < N7 ? 7 : value < N8 ? 8 : value < N9 ? 9 : 10;\n};\nvar varint = {\n    encode: encode_1,\n    decode: decode,\n    encodingLength: length\n};\nvar _brrp_varint = varint;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_brrp_varint);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/vendor/varint.js\n");

/***/ })

};
;