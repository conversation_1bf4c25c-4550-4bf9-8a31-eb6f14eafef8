/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/qrcode-generator";
exports.ids = ["vendor-chunks/qrcode-generator"];
exports.modules = {

/***/ "(ssr)/./node_modules/qrcode-generator/qrcode.js":
/*!*************************************************!*\
  !*** ./node_modules/qrcode-generator/qrcode.js ***!
  \*************************************************/
/***/ ((module, exports) => {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;//---------------------------------------------------------------------\n//\n// QR Code Generator for JavaScript\n//\n// Copyright (c) 2009 Kazuhiko Arase\n//\n// URL: http://www.d-project.com/\n//\n// Licensed under the MIT license:\n//  http://www.opensource.org/licenses/mit-license.php\n//\n// The word 'QR Code' is registered trademark of\n// DENSO WAVE INCORPORATED\n//  http://www.denso-wave.com/qrcode/faqpatent-e.html\n//\n//---------------------------------------------------------------------\nvar qrcode = function() {\n    //---------------------------------------------------------------------\n    // qrcode\n    //---------------------------------------------------------------------\n    /**\n   * qrcode\n   * @param typeNumber 1 to 40\n   * @param errorCorrectionLevel 'L','M','Q','H'\n   */ var qrcode = function(typeNumber, errorCorrectionLevel) {\n        var PAD0 = 0xEC;\n        var PAD1 = 0x11;\n        var _typeNumber = typeNumber;\n        var _errorCorrectionLevel = QRErrorCorrectionLevel[errorCorrectionLevel];\n        var _modules = null;\n        var _moduleCount = 0;\n        var _dataCache = null;\n        var _dataList = [];\n        var _this = {};\n        var makeImpl = function(test, maskPattern) {\n            _moduleCount = _typeNumber * 4 + 17;\n            _modules = function(moduleCount) {\n                var modules = new Array(moduleCount);\n                for(var row = 0; row < moduleCount; row += 1){\n                    modules[row] = new Array(moduleCount);\n                    for(var col = 0; col < moduleCount; col += 1){\n                        modules[row][col] = null;\n                    }\n                }\n                return modules;\n            }(_moduleCount);\n            setupPositionProbePattern(0, 0);\n            setupPositionProbePattern(_moduleCount - 7, 0);\n            setupPositionProbePattern(0, _moduleCount - 7);\n            setupPositionAdjustPattern();\n            setupTimingPattern();\n            setupTypeInfo(test, maskPattern);\n            if (_typeNumber >= 7) {\n                setupTypeNumber(test);\n            }\n            if (_dataCache == null) {\n                _dataCache = createData(_typeNumber, _errorCorrectionLevel, _dataList);\n            }\n            mapData(_dataCache, maskPattern);\n        };\n        var setupPositionProbePattern = function(row, col) {\n            for(var r = -1; r <= 7; r += 1){\n                if (row + r <= -1 || _moduleCount <= row + r) continue;\n                for(var c = -1; c <= 7; c += 1){\n                    if (col + c <= -1 || _moduleCount <= col + c) continue;\n                    if (0 <= r && r <= 6 && (c == 0 || c == 6) || 0 <= c && c <= 6 && (r == 0 || r == 6) || 2 <= r && r <= 4 && 2 <= c && c <= 4) {\n                        _modules[row + r][col + c] = true;\n                    } else {\n                        _modules[row + r][col + c] = false;\n                    }\n                }\n            }\n        };\n        var getBestMaskPattern = function() {\n            var minLostPoint = 0;\n            var pattern = 0;\n            for(var i = 0; i < 8; i += 1){\n                makeImpl(true, i);\n                var lostPoint = QRUtil.getLostPoint(_this);\n                if (i == 0 || minLostPoint > lostPoint) {\n                    minLostPoint = lostPoint;\n                    pattern = i;\n                }\n            }\n            return pattern;\n        };\n        var setupTimingPattern = function() {\n            for(var r = 8; r < _moduleCount - 8; r += 1){\n                if (_modules[r][6] != null) {\n                    continue;\n                }\n                _modules[r][6] = r % 2 == 0;\n            }\n            for(var c = 8; c < _moduleCount - 8; c += 1){\n                if (_modules[6][c] != null) {\n                    continue;\n                }\n                _modules[6][c] = c % 2 == 0;\n            }\n        };\n        var setupPositionAdjustPattern = function() {\n            var pos = QRUtil.getPatternPosition(_typeNumber);\n            for(var i = 0; i < pos.length; i += 1){\n                for(var j = 0; j < pos.length; j += 1){\n                    var row = pos[i];\n                    var col = pos[j];\n                    if (_modules[row][col] != null) {\n                        continue;\n                    }\n                    for(var r = -2; r <= 2; r += 1){\n                        for(var c = -2; c <= 2; c += 1){\n                            if (r == -2 || r == 2 || c == -2 || c == 2 || r == 0 && c == 0) {\n                                _modules[row + r][col + c] = true;\n                            } else {\n                                _modules[row + r][col + c] = false;\n                            }\n                        }\n                    }\n                }\n            }\n        };\n        var setupTypeNumber = function(test) {\n            var bits = QRUtil.getBCHTypeNumber(_typeNumber);\n            for(var i = 0; i < 18; i += 1){\n                var mod = !test && (bits >> i & 1) == 1;\n                _modules[Math.floor(i / 3)][i % 3 + _moduleCount - 8 - 3] = mod;\n            }\n            for(var i = 0; i < 18; i += 1){\n                var mod = !test && (bits >> i & 1) == 1;\n                _modules[i % 3 + _moduleCount - 8 - 3][Math.floor(i / 3)] = mod;\n            }\n        };\n        var setupTypeInfo = function(test, maskPattern) {\n            var data = _errorCorrectionLevel << 3 | maskPattern;\n            var bits = QRUtil.getBCHTypeInfo(data);\n            // vertical\n            for(var i = 0; i < 15; i += 1){\n                var mod = !test && (bits >> i & 1) == 1;\n                if (i < 6) {\n                    _modules[i][8] = mod;\n                } else if (i < 8) {\n                    _modules[i + 1][8] = mod;\n                } else {\n                    _modules[_moduleCount - 15 + i][8] = mod;\n                }\n            }\n            // horizontal\n            for(var i = 0; i < 15; i += 1){\n                var mod = !test && (bits >> i & 1) == 1;\n                if (i < 8) {\n                    _modules[8][_moduleCount - i - 1] = mod;\n                } else if (i < 9) {\n                    _modules[8][15 - i - 1 + 1] = mod;\n                } else {\n                    _modules[8][15 - i - 1] = mod;\n                }\n            }\n            // fixed module\n            _modules[_moduleCount - 8][8] = !test;\n        };\n        var mapData = function(data, maskPattern) {\n            var inc = -1;\n            var row = _moduleCount - 1;\n            var bitIndex = 7;\n            var byteIndex = 0;\n            var maskFunc = QRUtil.getMaskFunction(maskPattern);\n            for(var col = _moduleCount - 1; col > 0; col -= 2){\n                if (col == 6) col -= 1;\n                while(true){\n                    for(var c = 0; c < 2; c += 1){\n                        if (_modules[row][col - c] == null) {\n                            var dark = false;\n                            if (byteIndex < data.length) {\n                                dark = (data[byteIndex] >>> bitIndex & 1) == 1;\n                            }\n                            var mask = maskFunc(row, col - c);\n                            if (mask) {\n                                dark = !dark;\n                            }\n                            _modules[row][col - c] = dark;\n                            bitIndex -= 1;\n                            if (bitIndex == -1) {\n                                byteIndex += 1;\n                                bitIndex = 7;\n                            }\n                        }\n                    }\n                    row += inc;\n                    if (row < 0 || _moduleCount <= row) {\n                        row -= inc;\n                        inc = -inc;\n                        break;\n                    }\n                }\n            }\n        };\n        var createBytes = function(buffer, rsBlocks) {\n            var offset = 0;\n            var maxDcCount = 0;\n            var maxEcCount = 0;\n            var dcdata = new Array(rsBlocks.length);\n            var ecdata = new Array(rsBlocks.length);\n            for(var r = 0; r < rsBlocks.length; r += 1){\n                var dcCount = rsBlocks[r].dataCount;\n                var ecCount = rsBlocks[r].totalCount - dcCount;\n                maxDcCount = Math.max(maxDcCount, dcCount);\n                maxEcCount = Math.max(maxEcCount, ecCount);\n                dcdata[r] = new Array(dcCount);\n                for(var i = 0; i < dcdata[r].length; i += 1){\n                    dcdata[r][i] = 0xff & buffer.getBuffer()[i + offset];\n                }\n                offset += dcCount;\n                var rsPoly = QRUtil.getErrorCorrectPolynomial(ecCount);\n                var rawPoly = qrPolynomial(dcdata[r], rsPoly.getLength() - 1);\n                var modPoly = rawPoly.mod(rsPoly);\n                ecdata[r] = new Array(rsPoly.getLength() - 1);\n                for(var i = 0; i < ecdata[r].length; i += 1){\n                    var modIndex = i + modPoly.getLength() - ecdata[r].length;\n                    ecdata[r][i] = modIndex >= 0 ? modPoly.getAt(modIndex) : 0;\n                }\n            }\n            var totalCodeCount = 0;\n            for(var i = 0; i < rsBlocks.length; i += 1){\n                totalCodeCount += rsBlocks[i].totalCount;\n            }\n            var data = new Array(totalCodeCount);\n            var index = 0;\n            for(var i = 0; i < maxDcCount; i += 1){\n                for(var r = 0; r < rsBlocks.length; r += 1){\n                    if (i < dcdata[r].length) {\n                        data[index] = dcdata[r][i];\n                        index += 1;\n                    }\n                }\n            }\n            for(var i = 0; i < maxEcCount; i += 1){\n                for(var r = 0; r < rsBlocks.length; r += 1){\n                    if (i < ecdata[r].length) {\n                        data[index] = ecdata[r][i];\n                        index += 1;\n                    }\n                }\n            }\n            return data;\n        };\n        var createData = function(typeNumber, errorCorrectionLevel, dataList) {\n            var rsBlocks = QRRSBlock.getRSBlocks(typeNumber, errorCorrectionLevel);\n            var buffer = qrBitBuffer();\n            for(var i = 0; i < dataList.length; i += 1){\n                var data = dataList[i];\n                buffer.put(data.getMode(), 4);\n                buffer.put(data.getLength(), QRUtil.getLengthInBits(data.getMode(), typeNumber));\n                data.write(buffer);\n            }\n            // calc num max data.\n            var totalDataCount = 0;\n            for(var i = 0; i < rsBlocks.length; i += 1){\n                totalDataCount += rsBlocks[i].dataCount;\n            }\n            if (buffer.getLengthInBits() > totalDataCount * 8) {\n                throw \"code length overflow. (\" + buffer.getLengthInBits() + \">\" + totalDataCount * 8 + \")\";\n            }\n            // end code\n            if (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {\n                buffer.put(0, 4);\n            }\n            // padding\n            while(buffer.getLengthInBits() % 8 != 0){\n                buffer.putBit(false);\n            }\n            // padding\n            while(true){\n                if (buffer.getLengthInBits() >= totalDataCount * 8) {\n                    break;\n                }\n                buffer.put(PAD0, 8);\n                if (buffer.getLengthInBits() >= totalDataCount * 8) {\n                    break;\n                }\n                buffer.put(PAD1, 8);\n            }\n            return createBytes(buffer, rsBlocks);\n        };\n        _this.addData = function(data, mode) {\n            mode = mode || \"Byte\";\n            var newData = null;\n            switch(mode){\n                case \"Numeric\":\n                    newData = qrNumber(data);\n                    break;\n                case \"Alphanumeric\":\n                    newData = qrAlphaNum(data);\n                    break;\n                case \"Byte\":\n                    newData = qr8BitByte(data);\n                    break;\n                case \"Kanji\":\n                    newData = qrKanji(data);\n                    break;\n                default:\n                    throw \"mode:\" + mode;\n            }\n            _dataList.push(newData);\n            _dataCache = null;\n        };\n        _this.isDark = function(row, col) {\n            if (row < 0 || _moduleCount <= row || col < 0 || _moduleCount <= col) {\n                throw row + \",\" + col;\n            }\n            return _modules[row][col];\n        };\n        _this.getModuleCount = function() {\n            return _moduleCount;\n        };\n        _this.make = function() {\n            if (_typeNumber < 1) {\n                var typeNumber = 1;\n                for(; typeNumber < 40; typeNumber++){\n                    var rsBlocks = QRRSBlock.getRSBlocks(typeNumber, _errorCorrectionLevel);\n                    var buffer = qrBitBuffer();\n                    for(var i = 0; i < _dataList.length; i++){\n                        var data = _dataList[i];\n                        buffer.put(data.getMode(), 4);\n                        buffer.put(data.getLength(), QRUtil.getLengthInBits(data.getMode(), typeNumber));\n                        data.write(buffer);\n                    }\n                    var totalDataCount = 0;\n                    for(var i = 0; i < rsBlocks.length; i++){\n                        totalDataCount += rsBlocks[i].dataCount;\n                    }\n                    if (buffer.getLengthInBits() <= totalDataCount * 8) {\n                        break;\n                    }\n                }\n                _typeNumber = typeNumber;\n            }\n            makeImpl(false, getBestMaskPattern());\n        };\n        _this.createTableTag = function(cellSize, margin) {\n            cellSize = cellSize || 2;\n            margin = typeof margin == \"undefined\" ? cellSize * 4 : margin;\n            var qrHtml = \"\";\n            qrHtml += '<table style=\"';\n            qrHtml += \" border-width: 0px; border-style: none;\";\n            qrHtml += \" border-collapse: collapse;\";\n            qrHtml += \" padding: 0px; margin: \" + margin + \"px;\";\n            qrHtml += '\">';\n            qrHtml += \"<tbody>\";\n            for(var r = 0; r < _this.getModuleCount(); r += 1){\n                qrHtml += \"<tr>\";\n                for(var c = 0; c < _this.getModuleCount(); c += 1){\n                    qrHtml += '<td style=\"';\n                    qrHtml += \" border-width: 0px; border-style: none;\";\n                    qrHtml += \" border-collapse: collapse;\";\n                    qrHtml += \" padding: 0px; margin: 0px;\";\n                    qrHtml += \" width: \" + cellSize + \"px;\";\n                    qrHtml += \" height: \" + cellSize + \"px;\";\n                    qrHtml += \" background-color: \";\n                    qrHtml += _this.isDark(r, c) ? \"#000000\" : \"#ffffff\";\n                    qrHtml += \";\";\n                    qrHtml += '\"/>';\n                }\n                qrHtml += \"</tr>\";\n            }\n            qrHtml += \"</tbody>\";\n            qrHtml += \"</table>\";\n            return qrHtml;\n        };\n        _this.createSvgTag = function(cellSize, margin, alt, title) {\n            var opts = {};\n            if (typeof arguments[0] == \"object\") {\n                // Called by options.\n                opts = arguments[0];\n                // overwrite cellSize and margin.\n                cellSize = opts.cellSize;\n                margin = opts.margin;\n                alt = opts.alt;\n                title = opts.title;\n            }\n            cellSize = cellSize || 2;\n            margin = typeof margin == \"undefined\" ? cellSize * 4 : margin;\n            // Compose alt property surrogate\n            alt = typeof alt === \"string\" ? {\n                text: alt\n            } : alt || {};\n            alt.text = alt.text || null;\n            alt.id = alt.text ? alt.id || \"qrcode-description\" : null;\n            // Compose title property surrogate\n            title = typeof title === \"string\" ? {\n                text: title\n            } : title || {};\n            title.text = title.text || null;\n            title.id = title.text ? title.id || \"qrcode-title\" : null;\n            var size = _this.getModuleCount() * cellSize + margin * 2;\n            var c, mc, r, mr, qrSvg = \"\", rect;\n            rect = \"l\" + cellSize + \",0 0,\" + cellSize + \" -\" + cellSize + \",0 0,-\" + cellSize + \"z \";\n            qrSvg += '<svg version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\"';\n            qrSvg += !opts.scalable ? ' width=\"' + size + 'px\" height=\"' + size + 'px\"' : \"\";\n            qrSvg += ' viewBox=\"0 0 ' + size + \" \" + size + '\" ';\n            qrSvg += ' preserveAspectRatio=\"xMinYMin meet\"';\n            qrSvg += title.text || alt.text ? ' role=\"img\" aria-labelledby=\"' + escapeXml([\n                title.id,\n                alt.id\n            ].join(\" \").trim()) + '\"' : \"\";\n            qrSvg += \">\";\n            qrSvg += title.text ? '<title id=\"' + escapeXml(title.id) + '\">' + escapeXml(title.text) + \"</title>\" : \"\";\n            qrSvg += alt.text ? '<description id=\"' + escapeXml(alt.id) + '\">' + escapeXml(alt.text) + \"</description>\" : \"\";\n            qrSvg += '<rect width=\"100%\" height=\"100%\" fill=\"white\" cx=\"0\" cy=\"0\"/>';\n            qrSvg += '<path d=\"';\n            for(r = 0; r < _this.getModuleCount(); r += 1){\n                mr = r * cellSize + margin;\n                for(c = 0; c < _this.getModuleCount(); c += 1){\n                    if (_this.isDark(r, c)) {\n                        mc = c * cellSize + margin;\n                        qrSvg += \"M\" + mc + \",\" + mr + rect;\n                    }\n                }\n            }\n            qrSvg += '\" stroke=\"transparent\" fill=\"black\"/>';\n            qrSvg += \"</svg>\";\n            return qrSvg;\n        };\n        _this.createDataURL = function(cellSize, margin) {\n            cellSize = cellSize || 2;\n            margin = typeof margin == \"undefined\" ? cellSize * 4 : margin;\n            var size = _this.getModuleCount() * cellSize + margin * 2;\n            var min = margin;\n            var max = size - margin;\n            return createDataURL(size, size, function(x, y) {\n                if (min <= x && x < max && min <= y && y < max) {\n                    var c = Math.floor((x - min) / cellSize);\n                    var r = Math.floor((y - min) / cellSize);\n                    return _this.isDark(r, c) ? 0 : 1;\n                } else {\n                    return 1;\n                }\n            });\n        };\n        _this.createImgTag = function(cellSize, margin, alt) {\n            cellSize = cellSize || 2;\n            margin = typeof margin == \"undefined\" ? cellSize * 4 : margin;\n            var size = _this.getModuleCount() * cellSize + margin * 2;\n            var img = \"\";\n            img += \"<img\";\n            img += ' src=\"';\n            img += _this.createDataURL(cellSize, margin);\n            img += '\"';\n            img += ' width=\"';\n            img += size;\n            img += '\"';\n            img += ' height=\"';\n            img += size;\n            img += '\"';\n            if (alt) {\n                img += ' alt=\"';\n                img += escapeXml(alt);\n                img += '\"';\n            }\n            img += \"/>\";\n            return img;\n        };\n        var escapeXml = function(s) {\n            var escaped = \"\";\n            for(var i = 0; i < s.length; i += 1){\n                var c = s.charAt(i);\n                switch(c){\n                    case \"<\":\n                        escaped += \"&lt;\";\n                        break;\n                    case \">\":\n                        escaped += \"&gt;\";\n                        break;\n                    case \"&\":\n                        escaped += \"&amp;\";\n                        break;\n                    case '\"':\n                        escaped += \"&quot;\";\n                        break;\n                    default:\n                        escaped += c;\n                        break;\n                }\n            }\n            return escaped;\n        };\n        var _createHalfASCII = function(margin) {\n            var cellSize = 1;\n            margin = typeof margin == \"undefined\" ? cellSize * 2 : margin;\n            var size = _this.getModuleCount() * cellSize + margin * 2;\n            var min = margin;\n            var max = size - margin;\n            var y, x, r1, r2, p;\n            var blocks = {\n                \"██\": \"█\",\n                \"█ \": \"▀\",\n                \" █\": \"▄\",\n                \"  \": \" \"\n            };\n            var blocksLastLineNoMargin = {\n                \"██\": \"▀\",\n                \"█ \": \"▀\",\n                \" █\": \" \",\n                \"  \": \" \"\n            };\n            var ascii = \"\";\n            for(y = 0; y < size; y += 2){\n                r1 = Math.floor((y - min) / cellSize);\n                r2 = Math.floor((y + 1 - min) / cellSize);\n                for(x = 0; x < size; x += 1){\n                    p = \"█\";\n                    if (min <= x && x < max && min <= y && y < max && _this.isDark(r1, Math.floor((x - min) / cellSize))) {\n                        p = \" \";\n                    }\n                    if (min <= x && x < max && min <= y + 1 && y + 1 < max && _this.isDark(r2, Math.floor((x - min) / cellSize))) {\n                        p += \" \";\n                    } else {\n                        p += \"█\";\n                    }\n                    // Output 2 characters per pixel, to create full square. 1 character per pixels gives only half width of square.\n                    ascii += margin < 1 && y + 1 >= max ? blocksLastLineNoMargin[p] : blocks[p];\n                }\n                ascii += \"\\n\";\n            }\n            if (size % 2 && margin > 0) {\n                return ascii.substring(0, ascii.length - size - 1) + Array(size + 1).join(\"▀\");\n            }\n            return ascii.substring(0, ascii.length - 1);\n        };\n        _this.createASCII = function(cellSize, margin) {\n            cellSize = cellSize || 1;\n            if (cellSize < 2) {\n                return _createHalfASCII(margin);\n            }\n            cellSize -= 1;\n            margin = typeof margin == \"undefined\" ? cellSize * 2 : margin;\n            var size = _this.getModuleCount() * cellSize + margin * 2;\n            var min = margin;\n            var max = size - margin;\n            var y, x, r, p;\n            var white = Array(cellSize + 1).join(\"██\");\n            var black = Array(cellSize + 1).join(\"  \");\n            var ascii = \"\";\n            var line = \"\";\n            for(y = 0; y < size; y += 1){\n                r = Math.floor((y - min) / cellSize);\n                line = \"\";\n                for(x = 0; x < size; x += 1){\n                    p = 1;\n                    if (min <= x && x < max && min <= y && y < max && _this.isDark(r, Math.floor((x - min) / cellSize))) {\n                        p = 0;\n                    }\n                    // Output 2 characters per pixel, to create full square. 1 character per pixels gives only half width of square.\n                    line += p ? white : black;\n                }\n                for(r = 0; r < cellSize; r += 1){\n                    ascii += line + \"\\n\";\n                }\n            }\n            return ascii.substring(0, ascii.length - 1);\n        };\n        _this.renderTo2dContext = function(context, cellSize) {\n            cellSize = cellSize || 2;\n            var length = _this.getModuleCount();\n            for(var row = 0; row < length; row++){\n                for(var col = 0; col < length; col++){\n                    context.fillStyle = _this.isDark(row, col) ? \"black\" : \"white\";\n                    context.fillRect(row * cellSize, col * cellSize, cellSize, cellSize);\n                }\n            }\n        };\n        return _this;\n    };\n    //---------------------------------------------------------------------\n    // qrcode.stringToBytes\n    //---------------------------------------------------------------------\n    qrcode.stringToBytesFuncs = {\n        \"default\": function(s) {\n            var bytes = [];\n            for(var i = 0; i < s.length; i += 1){\n                var c = s.charCodeAt(i);\n                bytes.push(c & 0xff);\n            }\n            return bytes;\n        }\n    };\n    qrcode.stringToBytes = qrcode.stringToBytesFuncs[\"default\"];\n    //---------------------------------------------------------------------\n    // qrcode.createStringToBytes\n    //---------------------------------------------------------------------\n    /**\n   * @param unicodeData base64 string of byte array.\n   * [16bit Unicode],[16bit Bytes], ...\n   * @param numChars\n   */ qrcode.createStringToBytes = function(unicodeData, numChars) {\n        // create conversion map.\n        var unicodeMap = function() {\n            var bin = base64DecodeInputStream(unicodeData);\n            var read = function() {\n                var b = bin.read();\n                if (b == -1) throw \"eof\";\n                return b;\n            };\n            var count = 0;\n            var unicodeMap = {};\n            while(true){\n                var b0 = bin.read();\n                if (b0 == -1) break;\n                var b1 = read();\n                var b2 = read();\n                var b3 = read();\n                var k = String.fromCharCode(b0 << 8 | b1);\n                var v = b2 << 8 | b3;\n                unicodeMap[k] = v;\n                count += 1;\n            }\n            if (count != numChars) {\n                throw count + \" != \" + numChars;\n            }\n            return unicodeMap;\n        }();\n        var unknownChar = \"?\".charCodeAt(0);\n        return function(s) {\n            var bytes = [];\n            for(var i = 0; i < s.length; i += 1){\n                var c = s.charCodeAt(i);\n                if (c < 128) {\n                    bytes.push(c);\n                } else {\n                    var b = unicodeMap[s.charAt(i)];\n                    if (typeof b == \"number\") {\n                        if ((b & 0xff) == b) {\n                            // 1byte\n                            bytes.push(b);\n                        } else {\n                            // 2bytes\n                            bytes.push(b >>> 8);\n                            bytes.push(b & 0xff);\n                        }\n                    } else {\n                        bytes.push(unknownChar);\n                    }\n                }\n            }\n            return bytes;\n        };\n    };\n    //---------------------------------------------------------------------\n    // QRMode\n    //---------------------------------------------------------------------\n    var QRMode = {\n        MODE_NUMBER: 1 << 0,\n        MODE_ALPHA_NUM: 1 << 1,\n        MODE_8BIT_BYTE: 1 << 2,\n        MODE_KANJI: 1 << 3\n    };\n    //---------------------------------------------------------------------\n    // QRErrorCorrectionLevel\n    //---------------------------------------------------------------------\n    var QRErrorCorrectionLevel = {\n        L: 1,\n        M: 0,\n        Q: 3,\n        H: 2\n    };\n    //---------------------------------------------------------------------\n    // QRMaskPattern\n    //---------------------------------------------------------------------\n    var QRMaskPattern = {\n        PATTERN000: 0,\n        PATTERN001: 1,\n        PATTERN010: 2,\n        PATTERN011: 3,\n        PATTERN100: 4,\n        PATTERN101: 5,\n        PATTERN110: 6,\n        PATTERN111: 7\n    };\n    //---------------------------------------------------------------------\n    // QRUtil\n    //---------------------------------------------------------------------\n    var QRUtil = function() {\n        var PATTERN_POSITION_TABLE = [\n            [],\n            [\n                6,\n                18\n            ],\n            [\n                6,\n                22\n            ],\n            [\n                6,\n                26\n            ],\n            [\n                6,\n                30\n            ],\n            [\n                6,\n                34\n            ],\n            [\n                6,\n                22,\n                38\n            ],\n            [\n                6,\n                24,\n                42\n            ],\n            [\n                6,\n                26,\n                46\n            ],\n            [\n                6,\n                28,\n                50\n            ],\n            [\n                6,\n                30,\n                54\n            ],\n            [\n                6,\n                32,\n                58\n            ],\n            [\n                6,\n                34,\n                62\n            ],\n            [\n                6,\n                26,\n                46,\n                66\n            ],\n            [\n                6,\n                26,\n                48,\n                70\n            ],\n            [\n                6,\n                26,\n                50,\n                74\n            ],\n            [\n                6,\n                30,\n                54,\n                78\n            ],\n            [\n                6,\n                30,\n                56,\n                82\n            ],\n            [\n                6,\n                30,\n                58,\n                86\n            ],\n            [\n                6,\n                34,\n                62,\n                90\n            ],\n            [\n                6,\n                28,\n                50,\n                72,\n                94\n            ],\n            [\n                6,\n                26,\n                50,\n                74,\n                98\n            ],\n            [\n                6,\n                30,\n                54,\n                78,\n                102\n            ],\n            [\n                6,\n                28,\n                54,\n                80,\n                106\n            ],\n            [\n                6,\n                32,\n                58,\n                84,\n                110\n            ],\n            [\n                6,\n                30,\n                58,\n                86,\n                114\n            ],\n            [\n                6,\n                34,\n                62,\n                90,\n                118\n            ],\n            [\n                6,\n                26,\n                50,\n                74,\n                98,\n                122\n            ],\n            [\n                6,\n                30,\n                54,\n                78,\n                102,\n                126\n            ],\n            [\n                6,\n                26,\n                52,\n                78,\n                104,\n                130\n            ],\n            [\n                6,\n                30,\n                56,\n                82,\n                108,\n                134\n            ],\n            [\n                6,\n                34,\n                60,\n                86,\n                112,\n                138\n            ],\n            [\n                6,\n                30,\n                58,\n                86,\n                114,\n                142\n            ],\n            [\n                6,\n                34,\n                62,\n                90,\n                118,\n                146\n            ],\n            [\n                6,\n                30,\n                54,\n                78,\n                102,\n                126,\n                150\n            ],\n            [\n                6,\n                24,\n                50,\n                76,\n                102,\n                128,\n                154\n            ],\n            [\n                6,\n                28,\n                54,\n                80,\n                106,\n                132,\n                158\n            ],\n            [\n                6,\n                32,\n                58,\n                84,\n                110,\n                136,\n                162\n            ],\n            [\n                6,\n                26,\n                54,\n                82,\n                110,\n                138,\n                166\n            ],\n            [\n                6,\n                30,\n                58,\n                86,\n                114,\n                142,\n                170\n            ]\n        ];\n        var G15 = 1 << 10 | 1 << 8 | 1 << 5 | 1 << 4 | 1 << 2 | 1 << 1 | 1 << 0;\n        var G18 = 1 << 12 | 1 << 11 | 1 << 10 | 1 << 9 | 1 << 8 | 1 << 5 | 1 << 2 | 1 << 0;\n        var G15_MASK = 1 << 14 | 1 << 12 | 1 << 10 | 1 << 4 | 1 << 1;\n        var _this = {};\n        var getBCHDigit = function(data) {\n            var digit = 0;\n            while(data != 0){\n                digit += 1;\n                data >>>= 1;\n            }\n            return digit;\n        };\n        _this.getBCHTypeInfo = function(data) {\n            var d = data << 10;\n            while(getBCHDigit(d) - getBCHDigit(G15) >= 0){\n                d ^= G15 << getBCHDigit(d) - getBCHDigit(G15);\n            }\n            return (data << 10 | d) ^ G15_MASK;\n        };\n        _this.getBCHTypeNumber = function(data) {\n            var d = data << 12;\n            while(getBCHDigit(d) - getBCHDigit(G18) >= 0){\n                d ^= G18 << getBCHDigit(d) - getBCHDigit(G18);\n            }\n            return data << 12 | d;\n        };\n        _this.getPatternPosition = function(typeNumber) {\n            return PATTERN_POSITION_TABLE[typeNumber - 1];\n        };\n        _this.getMaskFunction = function(maskPattern) {\n            switch(maskPattern){\n                case QRMaskPattern.PATTERN000:\n                    return function(i, j) {\n                        return (i + j) % 2 == 0;\n                    };\n                case QRMaskPattern.PATTERN001:\n                    return function(i, j) {\n                        return i % 2 == 0;\n                    };\n                case QRMaskPattern.PATTERN010:\n                    return function(i, j) {\n                        return j % 3 == 0;\n                    };\n                case QRMaskPattern.PATTERN011:\n                    return function(i, j) {\n                        return (i + j) % 3 == 0;\n                    };\n                case QRMaskPattern.PATTERN100:\n                    return function(i, j) {\n                        return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 == 0;\n                    };\n                case QRMaskPattern.PATTERN101:\n                    return function(i, j) {\n                        return i * j % 2 + i * j % 3 == 0;\n                    };\n                case QRMaskPattern.PATTERN110:\n                    return function(i, j) {\n                        return (i * j % 2 + i * j % 3) % 2 == 0;\n                    };\n                case QRMaskPattern.PATTERN111:\n                    return function(i, j) {\n                        return (i * j % 3 + (i + j) % 2) % 2 == 0;\n                    };\n                default:\n                    throw \"bad maskPattern:\" + maskPattern;\n            }\n        };\n        _this.getErrorCorrectPolynomial = function(errorCorrectLength) {\n            var a = qrPolynomial([\n                1\n            ], 0);\n            for(var i = 0; i < errorCorrectLength; i += 1){\n                a = a.multiply(qrPolynomial([\n                    1,\n                    QRMath.gexp(i)\n                ], 0));\n            }\n            return a;\n        };\n        _this.getLengthInBits = function(mode, type) {\n            if (1 <= type && type < 10) {\n                // 1 - 9\n                switch(mode){\n                    case QRMode.MODE_NUMBER:\n                        return 10;\n                    case QRMode.MODE_ALPHA_NUM:\n                        return 9;\n                    case QRMode.MODE_8BIT_BYTE:\n                        return 8;\n                    case QRMode.MODE_KANJI:\n                        return 8;\n                    default:\n                        throw \"mode:\" + mode;\n                }\n            } else if (type < 27) {\n                // 10 - 26\n                switch(mode){\n                    case QRMode.MODE_NUMBER:\n                        return 12;\n                    case QRMode.MODE_ALPHA_NUM:\n                        return 11;\n                    case QRMode.MODE_8BIT_BYTE:\n                        return 16;\n                    case QRMode.MODE_KANJI:\n                        return 10;\n                    default:\n                        throw \"mode:\" + mode;\n                }\n            } else if (type < 41) {\n                // 27 - 40\n                switch(mode){\n                    case QRMode.MODE_NUMBER:\n                        return 14;\n                    case QRMode.MODE_ALPHA_NUM:\n                        return 13;\n                    case QRMode.MODE_8BIT_BYTE:\n                        return 16;\n                    case QRMode.MODE_KANJI:\n                        return 12;\n                    default:\n                        throw \"mode:\" + mode;\n                }\n            } else {\n                throw \"type:\" + type;\n            }\n        };\n        _this.getLostPoint = function(qrcode) {\n            var moduleCount = qrcode.getModuleCount();\n            var lostPoint = 0;\n            // LEVEL1\n            for(var row = 0; row < moduleCount; row += 1){\n                for(var col = 0; col < moduleCount; col += 1){\n                    var sameCount = 0;\n                    var dark = qrcode.isDark(row, col);\n                    for(var r = -1; r <= 1; r += 1){\n                        if (row + r < 0 || moduleCount <= row + r) {\n                            continue;\n                        }\n                        for(var c = -1; c <= 1; c += 1){\n                            if (col + c < 0 || moduleCount <= col + c) {\n                                continue;\n                            }\n                            if (r == 0 && c == 0) {\n                                continue;\n                            }\n                            if (dark == qrcode.isDark(row + r, col + c)) {\n                                sameCount += 1;\n                            }\n                        }\n                    }\n                    if (sameCount > 5) {\n                        lostPoint += 3 + sameCount - 5;\n                    }\n                }\n            }\n            ;\n            // LEVEL2\n            for(var row = 0; row < moduleCount - 1; row += 1){\n                for(var col = 0; col < moduleCount - 1; col += 1){\n                    var count = 0;\n                    if (qrcode.isDark(row, col)) count += 1;\n                    if (qrcode.isDark(row + 1, col)) count += 1;\n                    if (qrcode.isDark(row, col + 1)) count += 1;\n                    if (qrcode.isDark(row + 1, col + 1)) count += 1;\n                    if (count == 0 || count == 4) {\n                        lostPoint += 3;\n                    }\n                }\n            }\n            // LEVEL3\n            for(var row = 0; row < moduleCount; row += 1){\n                for(var col = 0; col < moduleCount - 6; col += 1){\n                    if (qrcode.isDark(row, col) && !qrcode.isDark(row, col + 1) && qrcode.isDark(row, col + 2) && qrcode.isDark(row, col + 3) && qrcode.isDark(row, col + 4) && !qrcode.isDark(row, col + 5) && qrcode.isDark(row, col + 6)) {\n                        lostPoint += 40;\n                    }\n                }\n            }\n            for(var col = 0; col < moduleCount; col += 1){\n                for(var row = 0; row < moduleCount - 6; row += 1){\n                    if (qrcode.isDark(row, col) && !qrcode.isDark(row + 1, col) && qrcode.isDark(row + 2, col) && qrcode.isDark(row + 3, col) && qrcode.isDark(row + 4, col) && !qrcode.isDark(row + 5, col) && qrcode.isDark(row + 6, col)) {\n                        lostPoint += 40;\n                    }\n                }\n            }\n            // LEVEL4\n            var darkCount = 0;\n            for(var col = 0; col < moduleCount; col += 1){\n                for(var row = 0; row < moduleCount; row += 1){\n                    if (qrcode.isDark(row, col)) {\n                        darkCount += 1;\n                    }\n                }\n            }\n            var ratio = Math.abs(100 * darkCount / moduleCount / moduleCount - 50) / 5;\n            lostPoint += ratio * 10;\n            return lostPoint;\n        };\n        return _this;\n    }();\n    //---------------------------------------------------------------------\n    // QRMath\n    //---------------------------------------------------------------------\n    var QRMath = function() {\n        var EXP_TABLE = new Array(256);\n        var LOG_TABLE = new Array(256);\n        // initialize tables\n        for(var i = 0; i < 8; i += 1){\n            EXP_TABLE[i] = 1 << i;\n        }\n        for(var i = 8; i < 256; i += 1){\n            EXP_TABLE[i] = EXP_TABLE[i - 4] ^ EXP_TABLE[i - 5] ^ EXP_TABLE[i - 6] ^ EXP_TABLE[i - 8];\n        }\n        for(var i = 0; i < 255; i += 1){\n            LOG_TABLE[EXP_TABLE[i]] = i;\n        }\n        var _this = {};\n        _this.glog = function(n) {\n            if (n < 1) {\n                throw \"glog(\" + n + \")\";\n            }\n            return LOG_TABLE[n];\n        };\n        _this.gexp = function(n) {\n            while(n < 0){\n                n += 255;\n            }\n            while(n >= 256){\n                n -= 255;\n            }\n            return EXP_TABLE[n];\n        };\n        return _this;\n    }();\n    //---------------------------------------------------------------------\n    // qrPolynomial\n    //---------------------------------------------------------------------\n    function qrPolynomial(num, shift) {\n        if (typeof num.length == \"undefined\") {\n            throw num.length + \"/\" + shift;\n        }\n        var _num = function() {\n            var offset = 0;\n            while(offset < num.length && num[offset] == 0){\n                offset += 1;\n            }\n            var _num = new Array(num.length - offset + shift);\n            for(var i = 0; i < num.length - offset; i += 1){\n                _num[i] = num[i + offset];\n            }\n            return _num;\n        }();\n        var _this = {};\n        _this.getAt = function(index) {\n            return _num[index];\n        };\n        _this.getLength = function() {\n            return _num.length;\n        };\n        _this.multiply = function(e) {\n            var num = new Array(_this.getLength() + e.getLength() - 1);\n            for(var i = 0; i < _this.getLength(); i += 1){\n                for(var j = 0; j < e.getLength(); j += 1){\n                    num[i + j] ^= QRMath.gexp(QRMath.glog(_this.getAt(i)) + QRMath.glog(e.getAt(j)));\n                }\n            }\n            return qrPolynomial(num, 0);\n        };\n        _this.mod = function(e) {\n            if (_this.getLength() - e.getLength() < 0) {\n                return _this;\n            }\n            var ratio = QRMath.glog(_this.getAt(0)) - QRMath.glog(e.getAt(0));\n            var num = new Array(_this.getLength());\n            for(var i = 0; i < _this.getLength(); i += 1){\n                num[i] = _this.getAt(i);\n            }\n            for(var i = 0; i < e.getLength(); i += 1){\n                num[i] ^= QRMath.gexp(QRMath.glog(e.getAt(i)) + ratio);\n            }\n            // recursive call\n            return qrPolynomial(num, 0).mod(e);\n        };\n        return _this;\n    }\n    ;\n    //---------------------------------------------------------------------\n    // QRRSBlock\n    //---------------------------------------------------------------------\n    var QRRSBlock = function() {\n        var RS_BLOCK_TABLE = [\n            // L\n            // M\n            // Q\n            // H\n            // 1\n            [\n                1,\n                26,\n                19\n            ],\n            [\n                1,\n                26,\n                16\n            ],\n            [\n                1,\n                26,\n                13\n            ],\n            [\n                1,\n                26,\n                9\n            ],\n            // 2\n            [\n                1,\n                44,\n                34\n            ],\n            [\n                1,\n                44,\n                28\n            ],\n            [\n                1,\n                44,\n                22\n            ],\n            [\n                1,\n                44,\n                16\n            ],\n            // 3\n            [\n                1,\n                70,\n                55\n            ],\n            [\n                1,\n                70,\n                44\n            ],\n            [\n                2,\n                35,\n                17\n            ],\n            [\n                2,\n                35,\n                13\n            ],\n            // 4\n            [\n                1,\n                100,\n                80\n            ],\n            [\n                2,\n                50,\n                32\n            ],\n            [\n                2,\n                50,\n                24\n            ],\n            [\n                4,\n                25,\n                9\n            ],\n            // 5\n            [\n                1,\n                134,\n                108\n            ],\n            [\n                2,\n                67,\n                43\n            ],\n            [\n                2,\n                33,\n                15,\n                2,\n                34,\n                16\n            ],\n            [\n                2,\n                33,\n                11,\n                2,\n                34,\n                12\n            ],\n            // 6\n            [\n                2,\n                86,\n                68\n            ],\n            [\n                4,\n                43,\n                27\n            ],\n            [\n                4,\n                43,\n                19\n            ],\n            [\n                4,\n                43,\n                15\n            ],\n            // 7\n            [\n                2,\n                98,\n                78\n            ],\n            [\n                4,\n                49,\n                31\n            ],\n            [\n                2,\n                32,\n                14,\n                4,\n                33,\n                15\n            ],\n            [\n                4,\n                39,\n                13,\n                1,\n                40,\n                14\n            ],\n            // 8\n            [\n                2,\n                121,\n                97\n            ],\n            [\n                2,\n                60,\n                38,\n                2,\n                61,\n                39\n            ],\n            [\n                4,\n                40,\n                18,\n                2,\n                41,\n                19\n            ],\n            [\n                4,\n                40,\n                14,\n                2,\n                41,\n                15\n            ],\n            // 9\n            [\n                2,\n                146,\n                116\n            ],\n            [\n                3,\n                58,\n                36,\n                2,\n                59,\n                37\n            ],\n            [\n                4,\n                36,\n                16,\n                4,\n                37,\n                17\n            ],\n            [\n                4,\n                36,\n                12,\n                4,\n                37,\n                13\n            ],\n            // 10\n            [\n                2,\n                86,\n                68,\n                2,\n                87,\n                69\n            ],\n            [\n                4,\n                69,\n                43,\n                1,\n                70,\n                44\n            ],\n            [\n                6,\n                43,\n                19,\n                2,\n                44,\n                20\n            ],\n            [\n                6,\n                43,\n                15,\n                2,\n                44,\n                16\n            ],\n            // 11\n            [\n                4,\n                101,\n                81\n            ],\n            [\n                1,\n                80,\n                50,\n                4,\n                81,\n                51\n            ],\n            [\n                4,\n                50,\n                22,\n                4,\n                51,\n                23\n            ],\n            [\n                3,\n                36,\n                12,\n                8,\n                37,\n                13\n            ],\n            // 12\n            [\n                2,\n                116,\n                92,\n                2,\n                117,\n                93\n            ],\n            [\n                6,\n                58,\n                36,\n                2,\n                59,\n                37\n            ],\n            [\n                4,\n                46,\n                20,\n                6,\n                47,\n                21\n            ],\n            [\n                7,\n                42,\n                14,\n                4,\n                43,\n                15\n            ],\n            // 13\n            [\n                4,\n                133,\n                107\n            ],\n            [\n                8,\n                59,\n                37,\n                1,\n                60,\n                38\n            ],\n            [\n                8,\n                44,\n                20,\n                4,\n                45,\n                21\n            ],\n            [\n                12,\n                33,\n                11,\n                4,\n                34,\n                12\n            ],\n            // 14\n            [\n                3,\n                145,\n                115,\n                1,\n                146,\n                116\n            ],\n            [\n                4,\n                64,\n                40,\n                5,\n                65,\n                41\n            ],\n            [\n                11,\n                36,\n                16,\n                5,\n                37,\n                17\n            ],\n            [\n                11,\n                36,\n                12,\n                5,\n                37,\n                13\n            ],\n            // 15\n            [\n                5,\n                109,\n                87,\n                1,\n                110,\n                88\n            ],\n            [\n                5,\n                65,\n                41,\n                5,\n                66,\n                42\n            ],\n            [\n                5,\n                54,\n                24,\n                7,\n                55,\n                25\n            ],\n            [\n                11,\n                36,\n                12,\n                7,\n                37,\n                13\n            ],\n            // 16\n            [\n                5,\n                122,\n                98,\n                1,\n                123,\n                99\n            ],\n            [\n                7,\n                73,\n                45,\n                3,\n                74,\n                46\n            ],\n            [\n                15,\n                43,\n                19,\n                2,\n                44,\n                20\n            ],\n            [\n                3,\n                45,\n                15,\n                13,\n                46,\n                16\n            ],\n            // 17\n            [\n                1,\n                135,\n                107,\n                5,\n                136,\n                108\n            ],\n            [\n                10,\n                74,\n                46,\n                1,\n                75,\n                47\n            ],\n            [\n                1,\n                50,\n                22,\n                15,\n                51,\n                23\n            ],\n            [\n                2,\n                42,\n                14,\n                17,\n                43,\n                15\n            ],\n            // 18\n            [\n                5,\n                150,\n                120,\n                1,\n                151,\n                121\n            ],\n            [\n                9,\n                69,\n                43,\n                4,\n                70,\n                44\n            ],\n            [\n                17,\n                50,\n                22,\n                1,\n                51,\n                23\n            ],\n            [\n                2,\n                42,\n                14,\n                19,\n                43,\n                15\n            ],\n            // 19\n            [\n                3,\n                141,\n                113,\n                4,\n                142,\n                114\n            ],\n            [\n                3,\n                70,\n                44,\n                11,\n                71,\n                45\n            ],\n            [\n                17,\n                47,\n                21,\n                4,\n                48,\n                22\n            ],\n            [\n                9,\n                39,\n                13,\n                16,\n                40,\n                14\n            ],\n            // 20\n            [\n                3,\n                135,\n                107,\n                5,\n                136,\n                108\n            ],\n            [\n                3,\n                67,\n                41,\n                13,\n                68,\n                42\n            ],\n            [\n                15,\n                54,\n                24,\n                5,\n                55,\n                25\n            ],\n            [\n                15,\n                43,\n                15,\n                10,\n                44,\n                16\n            ],\n            // 21\n            [\n                4,\n                144,\n                116,\n                4,\n                145,\n                117\n            ],\n            [\n                17,\n                68,\n                42\n            ],\n            [\n                17,\n                50,\n                22,\n                6,\n                51,\n                23\n            ],\n            [\n                19,\n                46,\n                16,\n                6,\n                47,\n                17\n            ],\n            // 22\n            [\n                2,\n                139,\n                111,\n                7,\n                140,\n                112\n            ],\n            [\n                17,\n                74,\n                46\n            ],\n            [\n                7,\n                54,\n                24,\n                16,\n                55,\n                25\n            ],\n            [\n                34,\n                37,\n                13\n            ],\n            // 23\n            [\n                4,\n                151,\n                121,\n                5,\n                152,\n                122\n            ],\n            [\n                4,\n                75,\n                47,\n                14,\n                76,\n                48\n            ],\n            [\n                11,\n                54,\n                24,\n                14,\n                55,\n                25\n            ],\n            [\n                16,\n                45,\n                15,\n                14,\n                46,\n                16\n            ],\n            // 24\n            [\n                6,\n                147,\n                117,\n                4,\n                148,\n                118\n            ],\n            [\n                6,\n                73,\n                45,\n                14,\n                74,\n                46\n            ],\n            [\n                11,\n                54,\n                24,\n                16,\n                55,\n                25\n            ],\n            [\n                30,\n                46,\n                16,\n                2,\n                47,\n                17\n            ],\n            // 25\n            [\n                8,\n                132,\n                106,\n                4,\n                133,\n                107\n            ],\n            [\n                8,\n                75,\n                47,\n                13,\n                76,\n                48\n            ],\n            [\n                7,\n                54,\n                24,\n                22,\n                55,\n                25\n            ],\n            [\n                22,\n                45,\n                15,\n                13,\n                46,\n                16\n            ],\n            // 26\n            [\n                10,\n                142,\n                114,\n                2,\n                143,\n                115\n            ],\n            [\n                19,\n                74,\n                46,\n                4,\n                75,\n                47\n            ],\n            [\n                28,\n                50,\n                22,\n                6,\n                51,\n                23\n            ],\n            [\n                33,\n                46,\n                16,\n                4,\n                47,\n                17\n            ],\n            // 27\n            [\n                8,\n                152,\n                122,\n                4,\n                153,\n                123\n            ],\n            [\n                22,\n                73,\n                45,\n                3,\n                74,\n                46\n            ],\n            [\n                8,\n                53,\n                23,\n                26,\n                54,\n                24\n            ],\n            [\n                12,\n                45,\n                15,\n                28,\n                46,\n                16\n            ],\n            // 28\n            [\n                3,\n                147,\n                117,\n                10,\n                148,\n                118\n            ],\n            [\n                3,\n                73,\n                45,\n                23,\n                74,\n                46\n            ],\n            [\n                4,\n                54,\n                24,\n                31,\n                55,\n                25\n            ],\n            [\n                11,\n                45,\n                15,\n                31,\n                46,\n                16\n            ],\n            // 29\n            [\n                7,\n                146,\n                116,\n                7,\n                147,\n                117\n            ],\n            [\n                21,\n                73,\n                45,\n                7,\n                74,\n                46\n            ],\n            [\n                1,\n                53,\n                23,\n                37,\n                54,\n                24\n            ],\n            [\n                19,\n                45,\n                15,\n                26,\n                46,\n                16\n            ],\n            // 30\n            [\n                5,\n                145,\n                115,\n                10,\n                146,\n                116\n            ],\n            [\n                19,\n                75,\n                47,\n                10,\n                76,\n                48\n            ],\n            [\n                15,\n                54,\n                24,\n                25,\n                55,\n                25\n            ],\n            [\n                23,\n                45,\n                15,\n                25,\n                46,\n                16\n            ],\n            // 31\n            [\n                13,\n                145,\n                115,\n                3,\n                146,\n                116\n            ],\n            [\n                2,\n                74,\n                46,\n                29,\n                75,\n                47\n            ],\n            [\n                42,\n                54,\n                24,\n                1,\n                55,\n                25\n            ],\n            [\n                23,\n                45,\n                15,\n                28,\n                46,\n                16\n            ],\n            // 32\n            [\n                17,\n                145,\n                115\n            ],\n            [\n                10,\n                74,\n                46,\n                23,\n                75,\n                47\n            ],\n            [\n                10,\n                54,\n                24,\n                35,\n                55,\n                25\n            ],\n            [\n                19,\n                45,\n                15,\n                35,\n                46,\n                16\n            ],\n            // 33\n            [\n                17,\n                145,\n                115,\n                1,\n                146,\n                116\n            ],\n            [\n                14,\n                74,\n                46,\n                21,\n                75,\n                47\n            ],\n            [\n                29,\n                54,\n                24,\n                19,\n                55,\n                25\n            ],\n            [\n                11,\n                45,\n                15,\n                46,\n                46,\n                16\n            ],\n            // 34\n            [\n                13,\n                145,\n                115,\n                6,\n                146,\n                116\n            ],\n            [\n                14,\n                74,\n                46,\n                23,\n                75,\n                47\n            ],\n            [\n                44,\n                54,\n                24,\n                7,\n                55,\n                25\n            ],\n            [\n                59,\n                46,\n                16,\n                1,\n                47,\n                17\n            ],\n            // 35\n            [\n                12,\n                151,\n                121,\n                7,\n                152,\n                122\n            ],\n            [\n                12,\n                75,\n                47,\n                26,\n                76,\n                48\n            ],\n            [\n                39,\n                54,\n                24,\n                14,\n                55,\n                25\n            ],\n            [\n                22,\n                45,\n                15,\n                41,\n                46,\n                16\n            ],\n            // 36\n            [\n                6,\n                151,\n                121,\n                14,\n                152,\n                122\n            ],\n            [\n                6,\n                75,\n                47,\n                34,\n                76,\n                48\n            ],\n            [\n                46,\n                54,\n                24,\n                10,\n                55,\n                25\n            ],\n            [\n                2,\n                45,\n                15,\n                64,\n                46,\n                16\n            ],\n            // 37\n            [\n                17,\n                152,\n                122,\n                4,\n                153,\n                123\n            ],\n            [\n                29,\n                74,\n                46,\n                14,\n                75,\n                47\n            ],\n            [\n                49,\n                54,\n                24,\n                10,\n                55,\n                25\n            ],\n            [\n                24,\n                45,\n                15,\n                46,\n                46,\n                16\n            ],\n            // 38\n            [\n                4,\n                152,\n                122,\n                18,\n                153,\n                123\n            ],\n            [\n                13,\n                74,\n                46,\n                32,\n                75,\n                47\n            ],\n            [\n                48,\n                54,\n                24,\n                14,\n                55,\n                25\n            ],\n            [\n                42,\n                45,\n                15,\n                32,\n                46,\n                16\n            ],\n            // 39\n            [\n                20,\n                147,\n                117,\n                4,\n                148,\n                118\n            ],\n            [\n                40,\n                75,\n                47,\n                7,\n                76,\n                48\n            ],\n            [\n                43,\n                54,\n                24,\n                22,\n                55,\n                25\n            ],\n            [\n                10,\n                45,\n                15,\n                67,\n                46,\n                16\n            ],\n            // 40\n            [\n                19,\n                148,\n                118,\n                6,\n                149,\n                119\n            ],\n            [\n                18,\n                75,\n                47,\n                31,\n                76,\n                48\n            ],\n            [\n                34,\n                54,\n                24,\n                34,\n                55,\n                25\n            ],\n            [\n                20,\n                45,\n                15,\n                61,\n                46,\n                16\n            ]\n        ];\n        var qrRSBlock = function(totalCount, dataCount) {\n            var _this = {};\n            _this.totalCount = totalCount;\n            _this.dataCount = dataCount;\n            return _this;\n        };\n        var _this = {};\n        var getRsBlockTable = function(typeNumber, errorCorrectionLevel) {\n            switch(errorCorrectionLevel){\n                case QRErrorCorrectionLevel.L:\n                    return RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 0];\n                case QRErrorCorrectionLevel.M:\n                    return RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 1];\n                case QRErrorCorrectionLevel.Q:\n                    return RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 2];\n                case QRErrorCorrectionLevel.H:\n                    return RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 3];\n                default:\n                    return undefined;\n            }\n        };\n        _this.getRSBlocks = function(typeNumber, errorCorrectionLevel) {\n            var rsBlock = getRsBlockTable(typeNumber, errorCorrectionLevel);\n            if (typeof rsBlock == \"undefined\") {\n                throw \"bad rs block @ typeNumber:\" + typeNumber + \"/errorCorrectionLevel:\" + errorCorrectionLevel;\n            }\n            var length = rsBlock.length / 3;\n            var list = [];\n            for(var i = 0; i < length; i += 1){\n                var count = rsBlock[i * 3 + 0];\n                var totalCount = rsBlock[i * 3 + 1];\n                var dataCount = rsBlock[i * 3 + 2];\n                for(var j = 0; j < count; j += 1){\n                    list.push(qrRSBlock(totalCount, dataCount));\n                }\n            }\n            return list;\n        };\n        return _this;\n    }();\n    //---------------------------------------------------------------------\n    // qrBitBuffer\n    //---------------------------------------------------------------------\n    var qrBitBuffer = function() {\n        var _buffer = [];\n        var _length = 0;\n        var _this = {};\n        _this.getBuffer = function() {\n            return _buffer;\n        };\n        _this.getAt = function(index) {\n            var bufIndex = Math.floor(index / 8);\n            return (_buffer[bufIndex] >>> 7 - index % 8 & 1) == 1;\n        };\n        _this.put = function(num, length) {\n            for(var i = 0; i < length; i += 1){\n                _this.putBit((num >>> length - i - 1 & 1) == 1);\n            }\n        };\n        _this.getLengthInBits = function() {\n            return _length;\n        };\n        _this.putBit = function(bit) {\n            var bufIndex = Math.floor(_length / 8);\n            if (_buffer.length <= bufIndex) {\n                _buffer.push(0);\n            }\n            if (bit) {\n                _buffer[bufIndex] |= 0x80 >>> _length % 8;\n            }\n            _length += 1;\n        };\n        return _this;\n    };\n    //---------------------------------------------------------------------\n    // qrNumber\n    //---------------------------------------------------------------------\n    var qrNumber = function(data) {\n        var _mode = QRMode.MODE_NUMBER;\n        var _data = data;\n        var _this = {};\n        _this.getMode = function() {\n            return _mode;\n        };\n        _this.getLength = function(buffer) {\n            return _data.length;\n        };\n        _this.write = function(buffer) {\n            var data = _data;\n            var i = 0;\n            while(i + 2 < data.length){\n                buffer.put(strToNum(data.substring(i, i + 3)), 10);\n                i += 3;\n            }\n            if (i < data.length) {\n                if (data.length - i == 1) {\n                    buffer.put(strToNum(data.substring(i, i + 1)), 4);\n                } else if (data.length - i == 2) {\n                    buffer.put(strToNum(data.substring(i, i + 2)), 7);\n                }\n            }\n        };\n        var strToNum = function(s) {\n            var num = 0;\n            for(var i = 0; i < s.length; i += 1){\n                num = num * 10 + chatToNum(s.charAt(i));\n            }\n            return num;\n        };\n        var chatToNum = function(c) {\n            if (\"0\" <= c && c <= \"9\") {\n                return c.charCodeAt(0) - \"0\".charCodeAt(0);\n            }\n            throw \"illegal char :\" + c;\n        };\n        return _this;\n    };\n    //---------------------------------------------------------------------\n    // qrAlphaNum\n    //---------------------------------------------------------------------\n    var qrAlphaNum = function(data) {\n        var _mode = QRMode.MODE_ALPHA_NUM;\n        var _data = data;\n        var _this = {};\n        _this.getMode = function() {\n            return _mode;\n        };\n        _this.getLength = function(buffer) {\n            return _data.length;\n        };\n        _this.write = function(buffer) {\n            var s = _data;\n            var i = 0;\n            while(i + 1 < s.length){\n                buffer.put(getCode(s.charAt(i)) * 45 + getCode(s.charAt(i + 1)), 11);\n                i += 2;\n            }\n            if (i < s.length) {\n                buffer.put(getCode(s.charAt(i)), 6);\n            }\n        };\n        var getCode = function(c) {\n            if (\"0\" <= c && c <= \"9\") {\n                return c.charCodeAt(0) - \"0\".charCodeAt(0);\n            } else if (\"A\" <= c && c <= \"Z\") {\n                return c.charCodeAt(0) - \"A\".charCodeAt(0) + 10;\n            } else {\n                switch(c){\n                    case \" \":\n                        return 36;\n                    case \"$\":\n                        return 37;\n                    case \"%\":\n                        return 38;\n                    case \"*\":\n                        return 39;\n                    case \"+\":\n                        return 40;\n                    case \"-\":\n                        return 41;\n                    case \".\":\n                        return 42;\n                    case \"/\":\n                        return 43;\n                    case \":\":\n                        return 44;\n                    default:\n                        throw \"illegal char :\" + c;\n                }\n            }\n        };\n        return _this;\n    };\n    //---------------------------------------------------------------------\n    // qr8BitByte\n    //---------------------------------------------------------------------\n    var qr8BitByte = function(data) {\n        var _mode = QRMode.MODE_8BIT_BYTE;\n        var _data = data;\n        var _bytes = qrcode.stringToBytes(data);\n        var _this = {};\n        _this.getMode = function() {\n            return _mode;\n        };\n        _this.getLength = function(buffer) {\n            return _bytes.length;\n        };\n        _this.write = function(buffer) {\n            for(var i = 0; i < _bytes.length; i += 1){\n                buffer.put(_bytes[i], 8);\n            }\n        };\n        return _this;\n    };\n    //---------------------------------------------------------------------\n    // qrKanji\n    //---------------------------------------------------------------------\n    var qrKanji = function(data) {\n        var _mode = QRMode.MODE_KANJI;\n        var _data = data;\n        var stringToBytes = qrcode.stringToBytesFuncs[\"SJIS\"];\n        if (!stringToBytes) {\n            throw \"sjis not supported.\";\n        }\n        !function(c, code) {\n            // self test for sjis support.\n            var test = stringToBytes(c);\n            if (test.length != 2 || (test[0] << 8 | test[1]) != code) {\n                throw \"sjis not supported.\";\n            }\n        }(\"友\", 0x9746);\n        var _bytes = stringToBytes(data);\n        var _this = {};\n        _this.getMode = function() {\n            return _mode;\n        };\n        _this.getLength = function(buffer) {\n            return ~~(_bytes.length / 2);\n        };\n        _this.write = function(buffer) {\n            var data = _bytes;\n            var i = 0;\n            while(i + 1 < data.length){\n                var c = (0xff & data[i]) << 8 | 0xff & data[i + 1];\n                if (0x8140 <= c && c <= 0x9FFC) {\n                    c -= 0x8140;\n                } else if (0xE040 <= c && c <= 0xEBBF) {\n                    c -= 0xC140;\n                } else {\n                    throw \"illegal char at \" + (i + 1) + \"/\" + c;\n                }\n                c = (c >>> 8 & 0xff) * 0xC0 + (c & 0xff);\n                buffer.put(c, 13);\n                i += 2;\n            }\n            if (i < data.length) {\n                throw \"illegal char at \" + (i + 1);\n            }\n        };\n        return _this;\n    };\n    //=====================================================================\n    // GIF Support etc.\n    //\n    //---------------------------------------------------------------------\n    // byteArrayOutputStream\n    //---------------------------------------------------------------------\n    var byteArrayOutputStream = function() {\n        var _bytes = [];\n        var _this = {};\n        _this.writeByte = function(b) {\n            _bytes.push(b & 0xff);\n        };\n        _this.writeShort = function(i) {\n            _this.writeByte(i);\n            _this.writeByte(i >>> 8);\n        };\n        _this.writeBytes = function(b, off, len) {\n            off = off || 0;\n            len = len || b.length;\n            for(var i = 0; i < len; i += 1){\n                _this.writeByte(b[i + off]);\n            }\n        };\n        _this.writeString = function(s) {\n            for(var i = 0; i < s.length; i += 1){\n                _this.writeByte(s.charCodeAt(i));\n            }\n        };\n        _this.toByteArray = function() {\n            return _bytes;\n        };\n        _this.toString = function() {\n            var s = \"\";\n            s += \"[\";\n            for(var i = 0; i < _bytes.length; i += 1){\n                if (i > 0) {\n                    s += \",\";\n                }\n                s += _bytes[i];\n            }\n            s += \"]\";\n            return s;\n        };\n        return _this;\n    };\n    //---------------------------------------------------------------------\n    // base64EncodeOutputStream\n    //---------------------------------------------------------------------\n    var base64EncodeOutputStream = function() {\n        var _buffer = 0;\n        var _buflen = 0;\n        var _length = 0;\n        var _base64 = \"\";\n        var _this = {};\n        var writeEncoded = function(b) {\n            _base64 += String.fromCharCode(encode(b & 0x3f));\n        };\n        var encode = function(n) {\n            if (n < 0) {\n            // error.\n            } else if (n < 26) {\n                return 0x41 + n;\n            } else if (n < 52) {\n                return 0x61 + (n - 26);\n            } else if (n < 62) {\n                return 0x30 + (n - 52);\n            } else if (n == 62) {\n                return 0x2b;\n            } else if (n == 63) {\n                return 0x2f;\n            }\n            throw \"n:\" + n;\n        };\n        _this.writeByte = function(n) {\n            _buffer = _buffer << 8 | n & 0xff;\n            _buflen += 8;\n            _length += 1;\n            while(_buflen >= 6){\n                writeEncoded(_buffer >>> _buflen - 6);\n                _buflen -= 6;\n            }\n        };\n        _this.flush = function() {\n            if (_buflen > 0) {\n                writeEncoded(_buffer << 6 - _buflen);\n                _buffer = 0;\n                _buflen = 0;\n            }\n            if (_length % 3 != 0) {\n                // padding\n                var padlen = 3 - _length % 3;\n                for(var i = 0; i < padlen; i += 1){\n                    _base64 += \"=\";\n                }\n            }\n        };\n        _this.toString = function() {\n            return _base64;\n        };\n        return _this;\n    };\n    //---------------------------------------------------------------------\n    // base64DecodeInputStream\n    //---------------------------------------------------------------------\n    var base64DecodeInputStream = function(str) {\n        var _str = str;\n        var _pos = 0;\n        var _buffer = 0;\n        var _buflen = 0;\n        var _this = {};\n        _this.read = function() {\n            while(_buflen < 8){\n                if (_pos >= _str.length) {\n                    if (_buflen == 0) {\n                        return -1;\n                    }\n                    throw \"unexpected end of file./\" + _buflen;\n                }\n                var c = _str.charAt(_pos);\n                _pos += 1;\n                if (c == \"=\") {\n                    _buflen = 0;\n                    return -1;\n                } else if (c.match(/^\\s$/)) {\n                    continue;\n                }\n                _buffer = _buffer << 6 | decode(c.charCodeAt(0));\n                _buflen += 6;\n            }\n            var n = _buffer >>> _buflen - 8 & 0xff;\n            _buflen -= 8;\n            return n;\n        };\n        var decode = function(c) {\n            if (0x41 <= c && c <= 0x5a) {\n                return c - 0x41;\n            } else if (0x61 <= c && c <= 0x7a) {\n                return c - 0x61 + 26;\n            } else if (0x30 <= c && c <= 0x39) {\n                return c - 0x30 + 52;\n            } else if (c == 0x2b) {\n                return 62;\n            } else if (c == 0x2f) {\n                return 63;\n            } else {\n                throw \"c:\" + c;\n            }\n        };\n        return _this;\n    };\n    //---------------------------------------------------------------------\n    // gifImage (B/W)\n    //---------------------------------------------------------------------\n    var gifImage = function(width, height) {\n        var _width = width;\n        var _height = height;\n        var _data = new Array(width * height);\n        var _this = {};\n        _this.setPixel = function(x, y, pixel) {\n            _data[y * _width + x] = pixel;\n        };\n        _this.write = function(out) {\n            //---------------------------------\n            // GIF Signature\n            out.writeString(\"GIF87a\");\n            //---------------------------------\n            // Screen Descriptor\n            out.writeShort(_width);\n            out.writeShort(_height);\n            out.writeByte(0x80); // 2bit\n            out.writeByte(0);\n            out.writeByte(0);\n            //---------------------------------\n            // Global Color Map\n            // black\n            out.writeByte(0x00);\n            out.writeByte(0x00);\n            out.writeByte(0x00);\n            // white\n            out.writeByte(0xff);\n            out.writeByte(0xff);\n            out.writeByte(0xff);\n            //---------------------------------\n            // Image Descriptor\n            out.writeString(\",\");\n            out.writeShort(0);\n            out.writeShort(0);\n            out.writeShort(_width);\n            out.writeShort(_height);\n            out.writeByte(0);\n            //---------------------------------\n            // Local Color Map\n            //---------------------------------\n            // Raster Data\n            var lzwMinCodeSize = 2;\n            var raster = getLZWRaster(lzwMinCodeSize);\n            out.writeByte(lzwMinCodeSize);\n            var offset = 0;\n            while(raster.length - offset > 255){\n                out.writeByte(255);\n                out.writeBytes(raster, offset, 255);\n                offset += 255;\n            }\n            out.writeByte(raster.length - offset);\n            out.writeBytes(raster, offset, raster.length - offset);\n            out.writeByte(0x00);\n            //---------------------------------\n            // GIF Terminator\n            out.writeString(\";\");\n        };\n        var bitOutputStream = function(out) {\n            var _out = out;\n            var _bitLength = 0;\n            var _bitBuffer = 0;\n            var _this = {};\n            _this.write = function(data, length) {\n                if (data >>> length != 0) {\n                    throw \"length over\";\n                }\n                while(_bitLength + length >= 8){\n                    _out.writeByte(0xff & (data << _bitLength | _bitBuffer));\n                    length -= 8 - _bitLength;\n                    data >>>= 8 - _bitLength;\n                    _bitBuffer = 0;\n                    _bitLength = 0;\n                }\n                _bitBuffer = data << _bitLength | _bitBuffer;\n                _bitLength = _bitLength + length;\n            };\n            _this.flush = function() {\n                if (_bitLength > 0) {\n                    _out.writeByte(_bitBuffer);\n                }\n            };\n            return _this;\n        };\n        var getLZWRaster = function(lzwMinCodeSize) {\n            var clearCode = 1 << lzwMinCodeSize;\n            var endCode = (1 << lzwMinCodeSize) + 1;\n            var bitLength = lzwMinCodeSize + 1;\n            // Setup LZWTable\n            var table = lzwTable();\n            for(var i = 0; i < clearCode; i += 1){\n                table.add(String.fromCharCode(i));\n            }\n            table.add(String.fromCharCode(clearCode));\n            table.add(String.fromCharCode(endCode));\n            var byteOut = byteArrayOutputStream();\n            var bitOut = bitOutputStream(byteOut);\n            // clear code\n            bitOut.write(clearCode, bitLength);\n            var dataIndex = 0;\n            var s = String.fromCharCode(_data[dataIndex]);\n            dataIndex += 1;\n            while(dataIndex < _data.length){\n                var c = String.fromCharCode(_data[dataIndex]);\n                dataIndex += 1;\n                if (table.contains(s + c)) {\n                    s = s + c;\n                } else {\n                    bitOut.write(table.indexOf(s), bitLength);\n                    if (table.size() < 0xfff) {\n                        if (table.size() == 1 << bitLength) {\n                            bitLength += 1;\n                        }\n                        table.add(s + c);\n                    }\n                    s = c;\n                }\n            }\n            bitOut.write(table.indexOf(s), bitLength);\n            // end code\n            bitOut.write(endCode, bitLength);\n            bitOut.flush();\n            return byteOut.toByteArray();\n        };\n        var lzwTable = function() {\n            var _map = {};\n            var _size = 0;\n            var _this = {};\n            _this.add = function(key) {\n                if (_this.contains(key)) {\n                    throw \"dup key:\" + key;\n                }\n                _map[key] = _size;\n                _size += 1;\n            };\n            _this.size = function() {\n                return _size;\n            };\n            _this.indexOf = function(key) {\n                return _map[key];\n            };\n            _this.contains = function(key) {\n                return typeof _map[key] != \"undefined\";\n            };\n            return _this;\n        };\n        return _this;\n    };\n    var createDataURL = function(width, height, getPixel) {\n        var gif = gifImage(width, height);\n        for(var y = 0; y < height; y += 1){\n            for(var x = 0; x < width; x += 1){\n                gif.setPixel(x, y, getPixel(x, y));\n            }\n        }\n        var b = byteArrayOutputStream();\n        gif.write(b);\n        var base64 = base64EncodeOutputStream();\n        var bytes = b.toByteArray();\n        for(var i = 0; i < bytes.length; i += 1){\n            base64.writeByte(bytes[i]);\n        }\n        base64.flush();\n        return \"data:image/gif;base64,\" + base64;\n    };\n    //---------------------------------------------------------------------\n    // returns qrcode function.\n    return qrcode;\n}();\n// multibyte support\n!function() {\n    qrcode.stringToBytesFuncs[\"UTF-8\"] = function(s) {\n        // http://stackoverflow.com/questions/18729405/how-to-convert-utf8-string-to-byte-array\n        function toUTF8Array(str) {\n            var utf8 = [];\n            for(var i = 0; i < str.length; i++){\n                var charcode = str.charCodeAt(i);\n                if (charcode < 0x80) utf8.push(charcode);\n                else if (charcode < 0x800) {\n                    utf8.push(0xc0 | charcode >> 6, 0x80 | charcode & 0x3f);\n                } else if (charcode < 0xd800 || charcode >= 0xe000) {\n                    utf8.push(0xe0 | charcode >> 12, 0x80 | charcode >> 6 & 0x3f, 0x80 | charcode & 0x3f);\n                } else {\n                    i++;\n                    // UTF-16 encodes 0x10000-0x10FFFF by\n                    // subtracting 0x10000 and splitting the\n                    // 20 bits of 0x0-0xFFFFF into two halves\n                    charcode = 0x10000 + ((charcode & 0x3ff) << 10 | str.charCodeAt(i) & 0x3ff);\n                    utf8.push(0xf0 | charcode >> 18, 0x80 | charcode >> 12 & 0x3f, 0x80 | charcode >> 6 & 0x3f, 0x80 | charcode & 0x3f);\n                }\n            }\n            return utf8;\n        }\n        return toUTF8Array(s);\n    };\n}();\n(function(factory) {\n    if (true) {\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    } else {}\n})(function() {\n    return qrcode;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode-generator/qrcode.js\n");

/***/ })

};
;