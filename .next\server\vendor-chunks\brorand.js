/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/brorand";
exports.ids = ["vendor-chunks/brorand"];
exports.modules = {

/***/ "(ssr)/./node_modules/brorand/index.js":
/*!***************************************!*\
  !*** ./node_modules/brorand/index.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var r;\nmodule.exports = function rand(len) {\n    if (!r) r = new Rand(null);\n    return r.generate(len);\n};\nfunction Rand(rand) {\n    this.rand = rand;\n}\nmodule.exports.Rand = Rand;\nRand.prototype.generate = function generate(len) {\n    return this._rand(len);\n};\n// Emulate crypto API using randy\nRand.prototype._rand = function _rand(n) {\n    if (this.rand.getBytes) return this.rand.getBytes(n);\n    var res = new Uint8Array(n);\n    for(var i = 0; i < res.length; i++)res[i] = this.rand.getByte();\n    return res;\n};\nif (typeof self === \"object\") {\n    if (self.crypto && self.crypto.getRandomValues) {\n        // Modern browsers\n        Rand.prototype._rand = function _rand(n) {\n            var arr = new Uint8Array(n);\n            self.crypto.getRandomValues(arr);\n            return arr;\n        };\n    } else if (self.msCrypto && self.msCrypto.getRandomValues) {\n        // IE\n        Rand.prototype._rand = function _rand(n) {\n            var arr = new Uint8Array(n);\n            self.msCrypto.getRandomValues(arr);\n            return arr;\n        };\n    // Safari's WebWorkers do not have `crypto`\n    } else if (false) {}\n} else {\n    // Node.js or Web worker with no crypto support\n    try {\n        var crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        if (typeof crypto.randomBytes !== \"function\") throw new Error(\"Not supported\");\n        Rand.prototype._rand = function _rand(n) {\n            return crypto.randomBytes(n);\n        };\n    } catch (e) {}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/brorand/index.js\n");

/***/ })

};
;