'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { web3auth } from '@/lib/web3auth'
import { IProvider } from '@web3auth/base'
import { Connection, PublicKey } from '@solana/web3.js'
import toast from 'react-hot-toast'

interface User {
  name?: string
  email?: string
  profileImage?: string
  publicKey?: string
  balance?: number
}

interface Web3AuthContextType {
  user: User | null
  provider: IProvider | null
  isLoading: boolean
  login: () => Promise<void>
  logout: () => Promise<void>
  getBalance: () => Promise<number>
}

const Web3AuthContext = createContext<Web3AuthContextType | null>(null)

export const useWeb3AuthContext = () => {
  const context = useContext(Web3AuthContext)
  if (!context) {
    throw new Error('useWeb3AuthContext must be used within Web3AuthProvider')
  }
  return context
}

interface Web3AuthProviderProps {
  children: ReactNode
}

export const Web3AuthProvider = ({ children }: Web3AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null)
  const [provider, setProvider] = useState<IProvider | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const connection = new Connection('https://api.devnet.solana.com', 'confirmed')

  useEffect(() => {
    const init = async () => {
      try {
        await web3auth.initModal()
        setProvider(web3auth.provider)

        if (web3auth.connected) {
          await setUserInfo()
        }
      } catch (error) {
        console.error('Web3Auth初始化失败:', error)
        toast.error('Web3Auth初始化失败')
      } finally {
        setIsLoading(false)
      }
    }

    init()
  }, [])

  const setUserInfo = async () => {
    if (!web3auth.provider) return

    try {
      const userInfo = await web3auth.getUserInfo()
      const accounts = await web3auth.provider.request({
        method: "getAccounts",
      }) as string[]

      if (accounts.length > 0) {
        const publicKey = accounts[0]
        const balance = await getBalance()

        setUser({
          name: userInfo.name,
          email: userInfo.email,
          profileImage: userInfo.profileImage,
          publicKey,
          balance,
        })
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      toast.error('获取用户信息失败')
    }
  }

  const login = async () => {
    if (!web3auth) {
      toast.error('Web3Auth未初始化')
      return
    }

    try {
      setIsLoading(true)
      const web3authProvider = await web3auth.connect()
      setProvider(web3authProvider)
      await setUserInfo()
      toast.success('登录成功！')
    } catch (error) {
      console.error('登录失败:', error)
      toast.error('登录失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    if (!web3auth) {
      toast.error('Web3Auth未初始化')
      return
    }

    try {
      setIsLoading(true)
      await web3auth.logout()
      setProvider(null)
      setUser(null)
      toast.success('已退出登录')
    } catch (error) {
      console.error('退出登录失败:', error)
      toast.error('退出登录失败')
    } finally {
      setIsLoading(false)
    }
  }

  const getBalance = async (): Promise<number> => {
    if (!provider || !user?.publicKey) return 0

    try {
      const publicKey = new PublicKey(user.publicKey)
      const balance = await connection.getBalance(publicKey)
      return balance / 1e9 // 转换为SOL
    } catch (error) {
      console.error('获取余额失败:', error)
      return 0
    }
  }

  const value: Web3AuthContextType = {
    user,
    provider,
    isLoading,
    login,
    logout,
    getBalance,
  }

  return (
    <Web3AuthContext.Provider value={value}>
      {children}
    </Web3AuthContext.Provider>
  )
}
