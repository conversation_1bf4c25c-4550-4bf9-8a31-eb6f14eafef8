/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/process";
exports.ids = ["vendor-chunks/process"];
exports.modules = {

/***/ "(ssr)/./node_modules/process/index.js":
/*!***************************************!*\
  !*** ./node_modules/process/index.js ***!
  \***************************************/
/***/ ((module) => {

eval("// for now just expose the builtin process global from node.js\nmodule.exports = global.process;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvcHJvY2Vzcy9pbmRleC5qcz9iNTM4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIGZvciBub3cganVzdCBleHBvc2UgdGhlIGJ1aWx0aW4gcHJvY2VzcyBnbG9iYWwgZnJvbSBub2RlLmpzXG5tb2R1bGUuZXhwb3J0cyA9IGdsb2JhbC5wcm9jZXNzO1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJnbG9iYWwiLCJwcm9jZXNzIl0sIm1hcHBpbmdzIjoiQUFBQSw4REFBOEQ7QUFDOURBLE9BQU9DLE9BQU8sR0FBR0MsT0FBT0MsT0FBTyIsImZpbGUiOiIoc3NyKS8uL25vZGVfbW9kdWxlcy9wcm9jZXNzL2luZGV4LmpzIiwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/process/index.js\n");

/***/ })

};
;