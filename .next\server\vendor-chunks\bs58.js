/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bs58";
exports.ids = ["vendor-chunks/bs58"];
exports.modules = {

/***/ "(ssr)/./node_modules/bs58/index.js":
/*!************************************!*\
  !*** ./node_modules/bs58/index.js ***!
  \************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const basex = __webpack_require__(/*! base-x */ \"(ssr)/./node_modules/base-x/src/index.js\");\nconst ALPHABET = \"123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz\";\nmodule.exports = basex(ALPHABET);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnM1OC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxNQUFNQSxRQUFRQyxtQkFBT0EsQ0FBQztBQUN0QixNQUFNQyxXQUFXO0FBRWpCQyxPQUFPQyxPQUFPLEdBQUdKLE1BQU1FIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL2JzNTgvaW5kZXguanM/NWFmNSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBiYXNleCA9IHJlcXVpcmUoJ2Jhc2UteCcpXG5jb25zdCBBTFBIQUJFVCA9ICcxMjM0NTY3ODlBQkNERUZHSEpLTE1OUFFSU1RVVldYWVphYmNkZWZnaGlqa21ub3BxcnN0dXZ3eHl6J1xuXG5tb2R1bGUuZXhwb3J0cyA9IGJhc2V4KEFMUEhBQkVUKVxuIl0sIm5hbWVzIjpbImJhc2V4IiwicmVxdWlyZSIsIkFMUEhBQkVUIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bs58/index.js\n");

/***/ })

};
;