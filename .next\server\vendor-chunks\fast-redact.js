"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-redact";
exports.ids = ["vendor-chunks/fast-redact"];
exports.modules = {

/***/ "(ssr)/./node_modules/fast-redact/index.js":
/*!*******************************************!*\
  !*** ./node_modules/fast-redact/index.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst validator = __webpack_require__(/*! ./lib/validator */ \"(ssr)/./node_modules/fast-redact/lib/validator.js\");\nconst parse = __webpack_require__(/*! ./lib/parse */ \"(ssr)/./node_modules/fast-redact/lib/parse.js\");\nconst redactor = __webpack_require__(/*! ./lib/redactor */ \"(ssr)/./node_modules/fast-redact/lib/redactor.js\");\nconst restorer = __webpack_require__(/*! ./lib/restorer */ \"(ssr)/./node_modules/fast-redact/lib/restorer.js\");\nconst { groupRedact, nestedRedact } = __webpack_require__(/*! ./lib/modifiers */ \"(ssr)/./node_modules/fast-redact/lib/modifiers.js\");\nconst state = __webpack_require__(/*! ./lib/state */ \"(ssr)/./node_modules/fast-redact/lib/state.js\");\nconst rx = __webpack_require__(/*! ./lib/rx */ \"(ssr)/./node_modules/fast-redact/lib/rx.js\");\nconst validate = validator();\nconst noop = (o)=>o;\nnoop.restore = noop;\nconst DEFAULT_CENSOR = \"[REDACTED]\";\nfastRedact.rx = rx;\nfastRedact.validator = validator;\nmodule.exports = fastRedact;\nfunction fastRedact(opts = {}) {\n    const paths = Array.from(new Set(opts.paths || []));\n    const serialize = \"serialize\" in opts ? opts.serialize === false ? opts.serialize : typeof opts.serialize === \"function\" ? opts.serialize : JSON.stringify : JSON.stringify;\n    const remove = opts.remove;\n    if (remove === true && serialize !== JSON.stringify) {\n        throw Error(\"fast-redact – remove option may only be set when serializer is JSON.stringify\");\n    }\n    const censor = remove === true ? undefined : \"censor\" in opts ? opts.censor : DEFAULT_CENSOR;\n    const isCensorFct = typeof censor === \"function\";\n    const censorFctTakesPath = isCensorFct && censor.length > 1;\n    if (paths.length === 0) return serialize || noop;\n    validate({\n        paths,\n        serialize,\n        censor\n    });\n    const { wildcards, wcLen, secret } = parse({\n        paths,\n        censor\n    });\n    const compileRestore = restorer();\n    const strict = \"strict\" in opts ? opts.strict : true;\n    return redactor({\n        secret,\n        wcLen,\n        serialize,\n        strict,\n        isCensorFct,\n        censorFctTakesPath\n    }, state({\n        secret,\n        censor,\n        compileRestore,\n        serialize,\n        groupRedact,\n        nestedRedact,\n        wildcards,\n        wcLen\n    }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC1yZWRhY3QvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQSxNQUFNQSxZQUFZQyxtQkFBT0EsQ0FBQztBQUMxQixNQUFNQyxRQUFRRCxtQkFBT0EsQ0FBQztBQUN0QixNQUFNRSxXQUFXRixtQkFBT0EsQ0FBQztBQUN6QixNQUFNRyxXQUFXSCxtQkFBT0EsQ0FBQztBQUN6QixNQUFNLEVBQUVJLFdBQVcsRUFBRUMsWUFBWSxFQUFFLEdBQUdMLG1CQUFPQSxDQUFDO0FBQzlDLE1BQU1NLFFBQVFOLG1CQUFPQSxDQUFDO0FBQ3RCLE1BQU1PLEtBQUtQLG1CQUFPQSxDQUFDO0FBQ25CLE1BQU1RLFdBQVdUO0FBQ2pCLE1BQU1VLE9BQU8sQ0FBQ0MsSUFBTUE7QUFDcEJELEtBQUtFLE9BQU8sR0FBR0Y7QUFFZixNQUFNRyxpQkFBaUI7QUFDdkJDLFdBQVdOLEVBQUUsR0FBR0E7QUFDaEJNLFdBQVdkLFNBQVMsR0FBR0E7QUFFdkJlLE9BQU9DLE9BQU8sR0FBR0Y7QUFFakIsU0FBU0EsV0FBWUcsT0FBTyxDQUFDLENBQUM7SUFDNUIsTUFBTUMsUUFBUUMsTUFBTUMsSUFBSSxDQUFDLElBQUlDLElBQUlKLEtBQUtDLEtBQUssSUFBSSxFQUFFO0lBQ2pELE1BQU1JLFlBQVksZUFBZUwsT0FDL0JBLEtBQUtLLFNBQVMsS0FBSyxRQUFRTCxLQUFLSyxTQUFTLEdBQ3BDLE9BQU9MLEtBQUtLLFNBQVMsS0FBSyxhQUFhTCxLQUFLSyxTQUFTLEdBQUdDLEtBQUtDLFNBQVMsR0FDekVELEtBQUtDLFNBQVM7SUFDbEIsTUFBTUMsU0FBU1IsS0FBS1EsTUFBTTtJQUMxQixJQUFJQSxXQUFXLFFBQVFILGNBQWNDLEtBQUtDLFNBQVMsRUFBRTtRQUNuRCxNQUFNRSxNQUFNO0lBQ2Q7SUFDQSxNQUFNQyxTQUFTRixXQUFXLE9BQ3RCRyxZQUNBLFlBQVlYLE9BQU9BLEtBQUtVLE1BQU0sR0FBR2Q7SUFFckMsTUFBTWdCLGNBQWMsT0FBT0YsV0FBVztJQUN0QyxNQUFNRyxxQkFBcUJELGVBQWVGLE9BQU9JLE1BQU0sR0FBRztJQUUxRCxJQUFJYixNQUFNYSxNQUFNLEtBQUssR0FBRyxPQUFPVCxhQUFhWjtJQUU1Q0QsU0FBUztRQUFFUztRQUFPSTtRQUFXSztJQUFPO0lBRXBDLE1BQU0sRUFBRUssU0FBUyxFQUFFQyxLQUFLLEVBQUVDLE1BQU0sRUFBRSxHQUFHaEMsTUFBTTtRQUFFZ0I7UUFBT1M7SUFBTztJQUUzRCxNQUFNUSxpQkFBaUIvQjtJQUN2QixNQUFNZ0MsU0FBUyxZQUFZbkIsT0FBT0EsS0FBS21CLE1BQU0sR0FBRztJQUVoRCxPQUFPakMsU0FBUztRQUFFK0I7UUFBUUQ7UUFBT1g7UUFBV2M7UUFBUVA7UUFBYUM7SUFBbUIsR0FBR3ZCLE1BQU07UUFDM0YyQjtRQUNBUDtRQUNBUTtRQUNBYjtRQUNBakI7UUFDQUM7UUFDQTBCO1FBQ0FDO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXdlYjMtY3JlYXRvci1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9mYXN0LXJlZGFjdC9pbmRleC5qcz8wNDkwIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCB2YWxpZGF0b3IgPSByZXF1aXJlKCcuL2xpYi92YWxpZGF0b3InKVxuY29uc3QgcGFyc2UgPSByZXF1aXJlKCcuL2xpYi9wYXJzZScpXG5jb25zdCByZWRhY3RvciA9IHJlcXVpcmUoJy4vbGliL3JlZGFjdG9yJylcbmNvbnN0IHJlc3RvcmVyID0gcmVxdWlyZSgnLi9saWIvcmVzdG9yZXInKVxuY29uc3QgeyBncm91cFJlZGFjdCwgbmVzdGVkUmVkYWN0IH0gPSByZXF1aXJlKCcuL2xpYi9tb2RpZmllcnMnKVxuY29uc3Qgc3RhdGUgPSByZXF1aXJlKCcuL2xpYi9zdGF0ZScpXG5jb25zdCByeCA9IHJlcXVpcmUoJy4vbGliL3J4JylcbmNvbnN0IHZhbGlkYXRlID0gdmFsaWRhdG9yKClcbmNvbnN0IG5vb3AgPSAobykgPT4gb1xubm9vcC5yZXN0b3JlID0gbm9vcFxuXG5jb25zdCBERUZBVUxUX0NFTlNPUiA9ICdbUkVEQUNURURdJ1xuZmFzdFJlZGFjdC5yeCA9IHJ4XG5mYXN0UmVkYWN0LnZhbGlkYXRvciA9IHZhbGlkYXRvclxuXG5tb2R1bGUuZXhwb3J0cyA9IGZhc3RSZWRhY3RcblxuZnVuY3Rpb24gZmFzdFJlZGFjdCAob3B0cyA9IHt9KSB7XG4gIGNvbnN0IHBhdGhzID0gQXJyYXkuZnJvbShuZXcgU2V0KG9wdHMucGF0aHMgfHwgW10pKVxuICBjb25zdCBzZXJpYWxpemUgPSAnc2VyaWFsaXplJyBpbiBvcHRzID8gKFxuICAgIG9wdHMuc2VyaWFsaXplID09PSBmYWxzZSA/IG9wdHMuc2VyaWFsaXplXG4gICAgICA6ICh0eXBlb2Ygb3B0cy5zZXJpYWxpemUgPT09ICdmdW5jdGlvbicgPyBvcHRzLnNlcmlhbGl6ZSA6IEpTT04uc3RyaW5naWZ5KVxuICApIDogSlNPTi5zdHJpbmdpZnlcbiAgY29uc3QgcmVtb3ZlID0gb3B0cy5yZW1vdmVcbiAgaWYgKHJlbW92ZSA9PT0gdHJ1ZSAmJiBzZXJpYWxpemUgIT09IEpTT04uc3RyaW5naWZ5KSB7XG4gICAgdGhyb3cgRXJyb3IoJ2Zhc3QtcmVkYWN0IOKAkyByZW1vdmUgb3B0aW9uIG1heSBvbmx5IGJlIHNldCB3aGVuIHNlcmlhbGl6ZXIgaXMgSlNPTi5zdHJpbmdpZnknKVxuICB9XG4gIGNvbnN0IGNlbnNvciA9IHJlbW92ZSA9PT0gdHJ1ZVxuICAgID8gdW5kZWZpbmVkXG4gICAgOiAnY2Vuc29yJyBpbiBvcHRzID8gb3B0cy5jZW5zb3IgOiBERUZBVUxUX0NFTlNPUlxuXG4gIGNvbnN0IGlzQ2Vuc29yRmN0ID0gdHlwZW9mIGNlbnNvciA9PT0gJ2Z1bmN0aW9uJ1xuICBjb25zdCBjZW5zb3JGY3RUYWtlc1BhdGggPSBpc0NlbnNvckZjdCAmJiBjZW5zb3IubGVuZ3RoID4gMVxuXG4gIGlmIChwYXRocy5sZW5ndGggPT09IDApIHJldHVybiBzZXJpYWxpemUgfHwgbm9vcFxuXG4gIHZhbGlkYXRlKHsgcGF0aHMsIHNlcmlhbGl6ZSwgY2Vuc29yIH0pXG5cbiAgY29uc3QgeyB3aWxkY2FyZHMsIHdjTGVuLCBzZWNyZXQgfSA9IHBhcnNlKHsgcGF0aHMsIGNlbnNvciB9KVxuXG4gIGNvbnN0IGNvbXBpbGVSZXN0b3JlID0gcmVzdG9yZXIoKVxuICBjb25zdCBzdHJpY3QgPSAnc3RyaWN0JyBpbiBvcHRzID8gb3B0cy5zdHJpY3QgOiB0cnVlXG5cbiAgcmV0dXJuIHJlZGFjdG9yKHsgc2VjcmV0LCB3Y0xlbiwgc2VyaWFsaXplLCBzdHJpY3QsIGlzQ2Vuc29yRmN0LCBjZW5zb3JGY3RUYWtlc1BhdGggfSwgc3RhdGUoe1xuICAgIHNlY3JldCxcbiAgICBjZW5zb3IsXG4gICAgY29tcGlsZVJlc3RvcmUsXG4gICAgc2VyaWFsaXplLFxuICAgIGdyb3VwUmVkYWN0LFxuICAgIG5lc3RlZFJlZGFjdCxcbiAgICB3aWxkY2FyZHMsXG4gICAgd2NMZW5cbiAgfSkpXG59XG4iXSwibmFtZXMiOlsidmFsaWRhdG9yIiwicmVxdWlyZSIsInBhcnNlIiwicmVkYWN0b3IiLCJyZXN0b3JlciIsImdyb3VwUmVkYWN0IiwibmVzdGVkUmVkYWN0Iiwic3RhdGUiLCJyeCIsInZhbGlkYXRlIiwibm9vcCIsIm8iLCJyZXN0b3JlIiwiREVGQVVMVF9DRU5TT1IiLCJmYXN0UmVkYWN0IiwibW9kdWxlIiwiZXhwb3J0cyIsIm9wdHMiLCJwYXRocyIsIkFycmF5IiwiZnJvbSIsIlNldCIsInNlcmlhbGl6ZSIsIkpTT04iLCJzdHJpbmdpZnkiLCJyZW1vdmUiLCJFcnJvciIsImNlbnNvciIsInVuZGVmaW5lZCIsImlzQ2Vuc29yRmN0IiwiY2Vuc29yRmN0VGFrZXNQYXRoIiwibGVuZ3RoIiwid2lsZGNhcmRzIiwid2NMZW4iLCJzZWNyZXQiLCJjb21waWxlUmVzdG9yZSIsInN0cmljdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-redact/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-redact/lib/modifiers.js":
/*!***************************************************!*\
  !*** ./node_modules/fast-redact/lib/modifiers.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\nmodule.exports = {\n    groupRedact,\n    groupRestore,\n    nestedRedact,\n    nestedRestore\n};\nfunction groupRestore({ keys, values, target }) {\n    if (target == null || typeof target === \"string\") return;\n    const length = keys.length;\n    for(var i = 0; i < length; i++){\n        const k = keys[i];\n        target[k] = values[i];\n    }\n}\nfunction groupRedact(o, path, censor, isCensorFct, censorFctTakesPath) {\n    const target = get(o, path);\n    if (target == null || typeof target === \"string\") return {\n        keys: null,\n        values: null,\n        target,\n        flat: true\n    };\n    const keys = Object.keys(target);\n    const keysLength = keys.length;\n    const pathLength = path.length;\n    const pathWithKey = censorFctTakesPath ? [\n        ...path\n    ] : undefined;\n    const values = new Array(keysLength);\n    for(var i = 0; i < keysLength; i++){\n        const key = keys[i];\n        values[i] = target[key];\n        if (censorFctTakesPath) {\n            pathWithKey[pathLength] = key;\n            target[key] = censor(target[key], pathWithKey);\n        } else if (isCensorFct) {\n            target[key] = censor(target[key]);\n        } else {\n            target[key] = censor;\n        }\n    }\n    return {\n        keys,\n        values,\n        target,\n        flat: true\n    };\n}\n/**\n * @param {RestoreInstruction[]} instructions a set of instructions for restoring values to objects\n */ function nestedRestore(instructions) {\n    for(let i = 0; i < instructions.length; i++){\n        const { target, path, value } = instructions[i];\n        let current = target;\n        for(let i = path.length - 1; i > 0; i--){\n            current = current[path[i]];\n        }\n        current[path[0]] = value;\n    }\n}\nfunction nestedRedact(store, o, path, ns, censor, isCensorFct, censorFctTakesPath) {\n    const target = get(o, path);\n    if (target == null) return;\n    const keys = Object.keys(target);\n    const keysLength = keys.length;\n    for(var i = 0; i < keysLength; i++){\n        const key = keys[i];\n        specialSet(store, target, key, path, ns, censor, isCensorFct, censorFctTakesPath);\n    }\n    return store;\n}\nfunction has(obj, prop) {\n    return obj !== undefined && obj !== null ? \"hasOwn\" in Object ? Object.hasOwn(obj, prop) : Object.prototype.hasOwnProperty.call(obj, prop) : false;\n}\nfunction specialSet(store, o, k, path, afterPath, censor, isCensorFct, censorFctTakesPath) {\n    const afterPathLen = afterPath.length;\n    const lastPathIndex = afterPathLen - 1;\n    const originalKey = k;\n    var i = -1;\n    var n;\n    var nv;\n    var ov;\n    var oov = null;\n    var wc = null;\n    var kIsWc;\n    var wcov;\n    var consecutive = false;\n    var level = 0;\n    // need to track depth of the `redactPath` tree\n    var depth = 0;\n    var redactPathCurrent = tree();\n    ov = n = o[k];\n    if (typeof n !== \"object\") return;\n    while(n != null && ++i < afterPathLen){\n        depth += 1;\n        k = afterPath[i];\n        oov = ov;\n        if (k !== \"*\" && !wc && !(typeof n === \"object\" && k in n)) {\n            break;\n        }\n        if (k === \"*\") {\n            if (wc === \"*\") {\n                consecutive = true;\n            }\n            wc = k;\n            if (i !== lastPathIndex) {\n                continue;\n            }\n        }\n        if (wc) {\n            const wcKeys = Object.keys(n);\n            for(var j = 0; j < wcKeys.length; j++){\n                const wck = wcKeys[j];\n                wcov = n[wck];\n                kIsWc = k === \"*\";\n                if (consecutive) {\n                    redactPathCurrent = node(redactPathCurrent, wck, depth);\n                    level = i;\n                    ov = iterateNthLevel(wcov, level - 1, k, path, afterPath, censor, isCensorFct, censorFctTakesPath, originalKey, n, nv, ov, kIsWc, wck, i, lastPathIndex, redactPathCurrent, store, o[originalKey], depth + 1);\n                } else {\n                    if (kIsWc || typeof wcov === \"object\" && wcov !== null && k in wcov) {\n                        if (kIsWc) {\n                            ov = wcov;\n                        } else {\n                            ov = wcov[k];\n                        }\n                        nv = i !== lastPathIndex ? ov : isCensorFct ? censorFctTakesPath ? censor(ov, [\n                            ...path,\n                            originalKey,\n                            ...afterPath\n                        ]) : censor(ov) : censor;\n                        if (kIsWc) {\n                            const rv = restoreInstr(node(redactPathCurrent, wck, depth), ov, o[originalKey]);\n                            store.push(rv);\n                            n[wck] = nv;\n                        } else {\n                            if (wcov[k] === nv) {\n                            // pass\n                            } else if (nv === undefined && censor !== undefined || has(wcov, k) && nv === ov) {\n                                redactPathCurrent = node(redactPathCurrent, wck, depth);\n                            } else {\n                                redactPathCurrent = node(redactPathCurrent, wck, depth);\n                                const rv = restoreInstr(node(redactPathCurrent, k, depth + 1), ov, o[originalKey]);\n                                store.push(rv);\n                                wcov[k] = nv;\n                            }\n                        }\n                    }\n                }\n            }\n            wc = null;\n        } else {\n            ov = n[k];\n            redactPathCurrent = node(redactPathCurrent, k, depth);\n            nv = i !== lastPathIndex ? ov : isCensorFct ? censorFctTakesPath ? censor(ov, [\n                ...path,\n                originalKey,\n                ...afterPath\n            ]) : censor(ov) : censor;\n            if (has(n, k) && nv === ov || nv === undefined && censor !== undefined) {\n            // pass\n            } else {\n                const rv = restoreInstr(redactPathCurrent, ov, o[originalKey]);\n                store.push(rv);\n                n[k] = nv;\n            }\n            n = n[k];\n        }\n        if (typeof n !== \"object\") break;\n        // prevent circular structure, see https://github.com/pinojs/pino/issues/1513\n        if (ov === oov || typeof ov === \"undefined\") {\n        // pass\n        }\n    }\n}\nfunction get(o, p) {\n    var i = -1;\n    var l = p.length;\n    var n = o;\n    while(n != null && ++i < l){\n        n = n[p[i]];\n    }\n    return n;\n}\nfunction iterateNthLevel(wcov, level, k, path, afterPath, censor, isCensorFct, censorFctTakesPath, originalKey, n, nv, ov, kIsWc, wck, i, lastPathIndex, redactPathCurrent, store, parent, depth) {\n    if (level === 0) {\n        if (kIsWc || typeof wcov === \"object\" && wcov !== null && k in wcov) {\n            if (kIsWc) {\n                ov = wcov;\n            } else {\n                ov = wcov[k];\n            }\n            nv = i !== lastPathIndex ? ov : isCensorFct ? censorFctTakesPath ? censor(ov, [\n                ...path,\n                originalKey,\n                ...afterPath\n            ]) : censor(ov) : censor;\n            if (kIsWc) {\n                const rv = restoreInstr(redactPathCurrent, ov, parent);\n                store.push(rv);\n                n[wck] = nv;\n            } else {\n                if (wcov[k] === nv) {\n                // pass\n                } else if (nv === undefined && censor !== undefined || has(wcov, k) && nv === ov) {\n                // pass\n                } else {\n                    const rv = restoreInstr(node(redactPathCurrent, k, depth + 1), ov, parent);\n                    store.push(rv);\n                    wcov[k] = nv;\n                }\n            }\n        }\n    }\n    for(const key in wcov){\n        if (typeof wcov[key] === \"object\") {\n            redactPathCurrent = node(redactPathCurrent, key, depth);\n            iterateNthLevel(wcov[key], level - 1, k, path, afterPath, censor, isCensorFct, censorFctTakesPath, originalKey, n, nv, ov, kIsWc, wck, i, lastPathIndex, redactPathCurrent, store, parent, depth + 1);\n        }\n    }\n}\n/**\n * @typedef {object} TreeNode\n * @prop {TreeNode} [parent] reference to the parent of this node in the tree, or `null` if there is no parent\n * @prop {string} key the key that this node represents (key here being part of the path being redacted\n * @prop {TreeNode[]} children the child nodes of this node\n * @prop {number} depth the depth of this node in the tree\n */ /**\n * instantiate a new, empty tree\n * @returns {TreeNode}\n */ function tree() {\n    return {\n        parent: null,\n        key: null,\n        children: [],\n        depth: 0\n    };\n}\n/**\n * creates a new node in the tree, attaching it as a child of the provided parent node\n * if the specified depth matches the parent depth, adds the new node as a _sibling_ of the parent instead\n  * @param {TreeNode} parent the parent node to add a new node to (if the parent depth matches the provided `depth` value, will instead add as a sibling of this\n  * @param {string} key the key that the new node represents (key here being part of the path being redacted)\n  * @param {number} depth the depth of the new node in the tree - used to determing whether to add the new node as a child or sibling of the provided `parent` node\n  * @returns {TreeNode} a reference to the newly created node in the tree\n */ function node(parent, key, depth) {\n    if (parent.depth === depth) {\n        return node(parent.parent, key, depth);\n    }\n    var child = {\n        parent,\n        key,\n        depth,\n        children: []\n    };\n    parent.children.push(child);\n    return child;\n}\n/**\n * @typedef {object} RestoreInstruction\n * @prop {string[]} path a reverse-order path that can be used to find the correct insertion point to restore a `value` for the given `parent` object\n * @prop {*} value the value to restore\n * @prop {object} target the object to restore the `value` in\n */ /**\n * create a restore instruction for the given redactPath node\n * generates a path in reverse order by walking up the redactPath tree\n * @param {TreeNode} node a tree node that should be at the bottom of the redact path (i.e. have no children) - this will be used to walk up the redact path tree to construct the path needed to restore\n * @param {*} value the value to restore\n * @param {object} target a reference to the parent object to apply the restore instruction to\n * @returns {RestoreInstruction} an instruction used to restore a nested value for a specific object\n */ function restoreInstr(node, value, target) {\n    let current = node;\n    const path = [];\n    do {\n        path.push(current.key);\n        current = current.parent;\n    }while (current.parent != null);\n    return {\n        path,\n        value,\n        target\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-redact/lib/modifiers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-redact/lib/parse.js":
/*!***********************************************!*\
  !*** ./node_modules/fast-redact/lib/parse.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst rx = __webpack_require__(/*! ./rx */ \"(ssr)/./node_modules/fast-redact/lib/rx.js\");\nmodule.exports = parse;\nfunction parse({ paths }) {\n    const wildcards = [];\n    var wcLen = 0;\n    const secret = paths.reduce(function(o, strPath, ix) {\n        var path = strPath.match(rx).map((p)=>p.replace(/'|\"|`/g, \"\"));\n        const leadingBracket = strPath[0] === \"[\";\n        path = path.map((p)=>{\n            if (p[0] === \"[\") return p.substr(1, p.length - 2);\n            else return p;\n        });\n        const star = path.indexOf(\"*\");\n        if (star > -1) {\n            const before = path.slice(0, star);\n            const beforeStr = before.join(\".\");\n            const after = path.slice(star + 1, path.length);\n            const nested = after.length > 0;\n            wcLen++;\n            wildcards.push({\n                before,\n                beforeStr,\n                after,\n                nested\n            });\n        } else {\n            o[strPath] = {\n                path: path,\n                val: undefined,\n                precensored: false,\n                circle: \"\",\n                escPath: JSON.stringify(strPath),\n                leadingBracket: leadingBracket\n            };\n        }\n        return o;\n    }, {});\n    return {\n        wildcards,\n        wcLen,\n        secret\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-redact/lib/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-redact/lib/redactor.js":
/*!**************************************************!*\
  !*** ./node_modules/fast-redact/lib/redactor.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst rx = __webpack_require__(/*! ./rx */ \"(ssr)/./node_modules/fast-redact/lib/rx.js\");\nmodule.exports = redactor;\nfunction redactor({ secret, serialize, wcLen, strict, isCensorFct, censorFctTakesPath }, state) {\n    /* eslint-disable-next-line */ const redact = Function(\"o\", `\n    if (typeof o !== 'object' || o == null) {\n      ${strictImpl(strict, serialize)}\n    }\n    const { censor, secret } = this\n    const originalSecret = {}\n    const secretKeys = Object.keys(secret)\n    for (var i = 0; i < secretKeys.length; i++) {\n      originalSecret[secretKeys[i]] = secret[secretKeys[i]]\n    }\n\n    ${redactTmpl(secret, isCensorFct, censorFctTakesPath)}\n    this.compileRestore()\n    ${dynamicRedactTmpl(wcLen > 0, isCensorFct, censorFctTakesPath)}\n    this.secret = originalSecret\n    ${resultTmpl(serialize)}\n  `).bind(state);\n    redact.state = state;\n    if (serialize === false) {\n        redact.restore = (o)=>state.restore(o);\n    }\n    return redact;\n}\nfunction redactTmpl(secret, isCensorFct, censorFctTakesPath) {\n    return Object.keys(secret).map((path)=>{\n        const { escPath, leadingBracket, path: arrPath } = secret[path];\n        const skip = leadingBracket ? 1 : 0;\n        const delim = leadingBracket ? \"\" : \".\";\n        const hops = [];\n        var match;\n        while((match = rx.exec(path)) !== null){\n            const [, ix] = match;\n            const { index, input } = match;\n            if (index > skip) hops.push(input.substring(0, index - (ix ? 0 : 1)));\n        }\n        var existence = hops.map((p)=>`o${delim}${p}`).join(\" && \");\n        if (existence.length === 0) existence += `o${delim}${path} != null`;\n        else existence += ` && o${delim}${path} != null`;\n        const circularDetection = `\n      switch (true) {\n        ${hops.reverse().map((p)=>`\n          case o${delim}${p} === censor:\n            secret[${escPath}].circle = ${JSON.stringify(p)}\n            break\n        `).join(\"\\n\")}\n      }\n    `;\n        const censorArgs = censorFctTakesPath ? `val, ${JSON.stringify(arrPath)}` : `val`;\n        return `\n      if (${existence}) {\n        const val = o${delim}${path}\n        if (val === censor) {\n          secret[${escPath}].precensored = true\n        } else {\n          secret[${escPath}].val = val\n          o${delim}${path} = ${isCensorFct ? `censor(${censorArgs})` : \"censor\"}\n          ${circularDetection}\n        }\n      }\n    `;\n    }).join(\"\\n\");\n}\nfunction dynamicRedactTmpl(hasWildcards, isCensorFct, censorFctTakesPath) {\n    return hasWildcards === true ? `\n    {\n      const { wildcards, wcLen, groupRedact, nestedRedact } = this\n      for (var i = 0; i < wcLen; i++) {\n        const { before, beforeStr, after, nested } = wildcards[i]\n        if (nested === true) {\n          secret[beforeStr] = secret[beforeStr] || []\n          nestedRedact(secret[beforeStr], o, before, after, censor, ${isCensorFct}, ${censorFctTakesPath})\n        } else secret[beforeStr] = groupRedact(o, before, censor, ${isCensorFct}, ${censorFctTakesPath})\n      }\n    }\n  ` : \"\";\n}\nfunction resultTmpl(serialize) {\n    return serialize === false ? `return o` : `\n    var s = this.serialize(o)\n    this.restore(o)\n    return s\n  `;\n}\nfunction strictImpl(strict, serialize) {\n    return strict === true ? `throw Error('fast-redact: primitives cannot be redacted')` : serialize === false ? `return o` : `return this.serialize(o)`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-redact/lib/redactor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-redact/lib/restorer.js":
/*!**************************************************!*\
  !*** ./node_modules/fast-redact/lib/restorer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { groupRestore, nestedRestore } = __webpack_require__(/*! ./modifiers */ \"(ssr)/./node_modules/fast-redact/lib/modifiers.js\");\nmodule.exports = restorer;\nfunction restorer() {\n    return function compileRestore() {\n        if (this.restore) {\n            this.restore.state.secret = this.secret;\n            return;\n        }\n        const { secret, wcLen } = this;\n        const paths = Object.keys(secret);\n        const resetters = resetTmpl(secret, paths);\n        const hasWildcards = wcLen > 0;\n        const state = hasWildcards ? {\n            secret,\n            groupRestore,\n            nestedRestore\n        } : {\n            secret\n        };\n        /* eslint-disable-next-line */ this.restore = Function(\"o\", restoreTmpl(resetters, paths, hasWildcards)).bind(state);\n        this.restore.state = state;\n    };\n}\n/**\n * Mutates the original object to be censored by restoring its original values\n * prior to censoring.\n *\n * @param {object} secret Compiled object describing which target fields should\n * be censored and the field states.\n * @param {string[]} paths The list of paths to censor as provided at\n * initialization time.\n *\n * @returns {string} String of JavaScript to be used by `Function()`. The\n * string compiles to the function that does the work in the description.\n */ function resetTmpl(secret, paths) {\n    return paths.map((path)=>{\n        const { circle, escPath, leadingBracket } = secret[path];\n        const delim = leadingBracket ? \"\" : \".\";\n        const reset = circle ? `o.${circle} = secret[${escPath}].val` : `o${delim}${path} = secret[${escPath}].val`;\n        const clear = `secret[${escPath}].val = undefined`;\n        return `\n      if (secret[${escPath}].val !== undefined) {\n        try { ${reset} } catch (e) {}\n        ${clear}\n      }\n    `;\n    }).join(\"\");\n}\n/**\n * Creates the body of the restore function\n *\n * Restoration of the redacted object happens\n * backwards, in reverse order of redactions,\n * so that repeated redactions on the same object\n * property can be eventually rolled back to the\n * original value.\n *\n * This way dynamic redactions are restored first,\n * starting from the last one working backwards and\n * followed by the static ones.\n *\n * @returns {string} the body of the restore function\n */ function restoreTmpl(resetters, paths, hasWildcards) {\n    const dynamicReset = hasWildcards === true ? `\n    const keys = Object.keys(secret)\n    const len = keys.length\n    for (var i = len - 1; i >= ${paths.length}; i--) {\n      const k = keys[i]\n      const o = secret[k]\n      if (o) {\n        if (o.flat === true) this.groupRestore(o)\n        else this.nestedRestore(o)\n        secret[k] = null\n      }\n    }\n  ` : \"\";\n    return `\n    const secret = this.secret\n    ${dynamicReset}\n    ${resetters}\n    return o\n  `;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC1yZWRhY3QvbGliL3Jlc3RvcmVyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsTUFBTSxFQUFFQSxZQUFZLEVBQUVDLGFBQWEsRUFBRSxHQUFHQyxtQkFBT0EsQ0FBQztBQUVoREMsT0FBT0MsT0FBTyxHQUFHQztBQUVqQixTQUFTQTtJQUNQLE9BQU8sU0FBU0M7UUFDZCxJQUFJLElBQUksQ0FBQ0MsT0FBTyxFQUFFO1lBQ2hCLElBQUksQ0FBQ0EsT0FBTyxDQUFDQyxLQUFLLENBQUNDLE1BQU0sR0FBRyxJQUFJLENBQUNBLE1BQU07WUFDdkM7UUFDRjtRQUNBLE1BQU0sRUFBRUEsTUFBTSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxJQUFJO1FBQzlCLE1BQU1DLFFBQVFDLE9BQU9DLElBQUksQ0FBQ0o7UUFDMUIsTUFBTUssWUFBWUMsVUFBVU4sUUFBUUU7UUFDcEMsTUFBTUssZUFBZU4sUUFBUTtRQUM3QixNQUFNRixRQUFRUSxlQUFlO1lBQUVQO1lBQVFUO1lBQWNDO1FBQWMsSUFBSTtZQUFFUTtRQUFPO1FBQ2hGLDRCQUE0QixHQUM1QixJQUFJLENBQUNGLE9BQU8sR0FBR1UsU0FDYixLQUNBQyxZQUFZSixXQUFXSCxPQUFPSyxlQUM5QkcsSUFBSSxDQUFDWDtRQUNQLElBQUksQ0FBQ0QsT0FBTyxDQUFDQyxLQUFLLEdBQUdBO0lBQ3ZCO0FBQ0Y7QUFFQTs7Ozs7Ozs7Ozs7Q0FXQyxHQUNELFNBQVNPLFVBQVdOLE1BQU0sRUFBRUUsS0FBSztJQUMvQixPQUFPQSxNQUFNUyxHQUFHLENBQUMsQ0FBQ0M7UUFDaEIsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLE9BQU8sRUFBRUMsY0FBYyxFQUFFLEdBQUdmLE1BQU0sQ0FBQ1ksS0FBSztRQUN4RCxNQUFNSSxRQUFRRCxpQkFBaUIsS0FBSztRQUNwQyxNQUFNRSxRQUFRSixTQUNWLENBQUMsRUFBRSxFQUFFQSxPQUFPLFVBQVUsRUFBRUMsUUFBUSxLQUFLLENBQUMsR0FDdEMsQ0FBQyxDQUFDLEVBQUVFLE1BQU0sRUFBRUosS0FBSyxVQUFVLEVBQUVFLFFBQVEsS0FBSyxDQUFDO1FBQy9DLE1BQU1JLFFBQVEsQ0FBQyxPQUFPLEVBQUVKLFFBQVEsaUJBQWlCLENBQUM7UUFDbEQsT0FBTyxDQUFDO2lCQUNLLEVBQUVBLFFBQVE7Y0FDYixFQUFFRyxNQUFNO1FBQ2QsRUFBRUMsTUFBTTs7SUFFWixDQUFDO0lBQ0gsR0FBR0MsSUFBSSxDQUFDO0FBQ1Y7QUFFQTs7Ozs7Ozs7Ozs7Ozs7Q0FjQyxHQUNELFNBQVNWLFlBQWFKLFNBQVMsRUFBRUgsS0FBSyxFQUFFSyxZQUFZO0lBQ2xELE1BQU1hLGVBQWViLGlCQUFpQixPQUFPLENBQUM7OzsrQkFHakIsRUFBRUwsTUFBTW1CLE1BQU0sQ0FBQzs7Ozs7Ozs7O0VBUzVDLENBQUMsR0FBRztJQUVKLE9BQU8sQ0FBQzs7SUFFTixFQUFFRCxhQUFhO0lBQ2YsRUFBRWYsVUFBVTs7RUFFZCxDQUFDO0FBQ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvZmFzdC1yZWRhY3QvbGliL3Jlc3RvcmVyLmpzP2QwMGYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IHsgZ3JvdXBSZXN0b3JlLCBuZXN0ZWRSZXN0b3JlIH0gPSByZXF1aXJlKCcuL21vZGlmaWVycycpXG5cbm1vZHVsZS5leHBvcnRzID0gcmVzdG9yZXJcblxuZnVuY3Rpb24gcmVzdG9yZXIgKCkge1xuICByZXR1cm4gZnVuY3Rpb24gY29tcGlsZVJlc3RvcmUgKCkge1xuICAgIGlmICh0aGlzLnJlc3RvcmUpIHtcbiAgICAgIHRoaXMucmVzdG9yZS5zdGF0ZS5zZWNyZXQgPSB0aGlzLnNlY3JldFxuICAgICAgcmV0dXJuXG4gICAgfVxuICAgIGNvbnN0IHsgc2VjcmV0LCB3Y0xlbiB9ID0gdGhpc1xuICAgIGNvbnN0IHBhdGhzID0gT2JqZWN0LmtleXMoc2VjcmV0KVxuICAgIGNvbnN0IHJlc2V0dGVycyA9IHJlc2V0VG1wbChzZWNyZXQsIHBhdGhzKVxuICAgIGNvbnN0IGhhc1dpbGRjYXJkcyA9IHdjTGVuID4gMFxuICAgIGNvbnN0IHN0YXRlID0gaGFzV2lsZGNhcmRzID8geyBzZWNyZXQsIGdyb3VwUmVzdG9yZSwgbmVzdGVkUmVzdG9yZSB9IDogeyBzZWNyZXQgfVxuICAgIC8qIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSAqL1xuICAgIHRoaXMucmVzdG9yZSA9IEZ1bmN0aW9uKFxuICAgICAgJ28nLFxuICAgICAgcmVzdG9yZVRtcGwocmVzZXR0ZXJzLCBwYXRocywgaGFzV2lsZGNhcmRzKVxuICAgICkuYmluZChzdGF0ZSlcbiAgICB0aGlzLnJlc3RvcmUuc3RhdGUgPSBzdGF0ZVxuICB9XG59XG5cbi8qKlxuICogTXV0YXRlcyB0aGUgb3JpZ2luYWwgb2JqZWN0IHRvIGJlIGNlbnNvcmVkIGJ5IHJlc3RvcmluZyBpdHMgb3JpZ2luYWwgdmFsdWVzXG4gKiBwcmlvciB0byBjZW5zb3JpbmcuXG4gKlxuICogQHBhcmFtIHtvYmplY3R9IHNlY3JldCBDb21waWxlZCBvYmplY3QgZGVzY3JpYmluZyB3aGljaCB0YXJnZXQgZmllbGRzIHNob3VsZFxuICogYmUgY2Vuc29yZWQgYW5kIHRoZSBmaWVsZCBzdGF0ZXMuXG4gKiBAcGFyYW0ge3N0cmluZ1tdfSBwYXRocyBUaGUgbGlzdCBvZiBwYXRocyB0byBjZW5zb3IgYXMgcHJvdmlkZWQgYXRcbiAqIGluaXRpYWxpemF0aW9uIHRpbWUuXG4gKlxuICogQHJldHVybnMge3N0cmluZ30gU3RyaW5nIG9mIEphdmFTY3JpcHQgdG8gYmUgdXNlZCBieSBgRnVuY3Rpb24oKWAuIFRoZVxuICogc3RyaW5nIGNvbXBpbGVzIHRvIHRoZSBmdW5jdGlvbiB0aGF0IGRvZXMgdGhlIHdvcmsgaW4gdGhlIGRlc2NyaXB0aW9uLlxuICovXG5mdW5jdGlvbiByZXNldFRtcGwgKHNlY3JldCwgcGF0aHMpIHtcbiAgcmV0dXJuIHBhdGhzLm1hcCgocGF0aCkgPT4ge1xuICAgIGNvbnN0IHsgY2lyY2xlLCBlc2NQYXRoLCBsZWFkaW5nQnJhY2tldCB9ID0gc2VjcmV0W3BhdGhdXG4gICAgY29uc3QgZGVsaW0gPSBsZWFkaW5nQnJhY2tldCA/ICcnIDogJy4nXG4gICAgY29uc3QgcmVzZXQgPSBjaXJjbGVcbiAgICAgID8gYG8uJHtjaXJjbGV9ID0gc2VjcmV0WyR7ZXNjUGF0aH1dLnZhbGBcbiAgICAgIDogYG8ke2RlbGltfSR7cGF0aH0gPSBzZWNyZXRbJHtlc2NQYXRofV0udmFsYFxuICAgIGNvbnN0IGNsZWFyID0gYHNlY3JldFske2VzY1BhdGh9XS52YWwgPSB1bmRlZmluZWRgXG4gICAgcmV0dXJuIGBcbiAgICAgIGlmIChzZWNyZXRbJHtlc2NQYXRofV0udmFsICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgdHJ5IHsgJHtyZXNldH0gfSBjYXRjaCAoZSkge31cbiAgICAgICAgJHtjbGVhcn1cbiAgICAgIH1cbiAgICBgXG4gIH0pLmpvaW4oJycpXG59XG5cbi8qKlxuICogQ3JlYXRlcyB0aGUgYm9keSBvZiB0aGUgcmVzdG9yZSBmdW5jdGlvblxuICpcbiAqIFJlc3RvcmF0aW9uIG9mIHRoZSByZWRhY3RlZCBvYmplY3QgaGFwcGVuc1xuICogYmFja3dhcmRzLCBpbiByZXZlcnNlIG9yZGVyIG9mIHJlZGFjdGlvbnMsXG4gKiBzbyB0aGF0IHJlcGVhdGVkIHJlZGFjdGlvbnMgb24gdGhlIHNhbWUgb2JqZWN0XG4gKiBwcm9wZXJ0eSBjYW4gYmUgZXZlbnR1YWxseSByb2xsZWQgYmFjayB0byB0aGVcbiAqIG9yaWdpbmFsIHZhbHVlLlxuICpcbiAqIFRoaXMgd2F5IGR5bmFtaWMgcmVkYWN0aW9ucyBhcmUgcmVzdG9yZWQgZmlyc3QsXG4gKiBzdGFydGluZyBmcm9tIHRoZSBsYXN0IG9uZSB3b3JraW5nIGJhY2t3YXJkcyBhbmRcbiAqIGZvbGxvd2VkIGJ5IHRoZSBzdGF0aWMgb25lcy5cbiAqXG4gKiBAcmV0dXJucyB7c3RyaW5nfSB0aGUgYm9keSBvZiB0aGUgcmVzdG9yZSBmdW5jdGlvblxuICovXG5mdW5jdGlvbiByZXN0b3JlVG1wbCAocmVzZXR0ZXJzLCBwYXRocywgaGFzV2lsZGNhcmRzKSB7XG4gIGNvbnN0IGR5bmFtaWNSZXNldCA9IGhhc1dpbGRjYXJkcyA9PT0gdHJ1ZSA/IGBcbiAgICBjb25zdCBrZXlzID0gT2JqZWN0LmtleXMoc2VjcmV0KVxuICAgIGNvbnN0IGxlbiA9IGtleXMubGVuZ3RoXG4gICAgZm9yICh2YXIgaSA9IGxlbiAtIDE7IGkgPj0gJHtwYXRocy5sZW5ndGh9OyBpLS0pIHtcbiAgICAgIGNvbnN0IGsgPSBrZXlzW2ldXG4gICAgICBjb25zdCBvID0gc2VjcmV0W2tdXG4gICAgICBpZiAobykge1xuICAgICAgICBpZiAoby5mbGF0ID09PSB0cnVlKSB0aGlzLmdyb3VwUmVzdG9yZShvKVxuICAgICAgICBlbHNlIHRoaXMubmVzdGVkUmVzdG9yZShvKVxuICAgICAgICBzZWNyZXRba10gPSBudWxsXG4gICAgICB9XG4gICAgfVxuICBgIDogJydcblxuICByZXR1cm4gYFxuICAgIGNvbnN0IHNlY3JldCA9IHRoaXMuc2VjcmV0XG4gICAgJHtkeW5hbWljUmVzZXR9XG4gICAgJHtyZXNldHRlcnN9XG4gICAgcmV0dXJuIG9cbiAgYFxufVxuIl0sIm5hbWVzIjpbImdyb3VwUmVzdG9yZSIsIm5lc3RlZFJlc3RvcmUiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsInJlc3RvcmVyIiwiY29tcGlsZVJlc3RvcmUiLCJyZXN0b3JlIiwic3RhdGUiLCJzZWNyZXQiLCJ3Y0xlbiIsInBhdGhzIiwiT2JqZWN0Iiwia2V5cyIsInJlc2V0dGVycyIsInJlc2V0VG1wbCIsImhhc1dpbGRjYXJkcyIsIkZ1bmN0aW9uIiwicmVzdG9yZVRtcGwiLCJiaW5kIiwibWFwIiwicGF0aCIsImNpcmNsZSIsImVzY1BhdGgiLCJsZWFkaW5nQnJhY2tldCIsImRlbGltIiwicmVzZXQiLCJjbGVhciIsImpvaW4iLCJkeW5hbWljUmVzZXQiLCJsZW5ndGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-redact/lib/restorer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-redact/lib/rx.js":
/*!********************************************!*\
  !*** ./node_modules/fast-redact/lib/rx.js ***!
  \********************************************/
/***/ ((module) => {

eval("\nmodule.exports = /[^.[\\]]+|\\[((?:.)*?)\\]/g /*\nRegular expression explanation:\n\nAlt 1: /[^.[\\]]+/ - Match one or more characters that are *not* a dot (.)\n                    opening square bracket ([) or closing square bracket (])\n\nAlt 2: /\\[((?:.)*?)\\]/ - If the char IS dot or square bracket, then create a capture\n                         group (which will be capture group $1) that matches anything\n                         within square brackets. Expansion is lazy so it will\n                         stop matching as soon as the first closing bracket is met `]`\n                         (rather than continuing to match until the final closing bracket).\n*/ ;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC1yZWRhY3QvbGliL3J4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUFBLE9BQU9DLE9BQU8sR0FBRywwQkFFakI7Ozs7Ozs7Ozs7O0FBV0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvZmFzdC1yZWRhY3QvbGliL3J4LmpzPzBmNzAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gL1teLltcXF1dK3xcXFsoKD86LikqPylcXF0vZ1xuXG4vKlxuUmVndWxhciBleHByZXNzaW9uIGV4cGxhbmF0aW9uOlxuXG5BbHQgMTogL1teLltcXF1dKy8gLSBNYXRjaCBvbmUgb3IgbW9yZSBjaGFyYWN0ZXJzIHRoYXQgYXJlICpub3QqIGEgZG90ICguKVxuICAgICAgICAgICAgICAgICAgICBvcGVuaW5nIHNxdWFyZSBicmFja2V0IChbKSBvciBjbG9zaW5nIHNxdWFyZSBicmFja2V0IChdKVxuXG5BbHQgMjogL1xcWygoPzouKSo/KVxcXS8gLSBJZiB0aGUgY2hhciBJUyBkb3Qgb3Igc3F1YXJlIGJyYWNrZXQsIHRoZW4gY3JlYXRlIGEgY2FwdHVyZVxuICAgICAgICAgICAgICAgICAgICAgICAgIGdyb3VwICh3aGljaCB3aWxsIGJlIGNhcHR1cmUgZ3JvdXAgJDEpIHRoYXQgbWF0Y2hlcyBhbnl0aGluZ1xuICAgICAgICAgICAgICAgICAgICAgICAgIHdpdGhpbiBzcXVhcmUgYnJhY2tldHMuIEV4cGFuc2lvbiBpcyBsYXp5IHNvIGl0IHdpbGxcbiAgICAgICAgICAgICAgICAgICAgICAgICBzdG9wIG1hdGNoaW5nIGFzIHNvb24gYXMgdGhlIGZpcnN0IGNsb3NpbmcgYnJhY2tldCBpcyBtZXQgYF1gXG4gICAgICAgICAgICAgICAgICAgICAgICAgKHJhdGhlciB0aGFuIGNvbnRpbnVpbmcgdG8gbWF0Y2ggdW50aWwgdGhlIGZpbmFsIGNsb3NpbmcgYnJhY2tldCkuXG4qL1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-redact/lib/rx.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-redact/lib/state.js":
/*!***********************************************!*\
  !*** ./node_modules/fast-redact/lib/state.js ***!
  \***********************************************/
/***/ ((module) => {

eval("\nmodule.exports = state;\nfunction state(o) {\n    const { secret, censor, compileRestore, serialize, groupRedact, nestedRedact, wildcards, wcLen } = o;\n    const builder = [\n        {\n            secret,\n            censor,\n            compileRestore\n        }\n    ];\n    if (serialize !== false) builder.push({\n        serialize\n    });\n    if (wcLen > 0) builder.push({\n        groupRedact,\n        nestedRedact,\n        wildcards,\n        wcLen\n    });\n    return Object.assign(...builder);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC1yZWRhY3QvbGliL3N0YXRlLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUFBLE9BQU9DLE9BQU8sR0FBR0M7QUFFakIsU0FBU0EsTUFBT0MsQ0FBQztJQUNmLE1BQU0sRUFDSkMsTUFBTSxFQUNOQyxNQUFNLEVBQ05DLGNBQWMsRUFDZEMsU0FBUyxFQUNUQyxXQUFXLEVBQ1hDLFlBQVksRUFDWkMsU0FBUyxFQUNUQyxLQUFLLEVBQ04sR0FBR1I7SUFDSixNQUFNUyxVQUFVO1FBQUM7WUFBRVI7WUFBUUM7WUFBUUM7UUFBZTtLQUFFO0lBQ3BELElBQUlDLGNBQWMsT0FBT0ssUUFBUUMsSUFBSSxDQUFDO1FBQUVOO0lBQVU7SUFDbEQsSUFBSUksUUFBUSxHQUFHQyxRQUFRQyxJQUFJLENBQUM7UUFBRUw7UUFBYUM7UUFBY0M7UUFBV0M7SUFBTTtJQUMxRSxPQUFPRyxPQUFPQyxNQUFNLElBQUlIO0FBQzFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL2Zhc3QtcmVkYWN0L2xpYi9zdGF0ZS5qcz85NjhhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IHN0YXRlXG5cbmZ1bmN0aW9uIHN0YXRlIChvKSB7XG4gIGNvbnN0IHtcbiAgICBzZWNyZXQsXG4gICAgY2Vuc29yLFxuICAgIGNvbXBpbGVSZXN0b3JlLFxuICAgIHNlcmlhbGl6ZSxcbiAgICBncm91cFJlZGFjdCxcbiAgICBuZXN0ZWRSZWRhY3QsXG4gICAgd2lsZGNhcmRzLFxuICAgIHdjTGVuXG4gIH0gPSBvXG4gIGNvbnN0IGJ1aWxkZXIgPSBbeyBzZWNyZXQsIGNlbnNvciwgY29tcGlsZVJlc3RvcmUgfV1cbiAgaWYgKHNlcmlhbGl6ZSAhPT0gZmFsc2UpIGJ1aWxkZXIucHVzaCh7IHNlcmlhbGl6ZSB9KVxuICBpZiAod2NMZW4gPiAwKSBidWlsZGVyLnB1c2goeyBncm91cFJlZGFjdCwgbmVzdGVkUmVkYWN0LCB3aWxkY2FyZHMsIHdjTGVuIH0pXG4gIHJldHVybiBPYmplY3QuYXNzaWduKC4uLmJ1aWxkZXIpXG59XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsInN0YXRlIiwibyIsInNlY3JldCIsImNlbnNvciIsImNvbXBpbGVSZXN0b3JlIiwic2VyaWFsaXplIiwiZ3JvdXBSZWRhY3QiLCJuZXN0ZWRSZWRhY3QiLCJ3aWxkY2FyZHMiLCJ3Y0xlbiIsImJ1aWxkZXIiLCJwdXNoIiwiT2JqZWN0IiwiYXNzaWduIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-redact/lib/state.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-redact/lib/validator.js":
/*!***************************************************!*\
  !*** ./node_modules/fast-redact/lib/validator.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\nmodule.exports = validator;\nfunction validator(opts = {}) {\n    const { ERR_PATHS_MUST_BE_STRINGS = ()=>\"fast-redact - Paths must be (non-empty) strings\", ERR_INVALID_PATH = (s)=>`fast-redact – Invalid path (${s})` } = opts;\n    return function validate({ paths }) {\n        paths.forEach((s)=>{\n            if (typeof s !== \"string\") {\n                throw Error(ERR_PATHS_MUST_BE_STRINGS());\n            }\n            try {\n                if (/〇/.test(s)) throw Error();\n                const expr = (s[0] === \"[\" ? \"\" : \".\") + s.replace(/^\\*/, \"〇\").replace(/\\.\\*/g, \".〇\").replace(/\\[\\*\\]/g, \"[〇]\");\n                if (/\\n|\\r|;/.test(expr)) throw Error();\n                if (/\\/\\*/.test(expr)) throw Error();\n                /* eslint-disable-next-line */ Function(`\n            'use strict'\n            const o = new Proxy({}, { get: () => o, set: () => { throw Error() } });\n            const 〇 = null;\n            o${expr}\n            if ([o${expr}].length !== 1) throw Error()`)();\n            } catch (e) {\n                throw Error(ERR_INVALID_PATH(s));\n            }\n        });\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-redact/lib/validator.js\n");

/***/ })

};
;