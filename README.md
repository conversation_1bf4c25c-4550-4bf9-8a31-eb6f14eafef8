# 🎨 AI + Web3 创作平台

基于AI和Web3技术的创作者平台，支持文字、图片、音乐创作并铸造为NFT。

## ✨ 核心功能

- 🔐 **Web3Auth社交登录** - 支持Google/Twitter/Discord/Email登录，自动创建Solana钱包
- 🤖 **AI创作引擎** - 文字生成、图片生成、音乐创作
- 💎 **NFT铸造** - 一键将创作转化为NFT，部署在Solana链上
- 💰 **收益系统** - 自动版税分配，实时收益统计
- 🌐 **IPFS存储** - 永久存储创作内容和元数据
- 👥 **社交功能** - 关注、点赞、评论、分享

## 🛠 技术栈

### 前端
- **框架**: Next.js 14 (React)
- **样式**: TailwindCSS + Framer Motion
- **状态管理**: Zustand
- **UI组件**: Radix UI

### Web3
- **钱包**: Web3Auth SDK
- **区块链**: Solana (Devnet)
- **NFT**: Metaplex SDK
- **存储**: IPFS (Pinata)

### AI服务
- **文字**: OpenAI GPT-4
- **图片**: DALL-E 3
- **音乐**: MusicGen

## 🚀 快速开始

### 1. 环境要求
- Node.js 18+
- npm/yarn/pnpm

### 2. 安装依赖
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 3. 环境配置
复制 `.env.example` 为 `.env.local` 并填入配置：

```bash
cp .env.example .env.local
```

需要配置的环境变量：
- `NEXT_PUBLIC_WEB3AUTH_CLIENT_ID`: Web3Auth客户端ID
- `OPENAI_API_KEY`: OpenAI API密钥
- `PINATA_JWT`: Pinata JWT令牌

### 4. 启动开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📋 获取API密钥

### Web3Auth配置
1. 访问 [Web3Auth Dashboard](https://dashboard.web3auth.io/)
2. 创建新项目
3. 选择 "Plug and Play" 类型
4. 配置域名为 `http://localhost:3000`
5. 复制 Client ID

### OpenAI API
1. 访问 [OpenAI Platform](https://platform.openai.com/)
2. 创建API密钥
3. 确保账户有足够余额

### Pinata IPFS
1. 访问 [Pinata](https://pinata.cloud/)
2. 创建账户
3. 生成JWT令牌

## 🏗 项目结构

```
src/
├── app/                 # Next.js App Router
├── components/          # React组件
│   ├── ui/             # 基础UI组件
│   └── features/       # 功能组件
├── hooks/              # 自定义Hooks
├── lib/                # 工具库
├── providers/          # Context Providers
├── store/              # 状态管理
├── types/              # TypeScript类型
└── utils/              # 工具函数
```

## 🎯 开发路线图

### Phase 1: 基础功能 (Day 1-2)
- [x] 项目初始化
- [x] Web3Auth集成
- [x] 基础UI框架
- [ ] Solana钱包连接

### Phase 2: AI创作 (Day 3-4)
- [ ] OpenAI文字生成
- [ ] DALL-E图片生成
- [ ] 音乐生成集成
- [ ] 创作参数配置

### Phase 3: NFT功能 (Day 5-6)
- [ ] Metaplex集成
- [ ] NFT铸造合约
- [ ] IPFS存储
- [ ] 元数据管理

### Phase 4: 完善功能 (Day 7-8)
- [ ] 收益系统
- [ ] 社交功能
- [ ] 测试部署
- [ ] 演示准备

## 🧪 测试

```bash
# 类型检查
npm run type-check

# 代码检查
npm run lint

# 构建测试
npm run build
```

## 📦 部署

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

## 🔗 相关链接

- [Web3Auth文档](https://web3auth.io/docs/)
- [Solana文档](https://docs.solana.com/)
- [Metaplex文档](https://docs.metaplex.com/)
- [Next.js文档](https://nextjs.org/docs)
