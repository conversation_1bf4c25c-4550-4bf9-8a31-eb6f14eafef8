"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/string_decoder";
exports.ids = ["vendor-chunks/string_decoder"];
exports.modules = {

/***/ "(ssr)/./node_modules/string_decoder/lib/string_decoder.js":
/*!***********************************************************!*\
  !*** ./node_modules/string_decoder/lib/string_decoder.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n/*<replacement>*/ var Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer);\n/*</replacement>*/ var isEncoding = Buffer.isEncoding || function(encoding) {\n    encoding = \"\" + encoding;\n    switch(encoding && encoding.toLowerCase()){\n        case \"hex\":\n        case \"utf8\":\n        case \"utf-8\":\n        case \"ascii\":\n        case \"binary\":\n        case \"base64\":\n        case \"ucs2\":\n        case \"ucs-2\":\n        case \"utf16le\":\n        case \"utf-16le\":\n        case \"raw\":\n            return true;\n        default:\n            return false;\n    }\n};\nfunction _normalizeEncoding(enc) {\n    if (!enc) return \"utf8\";\n    var retried;\n    while(true){\n        switch(enc){\n            case \"utf8\":\n            case \"utf-8\":\n                return \"utf8\";\n            case \"ucs2\":\n            case \"ucs-2\":\n            case \"utf16le\":\n            case \"utf-16le\":\n                return \"utf16le\";\n            case \"latin1\":\n            case \"binary\":\n                return \"latin1\";\n            case \"base64\":\n            case \"ascii\":\n            case \"hex\":\n                return enc;\n            default:\n                if (retried) return; // undefined\n                enc = (\"\" + enc).toLowerCase();\n                retried = true;\n        }\n    }\n}\n;\n// Do not cache `Buffer.isEncoding` when checking encoding names as some\n// modules monkey-patch it to support additional encodings\nfunction normalizeEncoding(enc) {\n    var nenc = _normalizeEncoding(enc);\n    if (typeof nenc !== \"string\" && (Buffer.isEncoding === isEncoding || !isEncoding(enc))) throw new Error(\"Unknown encoding: \" + enc);\n    return nenc || enc;\n}\n// StringDecoder provides an interface for efficiently splitting a series of\n// buffers into a series of JS strings without breaking apart multi-byte\n// characters.\nexports.StringDecoder = StringDecoder;\nfunction StringDecoder(encoding) {\n    this.encoding = normalizeEncoding(encoding);\n    var nb;\n    switch(this.encoding){\n        case \"utf16le\":\n            this.text = utf16Text;\n            this.end = utf16End;\n            nb = 4;\n            break;\n        case \"utf8\":\n            this.fillLast = utf8FillLast;\n            nb = 4;\n            break;\n        case \"base64\":\n            this.text = base64Text;\n            this.end = base64End;\n            nb = 3;\n            break;\n        default:\n            this.write = simpleWrite;\n            this.end = simpleEnd;\n            return;\n    }\n    this.lastNeed = 0;\n    this.lastTotal = 0;\n    this.lastChar = Buffer.allocUnsafe(nb);\n}\nStringDecoder.prototype.write = function(buf) {\n    if (buf.length === 0) return \"\";\n    var r;\n    var i;\n    if (this.lastNeed) {\n        r = this.fillLast(buf);\n        if (r === undefined) return \"\";\n        i = this.lastNeed;\n        this.lastNeed = 0;\n    } else {\n        i = 0;\n    }\n    if (i < buf.length) return r ? r + this.text(buf, i) : this.text(buf, i);\n    return r || \"\";\n};\nStringDecoder.prototype.end = utf8End;\n// Returns only complete characters in a Buffer\nStringDecoder.prototype.text = utf8Text;\n// Attempts to complete a partial non-UTF-8 character using bytes from a Buffer\nStringDecoder.prototype.fillLast = function(buf) {\n    if (this.lastNeed <= buf.length) {\n        buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, this.lastNeed);\n        return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n    }\n    buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, buf.length);\n    this.lastNeed -= buf.length;\n};\n// Checks the type of a UTF-8 byte, whether it's ASCII, a leading byte, or a\n// continuation byte. If an invalid byte is detected, -2 is returned.\nfunction utf8CheckByte(byte) {\n    if (byte <= 0x7F) return 0;\n    else if (byte >> 5 === 0x06) return 2;\n    else if (byte >> 4 === 0x0E) return 3;\n    else if (byte >> 3 === 0x1E) return 4;\n    return byte >> 6 === 0x02 ? -1 : -2;\n}\n// Checks at most 3 bytes at the end of a Buffer in order to detect an\n// incomplete multi-byte UTF-8 character. The total number of bytes (2, 3, or 4)\n// needed to complete the UTF-8 character (if applicable) are returned.\nfunction utf8CheckIncomplete(self, buf, i) {\n    var j = buf.length - 1;\n    if (j < i) return 0;\n    var nb = utf8CheckByte(buf[j]);\n    if (nb >= 0) {\n        if (nb > 0) self.lastNeed = nb - 1;\n        return nb;\n    }\n    if (--j < i || nb === -2) return 0;\n    nb = utf8CheckByte(buf[j]);\n    if (nb >= 0) {\n        if (nb > 0) self.lastNeed = nb - 2;\n        return nb;\n    }\n    if (--j < i || nb === -2) return 0;\n    nb = utf8CheckByte(buf[j]);\n    if (nb >= 0) {\n        if (nb > 0) {\n            if (nb === 2) nb = 0;\n            else self.lastNeed = nb - 3;\n        }\n        return nb;\n    }\n    return 0;\n}\n// Validates as many continuation bytes for a multi-byte UTF-8 character as\n// needed or are available. If we see a non-continuation byte where we expect\n// one, we \"replace\" the validated continuation bytes we've seen so far with\n// a single UTF-8 replacement character ('\\ufffd'), to match v8's UTF-8 decoding\n// behavior. The continuation byte check is included three times in the case\n// where all of the continuation bytes for a character exist in the same buffer.\n// It is also done this way as a slight performance increase instead of using a\n// loop.\nfunction utf8CheckExtraBytes(self, buf, p) {\n    if ((buf[0] & 0xC0) !== 0x80) {\n        self.lastNeed = 0;\n        return \"�\";\n    }\n    if (self.lastNeed > 1 && buf.length > 1) {\n        if ((buf[1] & 0xC0) !== 0x80) {\n            self.lastNeed = 1;\n            return \"�\";\n        }\n        if (self.lastNeed > 2 && buf.length > 2) {\n            if ((buf[2] & 0xC0) !== 0x80) {\n                self.lastNeed = 2;\n                return \"�\";\n            }\n        }\n    }\n}\n// Attempts to complete a multi-byte UTF-8 character using bytes from a Buffer.\nfunction utf8FillLast(buf) {\n    var p = this.lastTotal - this.lastNeed;\n    var r = utf8CheckExtraBytes(this, buf, p);\n    if (r !== undefined) return r;\n    if (this.lastNeed <= buf.length) {\n        buf.copy(this.lastChar, p, 0, this.lastNeed);\n        return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n    }\n    buf.copy(this.lastChar, p, 0, buf.length);\n    this.lastNeed -= buf.length;\n}\n// Returns all complete UTF-8 characters in a Buffer. If the Buffer ended on a\n// partial character, the character's bytes are buffered until the required\n// number of bytes are available.\nfunction utf8Text(buf, i) {\n    var total = utf8CheckIncomplete(this, buf, i);\n    if (!this.lastNeed) return buf.toString(\"utf8\", i);\n    this.lastTotal = total;\n    var end = buf.length - (total - this.lastNeed);\n    buf.copy(this.lastChar, 0, end);\n    return buf.toString(\"utf8\", i, end);\n}\n// For UTF-8, a replacement character is added when ending on a partial\n// character.\nfunction utf8End(buf) {\n    var r = buf && buf.length ? this.write(buf) : \"\";\n    if (this.lastNeed) return r + \"�\";\n    return r;\n}\n// UTF-16LE typically needs two bytes per character, but even if we have an even\n// number of bytes available, we need to check if we end on a leading/high\n// surrogate. In that case, we need to wait for the next two bytes in order to\n// decode the last character properly.\nfunction utf16Text(buf, i) {\n    if ((buf.length - i) % 2 === 0) {\n        var r = buf.toString(\"utf16le\", i);\n        if (r) {\n            var c = r.charCodeAt(r.length - 1);\n            if (c >= 0xD800 && c <= 0xDBFF) {\n                this.lastNeed = 2;\n                this.lastTotal = 4;\n                this.lastChar[0] = buf[buf.length - 2];\n                this.lastChar[1] = buf[buf.length - 1];\n                return r.slice(0, -1);\n            }\n        }\n        return r;\n    }\n    this.lastNeed = 1;\n    this.lastTotal = 2;\n    this.lastChar[0] = buf[buf.length - 1];\n    return buf.toString(\"utf16le\", i, buf.length - 1);\n}\n// For UTF-16LE we do not explicitly append special replacement characters if we\n// end on a partial character, we simply let v8 handle that.\nfunction utf16End(buf) {\n    var r = buf && buf.length ? this.write(buf) : \"\";\n    if (this.lastNeed) {\n        var end = this.lastTotal - this.lastNeed;\n        return r + this.lastChar.toString(\"utf16le\", 0, end);\n    }\n    return r;\n}\nfunction base64Text(buf, i) {\n    var n = (buf.length - i) % 3;\n    if (n === 0) return buf.toString(\"base64\", i);\n    this.lastNeed = 3 - n;\n    this.lastTotal = 3;\n    if (n === 1) {\n        this.lastChar[0] = buf[buf.length - 1];\n    } else {\n        this.lastChar[0] = buf[buf.length - 2];\n        this.lastChar[1] = buf[buf.length - 1];\n    }\n    return buf.toString(\"base64\", i, buf.length - n);\n}\nfunction base64End(buf) {\n    var r = buf && buf.length ? this.write(buf) : \"\";\n    if (this.lastNeed) return r + this.lastChar.toString(\"base64\", 0, 3 - this.lastNeed);\n    return r;\n}\n// Pass bytes on through for single-byte encodings (e.g. ascii, latin1, hex)\nfunction simpleWrite(buf) {\n    return buf.toString(this.encoding);\n}\nfunction simpleEnd(buf) {\n    return buf && buf.length ? this.write(buf) : \"\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/string_decoder/lib/string_decoder.js\n");

/***/ })

};
;