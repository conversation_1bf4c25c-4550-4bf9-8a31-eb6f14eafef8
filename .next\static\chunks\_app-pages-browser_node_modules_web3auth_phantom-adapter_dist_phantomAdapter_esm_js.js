"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_web3auth_phantom-adapter_dist_phantomAdapter_esm_js"],{

/***/ "(app-pages-browser)/./node_modules/@web3auth/base-solana-adapter/dist/baseSolanaAdapter.esm.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@web3auth/base-solana-adapter/dist/baseSolanaAdapter.esm.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseSolanaAdapter: function() { return /* binding */ BaseSolanaAdapter; }\n/* harmony export */ });\n/* harmony import */ var _web3auth_base__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @web3auth/base */ \"(app-pages-browser)/./node_modules/@web3auth/base/dist/base.esm.js\");\n/* harmony import */ var bs58__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bs58 */ \"(app-pages-browser)/./node_modules/bs58/index.js\");\n/* harmony import */ var bs58__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bs58__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nclass BaseSolanaAdapter extends _web3auth_base__WEBPACK_IMPORTED_MODULE_0__.BaseAdapter {\n  async init(_) {\n    if (!this.chainConfig) this.chainConfig = (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.getChainConfig)(_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.CHAIN_NAMESPACES.SOLANA, 1);\n  }\n  async authenticateUser() {\n    if (!this.provider || this.status !== _web3auth_base__WEBPACK_IMPORTED_MODULE_0__.ADAPTER_STATUS.CONNECTED) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_0__.WalletLoginError.notConnectedError();\n    const {\n      chainNamespace,\n      chainId\n    } = this.chainConfig;\n    const accounts = await this.provider.request({\n      method: \"getAccounts\"\n    });\n    if (accounts && accounts.length > 0) {\n      const existingToken = (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.getSavedToken)(accounts[0], this.name);\n      if (existingToken) {\n        const isExpired = (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.checkIfTokenIsExpired)(existingToken);\n        if (!isExpired) {\n          return {\n            idToken: existingToken\n          };\n        }\n      }\n      const payload = {\n        domain: window.location.origin,\n        uri: window.location.href,\n        address: accounts[0],\n        chainId: parseInt(chainId, 16),\n        version: \"1\",\n        nonce: Math.random().toString(36).slice(2),\n        issuedAt: new Date().toISOString()\n      };\n      const challenge = await (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.signChallenge)(payload, chainNamespace);\n      const encodedMessage = new TextEncoder().encode(challenge);\n      const signedMessage = await this.provider.request({\n        method: \"signMessage\",\n        params: {\n          message: encodedMessage,\n          display: \"utf8\"\n        }\n      });\n      const idToken = await (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.verifySignedChallenge)(chainNamespace, bs58__WEBPACK_IMPORTED_MODULE_1___default().encode(signedMessage), challenge, this.name, this.sessionTime, this.clientId, this.web3AuthNetwork);\n      (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.saveToken)(accounts[0], this.name, idToken);\n      return {\n        idToken\n      };\n    }\n    throw _web3auth_base__WEBPACK_IMPORTED_MODULE_0__.WalletLoginError.notConnectedError(\"Not connected with wallet, Please login/connect first\");\n  }\n  async disconnectSession() {\n    super.checkDisconnectionRequirements();\n    const accounts = await this.provider.request({\n      method: \"getAccounts\"\n    });\n    if (accounts && accounts.length > 0) {\n      (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.clearToken)(accounts[0], this.name);\n    }\n  }\n  async disconnect() {\n    this.rehydrated = false;\n    this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.ADAPTER_EVENTS.DISCONNECTED);\n  }\n}\n\n\n//# sourceMappingURL=baseSolanaAdapter.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/base-solana-adapter/dist/baseSolanaAdapter.esm.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@web3auth/phantom-adapter/dist/phantomAdapter.esm.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@web3auth/phantom-adapter/dist/phantomAdapter.esm.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PhantomAdapter: function() { return /* binding */ PhantomAdapter; }\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _web3auth_base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @web3auth/base */ \"(app-pages-browser)/./node_modules/@web3auth/base/dist/base.esm.js\");\n/* harmony import */ var _web3auth_base_solana_adapter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @web3auth/base-solana-adapter */ \"(app-pages-browser)/./node_modules/@web3auth/base-solana-adapter/dist/baseSolanaAdapter.esm.js\");\n/* harmony import */ var _web3auth_solana_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @web3auth/solana-provider */ \"(app-pages-browser)/./node_modules/@web3auth/solana-provider/dist/solanaProvider.esm.js\");\n\n\n\n\n\nfunction poll(callback, interval, count) {\n  return new Promise((resolve, reject) => {\n    if (count > 0) {\n      setTimeout(async () => {\n        const done = await callback();\n        if (done) resolve(done);\n        if (!done) poll(callback, interval, count - 1).then(res => {\n          resolve(res);\n          return res;\n        }).catch(err => reject(err));\n      }, interval);\n    } else {\n      resolve(false);\n    }\n  });\n}\nconst detectProvider = async function () {\n  var _window$solana;\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    interval: 1000,\n    count: 3\n  };\n  const isPhantomAvailable = typeof window !== \"undefined\" && !!((_window$solana = window.solana) !== null && _window$solana !== void 0 && _window$solana.isPhantom);\n  if (isPhantomAvailable) {\n    return window.solana;\n  }\n  const isAvailable = await poll(() => {\n    var _window$solana2;\n    return (_window$solana2 = window.solana) === null || _window$solana2 === void 0 ? void 0 : _window$solana2.isPhantom;\n  }, options.interval, options.count);\n  if (isAvailable) return window.solana;\n  return null;\n};\n\nclass PhantomAdapter extends _web3auth_base_solana_adapter__WEBPACK_IMPORTED_MODULE_2__.BaseSolanaAdapter {\n  constructor() {\n    super(...arguments);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"name\", _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WALLET_ADAPTERS.PHANTOM);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"adapterNamespace\", _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_NAMESPACES.SOLANA);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"currentChainNamespace\", _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.CHAIN_NAMESPACES.SOLANA);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"type\", _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_CATEGORY.EXTERNAL);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"status\", _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_STATUS.NOT_READY);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"_wallet\", null);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"phantomProvider\", null);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, \"_onDisconnect\", () => {\n      if (this._wallet) {\n        this._wallet.off(\"disconnect\", this._onDisconnect);\n        this.rehydrated = false;\n        // ready to be connected again only if it was previously connected and not cleaned up\n        this.status = this.status === _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_STATUS.CONNECTED ? _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_STATUS.READY : _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_STATUS.NOT_READY;\n        this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_EVENTS.DISCONNECTED);\n      }\n    });\n  }\n  get isWalletConnected() {\n    var _this$_wallet;\n    return !!((_this$_wallet = this._wallet) !== null && _this$_wallet !== void 0 && _this$_wallet.isConnected && this.status === _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_STATUS.CONNECTED);\n  }\n  get provider() {\n    if (this.status !== _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_STATUS.NOT_READY && this.phantomProvider) {\n      return this.phantomProvider;\n    }\n    return null;\n  }\n  set provider(_) {\n    throw new Error(\"Not implemented\");\n  }\n  async init() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    await super.init(options);\n    super.checkInitializationRequirements();\n    this._wallet = await detectProvider({\n      interval: 500,\n      count: 3\n    });\n    if (!this._wallet) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WalletInitializationError.notInstalled();\n    this.phantomProvider = new _web3auth_solana_provider__WEBPACK_IMPORTED_MODULE_3__.PhantomInjectedProvider({\n      config: {\n        chainConfig: this.chainConfig\n      }\n    });\n    this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_STATUS.READY;\n    this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_EVENTS.READY, _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WALLET_ADAPTERS.PHANTOM);\n    try {\n      _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.log.debug(\"initializing phantom adapter\");\n      if (options.autoConnect) {\n        this.rehydrated = true;\n        await this.connect();\n      }\n    } catch (error) {\n      _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.log.error(\"Failed to connect with cached phantom provider\", error);\n      this.emit(\"ERRORED\", error);\n    }\n  }\n  async connect() {\n    var _this = this;\n    try {\n      super.checkConnectionRequirements();\n      this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_STATUS.CONNECTING;\n      this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_EVENTS.CONNECTING, {\n        adapter: _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WALLET_ADAPTERS.PHANTOM\n      });\n      if (!this._wallet) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WalletInitializationError.notInstalled();\n      if (!this._wallet.isConnected) {\n        const handleDisconnect = this._wallet._handleDisconnect;\n        try {\n          await new Promise((resolve, reject) => {\n            const connect = async () => {\n              await this.connectWithProvider(this._wallet);\n              resolve(this.provider);\n            };\n            if (!this._wallet) {\n              reject(_web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WalletInitializationError.notInstalled());\n              return;\n            }\n            this._wallet.once(\"connect\", connect);\n            // Raise an issue on phantom that if window is closed, disconnect event is not fired\n            this._wallet._handleDisconnect = function () {\n              reject(_web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WalletInitializationError.windowClosed());\n              for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n                args[_key] = arguments[_key];\n              }\n              return handleDisconnect.apply(_this._wallet, args);\n            };\n            this._wallet.connect().catch(reason => {\n              reject(reason);\n            });\n          });\n        } catch (error) {\n          if (error instanceof _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.Web3AuthError) throw error;\n          throw _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WalletLoginError.connectionError(error === null || error === void 0 ? void 0 : error.message);\n        } finally {\n          this._wallet._handleDisconnect = handleDisconnect;\n        }\n      } else {\n        await this.connectWithProvider(this._wallet);\n      }\n      if (!this._wallet.publicKey) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WalletLoginError.connectionError();\n      this._wallet.on(\"disconnect\", this._onDisconnect);\n      return this.provider;\n    } catch (error) {\n      // ready again to be connected\n      this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_STATUS.READY;\n      this.rehydrated = false;\n      this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_EVENTS.ERRORED, error);\n      throw error;\n    }\n  }\n  async disconnect() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      cleanup: false\n    };\n    await super.disconnectSession();\n    try {\n      var _this$_wallet2;\n      await ((_this$_wallet2 = this._wallet) === null || _this$_wallet2 === void 0 ? void 0 : _this$_wallet2.disconnect());\n      if (options.cleanup) {\n        this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_STATUS.NOT_READY;\n        this.phantomProvider = null;\n        this._wallet = null;\n      }\n      await super.disconnect();\n    } catch (error) {\n      this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_EVENTS.ERRORED, _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WalletLoginError.disconnectionError(error === null || error === void 0 ? void 0 : error.message));\n    }\n  }\n  async getUserInfo() {\n    if (!this.isWalletConnected) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WalletLoginError.notConnectedError(\"Not connected with wallet, Please login/connect first\");\n    return {};\n  }\n  async addChain(chainConfig) {\n    var _this$phantomProvider;\n    let init = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    super.checkAddChainRequirements(chainConfig, init);\n    (_this$phantomProvider = this.phantomProvider) === null || _this$phantomProvider === void 0 || _this$phantomProvider.addChain(chainConfig);\n    this.addChainConfig(chainConfig);\n  }\n  async switchChain(params) {\n    var _this$phantomProvider2;\n    let init = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    super.checkSwitchChainRequirements(params, init);\n    await ((_this$phantomProvider2 = this.phantomProvider) === null || _this$phantomProvider2 === void 0 ? void 0 : _this$phantomProvider2.switchChain(params));\n    this.setAdapterSettings({\n      chainConfig: this.getChainConfig(params.chainId)\n    });\n  }\n  async connectWithProvider(injectedProvider) {\n    if (!this.phantomProvider) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WalletLoginError.connectionError(\"No phantom provider\");\n    await this.phantomProvider.setupProvider(injectedProvider);\n    this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_STATUS.CONNECTED;\n    this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_1__.ADAPTER_EVENTS.CONNECTED, {\n      adapter: _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WALLET_ADAPTERS.PHANTOM,\n      reconnected: this.rehydrated\n    });\n    return this.provider;\n  }\n}\n\n\n//# sourceMappingURL=phantomAdapter.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/phantom-adapter/dist/phantomAdapter.esm.js\n"));

/***/ })

}]);