/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lodash.merge";
exports.ids = ["vendor-chunks/lodash.merge"];
exports.modules = {

/***/ "(ssr)/./node_modules/lodash.merge/index.js":
/*!********************************************!*\
  !*** ./node_modules/lodash.merge/index.js ***!
  \********************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* module decorator */ module = __webpack_require__.nmd(module);\n/**\n * Lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors\n */ /** Used as the size to enable large array optimizations. */ var LARGE_ARRAY_SIZE = 200;\n/** Used to stand-in for `undefined` hash values. */ var HASH_UNDEFINED = \"__lodash_hash_undefined__\";\n/** Used to detect hot functions by number of calls within a span of milliseconds. */ var HOT_COUNT = 800, HOT_SPAN = 16;\n/** Used as references for various `Number` constants. */ var MAX_SAFE_INTEGER = 9007199254740991;\n/** `Object#toString` result references. */ var argsTag = \"[object Arguments]\", arrayTag = \"[object Array]\", asyncTag = \"[object AsyncFunction]\", boolTag = \"[object Boolean]\", dateTag = \"[object Date]\", errorTag = \"[object Error]\", funcTag = \"[object Function]\", genTag = \"[object GeneratorFunction]\", mapTag = \"[object Map]\", numberTag = \"[object Number]\", nullTag = \"[object Null]\", objectTag = \"[object Object]\", proxyTag = \"[object Proxy]\", regexpTag = \"[object RegExp]\", setTag = \"[object Set]\", stringTag = \"[object String]\", undefinedTag = \"[object Undefined]\", weakMapTag = \"[object WeakMap]\";\nvar arrayBufferTag = \"[object ArrayBuffer]\", dataViewTag = \"[object DataView]\", float32Tag = \"[object Float32Array]\", float64Tag = \"[object Float64Array]\", int8Tag = \"[object Int8Array]\", int16Tag = \"[object Int16Array]\", int32Tag = \"[object Int32Array]\", uint8Tag = \"[object Uint8Array]\", uint8ClampedTag = \"[object Uint8ClampedArray]\", uint16Tag = \"[object Uint16Array]\", uint32Tag = \"[object Uint32Array]\";\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */ var reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n/** Used to detect host constructors (Safari). */ var reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n/** Used to detect unsigned integer values. */ var reIsUint = /^(?:0|[1-9]\\d*)$/;\n/** Used to identify `toStringTag` values of typed arrays. */ var typedArrayTags = {};\ntypedArrayTags[float32Tag] = typedArrayTags[float64Tag] = typedArrayTags[int8Tag] = typedArrayTags[int16Tag] = typedArrayTags[int32Tag] = typedArrayTags[uint8Tag] = typedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] = typedArrayTags[uint32Tag] = true;\ntypedArrayTags[argsTag] = typedArrayTags[arrayTag] = typedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] = typedArrayTags[dataViewTag] = typedArrayTags[dateTag] = typedArrayTags[errorTag] = typedArrayTags[funcTag] = typedArrayTags[mapTag] = typedArrayTags[numberTag] = typedArrayTags[objectTag] = typedArrayTags[regexpTag] = typedArrayTags[setTag] = typedArrayTags[stringTag] = typedArrayTags[weakMapTag] = false;\n/** Detect free variable `global` from Node.js. */ var freeGlobal = typeof global == \"object\" && global && global.Object === Object && global;\n/** Detect free variable `self`. */ var freeSelf = typeof self == \"object\" && self && self.Object === Object && self;\n/** Used as a reference to the global object. */ var root = freeGlobal || freeSelf || Function(\"return this\")();\n/** Detect free variable `exports`. */ var freeExports =  true && exports && !exports.nodeType && exports;\n/** Detect free variable `module`. */ var freeModule = freeExports && \"object\" == \"object\" && module && !module.nodeType && module;\n/** Detect the popular CommonJS extension `module.exports`. */ var moduleExports = freeModule && freeModule.exports === freeExports;\n/** Detect free variable `process` from Node.js. */ var freeProcess = moduleExports && freeGlobal.process;\n/** Used to access faster Node.js helpers. */ var nodeUtil = function() {\n    try {\n        // Use `util.types` for Node.js 10+.\n        var types = freeModule && freeModule.require && freeModule.require(\"util\").types;\n        if (types) {\n            return types;\n        }\n        // Legacy `process.binding('util')` for Node.js < 10.\n        return freeProcess && freeProcess.binding && freeProcess.binding(\"util\");\n    } catch (e) {}\n}();\n/* Node.js helper references. */ var nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;\n/**\n * A faster alternative to `Function#apply`, this function invokes `func`\n * with the `this` binding of `thisArg` and the arguments of `args`.\n *\n * @private\n * @param {Function} func The function to invoke.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {Array} args The arguments to invoke `func` with.\n * @returns {*} Returns the result of `func`.\n */ function apply(func, thisArg, args) {\n    switch(args.length){\n        case 0:\n            return func.call(thisArg);\n        case 1:\n            return func.call(thisArg, args[0]);\n        case 2:\n            return func.call(thisArg, args[0], args[1]);\n        case 3:\n            return func.call(thisArg, args[0], args[1], args[2]);\n    }\n    return func.apply(thisArg, args);\n}\n/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */ function baseTimes(n, iteratee) {\n    var index = -1, result = Array(n);\n    while(++index < n){\n        result[index] = iteratee(index);\n    }\n    return result;\n}\n/**\n * The base implementation of `_.unary` without support for storing metadata.\n *\n * @private\n * @param {Function} func The function to cap arguments for.\n * @returns {Function} Returns the new capped function.\n */ function baseUnary(func) {\n    return function(value) {\n        return func(value);\n    };\n}\n/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */ function getValue(object, key) {\n    return object == null ? undefined : object[key];\n}\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */ function overArg(func, transform) {\n    return function(arg) {\n        return func(transform(arg));\n    };\n}\n/** Used for built-in method references. */ var arrayProto = Array.prototype, funcProto = Function.prototype, objectProto = Object.prototype;\n/** Used to detect overreaching core-js shims. */ var coreJsData = root[\"__core-js_shared__\"];\n/** Used to resolve the decompiled source of functions. */ var funcToString = funcProto.toString;\n/** Used to check objects for own properties. */ var hasOwnProperty = objectProto.hasOwnProperty;\n/** Used to detect methods masquerading as native. */ var maskSrcKey = function() {\n    var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || \"\");\n    return uid ? \"Symbol(src)_1.\" + uid : \"\";\n}();\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */ var nativeObjectToString = objectProto.toString;\n/** Used to infer the `Object` constructor. */ var objectCtorString = funcToString.call(Object);\n/** Used to detect if a method is native. */ var reIsNative = RegExp(\"^\" + funcToString.call(hasOwnProperty).replace(reRegExpChar, \"\\\\$&\").replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, \"$1.*?\") + \"$\");\n/** Built-in value references. */ var Buffer = moduleExports ? root.Buffer : undefined, Symbol = root.Symbol, Uint8Array = root.Uint8Array, allocUnsafe = Buffer ? Buffer.allocUnsafe : undefined, getPrototype = overArg(Object.getPrototypeOf, Object), objectCreate = Object.create, propertyIsEnumerable = objectProto.propertyIsEnumerable, splice = arrayProto.splice, symToStringTag = Symbol ? Symbol.toStringTag : undefined;\nvar defineProperty = function() {\n    try {\n        var func = getNative(Object, \"defineProperty\");\n        func({}, \"\", {});\n        return func;\n    } catch (e) {}\n}();\n/* Built-in method references for those with the same name as other `lodash` methods. */ var nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined, nativeMax = Math.max, nativeNow = Date.now;\n/* Built-in method references that are verified to be native. */ var Map = getNative(root, \"Map\"), nativeCreate = getNative(Object, \"create\");\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} proto The object to inherit from.\n * @returns {Object} Returns the new object.\n */ var baseCreate = function() {\n    function object() {}\n    return function(proto) {\n        if (!isObject(proto)) {\n            return {};\n        }\n        if (objectCreate) {\n            return objectCreate(proto);\n        }\n        object.prototype = proto;\n        var result = new object;\n        object.prototype = undefined;\n        return result;\n    };\n}();\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function Hash(entries) {\n    var index = -1, length = entries == null ? 0 : entries.length;\n    this.clear();\n    while(++index < length){\n        var entry = entries[index];\n        this.set(entry[0], entry[1]);\n    }\n}\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */ function hashClear() {\n    this.__data__ = nativeCreate ? nativeCreate(null) : {};\n    this.size = 0;\n}\n/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function hashDelete(key) {\n    var result = this.has(key) && delete this.__data__[key];\n    this.size -= result ? 1 : 0;\n    return result;\n}\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function hashGet(key) {\n    var data = this.__data__;\n    if (nativeCreate) {\n        var result = data[key];\n        return result === HASH_UNDEFINED ? undefined : result;\n    }\n    return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function hashHas(key) {\n    var data = this.__data__;\n    return nativeCreate ? data[key] !== undefined : hasOwnProperty.call(data, key);\n}\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */ function hashSet(key, value) {\n    var data = this.__data__;\n    this.size += this.has(key) ? 0 : 1;\n    data[key] = nativeCreate && value === undefined ? HASH_UNDEFINED : value;\n    return this;\n}\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype[\"delete\"] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function ListCache(entries) {\n    var index = -1, length = entries == null ? 0 : entries.length;\n    this.clear();\n    while(++index < length){\n        var entry = entries[index];\n        this.set(entry[0], entry[1]);\n    }\n}\n/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */ function listCacheClear() {\n    this.__data__ = [];\n    this.size = 0;\n}\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function listCacheDelete(key) {\n    var data = this.__data__, index = assocIndexOf(data, key);\n    if (index < 0) {\n        return false;\n    }\n    var lastIndex = data.length - 1;\n    if (index == lastIndex) {\n        data.pop();\n    } else {\n        splice.call(data, index, 1);\n    }\n    --this.size;\n    return true;\n}\n/**\n * Gets the list cache value for `key`.\n *\n * @private\n * @name get\n * @memberOf ListCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function listCacheGet(key) {\n    var data = this.__data__, index = assocIndexOf(data, key);\n    return index < 0 ? undefined : data[index][1];\n}\n/**\n * Checks if a list cache value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf ListCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function listCacheHas(key) {\n    return assocIndexOf(this.__data__, key) > -1;\n}\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */ function listCacheSet(key, value) {\n    var data = this.__data__, index = assocIndexOf(data, key);\n    if (index < 0) {\n        ++this.size;\n        data.push([\n            key,\n            value\n        ]);\n    } else {\n        data[index][1] = value;\n    }\n    return this;\n}\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype[\"delete\"] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function MapCache(entries) {\n    var index = -1, length = entries == null ? 0 : entries.length;\n    this.clear();\n    while(++index < length){\n        var entry = entries[index];\n        this.set(entry[0], entry[1]);\n    }\n}\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */ function mapCacheClear() {\n    this.size = 0;\n    this.__data__ = {\n        \"hash\": new Hash,\n        \"map\": new (Map || ListCache),\n        \"string\": new Hash\n    };\n}\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function mapCacheDelete(key) {\n    var result = getMapData(this, key)[\"delete\"](key);\n    this.size -= result ? 1 : 0;\n    return result;\n}\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function mapCacheGet(key) {\n    return getMapData(this, key).get(key);\n}\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function mapCacheHas(key) {\n    return getMapData(this, key).has(key);\n}\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */ function mapCacheSet(key, value) {\n    var data = getMapData(this, key), size = data.size;\n    data.set(key, value);\n    this.size += data.size == size ? 0 : 1;\n    return this;\n}\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype[\"delete\"] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function Stack(entries) {\n    var data = this.__data__ = new ListCache(entries);\n    this.size = data.size;\n}\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */ function stackClear() {\n    this.__data__ = new ListCache;\n    this.size = 0;\n}\n/**\n * Removes `key` and its value from the stack.\n *\n * @private\n * @name delete\n * @memberOf Stack\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function stackDelete(key) {\n    var data = this.__data__, result = data[\"delete\"](key);\n    this.size = data.size;\n    return result;\n}\n/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function stackGet(key) {\n    return this.__data__.get(key);\n}\n/**\n * Checks if a stack value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Stack\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function stackHas(key) {\n    return this.__data__.has(key);\n}\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */ function stackSet(key, value) {\n    var data = this.__data__;\n    if (data instanceof ListCache) {\n        var pairs = data.__data__;\n        if (!Map || pairs.length < LARGE_ARRAY_SIZE - 1) {\n            pairs.push([\n                key,\n                value\n            ]);\n            this.size = ++data.size;\n            return this;\n        }\n        data = this.__data__ = new MapCache(pairs);\n    }\n    data.set(key, value);\n    this.size = data.size;\n    return this;\n}\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype[\"delete\"] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */ function arrayLikeKeys(value, inherited) {\n    var isArr = isArray(value), isArg = !isArr && isArguments(value), isBuff = !isArr && !isArg && isBuffer(value), isType = !isArr && !isArg && !isBuff && isTypedArray(value), skipIndexes = isArr || isArg || isBuff || isType, result = skipIndexes ? baseTimes(value.length, String) : [], length = result.length;\n    for(var key in value){\n        if ((inherited || hasOwnProperty.call(value, key)) && !(skipIndexes && // Safari 9 has enumerable `arguments.length` in strict mode.\n        (key == \"length\" || // Node.js 0.10 has enumerable non-index properties on buffers.\n        isBuff && (key == \"offset\" || key == \"parent\") || // PhantomJS 2 has enumerable non-index properties on typed arrays.\n        isType && (key == \"buffer\" || key == \"byteLength\" || key == \"byteOffset\") || // Skip index properties.\n        isIndex(key, length)))) {\n            result.push(key);\n        }\n    }\n    return result;\n}\n/**\n * This function is like `assignValue` except that it doesn't assign\n * `undefined` values.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */ function assignMergeValue(object, key, value) {\n    if (value !== undefined && !eq(object[key], value) || value === undefined && !(key in object)) {\n        baseAssignValue(object, key, value);\n    }\n}\n/**\n * Assigns `value` to `key` of `object` if the existing value is not equivalent\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */ function assignValue(object, key, value) {\n    var objValue = object[key];\n    if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) || value === undefined && !(key in object)) {\n        baseAssignValue(object, key, value);\n    }\n}\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */ function assocIndexOf(array, key) {\n    var length = array.length;\n    while(length--){\n        if (eq(array[length][0], key)) {\n            return length;\n        }\n    }\n    return -1;\n}\n/**\n * The base implementation of `assignValue` and `assignMergeValue` without\n * value checks.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */ function baseAssignValue(object, key, value) {\n    if (key == \"__proto__\" && defineProperty) {\n        defineProperty(object, key, {\n            \"configurable\": true,\n            \"enumerable\": true,\n            \"value\": value,\n            \"writable\": true\n        });\n    } else {\n        object[key] = value;\n    }\n}\n/**\n * The base implementation of `baseForOwn` which iterates over `object`\n * properties returned by `keysFunc` and invokes `iteratee` for each property.\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @returns {Object} Returns `object`.\n */ var baseFor = createBaseFor();\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */ function baseGetTag(value) {\n    if (value == null) {\n        return value === undefined ? undefinedTag : nullTag;\n    }\n    return symToStringTag && symToStringTag in Object(value) ? getRawTag(value) : objectToString(value);\n}\n/**\n * The base implementation of `_.isArguments`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n */ function baseIsArguments(value) {\n    return isObjectLike(value) && baseGetTag(value) == argsTag;\n}\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */ function baseIsNative(value) {\n    if (!isObject(value) || isMasked(value)) {\n        return false;\n    }\n    var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\n    return pattern.test(toSource(value));\n}\n/**\n * The base implementation of `_.isTypedArray` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n */ function baseIsTypedArray(value) {\n    return isObjectLike(value) && isLength(value.length) && !!typedArrayTags[baseGetTag(value)];\n}\n/**\n * The base implementation of `_.keysIn` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */ function baseKeysIn(object) {\n    if (!isObject(object)) {\n        return nativeKeysIn(object);\n    }\n    var isProto = isPrototype(object), result = [];\n    for(var key in object){\n        if (!(key == \"constructor\" && (isProto || !hasOwnProperty.call(object, key)))) {\n            result.push(key);\n        }\n    }\n    return result;\n}\n/**\n * The base implementation of `_.merge` without support for multiple sources.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} [customizer] The function to customize merged values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */ function baseMerge(object, source, srcIndex, customizer, stack) {\n    if (object === source) {\n        return;\n    }\n    baseFor(source, function(srcValue, key) {\n        stack || (stack = new Stack);\n        if (isObject(srcValue)) {\n            baseMergeDeep(object, source, key, srcIndex, baseMerge, customizer, stack);\n        } else {\n            var newValue = customizer ? customizer(safeGet(object, key), srcValue, key + \"\", object, source, stack) : undefined;\n            if (newValue === undefined) {\n                newValue = srcValue;\n            }\n            assignMergeValue(object, key, newValue);\n        }\n    }, keysIn);\n}\n/**\n * A specialized version of `baseMerge` for arrays and objects which performs\n * deep merges and tracks traversed objects enabling objects with circular\n * references to be merged.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {string} key The key of the value to merge.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} mergeFunc The function to merge values.\n * @param {Function} [customizer] The function to customize assigned values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */ function baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {\n    var objValue = safeGet(object, key), srcValue = safeGet(source, key), stacked = stack.get(srcValue);\n    if (stacked) {\n        assignMergeValue(object, key, stacked);\n        return;\n    }\n    var newValue = customizer ? customizer(objValue, srcValue, key + \"\", object, source, stack) : undefined;\n    var isCommon = newValue === undefined;\n    if (isCommon) {\n        var isArr = isArray(srcValue), isBuff = !isArr && isBuffer(srcValue), isTyped = !isArr && !isBuff && isTypedArray(srcValue);\n        newValue = srcValue;\n        if (isArr || isBuff || isTyped) {\n            if (isArray(objValue)) {\n                newValue = objValue;\n            } else if (isArrayLikeObject(objValue)) {\n                newValue = copyArray(objValue);\n            } else if (isBuff) {\n                isCommon = false;\n                newValue = cloneBuffer(srcValue, true);\n            } else if (isTyped) {\n                isCommon = false;\n                newValue = cloneTypedArray(srcValue, true);\n            } else {\n                newValue = [];\n            }\n        } else if (isPlainObject(srcValue) || isArguments(srcValue)) {\n            newValue = objValue;\n            if (isArguments(objValue)) {\n                newValue = toPlainObject(objValue);\n            } else if (!isObject(objValue) || isFunction(objValue)) {\n                newValue = initCloneObject(srcValue);\n            }\n        } else {\n            isCommon = false;\n        }\n    }\n    if (isCommon) {\n        // Recursively merge objects and arrays (susceptible to call stack limits).\n        stack.set(srcValue, newValue);\n        mergeFunc(newValue, srcValue, srcIndex, customizer, stack);\n        stack[\"delete\"](srcValue);\n    }\n    assignMergeValue(object, key, newValue);\n}\n/**\n * The base implementation of `_.rest` which doesn't validate or coerce arguments.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @returns {Function} Returns the new function.\n */ function baseRest(func, start) {\n    return setToString(overRest(func, start, identity), func + \"\");\n}\n/**\n * The base implementation of `setToString` without support for hot loop shorting.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */ var baseSetToString = !defineProperty ? identity : function(func, string) {\n    return defineProperty(func, \"toString\", {\n        \"configurable\": true,\n        \"enumerable\": false,\n        \"value\": constant(string),\n        \"writable\": true\n    });\n};\n/**\n * Creates a clone of  `buffer`.\n *\n * @private\n * @param {Buffer} buffer The buffer to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Buffer} Returns the cloned buffer.\n */ function cloneBuffer(buffer, isDeep) {\n    if (isDeep) {\n        return buffer.slice();\n    }\n    var length = buffer.length, result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);\n    buffer.copy(result);\n    return result;\n}\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */ function cloneArrayBuffer(arrayBuffer) {\n    var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n    new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n    return result;\n}\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */ function cloneTypedArray(typedArray, isDeep) {\n    var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n    return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\n/**\n * Copies the values of `source` to `array`.\n *\n * @private\n * @param {Array} source The array to copy values from.\n * @param {Array} [array=[]] The array to copy values to.\n * @returns {Array} Returns `array`.\n */ function copyArray(source, array) {\n    var index = -1, length = source.length;\n    array || (array = Array(length));\n    while(++index < length){\n        array[index] = source[index];\n    }\n    return array;\n}\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */ function copyObject(source, props, object, customizer) {\n    var isNew = !object;\n    object || (object = {});\n    var index = -1, length = props.length;\n    while(++index < length){\n        var key = props[index];\n        var newValue = customizer ? customizer(object[key], source[key], key, object, source) : undefined;\n        if (newValue === undefined) {\n            newValue = source[key];\n        }\n        if (isNew) {\n            baseAssignValue(object, key, newValue);\n        } else {\n            assignValue(object, key, newValue);\n        }\n    }\n    return object;\n}\n/**\n * Creates a function like `_.assign`.\n *\n * @private\n * @param {Function} assigner The function to assign values.\n * @returns {Function} Returns the new assigner function.\n */ function createAssigner(assigner) {\n    return baseRest(function(object, sources) {\n        var index = -1, length = sources.length, customizer = length > 1 ? sources[length - 1] : undefined, guard = length > 2 ? sources[2] : undefined;\n        customizer = assigner.length > 3 && typeof customizer == \"function\" ? (length--, customizer) : undefined;\n        if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n            customizer = length < 3 ? undefined : customizer;\n            length = 1;\n        }\n        object = Object(object);\n        while(++index < length){\n            var source = sources[index];\n            if (source) {\n                assigner(object, source, index, customizer);\n            }\n        }\n        return object;\n    });\n}\n/**\n * Creates a base function for methods like `_.forIn` and `_.forOwn`.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */ function createBaseFor(fromRight) {\n    return function(object, iteratee, keysFunc) {\n        var index = -1, iterable = Object(object), props = keysFunc(object), length = props.length;\n        while(length--){\n            var key = props[fromRight ? length : ++index];\n            if (iteratee(iterable[key], key, iterable) === false) {\n                break;\n            }\n        }\n        return object;\n    };\n}\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */ function getMapData(map, key) {\n    var data = map.__data__;\n    return isKeyable(key) ? data[typeof key == \"string\" ? \"string\" : \"hash\"] : data.map;\n}\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */ function getNative(object, key) {\n    var value = getValue(object, key);\n    return baseIsNative(value) ? value : undefined;\n}\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */ function getRawTag(value) {\n    var isOwn = hasOwnProperty.call(value, symToStringTag), tag = value[symToStringTag];\n    try {\n        value[symToStringTag] = undefined;\n        var unmasked = true;\n    } catch (e) {}\n    var result = nativeObjectToString.call(value);\n    if (unmasked) {\n        if (isOwn) {\n            value[symToStringTag] = tag;\n        } else {\n            delete value[symToStringTag];\n        }\n    }\n    return result;\n}\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */ function initCloneObject(object) {\n    return typeof object.constructor == \"function\" && !isPrototype(object) ? baseCreate(getPrototype(object)) : {};\n}\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */ function isIndex(value, length) {\n    var type = typeof value;\n    length = length == null ? MAX_SAFE_INTEGER : length;\n    return !!length && (type == \"number\" || type != \"symbol\" && reIsUint.test(value)) && value > -1 && value % 1 == 0 && value < length;\n}\n/**\n * Checks if the given arguments are from an iteratee call.\n *\n * @private\n * @param {*} value The potential iteratee value argument.\n * @param {*} index The potential iteratee index or key argument.\n * @param {*} object The potential iteratee object argument.\n * @returns {boolean} Returns `true` if the arguments are from an iteratee call,\n *  else `false`.\n */ function isIterateeCall(value, index, object) {\n    if (!isObject(object)) {\n        return false;\n    }\n    var type = typeof index;\n    if (type == \"number\" ? isArrayLike(object) && isIndex(index, object.length) : type == \"string\" && index in object) {\n        return eq(object[index], value);\n    }\n    return false;\n}\n/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */ function isKeyable(value) {\n    var type = typeof value;\n    return type == \"string\" || type == \"number\" || type == \"symbol\" || type == \"boolean\" ? value !== \"__proto__\" : value === null;\n}\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */ function isMasked(func) {\n    return !!maskSrcKey && maskSrcKey in func;\n}\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */ function isPrototype(value) {\n    var Ctor = value && value.constructor, proto = typeof Ctor == \"function\" && Ctor.prototype || objectProto;\n    return value === proto;\n}\n/**\n * This function is like\n * [`Object.keys`](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * except that it includes inherited enumerable properties.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */ function nativeKeysIn(object) {\n    var result = [];\n    if (object != null) {\n        for(var key in Object(object)){\n            result.push(key);\n        }\n    }\n    return result;\n}\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */ function objectToString(value) {\n    return nativeObjectToString.call(value);\n}\n/**\n * A specialized version of `baseRest` which transforms the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @param {Function} transform The rest array transform.\n * @returns {Function} Returns the new function.\n */ function overRest(func, start, transform) {\n    start = nativeMax(start === undefined ? func.length - 1 : start, 0);\n    return function() {\n        var args = arguments, index = -1, length = nativeMax(args.length - start, 0), array = Array(length);\n        while(++index < length){\n            array[index] = args[start + index];\n        }\n        index = -1;\n        var otherArgs = Array(start + 1);\n        while(++index < start){\n            otherArgs[index] = args[index];\n        }\n        otherArgs[start] = transform(array);\n        return apply(func, this, otherArgs);\n    };\n}\n/**\n * Gets the value at `key`, unless `key` is \"__proto__\" or \"constructor\".\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */ function safeGet(object, key) {\n    if (key === \"constructor\" && typeof object[key] === \"function\") {\n        return;\n    }\n    if (key == \"__proto__\") {\n        return;\n    }\n    return object[key];\n}\n/**\n * Sets the `toString` method of `func` to return `string`.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */ var setToString = shortOut(baseSetToString);\n/**\n * Creates a function that'll short out and invoke `identity` instead\n * of `func` when it's called `HOT_COUNT` or more times in `HOT_SPAN`\n * milliseconds.\n *\n * @private\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new shortable function.\n */ function shortOut(func) {\n    var count = 0, lastCalled = 0;\n    return function() {\n        var stamp = nativeNow(), remaining = HOT_SPAN - (stamp - lastCalled);\n        lastCalled = stamp;\n        if (remaining > 0) {\n            if (++count >= HOT_COUNT) {\n                return arguments[0];\n            }\n        } else {\n            count = 0;\n        }\n        return func.apply(undefined, arguments);\n    };\n}\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to convert.\n * @returns {string} Returns the source code.\n */ function toSource(func) {\n    if (func != null) {\n        try {\n            return funcToString.call(func);\n        } catch (e) {}\n        try {\n            return func + \"\";\n        } catch (e) {}\n    }\n    return \"\";\n}\n/**\n * Performs a\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * comparison between two values to determine if they are equivalent.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.eq(object, object);\n * // => true\n *\n * _.eq(object, other);\n * // => false\n *\n * _.eq('a', 'a');\n * // => true\n *\n * _.eq('a', Object('a'));\n * // => false\n *\n * _.eq(NaN, NaN);\n * // => true\n */ function eq(value, other) {\n    return value === other || value !== value && other !== other;\n}\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */ var isArguments = baseIsArguments(function() {\n    return arguments;\n}()) ? baseIsArguments : function(value) {\n    return isObjectLike(value) && hasOwnProperty.call(value, \"callee\") && !propertyIsEnumerable.call(value, \"callee\");\n};\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */ var isArray = Array.isArray;\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */ function isArrayLike(value) {\n    return value != null && isLength(value.length) && !isFunction(value);\n}\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */ function isArrayLikeObject(value) {\n    return isObjectLike(value) && isArrayLike(value);\n}\n/**\n * Checks if `value` is a buffer.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\n * @example\n *\n * _.isBuffer(new Buffer(2));\n * // => true\n *\n * _.isBuffer(new Uint8Array(2));\n * // => false\n */ var isBuffer = nativeIsBuffer || stubFalse;\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */ function isFunction(value) {\n    if (!isObject(value)) {\n        return false;\n    }\n    // The use of `Object#toString` avoids issues with the `typeof` operator\n    // in Safari 9 which returns 'object' for typed arrays and other constructors.\n    var tag = baseGetTag(value);\n    return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */ function isLength(value) {\n    return typeof value == \"number\" && value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */ function isObject(value) {\n    var type = typeof value;\n    return value != null && (type == \"object\" || type == \"function\");\n}\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */ function isObjectLike(value) {\n    return value != null && typeof value == \"object\";\n}\n/**\n * Checks if `value` is a plain object, that is, an object created by the\n * `Object` constructor or one with a `[[Prototype]]` of `null`.\n *\n * @static\n * @memberOf _\n * @since 0.8.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * _.isPlainObject(new Foo);\n * // => false\n *\n * _.isPlainObject([1, 2, 3]);\n * // => false\n *\n * _.isPlainObject({ 'x': 0, 'y': 0 });\n * // => true\n *\n * _.isPlainObject(Object.create(null));\n * // => true\n */ function isPlainObject(value) {\n    if (!isObjectLike(value) || baseGetTag(value) != objectTag) {\n        return false;\n    }\n    var proto = getPrototype(value);\n    if (proto === null) {\n        return true;\n    }\n    var Ctor = hasOwnProperty.call(proto, \"constructor\") && proto.constructor;\n    return typeof Ctor == \"function\" && Ctor instanceof Ctor && funcToString.call(Ctor) == objectCtorString;\n}\n/**\n * Checks if `value` is classified as a typed array.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n * @example\n *\n * _.isTypedArray(new Uint8Array);\n * // => true\n *\n * _.isTypedArray([]);\n * // => false\n */ var isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;\n/**\n * Converts `value` to a plain object flattening inherited enumerable string\n * keyed properties of `value` to own properties of the plain object.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {Object} Returns the converted plain object.\n * @example\n *\n * function Foo() {\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.assign({ 'a': 1 }, new Foo);\n * // => { 'a': 1, 'b': 2 }\n *\n * _.assign({ 'a': 1 }, _.toPlainObject(new Foo));\n * // => { 'a': 1, 'b': 2, 'c': 3 }\n */ function toPlainObject(value) {\n    return copyObject(value, keysIn(value));\n}\n/**\n * Creates an array of the own and inherited enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keysIn(new Foo);\n * // => ['a', 'b', 'c'] (iteration order is not guaranteed)\n */ function keysIn(object) {\n    return isArrayLike(object) ? arrayLikeKeys(object, true) : baseKeysIn(object);\n}\n/**\n * This method is like `_.assign` except that it recursively merges own and\n * inherited enumerable string keyed properties of source objects into the\n * destination object. Source properties that resolve to `undefined` are\n * skipped if a destination value exists. Array and plain object properties\n * are merged recursively. Other objects and value types are overridden by\n * assignment. Source objects are applied from left to right. Subsequent\n * sources overwrite property assignments of previous sources.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 0.5.0\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @example\n *\n * var object = {\n *   'a': [{ 'b': 2 }, { 'd': 4 }]\n * };\n *\n * var other = {\n *   'a': [{ 'c': 3 }, { 'e': 5 }]\n * };\n *\n * _.merge(object, other);\n * // => { 'a': [{ 'b': 2, 'c': 3 }, { 'd': 4, 'e': 5 }] }\n */ var merge = createAssigner(function(object, source, srcIndex) {\n    baseMerge(object, source, srcIndex);\n});\n/**\n * Creates a function that returns `value`.\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Util\n * @param {*} value The value to return from the new function.\n * @returns {Function} Returns the new constant function.\n * @example\n *\n * var objects = _.times(2, _.constant({ 'a': 1 }));\n *\n * console.log(objects);\n * // => [{ 'a': 1 }, { 'a': 1 }]\n *\n * console.log(objects[0] === objects[1]);\n * // => true\n */ function constant(value) {\n    return function() {\n        return value;\n    };\n}\n/**\n * This method returns the first argument it receives.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {*} value Any value.\n * @returns {*} Returns `value`.\n * @example\n *\n * var object = { 'a': 1 };\n *\n * console.log(_.identity(object) === object);\n * // => true\n */ function identity(value) {\n    return value;\n}\n/**\n * This method returns `false`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `false`.\n * @example\n *\n * _.times(2, _.stubFalse);\n * // => [false, false]\n */ function stubFalse() {\n    return false;\n}\nmodule.exports = merge;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lodash.merge/index.js\n");

/***/ })

};
;