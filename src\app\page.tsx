'use client'

import { useWeb3Auth } from '@/hooks/useWeb3Auth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Sparkles, Palette, Music, Coins } from 'lucide-react'
import { motion } from 'framer-motion'

export default function HomePage() {
  const { user, login, logout, isLoading } = useWeb3Auth()

  const features = [
    {
      icon: <Sparkles className="h-8 w-8" />,
      title: "AI文字创作",
      description: "使用先进的AI技术生成故事、诗歌、文案等文字内容"
    },
    {
      icon: <Palette className="h-8 w-8" />,
      title: "AI图片生成",
      description: "创造独特的艺术画作、插画和数字艺术品"
    },
    {
      icon: <Music className="h-8 w-8" />,
      title: "AI音乐创作",
      description: "生成原创背景音乐和音效，为作品增添声音魅力"
    },
    {
      icon: <Coins className="h-8 w-8" />,
      title: "NFT铸造",
      description: "一键将创作转化为NFT，在Solana链上永久保存"
    }
  ]

  return (
    <div className="min-h-screen gradient-bg">
      {/* 导航栏 */}
      <nav className="p-6 flex justify-between items-center glass-effect">
        <div className="text-2xl font-bold text-white">
          AI + Web3 创作平台
        </div>
        <div>
          {user ? (
            <div className="flex items-center gap-4">
              <span className="text-white">
                欢迎, {user.name || '创作者'}
              </span>
              <Button onClick={logout} variant="outline">
                退出登录
              </Button>
            </div>
          ) : (
            <Button 
              onClick={login} 
              disabled={isLoading}
              className="bg-white text-purple-600 hover:bg-gray-100"
            >
              {isLoading ? '连接中...' : '连接钱包'}
            </Button>
          )}
        </div>
      </nav>

      {/* 主要内容 */}
      <div className="container mx-auto px-6 py-12">
        {/* 英雄区域 */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-6xl font-bold text-white mb-6">
            创作的未来，从这里开始
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
            结合AI创作能力与Web3技术，让每个人都能成为数字艺术家，
            创作独特内容并通过NFT获得收益
          </p>
          {!user && (
            <Button 
              onClick={login}
              disabled={isLoading}
              size="lg"
              className="bg-white text-purple-600 hover:bg-gray-100 text-lg px-8 py-4"
            >
              {isLoading ? '连接中...' : '开始创作之旅'}
            </Button>
          )}
        </motion.div>

        {/* 功能特性 */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
            >
              <Card className="glass-effect border-white/20 text-white h-full">
                <CardHeader>
                  <div className="text-purple-300 mb-2">
                    {feature.icon}
                  </div>
                  <CardTitle>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-white/70">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* 用户状态显示 */}
        {user && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <Card className="glass-effect border-white/20 text-white max-w-md mx-auto">
              <CardHeader>
                <CardTitle>钱包信息</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-white/70 mb-2">
                  钱包地址: {user.publicKey?.slice(0, 8)}...{user.publicKey?.slice(-8)}
                </p>
                <Button className="w-full bg-purple-600 hover:bg-purple-700">
                  开始创作
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  )
}
