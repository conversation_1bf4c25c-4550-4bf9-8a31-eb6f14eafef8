"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_web3auth_ui_dist_german-SYmK-a-e_js"],{

/***/ "(app-pages-browser)/./node_modules/@web3auth/ui/dist/german-SYmK-a-e.js":
/*!***********************************************************!*\
  !*** ./node_modules/@web3auth/ui/dist/german-SYmK-a-e.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ german; },\n/* harmony export */   modal: function() { return /* binding */ modal; }\n/* harmony export */ });\nvar modal = {\n  \"adapter-loader.message\": \"Bestätigen Sie Ihr {{adapter}}-Konto, um fortzufahren\",\n  \"adapter-loader.message1\": \"Bestätigen Sie Ihr {{adapter}}\",\n  \"adapter-loader.message2\": \"Konto fortzusetzen\",\n  \"errors-invalid-number-email\": \"Ungültige E-Mail-Adresse oder Telefonnummer\",\n  \"errors-required\": \"Erforderlich\",\n  \"external.back\": \"Zurück\",\n  \"external.connect\": \"Mit Wallet verbinden\",\n  \"external.title\": \"Externe Geldbörse\",\n  \"external.walletconnect-connect\": \"Verbinden\",\n  \"external.walletconnect-copy\": \"Klicken Sie auf den QR-Code, um ihn in die Zwischenablage zu kopieren\",\n  \"external.walletconnect-subtitle\": \"Scannen Sie den QR-Code mit einer WalletConnect-kompatiblen Geldbörse\",\n  \"footer.message\": \"Selbstverwahrungs-Login durch\",\n  \"footer.message-new\": \"Selbstverwahrungs-Login durch Web3Auth\",\n  \"footer.policy\": \"Datenschutzrichtlinie\",\n  \"footer.terms\": \"Nutzungsbedingungen\",\n  \"footer.terms-service\": \"Nutzungsbedingungen\",\n  \"footer.version\": \"Versión\",\n  \"header-subtitle\": \"Wählen Sie eine der folgenden Optionen aus, um fortzufahren\",\n  \"header-subtitle-name\": \"Ihre {{appName}}-Brieftasche mit einem Klick\",\n  \"header-subtitle-new\": \"Ihre Blockchain-Brieftasche mit einem Klick\",\n  \"header-title\": \"Einloggen\",\n  \"header-tooltip-desc\": \"Die Brieftasche dient als Konto zum Speichern und Verwalten Ihrer digitalen Assets auf der Blockchain.\",\n  \"header-tooltip-title\": \"Brieftasche\",\n  \"network.add-request\": \"Diese Website fordert das Hinzufügen eines Netzwerks an\",\n  \"network.cancel\": \"Abbrechen\",\n  \"network.from\": \"Von\",\n  \"network.proceed\": \"Fortfahren\",\n  \"network.switch-request\": \"Diese Website fordert einen Netzwerkwechsel an\",\n  \"network.to\": \"Zu\",\n  \"popup.phone-body\": \"Ihr Ländercode wird automatisch erkannt, aber wenn Sie eine Telefonnummer aus einem anderen Land verwenden, müssen Sie den richtigen Ländercode manuell eingeben.\",\n  \"popup.phone-header\": \"Telefonnummer und Ländercode\",\n  \"social.continue\": \"Weitermachen mit\",\n  \"social.continueCustom\": \"Fahren Sie mit {{adapter}} fort\",\n  \"social.email\": \"E-Mail\",\n  \"social.email-continue\": \"Weitermachen mit E-Mail\",\n  \"social.email-new\": \"<EMAIL>\",\n  \"social.passwordless-cta\": \"Weitermachen\",\n  \"social.passwordless-title\": \"E-Mail oder Telefon\",\n  \"social.phone\": \"Telefon\",\n  \"social.policy\": \"Wir speichern keine Daten im Zusammenhang mit Ihren Social Logins.\",\n  \"social.sms\": \"Mobiltelefon\",\n  \"social.sms-continue\": \"Mit Mobilgerät fortfahren\",\n  \"social.sms-invalid-number\": \"Ungültige Telefonnummer\",\n  \"social.sms-placeholder-text\": \"Z.B.:\",\n  \"social.view-less\": \"Weniger Optionen anzeigen\",\n  \"social.view-less-new\": \"Weniger anzeigen\",\n  \"social.view-more\": \"Weitere Optionen anzeigen\",\n  \"social.view-more-new\": \"Mehr anzeigen\",\n  \"post-loading.connected\": \"Sie sind mit Ihrem Konto verbunden\",\n  \"post-loading.something-wrong\": \"Etwas ist schief gelaufen!\"\n};\nvar german = {\n  modal: modal\n};\n\n\n//# sourceMappingURL=german-SYmK-a-e.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/ui/dist/german-SYmK-a-e.js\n"));

/***/ })

}]);