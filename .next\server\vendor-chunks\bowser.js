"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bowser";
exports.ids = ["vendor-chunks/bowser"];
exports.modules = {

/***/ "(ssr)/./node_modules/bowser/src/bowser.js":
/*!*******************************************!*\
  !*** ./node_modules/bowser/src/bowser.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _parser_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parser.js */ \"(ssr)/./node_modules/bowser/src/parser.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/bowser/src/constants.js\");\n/*!\n * Bowser - a browser detector\n * https://github.com/lancedikson/bowser\n * MIT License | (c) Dustin Diaz 2012-2015\n * MIT License | (c) Denis Demchenko 2015-2019\n */ \n\n/**\n * Bowser class.\n * Keep it simple as much as it can be.\n * It's supposed to work with collections of {@link Parser} instances\n * rather then solve one-instance problems.\n * All the one-instance stuff is located in Parser class.\n *\n * @class\n * @classdesc Bowser is a static object, that provides an API to the Parsers\n * @hideconstructor\n */ class Bowser {\n    /**\n   * Creates a {@link Parser} instance\n   *\n   * @param {String} UA UserAgent string\n   * @param {Boolean} [skipParsing=false] Will make the Parser postpone parsing until you ask it\n   * explicitly. Same as `skipParsing` for {@link Parser}.\n   * @returns {Parser}\n   * @throws {Error} when UA is not a String\n   *\n   * @example\n   * const parser = Bowser.getParser(window.navigator.userAgent);\n   * const result = parser.getResult();\n   */ static getParser(UA, skipParsing = false) {\n        if (typeof UA !== \"string\") {\n            throw new Error(\"UserAgent should be a string\");\n        }\n        return new _parser_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](UA, skipParsing);\n    }\n    /**\n   * Creates a {@link Parser} instance and runs {@link Parser.getResult} immediately\n   *\n   * @param UA\n   * @return {ParsedResult}\n   *\n   * @example\n   * const result = Bowser.parse(window.navigator.userAgent);\n   */ static parse(UA) {\n        return new _parser_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](UA).getResult();\n    }\n    static get BROWSER_MAP() {\n        return _constants_js__WEBPACK_IMPORTED_MODULE_1__.BROWSER_MAP;\n    }\n    static get ENGINE_MAP() {\n        return _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP;\n    }\n    static get OS_MAP() {\n        return _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP;\n    }\n    static get PLATFORMS_MAP() {\n        return _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Bowser);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/bowser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/constants.js":
/*!**********************************************!*\
  !*** ./node_modules/bowser/src/constants.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BROWSER_ALIASES_MAP: () => (/* binding */ BROWSER_ALIASES_MAP),\n/* harmony export */   BROWSER_MAP: () => (/* binding */ BROWSER_MAP),\n/* harmony export */   ENGINE_MAP: () => (/* binding */ ENGINE_MAP),\n/* harmony export */   OS_MAP: () => (/* binding */ OS_MAP),\n/* harmony export */   PLATFORMS_MAP: () => (/* binding */ PLATFORMS_MAP)\n/* harmony export */ });\n// NOTE: this list must be up-to-date with browsers listed in\n// test/acceptance/useragentstrings.yml\nconst BROWSER_ALIASES_MAP = {\n    \"Amazon Silk\": \"amazon_silk\",\n    \"Android Browser\": \"android\",\n    Bada: \"bada\",\n    BlackBerry: \"blackberry\",\n    Chrome: \"chrome\",\n    Chromium: \"chromium\",\n    Electron: \"electron\",\n    Epiphany: \"epiphany\",\n    Firefox: \"firefox\",\n    Focus: \"focus\",\n    Generic: \"generic\",\n    \"Google Search\": \"google_search\",\n    Googlebot: \"googlebot\",\n    \"Internet Explorer\": \"ie\",\n    \"K-Meleon\": \"k_meleon\",\n    Maxthon: \"maxthon\",\n    \"Microsoft Edge\": \"edge\",\n    \"MZ Browser\": \"mz\",\n    \"NAVER Whale Browser\": \"naver\",\n    Opera: \"opera\",\n    \"Opera Coast\": \"opera_coast\",\n    \"Pale Moon\": \"pale_moon\",\n    PhantomJS: \"phantomjs\",\n    Puffin: \"puffin\",\n    QupZilla: \"qupzilla\",\n    QQ: \"qq\",\n    QQLite: \"qqlite\",\n    Safari: \"safari\",\n    Sailfish: \"sailfish\",\n    \"Samsung Internet for Android\": \"samsung_internet\",\n    SeaMonkey: \"seamonkey\",\n    Sleipnir: \"sleipnir\",\n    Swing: \"swing\",\n    Tizen: \"tizen\",\n    \"UC Browser\": \"uc\",\n    Vivaldi: \"vivaldi\",\n    \"WebOS Browser\": \"webos\",\n    WeChat: \"wechat\",\n    \"Yandex Browser\": \"yandex\",\n    Roku: \"roku\"\n};\nconst BROWSER_MAP = {\n    amazon_silk: \"Amazon Silk\",\n    android: \"Android Browser\",\n    bada: \"Bada\",\n    blackberry: \"BlackBerry\",\n    chrome: \"Chrome\",\n    chromium: \"Chromium\",\n    electron: \"Electron\",\n    epiphany: \"Epiphany\",\n    firefox: \"Firefox\",\n    focus: \"Focus\",\n    generic: \"Generic\",\n    googlebot: \"Googlebot\",\n    google_search: \"Google Search\",\n    ie: \"Internet Explorer\",\n    k_meleon: \"K-Meleon\",\n    maxthon: \"Maxthon\",\n    edge: \"Microsoft Edge\",\n    mz: \"MZ Browser\",\n    naver: \"NAVER Whale Browser\",\n    opera: \"Opera\",\n    opera_coast: \"Opera Coast\",\n    pale_moon: \"Pale Moon\",\n    phantomjs: \"PhantomJS\",\n    puffin: \"Puffin\",\n    qupzilla: \"QupZilla\",\n    qq: \"QQ Browser\",\n    qqlite: \"QQ Browser Lite\",\n    safari: \"Safari\",\n    sailfish: \"Sailfish\",\n    samsung_internet: \"Samsung Internet for Android\",\n    seamonkey: \"SeaMonkey\",\n    sleipnir: \"Sleipnir\",\n    swing: \"Swing\",\n    tizen: \"Tizen\",\n    uc: \"UC Browser\",\n    vivaldi: \"Vivaldi\",\n    webos: \"WebOS Browser\",\n    wechat: \"WeChat\",\n    yandex: \"Yandex Browser\"\n};\nconst PLATFORMS_MAP = {\n    tablet: \"tablet\",\n    mobile: \"mobile\",\n    desktop: \"desktop\",\n    tv: \"tv\",\n    bot: \"bot\"\n};\nconst OS_MAP = {\n    WindowsPhone: \"Windows Phone\",\n    Windows: \"Windows\",\n    MacOS: \"macOS\",\n    iOS: \"iOS\",\n    Android: \"Android\",\n    WebOS: \"WebOS\",\n    BlackBerry: \"BlackBerry\",\n    Bada: \"Bada\",\n    Tizen: \"Tizen\",\n    Linux: \"Linux\",\n    ChromeOS: \"Chrome OS\",\n    PlayStation4: \"PlayStation 4\",\n    Roku: \"Roku\"\n};\nconst ENGINE_MAP = {\n    EdgeHTML: \"EdgeHTML\",\n    Blink: \"Blink\",\n    Trident: \"Trident\",\n    Presto: \"Presto\",\n    Gecko: \"Gecko\",\n    WebKit: \"WebKit\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/parser-browsers.js":
/*!****************************************************!*\
  !*** ./node_modules/bowser/src/parser-browsers.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/bowser/src/utils.js\");\n/**\n * Browsers' descriptors\n *\n * The idea of descriptors is simple. You should know about them two simple things:\n * 1. Every descriptor has a method or property called `test` and a `describe` method.\n * 2. Order of descriptors is important.\n *\n * More details:\n * 1. Method or property `test` serves as a way to detect whether the UA string\n * matches some certain browser or not. The `describe` method helps to make a result\n * object with params that show some browser-specific things: name, version, etc.\n * 2. Order of descriptors is important because a Parser goes through them one by one\n * in course. For example, if you insert Chrome's descriptor as the first one,\n * more then a half of browsers will be described as Chrome, because they will pass\n * the Chrome descriptor's test.\n *\n * Descriptor's `test` could be a property with an array of RegExps, where every RegExp\n * will be applied to a UA string to test it whether it matches or not.\n * If a descriptor has two or more regexps in the `test` array it tests them one by one\n * with a logical sum operation. Parser stops if it has found any RegExp that matches the UA.\n *\n * Or `test` could be a method. In that case it gets a Parser instance and should\n * return true/false to get the Parser know if this browser descriptor matches the UA or not.\n */ \nconst commonVersionIdentifier = /version\\/(\\d+(\\.?_?\\d+)+)/i;\nconst browsersList = [\n    /* Googlebot */ {\n        test: [\n            /googlebot/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Googlebot\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/googlebot\\/(\\d+(\\.\\d+))/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    /* Opera < 13.0 */ {\n        test: [\n            /opera/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Opera\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:opera)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    /* Opera > 13.0 */ {\n        test: [\n            /opr\\/|opios/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Opera\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:opr|opios)[\\s/](\\S+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /SamsungBrowser/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Samsung Internet for Android\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:SamsungBrowser)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /Whale/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"NAVER Whale Browser\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:whale)[\\s/](\\d+(?:\\.\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /PaleMoon/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Pale Moon\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:PaleMoon)[\\s/](\\d+(?:\\.\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /MZBrowser/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"MZ Browser\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:MZBrowser)[\\s/](\\d+(?:\\.\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /focus/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Focus\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:focus)[\\s/](\\d+(?:\\.\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /swing/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Swing\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:swing)[\\s/](\\d+(?:\\.\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /coast/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Opera Coast\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:coast)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /opt\\/\\d+(?:.?_?\\d+)+/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Opera Touch\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:opt)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /yabrowser/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Yandex Browser\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:yabrowser)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /ucbrowser/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"UC Browser\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:ucbrowser)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /Maxthon|mxios/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Maxthon\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:Maxthon|mxios)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /epiphany/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Epiphany\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:epiphany)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /puffin/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Puffin\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:puffin)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /sleipnir/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Sleipnir\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:sleipnir)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /k-meleon/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"K-Meleon\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:k-meleon)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /micromessenger/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"WeChat\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:micromessenger)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /qqbrowser/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: /qqbrowserlite/i.test(ua) ? \"QQ Browser Lite\" : \"QQ Browser\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /msie|trident/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Internet Explorer\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:msie |rv:)(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /\\sedg\\//i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Microsoft Edge\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/\\sedg\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /edg([ea]|ios)/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Microsoft Edge\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getSecondMatch(/edg([ea]|ios)\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /vivaldi/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Vivaldi\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/vivaldi\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /seamonkey/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"SeaMonkey\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/seamonkey\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /sailfish/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Sailfish\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/sailfish\\s?browser\\/(\\d+(\\.\\d+)?)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /silk/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Amazon Silk\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/silk\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /phantom/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"PhantomJS\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/phantomjs\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /slimerjs/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"SlimerJS\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/slimerjs\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /blackberry|\\bbb\\d+/i,\n            /rim\\stablet/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"BlackBerry\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/blackberry[\\d]+\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /(web|hpw)[o0]s/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"WebOS Browser\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/w(?:eb)?[o0]sbrowser\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /bada/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Bada\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/dolfin\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /tizen/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Tizen\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:tizen\\s?)?browser\\/(\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /qupzilla/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"QupZilla\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:qupzilla)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /firefox|iceweasel|fxios/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Firefox\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:firefox|iceweasel|fxios)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /electron/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Electron\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:electron)\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /MiuiBrowser/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Miui\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:MiuiBrowser)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /chromium/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Chromium\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:chromium)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /chrome|crios|crmo/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Chrome\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:chrome|crios|crmo)\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    {\n        test: [\n            /GSA/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Google Search\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:GSA)\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    /* Android Browser */ {\n        test (parser) {\n            const notLikeAndroid = !parser.test(/like android/i);\n            const butAndroid = parser.test(/android/i);\n            return notLikeAndroid && butAndroid;\n        },\n        describe (ua) {\n            const browser = {\n                name: \"Android Browser\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    /* PlayStation 4 */ {\n        test: [\n            /playstation 4/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"PlayStation 4\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    /* Safari */ {\n        test: [\n            /safari|applewebkit/i\n        ],\n        describe (ua) {\n            const browser = {\n                name: \"Safari\"\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n            if (version) {\n                browser.version = version;\n            }\n            return browser;\n        }\n    },\n    /* Something else */ {\n        test: [\n            /.*/i\n        ],\n        describe (ua) {\n            /* Here we try to make sure that there are explicit details about the device\n       * in order to decide what regexp exactly we want to apply\n       * (as there is a specific decision based on that conclusion)\n       */ const regexpWithoutDeviceSpec = /^(.*)\\/(.*) /;\n            const regexpWithDeviceSpec = /^(.*)\\/(.*)[ \\t]\\((.*)/;\n            const hasDeviceSpec = ua.search(\"\\\\(\") !== -1;\n            const regexp = hasDeviceSpec ? regexpWithDeviceSpec : regexpWithoutDeviceSpec;\n            return {\n                name: _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(regexp, ua),\n                version: _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getSecondMatch(regexp, ua)\n            };\n        }\n    }\n];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (browsersList);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/parser-browsers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/parser-engines.js":
/*!***************************************************!*\
  !*** ./node_modules/bowser/src/parser-engines.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/bowser/src/utils.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/bowser/src/constants.js\");\n\n\n/*\n * More specific goes first\n */ /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n    /* EdgeHTML */ {\n        test (parser) {\n            return parser.getBrowserName(true) === \"microsoft edge\";\n        },\n        describe (ua) {\n            const isBlinkBased = /\\sedg\\//i.test(ua);\n            // return blink if it's blink-based one\n            if (isBlinkBased) {\n                return {\n                    name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.Blink\n                };\n            }\n            // otherwise match the version and return EdgeHTML\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/edge\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            return {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.EdgeHTML,\n                version\n            };\n        }\n    },\n    /* Trident */ {\n        test: [\n            /trident/i\n        ],\n        describe (ua) {\n            const engine = {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.Trident\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/trident\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                engine.version = version;\n            }\n            return engine;\n        }\n    },\n    /* Presto */ {\n        test (parser) {\n            return parser.test(/presto/i);\n        },\n        describe (ua) {\n            const engine = {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.Presto\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/presto\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                engine.version = version;\n            }\n            return engine;\n        }\n    },\n    /* Gecko */ {\n        test (parser) {\n            const isGecko = parser.test(/gecko/i);\n            const likeGecko = parser.test(/like gecko/i);\n            return isGecko && !likeGecko;\n        },\n        describe (ua) {\n            const engine = {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.Gecko\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/gecko\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                engine.version = version;\n            }\n            return engine;\n        }\n    },\n    /* Blink */ {\n        test: [\n            /(apple)?webkit\\/537\\.36/i\n        ],\n        describe () {\n            return {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.Blink\n            };\n        }\n    },\n    /* WebKit */ {\n        test: [\n            /(apple)?webkit/i\n        ],\n        describe (ua) {\n            const engine = {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.WebKit\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/webkit\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n            if (version) {\n                engine.version = version;\n            }\n            return engine;\n        }\n    }\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/parser-engines.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/parser-os.js":
/*!**********************************************!*\
  !*** ./node_modules/bowser/src/parser-os.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/bowser/src/utils.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/bowser/src/constants.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n    /* Roku */ {\n        test: [\n            /Roku\\/DVP/\n        ],\n        describe (ua) {\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/Roku\\/DVP-(\\d+\\.\\d+)/i, ua);\n            return {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.Roku,\n                version\n            };\n        }\n    },\n    /* Windows Phone */ {\n        test: [\n            /windows phone/i\n        ],\n        describe (ua) {\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/windows phone (?:os)?\\s?(\\d+(\\.\\d+)*)/i, ua);\n            return {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.WindowsPhone,\n                version\n            };\n        }\n    },\n    /* Windows */ {\n        test: [\n            /windows /i\n        ],\n        describe (ua) {\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/Windows ((NT|XP)( \\d\\d?.\\d)?)/i, ua);\n            const versionName = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getWindowsVersionName(version);\n            return {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.Windows,\n                version,\n                versionName\n            };\n        }\n    },\n    /* Firefox on iPad */ {\n        test: [\n            /Macintosh(.*?) FxiOS(.*?)\\//\n        ],\n        describe (ua) {\n            const result = {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.iOS\n            };\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getSecondMatch(/(Version\\/)(\\d[\\d.]+)/, ua);\n            if (version) {\n                result.version = version;\n            }\n            return result;\n        }\n    },\n    /* macOS */ {\n        test: [\n            /macintosh/i\n        ],\n        describe (ua) {\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/mac os x (\\d+(\\.?_?\\d+)+)/i, ua).replace(/[_\\s]/g, \".\");\n            const versionName = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMacOSVersionName(version);\n            const os = {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.MacOS,\n                version\n            };\n            if (versionName) {\n                os.versionName = versionName;\n            }\n            return os;\n        }\n    },\n    /* iOS */ {\n        test: [\n            /(ipod|iphone|ipad)/i\n        ],\n        describe (ua) {\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/os (\\d+([_\\s]\\d+)*) like mac os x/i, ua).replace(/[_\\s]/g, \".\");\n            return {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.iOS,\n                version\n            };\n        }\n    },\n    /* Android */ {\n        test (parser) {\n            const notLikeAndroid = !parser.test(/like android/i);\n            const butAndroid = parser.test(/android/i);\n            return notLikeAndroid && butAndroid;\n        },\n        describe (ua) {\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/android[\\s/-](\\d+(\\.\\d+)*)/i, ua);\n            const versionName = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getAndroidVersionName(version);\n            const os = {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.Android,\n                version\n            };\n            if (versionName) {\n                os.versionName = versionName;\n            }\n            return os;\n        }\n    },\n    /* WebOS */ {\n        test: [\n            /(web|hpw)[o0]s/i\n        ],\n        describe (ua) {\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:web|hpw)[o0]s\\/(\\d+(\\.\\d+)*)/i, ua);\n            const os = {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.WebOS\n            };\n            if (version && version.length) {\n                os.version = version;\n            }\n            return os;\n        }\n    },\n    /* BlackBerry */ {\n        test: [\n            /blackberry|\\bbb\\d+/i,\n            /rim\\stablet/i\n        ],\n        describe (ua) {\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/rim\\stablet\\sos\\s(\\d+(\\.\\d+)*)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/blackberry\\d+\\/(\\d+([_\\s]\\d+)*)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/\\bbb(\\d+)/i, ua);\n            return {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.BlackBerry,\n                version\n            };\n        }\n    },\n    /* Bada */ {\n        test: [\n            /bada/i\n        ],\n        describe (ua) {\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/bada\\/(\\d+(\\.\\d+)*)/i, ua);\n            return {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.Bada,\n                version\n            };\n        }\n    },\n    /* Tizen */ {\n        test: [\n            /tizen/i\n        ],\n        describe (ua) {\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/tizen[/\\s](\\d+(\\.\\d+)*)/i, ua);\n            return {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.Tizen,\n                version\n            };\n        }\n    },\n    /* Linux */ {\n        test: [\n            /linux/i\n        ],\n        describe () {\n            return {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.Linux\n            };\n        }\n    },\n    /* Chrome OS */ {\n        test: [\n            /CrOS/\n        ],\n        describe () {\n            return {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.ChromeOS\n            };\n        }\n    },\n    /* Playstation 4 */ {\n        test: [\n            /PlayStation 4/\n        ],\n        describe (ua) {\n            const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/PlayStation 4[/\\s](\\d+(\\.\\d+)*)/i, ua);\n            return {\n                name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.PlayStation4,\n                version\n            };\n        }\n    }\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/parser-os.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/parser-platforms.js":
/*!*****************************************************!*\
  !*** ./node_modules/bowser/src/parser-platforms.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/bowser/src/utils.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/bowser/src/constants.js\");\n\n\n/*\n * Tablets go first since usually they have more specific\n * signs to detect.\n */ /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n    /* Googlebot */ {\n        test: [\n            /googlebot/i\n        ],\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.bot,\n                vendor: \"Google\"\n            };\n        }\n    },\n    /* Huawei */ {\n        test: [\n            /huawei/i\n        ],\n        describe (ua) {\n            const model = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(can-l01)/i, ua) && \"Nova\";\n            const platform = {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n                vendor: \"Huawei\"\n            };\n            if (model) {\n                platform.model = model;\n            }\n            return platform;\n        }\n    },\n    /* Nexus Tablet */ {\n        test: [\n            /nexus\\s*(?:7|8|9|10).*/i\n        ],\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet,\n                vendor: \"Nexus\"\n            };\n        }\n    },\n    /* iPad */ {\n        test: [\n            /ipad/i\n        ],\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet,\n                vendor: \"Apple\",\n                model: \"iPad\"\n            };\n        }\n    },\n    /* Firefox on iPad */ {\n        test: [\n            /Macintosh(.*?) FxiOS(.*?)\\//\n        ],\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet,\n                vendor: \"Apple\",\n                model: \"iPad\"\n            };\n        }\n    },\n    /* Amazon Kindle Fire */ {\n        test: [\n            /kftt build/i\n        ],\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet,\n                vendor: \"Amazon\",\n                model: \"Kindle Fire HD 7\"\n            };\n        }\n    },\n    /* Another Amazon Tablet with Silk */ {\n        test: [\n            /silk/i\n        ],\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet,\n                vendor: \"Amazon\"\n            };\n        }\n    },\n    /* Tablet */ {\n        test: [\n            /tablet(?! pc)/i\n        ],\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet\n            };\n        }\n    },\n    /* iPod/iPhone */ {\n        test (parser) {\n            const iDevice = parser.test(/ipod|iphone/i);\n            const likeIDevice = parser.test(/like (ipod|iphone)/i);\n            return iDevice && !likeIDevice;\n        },\n        describe (ua) {\n            const model = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(ipod|iphone)/i, ua);\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n                vendor: \"Apple\",\n                model\n            };\n        }\n    },\n    /* Nexus Mobile */ {\n        test: [\n            /nexus\\s*[0-6].*/i,\n            /galaxy nexus/i\n        ],\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n                vendor: \"Nexus\"\n            };\n        }\n    },\n    /* Nokia */ {\n        test: [\n            /Nokia/i\n        ],\n        describe (ua) {\n            const model = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/Nokia\\s+([0-9]+(\\.[0-9]+)?)/i, ua);\n            const platform = {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n                vendor: \"Nokia\"\n            };\n            if (model) {\n                platform.model = model;\n            }\n            return platform;\n        }\n    },\n    /* Mobile */ {\n        test: [\n            /[^-]mobi/i\n        ],\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile\n            };\n        }\n    },\n    /* BlackBerry */ {\n        test (parser) {\n            return parser.getBrowserName(true) === \"blackberry\";\n        },\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n                vendor: \"BlackBerry\"\n            };\n        }\n    },\n    /* Bada */ {\n        test (parser) {\n            return parser.getBrowserName(true) === \"bada\";\n        },\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile\n            };\n        }\n    },\n    /* Windows Phone */ {\n        test (parser) {\n            return parser.getBrowserName() === \"windows phone\";\n        },\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n                vendor: \"Microsoft\"\n            };\n        }\n    },\n    /* Android Tablet */ {\n        test (parser) {\n            const osMajorVersion = Number(String(parser.getOSVersion()).split(\".\")[0]);\n            return parser.getOSName(true) === \"android\" && osMajorVersion >= 3;\n        },\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet\n            };\n        }\n    },\n    /* Android Mobile */ {\n        test (parser) {\n            return parser.getOSName(true) === \"android\";\n        },\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile\n            };\n        }\n    },\n    /* desktop */ {\n        test (parser) {\n            return parser.getOSName(true) === \"macos\";\n        },\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.desktop,\n                vendor: \"Apple\"\n            };\n        }\n    },\n    /* Windows */ {\n        test (parser) {\n            return parser.getOSName(true) === \"windows\";\n        },\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.desktop\n            };\n        }\n    },\n    /* Linux */ {\n        test (parser) {\n            return parser.getOSName(true) === \"linux\";\n        },\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.desktop\n            };\n        }\n    },\n    /* PlayStation 4 */ {\n        test (parser) {\n            return parser.getOSName(true) === \"playstation 4\";\n        },\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tv\n            };\n        }\n    },\n    /* Roku */ {\n        test (parser) {\n            return parser.getOSName(true) === \"roku\";\n        },\n        describe () {\n            return {\n                type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tv\n            };\n        }\n    }\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/parser-platforms.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/parser.js":
/*!*******************************************!*\
  !*** ./node_modules/bowser/src/parser.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _parser_browsers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parser-browsers.js */ \"(ssr)/./node_modules/bowser/src/parser-browsers.js\");\n/* harmony import */ var _parser_os_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parser-os.js */ \"(ssr)/./node_modules/bowser/src/parser-os.js\");\n/* harmony import */ var _parser_platforms_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./parser-platforms.js */ \"(ssr)/./node_modules/bowser/src/parser-platforms.js\");\n/* harmony import */ var _parser_engines_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./parser-engines.js */ \"(ssr)/./node_modules/bowser/src/parser-engines.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/bowser/src/utils.js\");\n\n\n\n\n\n/**\n * The main class that arranges the whole parsing process.\n */ class Parser {\n    /**\n   * Create instance of Parser\n   *\n   * @param {String} UA User-Agent string\n   * @param {Boolean} [skipParsing=false] parser can skip parsing in purpose of performance\n   * improvements if you need to make a more particular parsing\n   * like {@link Parser#parseBrowser} or {@link Parser#parsePlatform}\n   *\n   * @throw {Error} in case of empty UA String\n   *\n   * @constructor\n   */ constructor(UA, skipParsing = false){\n        if (UA === void 0 || UA === null || UA === \"\") {\n            throw new Error(\"UserAgent parameter can't be empty\");\n        }\n        this._ua = UA;\n        /**\n     * @typedef ParsedResult\n     * @property {Object} browser\n     * @property {String|undefined} [browser.name]\n     * Browser name, like `\"Chrome\"` or `\"Internet Explorer\"`\n     * @property {String|undefined} [browser.version] Browser version as a String `\"12.01.45334.10\"`\n     * @property {Object} os\n     * @property {String|undefined} [os.name] OS name, like `\"Windows\"` or `\"macOS\"`\n     * @property {String|undefined} [os.version] OS version, like `\"NT 5.1\"` or `\"10.11.1\"`\n     * @property {String|undefined} [os.versionName] OS name, like `\"XP\"` or `\"High Sierra\"`\n     * @property {Object} platform\n     * @property {String|undefined} [platform.type]\n     * platform type, can be either `\"desktop\"`, `\"tablet\"` or `\"mobile\"`\n     * @property {String|undefined} [platform.vendor] Vendor of the device,\n     * like `\"Apple\"` or `\"Samsung\"`\n     * @property {String|undefined} [platform.model] Device model,\n     * like `\"iPhone\"` or `\"Kindle Fire HD 7\"`\n     * @property {Object} engine\n     * @property {String|undefined} [engine.name]\n     * Can be any of this: `WebKit`, `Blink`, `Gecko`, `Trident`, `Presto`, `EdgeHTML`\n     * @property {String|undefined} [engine.version] String version of the engine\n     */ this.parsedResult = {};\n        if (skipParsing !== true) {\n            this.parse();\n        }\n    }\n    /**\n   * Get UserAgent string of current Parser instance\n   * @return {String} User-Agent String of the current <Parser> object\n   *\n   * @public\n   */ getUA() {\n        return this._ua;\n    }\n    /**\n   * Test a UA string for a regexp\n   * @param {RegExp} regex\n   * @return {Boolean}\n   */ test(regex) {\n        return regex.test(this._ua);\n    }\n    /**\n   * Get parsed browser object\n   * @return {Object}\n   */ parseBrowser() {\n        this.parsedResult.browser = {};\n        const browserDescriptor = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(_parser_browsers_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], (_browser)=>{\n            if (typeof _browser.test === \"function\") {\n                return _browser.test(this);\n            }\n            if (Array.isArray(_browser.test)) {\n                return _browser.test.some((condition)=>this.test(condition));\n            }\n            throw new Error(\"Browser's test function is not valid\");\n        });\n        if (browserDescriptor) {\n            this.parsedResult.browser = browserDescriptor.describe(this.getUA());\n        }\n        return this.parsedResult.browser;\n    }\n    /**\n   * Get parsed browser object\n   * @return {Object}\n   *\n   * @public\n   */ getBrowser() {\n        if (this.parsedResult.browser) {\n            return this.parsedResult.browser;\n        }\n        return this.parseBrowser();\n    }\n    /**\n   * Get browser's name\n   * @return {String} Browser's name or an empty string\n   *\n   * @public\n   */ getBrowserName(toLowerCase) {\n        if (toLowerCase) {\n            return String(this.getBrowser().name).toLowerCase() || \"\";\n        }\n        return this.getBrowser().name || \"\";\n    }\n    /**\n   * Get browser's version\n   * @return {String} version of browser\n   *\n   * @public\n   */ getBrowserVersion() {\n        return this.getBrowser().version;\n    }\n    /**\n   * Get OS\n   * @return {Object}\n   *\n   * @example\n   * this.getOS();\n   * {\n   *   name: 'macOS',\n   *   version: '10.11.12'\n   * }\n   */ getOS() {\n        if (this.parsedResult.os) {\n            return this.parsedResult.os;\n        }\n        return this.parseOS();\n    }\n    /**\n   * Parse OS and save it to this.parsedResult.os\n   * @return {*|{}}\n   */ parseOS() {\n        this.parsedResult.os = {};\n        const os = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(_parser_os_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], (_os)=>{\n            if (typeof _os.test === \"function\") {\n                return _os.test(this);\n            }\n            if (Array.isArray(_os.test)) {\n                return _os.test.some((condition)=>this.test(condition));\n            }\n            throw new Error(\"Browser's test function is not valid\");\n        });\n        if (os) {\n            this.parsedResult.os = os.describe(this.getUA());\n        }\n        return this.parsedResult.os;\n    }\n    /**\n   * Get OS name\n   * @param {Boolean} [toLowerCase] return lower-cased value\n   * @return {String} name of the OS — macOS, Windows, Linux, etc.\n   */ getOSName(toLowerCase) {\n        const { name } = this.getOS();\n        if (toLowerCase) {\n            return String(name).toLowerCase() || \"\";\n        }\n        return name || \"\";\n    }\n    /**\n   * Get OS version\n   * @return {String} full version with dots ('10.11.12', '5.6', etc)\n   */ getOSVersion() {\n        return this.getOS().version;\n    }\n    /**\n   * Get parsed platform\n   * @return {{}}\n   */ getPlatform() {\n        if (this.parsedResult.platform) {\n            return this.parsedResult.platform;\n        }\n        return this.parsePlatform();\n    }\n    /**\n   * Get platform name\n   * @param {Boolean} [toLowerCase=false]\n   * @return {*}\n   */ getPlatformType(toLowerCase = false) {\n        const { type } = this.getPlatform();\n        if (toLowerCase) {\n            return String(type).toLowerCase() || \"\";\n        }\n        return type || \"\";\n    }\n    /**\n   * Get parsed platform\n   * @return {{}}\n   */ parsePlatform() {\n        this.parsedResult.platform = {};\n        const platform = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(_parser_platforms_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"], (_platform)=>{\n            if (typeof _platform.test === \"function\") {\n                return _platform.test(this);\n            }\n            if (Array.isArray(_platform.test)) {\n                return _platform.test.some((condition)=>this.test(condition));\n            }\n            throw new Error(\"Browser's test function is not valid\");\n        });\n        if (platform) {\n            this.parsedResult.platform = platform.describe(this.getUA());\n        }\n        return this.parsedResult.platform;\n    }\n    /**\n   * Get parsed engine\n   * @return {{}}\n   */ getEngine() {\n        if (this.parsedResult.engine) {\n            return this.parsedResult.engine;\n        }\n        return this.parseEngine();\n    }\n    /**\n   * Get engines's name\n   * @return {String} Engines's name or an empty string\n   *\n   * @public\n   */ getEngineName(toLowerCase) {\n        if (toLowerCase) {\n            return String(this.getEngine().name).toLowerCase() || \"\";\n        }\n        return this.getEngine().name || \"\";\n    }\n    /**\n   * Get parsed platform\n   * @return {{}}\n   */ parseEngine() {\n        this.parsedResult.engine = {};\n        const engine = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(_parser_engines_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], (_engine)=>{\n            if (typeof _engine.test === \"function\") {\n                return _engine.test(this);\n            }\n            if (Array.isArray(_engine.test)) {\n                return _engine.test.some((condition)=>this.test(condition));\n            }\n            throw new Error(\"Browser's test function is not valid\");\n        });\n        if (engine) {\n            this.parsedResult.engine = engine.describe(this.getUA());\n        }\n        return this.parsedResult.engine;\n    }\n    /**\n   * Parse full information about the browser\n   * @returns {Parser}\n   */ parse() {\n        this.parseBrowser();\n        this.parseOS();\n        this.parsePlatform();\n        this.parseEngine();\n        return this;\n    }\n    /**\n   * Get parsed result\n   * @return {ParsedResult}\n   */ getResult() {\n        return _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].assign({}, this.parsedResult);\n    }\n    /**\n   * Check if parsed browser matches certain conditions\n   *\n   * @param {Object} checkTree It's one or two layered object,\n   * which can include a platform or an OS on the first layer\n   * and should have browsers specs on the bottom-laying layer\n   *\n   * @returns {Boolean|undefined} Whether the browser satisfies the set conditions or not.\n   * Returns `undefined` when the browser is no described in the checkTree object.\n   *\n   * @example\n   * const browser = Bowser.getParser(window.navigator.userAgent);\n   * if (browser.satisfies({chrome: '>118.01.1322' }))\n   * // or with os\n   * if (browser.satisfies({windows: { chrome: '>118.01.1322' } }))\n   * // or with platforms\n   * if (browser.satisfies({desktop: { chrome: '>118.01.1322' } }))\n   */ satisfies(checkTree) {\n        const platformsAndOSes = {};\n        let platformsAndOSCounter = 0;\n        const browsers = {};\n        let browsersCounter = 0;\n        const allDefinitions = Object.keys(checkTree);\n        allDefinitions.forEach((key)=>{\n            const currentDefinition = checkTree[key];\n            if (typeof currentDefinition === \"string\") {\n                browsers[key] = currentDefinition;\n                browsersCounter += 1;\n            } else if (typeof currentDefinition === \"object\") {\n                platformsAndOSes[key] = currentDefinition;\n                platformsAndOSCounter += 1;\n            }\n        });\n        if (platformsAndOSCounter > 0) {\n            const platformsAndOSNames = Object.keys(platformsAndOSes);\n            const OSMatchingDefinition = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(platformsAndOSNames, (name)=>this.isOS(name));\n            if (OSMatchingDefinition) {\n                const osResult = this.satisfies(platformsAndOSes[OSMatchingDefinition]);\n                if (osResult !== void 0) {\n                    return osResult;\n                }\n            }\n            const platformMatchingDefinition = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(platformsAndOSNames, (name)=>this.isPlatform(name));\n            if (platformMatchingDefinition) {\n                const platformResult = this.satisfies(platformsAndOSes[platformMatchingDefinition]);\n                if (platformResult !== void 0) {\n                    return platformResult;\n                }\n            }\n        }\n        if (browsersCounter > 0) {\n            const browserNames = Object.keys(browsers);\n            const matchingDefinition = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(browserNames, (name)=>this.isBrowser(name, true));\n            if (matchingDefinition !== void 0) {\n                return this.compareVersion(browsers[matchingDefinition]);\n            }\n        }\n        return undefined;\n    }\n    /**\n   * Check if the browser name equals the passed string\n   * @param {string} browserName The string to compare with the browser name\n   * @param [includingAlias=false] The flag showing whether alias will be included into comparison\n   * @returns {boolean}\n   */ isBrowser(browserName, includingAlias = false) {\n        const defaultBrowserName = this.getBrowserName().toLowerCase();\n        let browserNameLower = browserName.toLowerCase();\n        const alias = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getBrowserTypeByAlias(browserNameLower);\n        if (includingAlias && alias) {\n            browserNameLower = alias.toLowerCase();\n        }\n        return browserNameLower === defaultBrowserName;\n    }\n    compareVersion(version) {\n        let expectedResults = [\n            0\n        ];\n        let comparableVersion = version;\n        let isLoose = false;\n        const currentBrowserVersion = this.getBrowserVersion();\n        if (typeof currentBrowserVersion !== \"string\") {\n            return void 0;\n        }\n        if (version[0] === \">\" || version[0] === \"<\") {\n            comparableVersion = version.substr(1);\n            if (version[1] === \"=\") {\n                isLoose = true;\n                comparableVersion = version.substr(2);\n            } else {\n                expectedResults = [];\n            }\n            if (version[0] === \">\") {\n                expectedResults.push(1);\n            } else {\n                expectedResults.push(-1);\n            }\n        } else if (version[0] === \"=\") {\n            comparableVersion = version.substr(1);\n        } else if (version[0] === \"~\") {\n            isLoose = true;\n            comparableVersion = version.substr(1);\n        }\n        return expectedResults.indexOf(_utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compareVersions(currentBrowserVersion, comparableVersion, isLoose)) > -1;\n    }\n    /**\n   * Check if the OS name equals the passed string\n   * @param {string} osName The string to compare with the OS name\n   * @returns {boolean}\n   */ isOS(osName) {\n        return this.getOSName(true) === String(osName).toLowerCase();\n    }\n    /**\n   * Check if the platform type equals the passed string\n   * @param {string} platformType The string to compare with the platform type\n   * @returns {boolean}\n   */ isPlatform(platformType) {\n        return this.getPlatformType(true) === String(platformType).toLowerCase();\n    }\n    /**\n   * Check if the engine name equals the passed string\n   * @param {string} engineName The string to compare with the engine name\n   * @returns {boolean}\n   */ isEngine(engineName) {\n        return this.getEngineName(true) === String(engineName).toLowerCase();\n    }\n    /**\n   * Is anything? Check if the browser is called \"anything\",\n   * the OS called \"anything\" or the platform called \"anything\"\n   * @param {String} anything\n   * @param [includingAlias=false] The flag showing whether alias will be included into comparison\n   * @returns {Boolean}\n   */ is(anything, includingAlias = false) {\n        return this.isBrowser(anything, includingAlias) || this.isOS(anything) || this.isPlatform(anything);\n    }\n    /**\n   * Check if any of the given values satisfies this.is(anything)\n   * @param {String[]} anythings\n   * @returns {Boolean}\n   */ some(anythings = []) {\n        return anythings.some((anything)=>this.is(anything));\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Parser);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/utils.js":
/*!******************************************!*\
  !*** ./node_modules/bowser/src/utils.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Utils)\n/* harmony export */ });\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/bowser/src/constants.js\");\n\nclass Utils {\n    /**\n   * Get first matched item for a string\n   * @param {RegExp} regexp\n   * @param {String} ua\n   * @return {Array|{index: number, input: string}|*|boolean|string}\n   */ static getFirstMatch(regexp, ua) {\n        const match = ua.match(regexp);\n        return match && match.length > 0 && match[1] || \"\";\n    }\n    /**\n   * Get second matched item for a string\n   * @param regexp\n   * @param {String} ua\n   * @return {Array|{index: number, input: string}|*|boolean|string}\n   */ static getSecondMatch(regexp, ua) {\n        const match = ua.match(regexp);\n        return match && match.length > 1 && match[2] || \"\";\n    }\n    /**\n   * Match a regexp and return a constant or undefined\n   * @param {RegExp} regexp\n   * @param {String} ua\n   * @param {*} _const Any const that will be returned if regexp matches the string\n   * @return {*}\n   */ static matchAndReturnConst(regexp, ua, _const) {\n        if (regexp.test(ua)) {\n            return _const;\n        }\n        return void 0;\n    }\n    static getWindowsVersionName(version) {\n        switch(version){\n            case \"NT\":\n                return \"NT\";\n            case \"XP\":\n                return \"XP\";\n            case \"NT 5.0\":\n                return \"2000\";\n            case \"NT 5.1\":\n                return \"XP\";\n            case \"NT 5.2\":\n                return \"2003\";\n            case \"NT 6.0\":\n                return \"Vista\";\n            case \"NT 6.1\":\n                return \"7\";\n            case \"NT 6.2\":\n                return \"8\";\n            case \"NT 6.3\":\n                return \"8.1\";\n            case \"NT 10.0\":\n                return \"10\";\n            default:\n                return undefined;\n        }\n    }\n    /**\n   * Get macOS version name\n   *    10.5 - Leopard\n   *    10.6 - Snow Leopard\n   *    10.7 - Lion\n   *    10.8 - Mountain Lion\n   *    10.9 - Mavericks\n   *    10.10 - Yosemite\n   *    10.11 - El Capitan\n   *    10.12 - Sierra\n   *    10.13 - High Sierra\n   *    10.14 - Mojave\n   *    10.15 - Catalina\n   *\n   * @example\n   *   getMacOSVersionName(\"10.14\") // 'Mojave'\n   *\n   * @param  {string} version\n   * @return {string} versionName\n   */ static getMacOSVersionName(version) {\n        const v = version.split(\".\").splice(0, 2).map((s)=>parseInt(s, 10) || 0);\n        v.push(0);\n        if (v[0] !== 10) return undefined;\n        switch(v[1]){\n            case 5:\n                return \"Leopard\";\n            case 6:\n                return \"Snow Leopard\";\n            case 7:\n                return \"Lion\";\n            case 8:\n                return \"Mountain Lion\";\n            case 9:\n                return \"Mavericks\";\n            case 10:\n                return \"Yosemite\";\n            case 11:\n                return \"El Capitan\";\n            case 12:\n                return \"Sierra\";\n            case 13:\n                return \"High Sierra\";\n            case 14:\n                return \"Mojave\";\n            case 15:\n                return \"Catalina\";\n            default:\n                return undefined;\n        }\n    }\n    /**\n   * Get Android version name\n   *    1.5 - Cupcake\n   *    1.6 - Donut\n   *    2.0 - Eclair\n   *    2.1 - Eclair\n   *    2.2 - Froyo\n   *    2.x - Gingerbread\n   *    3.x - Honeycomb\n   *    4.0 - Ice Cream Sandwich\n   *    4.1 - Jelly Bean\n   *    4.4 - KitKat\n   *    5.x - Lollipop\n   *    6.x - Marshmallow\n   *    7.x - Nougat\n   *    8.x - Oreo\n   *    9.x - Pie\n   *\n   * @example\n   *   getAndroidVersionName(\"7.0\") // 'Nougat'\n   *\n   * @param  {string} version\n   * @return {string} versionName\n   */ static getAndroidVersionName(version) {\n        const v = version.split(\".\").splice(0, 2).map((s)=>parseInt(s, 10) || 0);\n        v.push(0);\n        if (v[0] === 1 && v[1] < 5) return undefined;\n        if (v[0] === 1 && v[1] < 6) return \"Cupcake\";\n        if (v[0] === 1 && v[1] >= 6) return \"Donut\";\n        if (v[0] === 2 && v[1] < 2) return \"Eclair\";\n        if (v[0] === 2 && v[1] === 2) return \"Froyo\";\n        if (v[0] === 2 && v[1] > 2) return \"Gingerbread\";\n        if (v[0] === 3) return \"Honeycomb\";\n        if (v[0] === 4 && v[1] < 1) return \"Ice Cream Sandwich\";\n        if (v[0] === 4 && v[1] < 4) return \"Jelly Bean\";\n        if (v[0] === 4 && v[1] >= 4) return \"KitKat\";\n        if (v[0] === 5) return \"Lollipop\";\n        if (v[0] === 6) return \"Marshmallow\";\n        if (v[0] === 7) return \"Nougat\";\n        if (v[0] === 8) return \"Oreo\";\n        if (v[0] === 9) return \"Pie\";\n        return undefined;\n    }\n    /**\n   * Get version precisions count\n   *\n   * @example\n   *   getVersionPrecision(\"1.10.3\") // 3\n   *\n   * @param  {string} version\n   * @return {number}\n   */ static getVersionPrecision(version) {\n        return version.split(\".\").length;\n    }\n    /**\n   * Calculate browser version weight\n   *\n   * @example\n   *   compareVersions('********',  '*******.90')    // 1\n   *   compareVersions('*********', '********.90');  // 1\n   *   compareVersions('********',  '********');     // 0\n   *   compareVersions('********',  '1.0800.2');     // -1\n   *   compareVersions('********',  '1.10',  true);  // 0\n   *\n   * @param {String} versionA versions versions to compare\n   * @param {String} versionB versions versions to compare\n   * @param {boolean} [isLoose] enable loose comparison\n   * @return {Number} comparison result: -1 when versionA is lower,\n   * 1 when versionA is bigger, 0 when both equal\n   */ /* eslint consistent-return: 1 */ static compareVersions(versionA, versionB, isLoose = false) {\n        // 1) get common precision for both versions, for example for \"10.0\" and \"9\" it should be 2\n        const versionAPrecision = Utils.getVersionPrecision(versionA);\n        const versionBPrecision = Utils.getVersionPrecision(versionB);\n        let precision = Math.max(versionAPrecision, versionBPrecision);\n        let lastPrecision = 0;\n        const chunks = Utils.map([\n            versionA,\n            versionB\n        ], (version)=>{\n            const delta = precision - Utils.getVersionPrecision(version);\n            // 2) \"9\" -> \"9.0\" (for precision = 2)\n            const _version = version + new Array(delta + 1).join(\".0\");\n            // 3) \"9.0\" -> [\"000000000\"\", \"000000009\"]\n            return Utils.map(_version.split(\".\"), (chunk)=>new Array(20 - chunk.length).join(\"0\") + chunk).reverse();\n        });\n        // adjust precision for loose comparison\n        if (isLoose) {\n            lastPrecision = precision - Math.min(versionAPrecision, versionBPrecision);\n        }\n        // iterate in reverse order by reversed chunks array\n        precision -= 1;\n        while(precision >= lastPrecision){\n            // 4) compare: \"000000009\" > \"000000010\" = false (but \"9\" > \"10\" = true)\n            if (chunks[0][precision] > chunks[1][precision]) {\n                return 1;\n            }\n            if (chunks[0][precision] === chunks[1][precision]) {\n                if (precision === lastPrecision) {\n                    // all version chunks are same\n                    return 0;\n                }\n                precision -= 1;\n            } else if (chunks[0][precision] < chunks[1][precision]) {\n                return -1;\n            }\n        }\n        return undefined;\n    }\n    /**\n   * Array::map polyfill\n   *\n   * @param  {Array} arr\n   * @param  {Function} iterator\n   * @return {Array}\n   */ static map(arr, iterator) {\n        const result = [];\n        let i;\n        if (Array.prototype.map) {\n            return Array.prototype.map.call(arr, iterator);\n        }\n        for(i = 0; i < arr.length; i += 1){\n            result.push(iterator(arr[i]));\n        }\n        return result;\n    }\n    /**\n   * Array::find polyfill\n   *\n   * @param  {Array} arr\n   * @param  {Function} predicate\n   * @return {Array}\n   */ static find(arr, predicate) {\n        let i;\n        let l;\n        if (Array.prototype.find) {\n            return Array.prototype.find.call(arr, predicate);\n        }\n        for(i = 0, l = arr.length; i < l; i += 1){\n            const value = arr[i];\n            if (predicate(value, i)) {\n                return value;\n            }\n        }\n        return undefined;\n    }\n    /**\n   * Object::assign polyfill\n   *\n   * @param  {Object} obj\n   * @param  {Object} ...objs\n   * @return {Object}\n   */ static assign(obj, ...assigners) {\n        const result = obj;\n        let i;\n        let l;\n        if (Object.assign) {\n            return Object.assign(obj, ...assigners);\n        }\n        for(i = 0, l = assigners.length; i < l; i += 1){\n            const assigner = assigners[i];\n            if (typeof assigner === \"object\" && assigner !== null) {\n                const keys = Object.keys(assigner);\n                keys.forEach((key)=>{\n                    result[key] = assigner[key];\n                });\n            }\n        }\n        return obj;\n    }\n    /**\n   * Get short version/alias for a browser name\n   *\n   * @example\n   *   getBrowserAlias('Microsoft Edge') // edge\n   *\n   * @param  {string} browserName\n   * @return {string}\n   */ static getBrowserAlias(browserName) {\n        return _constants_js__WEBPACK_IMPORTED_MODULE_0__.BROWSER_ALIASES_MAP[browserName];\n    }\n    /**\n   * Get browser name for a short version/alias\n   *\n   * @example\n   *   getBrowserTypeByAlias('edge') // Microsoft Edge\n   *\n   * @param  {string} browserAlias\n   * @return {string}\n   */ static getBrowserTypeByAlias(browserAlias) {\n        return _constants_js__WEBPACK_IMPORTED_MODULE_0__.BROWSER_MAP[browserAlias] || \"\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/utils.js\n");

/***/ })

};
;