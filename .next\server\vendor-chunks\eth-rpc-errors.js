"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eth-rpc-errors";
exports.ids = ["vendor-chunks/eth-rpc-errors"];
exports.modules = {

/***/ "(ssr)/./node_modules/eth-rpc-errors/dist/classes.js":
/*!*****************************************************!*\
  !*** ./node_modules/eth-rpc-errors/dist/classes.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.EthereumProviderError = exports.EthereumRpcError = void 0;\nconst fast_safe_stringify_1 = __webpack_require__(/*! fast-safe-stringify */ \"(ssr)/./node_modules/fast-safe-stringify/index.js\");\n/**\n * Error subclass implementing JSON RPC 2.0 errors and Ethereum RPC errors\n * per EIP-1474.\n * Permits any integer error code.\n */ class EthereumRpcError extends Error {\n    constructor(code, message, data){\n        if (!Number.isInteger(code)) {\n            throw new Error('\"code\" must be an integer.');\n        }\n        if (!message || typeof message !== \"string\") {\n            throw new Error('\"message\" must be a nonempty string.');\n        }\n        super(message);\n        this.code = code;\n        if (data !== undefined) {\n            this.data = data;\n        }\n    }\n    /**\n     * Returns a plain object with all public class properties.\n     */ serialize() {\n        const serialized = {\n            code: this.code,\n            message: this.message\n        };\n        if (this.data !== undefined) {\n            serialized.data = this.data;\n        }\n        if (this.stack) {\n            serialized.stack = this.stack;\n        }\n        return serialized;\n    }\n    /**\n     * Return a string representation of the serialized error, omitting\n     * any circular references.\n     */ toString() {\n        return fast_safe_stringify_1.default(this.serialize(), stringifyReplacer, 2);\n    }\n}\nexports.EthereumRpcError = EthereumRpcError;\n/**\n * Error subclass implementing Ethereum Provider errors per EIP-1193.\n * Permits integer error codes in the [ 1000 <= 4999 ] range.\n */ class EthereumProviderError extends EthereumRpcError {\n    /**\n     * Create an Ethereum Provider JSON-RPC error.\n     * `code` must be an integer in the 1000 <= 4999 range.\n     */ constructor(code, message, data){\n        if (!isValidEthProviderCode(code)) {\n            throw new Error('\"code\" must be an integer such that: 1000 <= code <= 4999');\n        }\n        super(code, message, data);\n    }\n}\nexports.EthereumProviderError = EthereumProviderError;\n// Internal\nfunction isValidEthProviderCode(code) {\n    return Number.isInteger(code) && code >= 1000 && code <= 4999;\n}\nfunction stringifyReplacer(_, value) {\n    if (value === \"[Circular]\") {\n        return undefined;\n    }\n    return value;\n} //# sourceMappingURL=data:application/json;base64,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\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-rpc-errors/dist/classes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-rpc-errors/dist/error-constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/eth-rpc-errors/dist/error-constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.errorValues = exports.errorCodes = void 0;\nexports.errorCodes = {\n    rpc: {\n        invalidInput: -32000,\n        resourceNotFound: -32001,\n        resourceUnavailable: -32002,\n        transactionRejected: -32003,\n        methodNotSupported: -32004,\n        limitExceeded: -32005,\n        parse: -32700,\n        invalidRequest: -32600,\n        methodNotFound: -32601,\n        invalidParams: -32602,\n        internal: -32603\n    },\n    provider: {\n        userRejectedRequest: 4001,\n        unauthorized: 4100,\n        unsupportedMethod: 4200,\n        disconnected: 4900,\n        chainDisconnected: 4901\n    }\n};\nexports.errorValues = {\n    \"-32700\": {\n        standard: \"JSON RPC 2.0\",\n        message: \"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text.\"\n    },\n    \"-32600\": {\n        standard: \"JSON RPC 2.0\",\n        message: \"The JSON sent is not a valid Request object.\"\n    },\n    \"-32601\": {\n        standard: \"JSON RPC 2.0\",\n        message: \"The method does not exist / is not available.\"\n    },\n    \"-32602\": {\n        standard: \"JSON RPC 2.0\",\n        message: \"Invalid method parameter(s).\"\n    },\n    \"-32603\": {\n        standard: \"JSON RPC 2.0\",\n        message: \"Internal JSON-RPC error.\"\n    },\n    \"-32000\": {\n        standard: \"EIP-1474\",\n        message: \"Invalid input.\"\n    },\n    \"-32001\": {\n        standard: \"EIP-1474\",\n        message: \"Resource not found.\"\n    },\n    \"-32002\": {\n        standard: \"EIP-1474\",\n        message: \"Resource unavailable.\"\n    },\n    \"-32003\": {\n        standard: \"EIP-1474\",\n        message: \"Transaction rejected.\"\n    },\n    \"-32004\": {\n        standard: \"EIP-1474\",\n        message: \"Method not supported.\"\n    },\n    \"-32005\": {\n        standard: \"EIP-1474\",\n        message: \"Request limit exceeded.\"\n    },\n    \"4001\": {\n        standard: \"EIP-1193\",\n        message: \"User rejected the request.\"\n    },\n    \"4100\": {\n        standard: \"EIP-1193\",\n        message: \"The requested account and/or method has not been authorized by the user.\"\n    },\n    \"4200\": {\n        standard: \"EIP-1193\",\n        message: \"The requested method is not supported by this Ethereum provider.\"\n    },\n    \"4900\": {\n        standard: \"EIP-1193\",\n        message: \"The provider is disconnected from all chains.\"\n    },\n    \"4901\": {\n        standard: \"EIP-1193\",\n        message: \"The provider is disconnected from the specified chain.\"\n    }\n}; //# sourceMappingURL=data:application/json;base64,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\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLXJwYy1lcnJvcnMvZGlzdC9lcnJvci1jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYkEsOENBQTZDO0lBQUVHLE9BQU87QUFBSyxDQUFDLEVBQUM7QUFDN0RELG1CQUFtQixHQUFHQSxrQkFBa0IsR0FBRyxLQUFLO0FBQ2hEQSxrQkFBa0IsR0FBRztJQUNqQkksS0FBSztRQUNEQyxjQUFjLENBQUM7UUFDZkMsa0JBQWtCLENBQUM7UUFDbkJDLHFCQUFxQixDQUFDO1FBQ3RCQyxxQkFBcUIsQ0FBQztRQUN0QkMsb0JBQW9CLENBQUM7UUFDckJDLGVBQWUsQ0FBQztRQUNoQkMsT0FBTyxDQUFDO1FBQ1JDLGdCQUFnQixDQUFDO1FBQ2pCQyxnQkFBZ0IsQ0FBQztRQUNqQkMsZUFBZSxDQUFDO1FBQ2hCQyxVQUFVLENBQUM7SUFDZjtJQUNBQyxVQUFVO1FBQ05DLHFCQUFxQjtRQUNyQkMsY0FBYztRQUNkQyxtQkFBbUI7UUFDbkJDLGNBQWM7UUFDZEMsbUJBQW1CO0lBQ3ZCO0FBQ0o7QUFDQXJCLG1CQUFtQixHQUFHO0lBQ2xCLFVBQVU7UUFDTnNCLFVBQVU7UUFDVkMsU0FBUztJQUNiO0lBQ0EsVUFBVTtRQUNORCxVQUFVO1FBQ1ZDLFNBQVM7SUFDYjtJQUNBLFVBQVU7UUFDTkQsVUFBVTtRQUNWQyxTQUFTO0lBQ2I7SUFDQSxVQUFVO1FBQ05ELFVBQVU7UUFDVkMsU0FBUztJQUNiO0lBQ0EsVUFBVTtRQUNORCxVQUFVO1FBQ1ZDLFNBQVM7SUFDYjtJQUNBLFVBQVU7UUFDTkQsVUFBVTtRQUNWQyxTQUFTO0lBQ2I7SUFDQSxVQUFVO1FBQ05ELFVBQVU7UUFDVkMsU0FBUztJQUNiO0lBQ0EsVUFBVTtRQUNORCxVQUFVO1FBQ1ZDLFNBQVM7SUFDYjtJQUNBLFVBQVU7UUFDTkQsVUFBVTtRQUNWQyxTQUFTO0lBQ2I7SUFDQSxVQUFVO1FBQ05ELFVBQVU7UUFDVkMsU0FBUztJQUNiO0lBQ0EsVUFBVTtRQUNORCxVQUFVO1FBQ1ZDLFNBQVM7SUFDYjtJQUNBLFFBQVE7UUFDSkQsVUFBVTtRQUNWQyxTQUFTO0lBQ2I7SUFDQSxRQUFRO1FBQ0pELFVBQVU7UUFDVkMsU0FBUztJQUNiO0lBQ0EsUUFBUTtRQUNKRCxVQUFVO1FBQ1ZDLFNBQVM7SUFDYjtJQUNBLFFBQVE7UUFDSkQsVUFBVTtRQUNWQyxTQUFTO0lBQ2I7SUFDQSxRQUFRO1FBQ0pELFVBQVU7UUFDVkMsU0FBUztJQUNiO0FBQ0osR0FDQSxrdEVBQWt0RSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXdlYjMtY3JlYXRvci1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9ldGgtcnBjLWVycm9ycy9kaXN0L2Vycm9yLWNvbnN0YW50cy5qcz8xYmUzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5lcnJvclZhbHVlcyA9IGV4cG9ydHMuZXJyb3JDb2RlcyA9IHZvaWQgMDtcbmV4cG9ydHMuZXJyb3JDb2RlcyA9IHtcbiAgICBycGM6IHtcbiAgICAgICAgaW52YWxpZElucHV0OiAtMzIwMDAsXG4gICAgICAgIHJlc291cmNlTm90Rm91bmQ6IC0zMjAwMSxcbiAgICAgICAgcmVzb3VyY2VVbmF2YWlsYWJsZTogLTMyMDAyLFxuICAgICAgICB0cmFuc2FjdGlvblJlamVjdGVkOiAtMzIwMDMsXG4gICAgICAgIG1ldGhvZE5vdFN1cHBvcnRlZDogLTMyMDA0LFxuICAgICAgICBsaW1pdEV4Y2VlZGVkOiAtMzIwMDUsXG4gICAgICAgIHBhcnNlOiAtMzI3MDAsXG4gICAgICAgIGludmFsaWRSZXF1ZXN0OiAtMzI2MDAsXG4gICAgICAgIG1ldGhvZE5vdEZvdW5kOiAtMzI2MDEsXG4gICAgICAgIGludmFsaWRQYXJhbXM6IC0zMjYwMixcbiAgICAgICAgaW50ZXJuYWw6IC0zMjYwMyxcbiAgICB9LFxuICAgIHByb3ZpZGVyOiB7XG4gICAgICAgIHVzZXJSZWplY3RlZFJlcXVlc3Q6IDQwMDEsXG4gICAgICAgIHVuYXV0aG9yaXplZDogNDEwMCxcbiAgICAgICAgdW5zdXBwb3J0ZWRNZXRob2Q6IDQyMDAsXG4gICAgICAgIGRpc2Nvbm5lY3RlZDogNDkwMCxcbiAgICAgICAgY2hhaW5EaXNjb25uZWN0ZWQ6IDQ5MDEsXG4gICAgfSxcbn07XG5leHBvcnRzLmVycm9yVmFsdWVzID0ge1xuICAgICctMzI3MDAnOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnSlNPTiBSUEMgMi4wJyxcbiAgICAgICAgbWVzc2FnZTogJ0ludmFsaWQgSlNPTiB3YXMgcmVjZWl2ZWQgYnkgdGhlIHNlcnZlci4gQW4gZXJyb3Igb2NjdXJyZWQgb24gdGhlIHNlcnZlciB3aGlsZSBwYXJzaW5nIHRoZSBKU09OIHRleHQuJyxcbiAgICB9LFxuICAgICctMzI2MDAnOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnSlNPTiBSUEMgMi4wJyxcbiAgICAgICAgbWVzc2FnZTogJ1RoZSBKU09OIHNlbnQgaXMgbm90IGEgdmFsaWQgUmVxdWVzdCBvYmplY3QuJyxcbiAgICB9LFxuICAgICctMzI2MDEnOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnSlNPTiBSUEMgMi4wJyxcbiAgICAgICAgbWVzc2FnZTogJ1RoZSBtZXRob2QgZG9lcyBub3QgZXhpc3QgLyBpcyBub3QgYXZhaWxhYmxlLicsXG4gICAgfSxcbiAgICAnLTMyNjAyJzoge1xuICAgICAgICBzdGFuZGFyZDogJ0pTT04gUlBDIDIuMCcsXG4gICAgICAgIG1lc3NhZ2U6ICdJbnZhbGlkIG1ldGhvZCBwYXJhbWV0ZXIocykuJyxcbiAgICB9LFxuICAgICctMzI2MDMnOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnSlNPTiBSUEMgMi4wJyxcbiAgICAgICAgbWVzc2FnZTogJ0ludGVybmFsIEpTT04tUlBDIGVycm9yLicsXG4gICAgfSxcbiAgICAnLTMyMDAwJzoge1xuICAgICAgICBzdGFuZGFyZDogJ0VJUC0xNDc0JyxcbiAgICAgICAgbWVzc2FnZTogJ0ludmFsaWQgaW5wdXQuJyxcbiAgICB9LFxuICAgICctMzIwMDEnOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnRUlQLTE0NzQnLFxuICAgICAgICBtZXNzYWdlOiAnUmVzb3VyY2Ugbm90IGZvdW5kLicsXG4gICAgfSxcbiAgICAnLTMyMDAyJzoge1xuICAgICAgICBzdGFuZGFyZDogJ0VJUC0xNDc0JyxcbiAgICAgICAgbWVzc2FnZTogJ1Jlc291cmNlIHVuYXZhaWxhYmxlLicsXG4gICAgfSxcbiAgICAnLTMyMDAzJzoge1xuICAgICAgICBzdGFuZGFyZDogJ0VJUC0xNDc0JyxcbiAgICAgICAgbWVzc2FnZTogJ1RyYW5zYWN0aW9uIHJlamVjdGVkLicsXG4gICAgfSxcbiAgICAnLTMyMDA0Jzoge1xuICAgICAgICBzdGFuZGFyZDogJ0VJUC0xNDc0JyxcbiAgICAgICAgbWVzc2FnZTogJ01ldGhvZCBub3Qgc3VwcG9ydGVkLicsXG4gICAgfSxcbiAgICAnLTMyMDA1Jzoge1xuICAgICAgICBzdGFuZGFyZDogJ0VJUC0xNDc0JyxcbiAgICAgICAgbWVzc2FnZTogJ1JlcXVlc3QgbGltaXQgZXhjZWVkZWQuJyxcbiAgICB9LFxuICAgICc0MDAxJzoge1xuICAgICAgICBzdGFuZGFyZDogJ0VJUC0xMTkzJyxcbiAgICAgICAgbWVzc2FnZTogJ1VzZXIgcmVqZWN0ZWQgdGhlIHJlcXVlc3QuJyxcbiAgICB9LFxuICAgICc0MTAwJzoge1xuICAgICAgICBzdGFuZGFyZDogJ0VJUC0xMTkzJyxcbiAgICAgICAgbWVzc2FnZTogJ1RoZSByZXF1ZXN0ZWQgYWNjb3VudCBhbmQvb3IgbWV0aG9kIGhhcyBub3QgYmVlbiBhdXRob3JpemVkIGJ5IHRoZSB1c2VyLicsXG4gICAgfSxcbiAgICAnNDIwMCc6IHtcbiAgICAgICAgc3RhbmRhcmQ6ICdFSVAtMTE5MycsXG4gICAgICAgIG1lc3NhZ2U6ICdUaGUgcmVxdWVzdGVkIG1ldGhvZCBpcyBub3Qgc3VwcG9ydGVkIGJ5IHRoaXMgRXRoZXJldW0gcHJvdmlkZXIuJyxcbiAgICB9LFxuICAgICc0OTAwJzoge1xuICAgICAgICBzdGFuZGFyZDogJ0VJUC0xMTkzJyxcbiAgICAgICAgbWVzc2FnZTogJ1RoZSBwcm92aWRlciBpcyBkaXNjb25uZWN0ZWQgZnJvbSBhbGwgY2hhaW5zLicsXG4gICAgfSxcbiAgICAnNDkwMSc6IHtcbiAgICAgICAgc3RhbmRhcmQ6ICdFSVAtMTE5MycsXG4gICAgICAgIG1lc3NhZ2U6ICdUaGUgcHJvdmlkZXIgaXMgZGlzY29ubmVjdGVkIGZyb20gdGhlIHNwZWNpZmllZCBjaGFpbi4nLFxuICAgIH0sXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2Jhc2U2NCxleUoyWlhKemFXOXVJam96TENKbWFXeGxJam9pWlhKeWIzSXRZMjl1YzNSaGJuUnpMbXB6SWl3aWMyOTFjbU5sVW05dmRDSTZJaUlzSW5OdmRYSmpaWE1pT2xzaUxpNHZjM0pqTDJWeWNtOXlMV052Ym5OMFlXNTBjeTUwY3lKZExDSnVZVzFsY3lJNlcxMHNJbTFoY0hCcGJtZHpJam9pT3pzN1FVRjFRbUVzVVVGQlFTeFZRVUZWTEVkQlFXVTdTVUZEY0VNc1IwRkJSeXhGUVVGRk8xRkJRMGdzV1VGQldTeEZRVUZGTEVOQlFVTXNTMEZCU3p0UlFVTndRaXhuUWtGQlowSXNSVUZCUlN4RFFVRkRMRXRCUVVzN1VVRkRlRUlzYlVKQlFXMUNMRVZCUVVVc1EwRkJReXhMUVVGTE8xRkJRek5DTEcxQ1FVRnRRaXhGUVVGRkxFTkJRVU1zUzBGQlN6dFJRVU16UWl4clFrRkJhMElzUlVGQlJTeERRVUZETEV0QlFVczdVVUZETVVJc1lVRkJZU3hGUVVGRkxFTkJRVU1zUzBGQlN6dFJRVU55UWl4TFFVRkxMRVZCUVVVc1EwRkJReXhMUVVGTE8xRkJRMklzWTBGQll5eEZRVUZGTEVOQlFVTXNTMEZCU3p0UlFVTjBRaXhqUVVGakxFVkJRVVVzUTBGQlF5eExRVUZMTzFGQlEzUkNMR0ZCUVdFc1JVRkJSU3hEUVVGRExFdEJRVXM3VVVGRGNrSXNVVUZCVVN4RlFVRkZMRU5CUVVNc1MwRkJTenRMUVVOcVFqdEpRVU5FTEZGQlFWRXNSVUZCUlR0UlFVTlNMRzFDUVVGdFFpeEZRVUZGTEVsQlFVazdVVUZEZWtJc1dVRkJXU3hGUVVGRkxFbEJRVWs3VVVGRGJFSXNhVUpCUVdsQ0xFVkJRVVVzU1VGQlNUdFJRVU4yUWl4WlFVRlpMRVZCUVVVc1NVRkJTVHRSUVVOc1FpeHBRa0ZCYVVJc1JVRkJSU3hKUVVGSk8wdEJRM2hDTzBOQlEwWXNRMEZCUXp0QlFVVlhMRkZCUVVFc1YwRkJWeXhIUVVGSE8wbEJRM3BDTEZGQlFWRXNSVUZCUlR0UlFVTlNMRkZCUVZFc1JVRkJSU3hqUVVGak8xRkJRM2hDTEU5QlFVOHNSVUZCUlN4MVIwRkJkVWM3UzBGRGFrZzdTVUZEUkN4UlFVRlJMRVZCUVVVN1VVRkRVaXhSUVVGUkxFVkJRVVVzWTBGQll6dFJRVU40UWl4UFFVRlBMRVZCUVVVc09FTkJRVGhETzB0QlEzaEVPMGxCUTBRc1VVRkJVU3hGUVVGRk8xRkJRMUlzVVVGQlVTeEZRVUZGTEdOQlFXTTdVVUZEZUVJc1QwRkJUeXhGUVVGRkxDdERRVUVyUXp0TFFVTjZSRHRKUVVORUxGRkJRVkVzUlVGQlJUdFJRVU5TTEZGQlFWRXNSVUZCUlN4alFVRmpPMUZCUTNoQ0xFOUJRVThzUlVGQlJTdzRRa0ZCT0VJN1MwRkRlRU03U1VGRFJDeFJRVUZSTEVWQlFVVTdVVUZEVWl4UlFVRlJMRVZCUVVVc1kwRkJZenRSUVVONFFpeFBRVUZQTEVWQlFVVXNNRUpCUVRCQ08wdEJRM0JETzBsQlEwUXNVVUZCVVN4RlFVRkZPMUZCUTFJc1VVRkJVU3hGUVVGRkxGVkJRVlU3VVVGRGNFSXNUMEZCVHl4RlFVRkZMR2RDUVVGblFqdExRVU14UWp0SlFVTkVMRkZCUVZFc1JVRkJSVHRSUVVOU0xGRkJRVkVzUlVGQlJTeFZRVUZWTzFGQlEzQkNMRTlCUVU4c1JVRkJSU3h4UWtGQmNVSTdTMEZETDBJN1NVRkRSQ3hSUVVGUkxFVkJRVVU3VVVGRFVpeFJRVUZSTEVWQlFVVXNWVUZCVlR0UlFVTndRaXhQUVVGUExFVkJRVVVzZFVKQlFYVkNPMHRCUTJwRE8wbEJRMFFzVVVGQlVTeEZRVUZGTzFGQlExSXNVVUZCVVN4RlFVRkZMRlZCUVZVN1VVRkRjRUlzVDBGQlR5eEZRVUZGTEhWQ1FVRjFRanRMUVVOcVF6dEpRVU5FTEZGQlFWRXNSVUZCUlR0UlFVTlNMRkZCUVZFc1JVRkJSU3hWUVVGVk8xRkJRM0JDTEU5QlFVOHNSVUZCUlN4MVFrRkJkVUk3UzBGRGFrTTdTVUZEUkN4UlFVRlJMRVZCUVVVN1VVRkRVaXhSUVVGUkxFVkJRVVVzVlVGQlZUdFJRVU53UWl4UFFVRlBMRVZCUVVVc2VVSkJRWGxDTzB0QlEyNURPMGxCUTBRc1RVRkJUU3hGUVVGRk8xRkJRMDRzVVVGQlVTeEZRVUZGTEZWQlFWVTdVVUZEY0VJc1QwRkJUeXhGUVVGRkxEUkNRVUUwUWp0TFFVTjBRenRKUVVORUxFMUJRVTBzUlVGQlJUdFJRVU5PTEZGQlFWRXNSVUZCUlN4VlFVRlZPMUZCUTNCQ0xFOUJRVThzUlVGQlJTd3dSVUZCTUVVN1MwRkRjRVk3U1VGRFJDeE5RVUZOTEVWQlFVVTdVVUZEVGl4UlFVRlJMRVZCUVVVc1ZVRkJWVHRSUVVOd1FpeFBRVUZQTEVWQlFVVXNhMFZCUVd0Rk8wdEJRelZGTzBsQlEwUXNUVUZCVFN4RlFVRkZPMUZCUTA0c1VVRkJVU3hGUVVGRkxGVkJRVlU3VVVGRGNFSXNUMEZCVHl4RlFVRkZMQ3REUVVFclF6dExRVU42UkR0SlFVTkVMRTFCUVUwc1JVRkJSVHRSUVVOT0xGRkJRVkVzUlVGQlJTeFZRVUZWTzFGQlEzQkNMRTlCUVU4c1JVRkJSU3gzUkVGQmQwUTdTMEZEYkVVN1EwRkRSaXhEUVVGREluMD0iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJlcnJvclZhbHVlcyIsImVycm9yQ29kZXMiLCJycGMiLCJpbnZhbGlkSW5wdXQiLCJyZXNvdXJjZU5vdEZvdW5kIiwicmVzb3VyY2VVbmF2YWlsYWJsZSIsInRyYW5zYWN0aW9uUmVqZWN0ZWQiLCJtZXRob2ROb3RTdXBwb3J0ZWQiLCJsaW1pdEV4Y2VlZGVkIiwicGFyc2UiLCJpbnZhbGlkUmVxdWVzdCIsIm1ldGhvZE5vdEZvdW5kIiwiaW52YWxpZFBhcmFtcyIsImludGVybmFsIiwicHJvdmlkZXIiLCJ1c2VyUmVqZWN0ZWRSZXF1ZXN0IiwidW5hdXRob3JpemVkIiwidW5zdXBwb3J0ZWRNZXRob2QiLCJkaXNjb25uZWN0ZWQiLCJjaGFpbkRpc2Nvbm5lY3RlZCIsInN0YW5kYXJkIiwibWVzc2FnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-rpc-errors/dist/error-constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-rpc-errors/dist/errors.js":
/*!****************************************************!*\
  !*** ./node_modules/eth-rpc-errors/dist/errors.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.ethErrors = void 0;\nconst classes_1 = __webpack_require__(/*! ./classes */ \"(ssr)/./node_modules/eth-rpc-errors/dist/classes.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/eth-rpc-errors/dist/utils.js\");\nconst error_constants_1 = __webpack_require__(/*! ./error-constants */ \"(ssr)/./node_modules/eth-rpc-errors/dist/error-constants.js\");\nexports.ethErrors = {\n    rpc: {\n        /**\n         * Get a JSON RPC 2.0 Parse (-32700) error.\n         */ parse: (arg)=>getEthJsonRpcError(error_constants_1.errorCodes.rpc.parse, arg),\n        /**\n         * Get a JSON RPC 2.0 Invalid Request (-32600) error.\n         */ invalidRequest: (arg)=>getEthJsonRpcError(error_constants_1.errorCodes.rpc.invalidRequest, arg),\n        /**\n         * Get a JSON RPC 2.0 Invalid Params (-32602) error.\n         */ invalidParams: (arg)=>getEthJsonRpcError(error_constants_1.errorCodes.rpc.invalidParams, arg),\n        /**\n         * Get a JSON RPC 2.0 Method Not Found (-32601) error.\n         */ methodNotFound: (arg)=>getEthJsonRpcError(error_constants_1.errorCodes.rpc.methodNotFound, arg),\n        /**\n         * Get a JSON RPC 2.0 Internal (-32603) error.\n         */ internal: (arg)=>getEthJsonRpcError(error_constants_1.errorCodes.rpc.internal, arg),\n        /**\n         * Get a JSON RPC 2.0 Server error.\n         * Permits integer error codes in the [ -32099 <= -32005 ] range.\n         * Codes -32000 through -32004 are reserved by EIP-1474.\n         */ server: (opts)=>{\n            if (!opts || typeof opts !== \"object\" || Array.isArray(opts)) {\n                throw new Error(\"Ethereum RPC Server errors must provide single object argument.\");\n            }\n            const { code } = opts;\n            if (!Number.isInteger(code) || code > -32005 || code < -32099) {\n                throw new Error('\"code\" must be an integer such that: -32099 <= code <= -32005');\n            }\n            return getEthJsonRpcError(code, opts);\n        },\n        /**\n         * Get an Ethereum JSON RPC Invalid Input (-32000) error.\n         */ invalidInput: (arg)=>getEthJsonRpcError(error_constants_1.errorCodes.rpc.invalidInput, arg),\n        /**\n         * Get an Ethereum JSON RPC Resource Not Found (-32001) error.\n         */ resourceNotFound: (arg)=>getEthJsonRpcError(error_constants_1.errorCodes.rpc.resourceNotFound, arg),\n        /**\n         * Get an Ethereum JSON RPC Resource Unavailable (-32002) error.\n         */ resourceUnavailable: (arg)=>getEthJsonRpcError(error_constants_1.errorCodes.rpc.resourceUnavailable, arg),\n        /**\n         * Get an Ethereum JSON RPC Transaction Rejected (-32003) error.\n         */ transactionRejected: (arg)=>getEthJsonRpcError(error_constants_1.errorCodes.rpc.transactionRejected, arg),\n        /**\n         * Get an Ethereum JSON RPC Method Not Supported (-32004) error.\n         */ methodNotSupported: (arg)=>getEthJsonRpcError(error_constants_1.errorCodes.rpc.methodNotSupported, arg),\n        /**\n         * Get an Ethereum JSON RPC Limit Exceeded (-32005) error.\n         */ limitExceeded: (arg)=>getEthJsonRpcError(error_constants_1.errorCodes.rpc.limitExceeded, arg)\n    },\n    provider: {\n        /**\n         * Get an Ethereum Provider User Rejected Request (4001) error.\n         */ userRejectedRequest: (arg)=>{\n            return getEthProviderError(error_constants_1.errorCodes.provider.userRejectedRequest, arg);\n        },\n        /**\n         * Get an Ethereum Provider Unauthorized (4100) error.\n         */ unauthorized: (arg)=>{\n            return getEthProviderError(error_constants_1.errorCodes.provider.unauthorized, arg);\n        },\n        /**\n         * Get an Ethereum Provider Unsupported Method (4200) error.\n         */ unsupportedMethod: (arg)=>{\n            return getEthProviderError(error_constants_1.errorCodes.provider.unsupportedMethod, arg);\n        },\n        /**\n         * Get an Ethereum Provider Not Connected (4900) error.\n         */ disconnected: (arg)=>{\n            return getEthProviderError(error_constants_1.errorCodes.provider.disconnected, arg);\n        },\n        /**\n         * Get an Ethereum Provider Chain Not Connected (4901) error.\n         */ chainDisconnected: (arg)=>{\n            return getEthProviderError(error_constants_1.errorCodes.provider.chainDisconnected, arg);\n        },\n        /**\n         * Get a custom Ethereum Provider error.\n         */ custom: (opts)=>{\n            if (!opts || typeof opts !== \"object\" || Array.isArray(opts)) {\n                throw new Error(\"Ethereum Provider custom errors must provide single object argument.\");\n            }\n            const { code, message, data } = opts;\n            if (!message || typeof message !== \"string\") {\n                throw new Error('\"message\" must be a nonempty string');\n            }\n            return new classes_1.EthereumProviderError(code, message, data);\n        }\n    }\n};\n// Internal\nfunction getEthJsonRpcError(code, arg) {\n    const [message, data] = parseOpts(arg);\n    return new classes_1.EthereumRpcError(code, message || utils_1.getMessageFromCode(code), data);\n}\nfunction getEthProviderError(code, arg) {\n    const [message, data] = parseOpts(arg);\n    return new classes_1.EthereumProviderError(code, message || utils_1.getMessageFromCode(code), data);\n}\nfunction parseOpts(arg) {\n    if (arg) {\n        if (typeof arg === \"string\") {\n            return [\n                arg\n            ];\n        } else if (typeof arg === \"object\" && !Array.isArray(arg)) {\n            const { message, data } = arg;\n            if (message && typeof message !== \"string\") {\n                throw new Error(\"Must specify string message.\");\n            }\n            return [\n                message || undefined,\n                data\n            ];\n        }\n    }\n    return [];\n} //# sourceMappingURL=data:application/json;base64,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\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-rpc-errors/dist/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-rpc-errors/dist/index.js":
/*!***************************************************!*\
  !*** ./node_modules/eth-rpc-errors/dist/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.getMessageFromCode = exports.serializeError = exports.EthereumProviderError = exports.EthereumRpcError = exports.ethErrors = exports.errorCodes = void 0;\nconst classes_1 = __webpack_require__(/*! ./classes */ \"(ssr)/./node_modules/eth-rpc-errors/dist/classes.js\");\nObject.defineProperty(exports, \"EthereumRpcError\", ({\n    enumerable: true,\n    get: function() {\n        return classes_1.EthereumRpcError;\n    }\n}));\nObject.defineProperty(exports, \"EthereumProviderError\", ({\n    enumerable: true,\n    get: function() {\n        return classes_1.EthereumProviderError;\n    }\n}));\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/eth-rpc-errors/dist/utils.js\");\nObject.defineProperty(exports, \"serializeError\", ({\n    enumerable: true,\n    get: function() {\n        return utils_1.serializeError;\n    }\n}));\nObject.defineProperty(exports, \"getMessageFromCode\", ({\n    enumerable: true,\n    get: function() {\n        return utils_1.getMessageFromCode;\n    }\n}));\nconst errors_1 = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/eth-rpc-errors/dist/errors.js\");\nObject.defineProperty(exports, \"ethErrors\", ({\n    enumerable: true,\n    get: function() {\n        return errors_1.ethErrors;\n    }\n}));\nconst error_constants_1 = __webpack_require__(/*! ./error-constants */ \"(ssr)/./node_modules/eth-rpc-errors/dist/error-constants.js\");\nObject.defineProperty(exports, \"errorCodes\", ({\n    enumerable: true,\n    get: function() {\n        return error_constants_1.errorCodes;\n    }\n})); //# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsdUNBQW9FO0FBVWxFLGlHQVZPLDBCQUFnQixPQVVQO0FBQ2hCLHNHQVh5QiwrQkFBcUIsT0FXekI7QUFWdkIsbUNBRWlCO0FBU2YsK0ZBVkEsc0JBQWMsT0FVQTtBQUNkLG1HQVhnQiwwQkFBa0IsT0FXaEI7QUFUcEIscUNBQXFDO0FBS25DLDBGQUxPLGtCQUFTLE9BS1A7QUFKWCx1REFBK0M7QUFHN0MsMkZBSE8sNEJBQVUsT0FHUCJ9\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-rpc-errors/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-rpc-errors/dist/utils.js":
/*!***************************************************!*\
  !*** ./node_modules/eth-rpc-errors/dist/utils.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.serializeError = exports.isValidCode = exports.getMessageFromCode = exports.JSON_RPC_SERVER_ERROR_MESSAGE = void 0;\nconst error_constants_1 = __webpack_require__(/*! ./error-constants */ \"(ssr)/./node_modules/eth-rpc-errors/dist/error-constants.js\");\nconst classes_1 = __webpack_require__(/*! ./classes */ \"(ssr)/./node_modules/eth-rpc-errors/dist/classes.js\");\nconst FALLBACK_ERROR_CODE = error_constants_1.errorCodes.rpc.internal;\nconst FALLBACK_MESSAGE = \"Unspecified error message. This is a bug, please report it.\";\nconst FALLBACK_ERROR = {\n    code: FALLBACK_ERROR_CODE,\n    message: getMessageFromCode(FALLBACK_ERROR_CODE)\n};\nexports.JSON_RPC_SERVER_ERROR_MESSAGE = \"Unspecified server error.\";\n/**\n * Gets the message for a given code, or a fallback message if the code has\n * no corresponding message.\n */ function getMessageFromCode(code, fallbackMessage = FALLBACK_MESSAGE) {\n    if (Number.isInteger(code)) {\n        const codeString = code.toString();\n        if (hasKey(error_constants_1.errorValues, codeString)) {\n            return error_constants_1.errorValues[codeString].message;\n        }\n        if (isJsonRpcServerError(code)) {\n            return exports.JSON_RPC_SERVER_ERROR_MESSAGE;\n        }\n    }\n    return fallbackMessage;\n}\nexports.getMessageFromCode = getMessageFromCode;\n/**\n * Returns whether the given code is valid.\n * A code is only valid if it has a message.\n */ function isValidCode(code) {\n    if (!Number.isInteger(code)) {\n        return false;\n    }\n    const codeString = code.toString();\n    if (error_constants_1.errorValues[codeString]) {\n        return true;\n    }\n    if (isJsonRpcServerError(code)) {\n        return true;\n    }\n    return false;\n}\nexports.isValidCode = isValidCode;\n/**\n * Serializes the given error to an Ethereum JSON RPC-compatible error object.\n * Merely copies the given error's values if it is already compatible.\n * If the given error is not fully compatible, it will be preserved on the\n * returned object's data.originalError property.\n */ function serializeError(error, { fallbackError = FALLBACK_ERROR, shouldIncludeStack = false } = {}) {\n    var _a, _b;\n    if (!fallbackError || !Number.isInteger(fallbackError.code) || typeof fallbackError.message !== \"string\") {\n        throw new Error(\"Must provide fallback error with integer number code and string message.\");\n    }\n    if (error instanceof classes_1.EthereumRpcError) {\n        return error.serialize();\n    }\n    const serialized = {};\n    if (error && typeof error === \"object\" && !Array.isArray(error) && hasKey(error, \"code\") && isValidCode(error.code)) {\n        const _error = error;\n        serialized.code = _error.code;\n        if (_error.message && typeof _error.message === \"string\") {\n            serialized.message = _error.message;\n            if (hasKey(_error, \"data\")) {\n                serialized.data = _error.data;\n            }\n        } else {\n            serialized.message = getMessageFromCode(serialized.code);\n            serialized.data = {\n                originalError: assignOriginalError(error)\n            };\n        }\n    } else {\n        serialized.code = fallbackError.code;\n        const message = (_a = error) === null || _a === void 0 ? void 0 : _a.message;\n        serialized.message = message && typeof message === \"string\" ? message : fallbackError.message;\n        serialized.data = {\n            originalError: assignOriginalError(error)\n        };\n    }\n    const stack = (_b = error) === null || _b === void 0 ? void 0 : _b.stack;\n    if (shouldIncludeStack && error && stack && typeof stack === \"string\") {\n        serialized.stack = stack;\n    }\n    return serialized;\n}\nexports.serializeError = serializeError;\n// Internal\nfunction isJsonRpcServerError(code) {\n    return code >= -32099 && code <= -32000;\n}\nfunction assignOriginalError(error) {\n    if (error && typeof error === \"object\" && !Array.isArray(error)) {\n        return Object.assign({}, error);\n    }\n    return error;\n}\nfunction hasKey(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n} //# sourceMappingURL=data:application/json;base64,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\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-rpc-errors/dist/utils.js\n");

/***/ })

};
;