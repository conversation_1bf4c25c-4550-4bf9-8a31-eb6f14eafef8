/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/loglevel";
exports.ids = ["vendor-chunks/loglevel"];
exports.modules = {

/***/ "(ssr)/./node_modules/loglevel/lib/loglevel.js":
/*!***********************************************!*\
  !*** ./node_modules/loglevel/lib/loglevel.js ***!
  \***********************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_RESULT__;/*\n* loglevel - https://github.com/pimterry/loglevel\n*\n* Copyright (c) 2013 Tim Perry\n* Licensed under the MIT license.\n*/ (function(root, definition) {\n    \"use strict\";\n    if (true) {\n        !(__WEBPACK_AMD_DEFINE_FACTORY__ = (definition),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.call(exports, __webpack_require__, exports, module)) :\n\t\t__WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    } else {}\n})(this, function() {\n    \"use strict\";\n    // Slightly dubious tricks to cut down minimized file size\n    var noop = function() {};\n    var undefinedType = \"undefined\";\n    var isIE = \"undefined\" !== undefinedType && typeof window.navigator !== undefinedType && /Trident\\/|MSIE /.test(window.navigator.userAgent);\n    var logMethods = [\n        \"trace\",\n        \"debug\",\n        \"info\",\n        \"warn\",\n        \"error\"\n    ];\n    var _loggersByName = {};\n    var defaultLogger = null;\n    // Cross-browser bind equivalent that works at least back to IE6\n    function bindMethod(obj, methodName) {\n        var method = obj[methodName];\n        if (typeof method.bind === \"function\") {\n            return method.bind(obj);\n        } else {\n            try {\n                return Function.prototype.bind.call(method, obj);\n            } catch (e) {\n                // Missing bind shim or IE8 + Modernizr, fallback to wrapping\n                return function() {\n                    return Function.prototype.apply.apply(method, [\n                        obj,\n                        arguments\n                    ]);\n                };\n            }\n        }\n    }\n    // Trace() doesn't print the message in IE, so for that case we need to wrap it\n    function traceForIE() {\n        if (console.log) {\n            if (console.log.apply) {\n                console.log.apply(console, arguments);\n            } else {\n                // In old IE, native console methods themselves don't have apply().\n                Function.prototype.apply.apply(console.log, [\n                    console,\n                    arguments\n                ]);\n            }\n        }\n        if (console.trace) console.trace();\n    }\n    // Build the best logging method possible for this env\n    // Wherever possible we want to bind, not wrap, to preserve stack traces\n    function realMethod(methodName) {\n        if (methodName === \"debug\") {\n            methodName = \"log\";\n        }\n        if (typeof console === undefinedType) {\n            return false; // No method possible, for now - fixed later by enableLoggingWhenConsoleArrives\n        } else if (methodName === \"trace\" && isIE) {\n            return traceForIE;\n        } else if (console[methodName] !== undefined) {\n            return bindMethod(console, methodName);\n        } else if (console.log !== undefined) {\n            return bindMethod(console, \"log\");\n        } else {\n            return noop;\n        }\n    }\n    // These private functions always need `this` to be set properly\n    function replaceLoggingMethods() {\n        /*jshint validthis:true */ var level = this.getLevel();\n        // Replace the actual methods.\n        for(var i = 0; i < logMethods.length; i++){\n            var methodName = logMethods[i];\n            this[methodName] = i < level ? noop : this.methodFactory(methodName, level, this.name);\n        }\n        // Define log.log as an alias for log.debug\n        this.log = this.debug;\n        // Return any important warnings.\n        if (typeof console === undefinedType && level < this.levels.SILENT) {\n            return \"No console available for logging\";\n        }\n    }\n    // In old IE versions, the console isn't present until you first open it.\n    // We build realMethod() replacements here that regenerate logging methods\n    function enableLoggingWhenConsoleArrives(methodName) {\n        return function() {\n            if (typeof console !== undefinedType) {\n                replaceLoggingMethods.call(this);\n                this[methodName].apply(this, arguments);\n            }\n        };\n    }\n    // By default, we use closely bound real methods wherever possible, and\n    // otherwise we wait for a console to appear, and then try again.\n    function defaultMethodFactory(methodName, _level, _loggerName) {\n        /*jshint validthis:true */ return realMethod(methodName) || enableLoggingWhenConsoleArrives.apply(this, arguments);\n    }\n    function Logger(name, factory) {\n        // Private instance variables.\n        var self = this;\n        /**\n       * The level inherited from a parent logger (or a global default). We\n       * cache this here rather than delegating to the parent so that it stays\n       * in sync with the actual logging methods that we have installed (the\n       * parent could change levels but we might not have rebuilt the loggers\n       * in this child yet).\n       * @type {number}\n       */ var inheritedLevel;\n        /**\n       * The default level for this logger, if any. If set, this overrides\n       * `inheritedLevel`.\n       * @type {number|null}\n       */ var defaultLevel;\n        /**\n       * A user-specific level for this logger. If set, this overrides\n       * `defaultLevel`.\n       * @type {number|null}\n       */ var userLevel;\n        var storageKey = \"loglevel\";\n        if (typeof name === \"string\") {\n            storageKey += \":\" + name;\n        } else if (typeof name === \"symbol\") {\n            storageKey = undefined;\n        }\n        function persistLevelIfPossible(levelNum) {\n            var levelName = (logMethods[levelNum] || \"silent\").toUpperCase();\n            if (\"undefined\" === undefinedType || !storageKey) return;\n            // Use localStorage if available\n            try {\n                window.localStorage[storageKey] = levelName;\n                return;\n            } catch (ignore) {}\n            // Use session cookie as fallback\n            try {\n                window.document.cookie = encodeURIComponent(storageKey) + \"=\" + levelName + \";\";\n            } catch (ignore) {}\n        }\n        function getPersistedLevel() {\n            var storedLevel;\n            if (\"undefined\" === undefinedType || !storageKey) return;\n            try {\n                storedLevel = window.localStorage[storageKey];\n            } catch (ignore) {}\n            // Fallback to cookies if local storage gives us nothing\n            if (typeof storedLevel === undefinedType) {\n                try {\n                    var cookie = window.document.cookie;\n                    var cookieName = encodeURIComponent(storageKey);\n                    var location = cookie.indexOf(cookieName + \"=\");\n                    if (location !== -1) {\n                        storedLevel = /^([^;]+)/.exec(cookie.slice(location + cookieName.length + 1))[1];\n                    }\n                } catch (ignore) {}\n            }\n            // If the stored level is not valid, treat it as if nothing was stored.\n            if (self.levels[storedLevel] === undefined) {\n                storedLevel = undefined;\n            }\n            return storedLevel;\n        }\n        function clearPersistedLevel() {\n            if (\"undefined\" === undefinedType || !storageKey) return;\n            // Use localStorage if available\n            try {\n                window.localStorage.removeItem(storageKey);\n            } catch (ignore) {}\n            // Use session cookie as fallback\n            try {\n                window.document.cookie = encodeURIComponent(storageKey) + \"=; expires=Thu, 01 Jan 1970 00:00:00 UTC\";\n            } catch (ignore) {}\n        }\n        function normalizeLevel(input) {\n            var level = input;\n            if (typeof level === \"string\" && self.levels[level.toUpperCase()] !== undefined) {\n                level = self.levels[level.toUpperCase()];\n            }\n            if (typeof level === \"number\" && level >= 0 && level <= self.levels.SILENT) {\n                return level;\n            } else {\n                throw new TypeError(\"log.setLevel() called with invalid level: \" + input);\n            }\n        }\n        /*\n       *\n       * Public logger API - see https://github.com/pimterry/loglevel for details\n       *\n       */ self.name = name;\n        self.levels = {\n            \"TRACE\": 0,\n            \"DEBUG\": 1,\n            \"INFO\": 2,\n            \"WARN\": 3,\n            \"ERROR\": 4,\n            \"SILENT\": 5\n        };\n        self.methodFactory = factory || defaultMethodFactory;\n        self.getLevel = function() {\n            if (userLevel != null) {\n                return userLevel;\n            } else if (defaultLevel != null) {\n                return defaultLevel;\n            } else {\n                return inheritedLevel;\n            }\n        };\n        self.setLevel = function(level, persist) {\n            userLevel = normalizeLevel(level);\n            if (persist !== false) {\n                persistLevelIfPossible(userLevel);\n            }\n            // NOTE: in v2, this should call rebuild(), which updates children.\n            return replaceLoggingMethods.call(self);\n        };\n        self.setDefaultLevel = function(level) {\n            defaultLevel = normalizeLevel(level);\n            if (!getPersistedLevel()) {\n                self.setLevel(level, false);\n            }\n        };\n        self.resetLevel = function() {\n            userLevel = null;\n            clearPersistedLevel();\n            replaceLoggingMethods.call(self);\n        };\n        self.enableAll = function(persist) {\n            self.setLevel(self.levels.TRACE, persist);\n        };\n        self.disableAll = function(persist) {\n            self.setLevel(self.levels.SILENT, persist);\n        };\n        self.rebuild = function() {\n            if (defaultLogger !== self) {\n                inheritedLevel = normalizeLevel(defaultLogger.getLevel());\n            }\n            replaceLoggingMethods.call(self);\n            if (defaultLogger === self) {\n                for(var childName in _loggersByName){\n                    _loggersByName[childName].rebuild();\n                }\n            }\n        };\n        // Initialize all the internal levels.\n        inheritedLevel = normalizeLevel(defaultLogger ? defaultLogger.getLevel() : \"WARN\");\n        var initialLevel = getPersistedLevel();\n        if (initialLevel != null) {\n            userLevel = normalizeLevel(initialLevel);\n        }\n        replaceLoggingMethods.call(self);\n    }\n    /*\n     *\n     * Top-level API\n     *\n     */ defaultLogger = new Logger();\n    defaultLogger.getLogger = function getLogger(name) {\n        if (typeof name !== \"symbol\" && typeof name !== \"string\" || name === \"\") {\n            throw new TypeError(\"You must supply a name when creating a logger.\");\n        }\n        var logger = _loggersByName[name];\n        if (!logger) {\n            logger = _loggersByName[name] = new Logger(name, defaultLogger.methodFactory);\n        }\n        return logger;\n    };\n    // Grab the current global log variable in case of overwrite\n    var _log = \"undefined\" !== undefinedType ? window.log : undefined;\n    defaultLogger.noConflict = function() {\n        if (\"undefined\" !== undefinedType && window.log === defaultLogger) {\n            window.log = _log;\n        }\n        return defaultLogger;\n    };\n    defaultLogger.getLoggers = function getLoggers() {\n        return _loggersByName;\n    };\n    // ES6 default export, for compatibility\n    defaultLogger[\"default\"] = defaultLogger;\n    return defaultLogger;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/loglevel/lib/loglevel.js\n");

/***/ })

};
;