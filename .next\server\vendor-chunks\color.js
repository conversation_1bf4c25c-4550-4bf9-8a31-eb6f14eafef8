/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/color";
exports.ids = ["vendor-chunks/color"];
exports.modules = {

/***/ "(ssr)/./node_modules/color/index.js":
/*!*************************************!*\
  !*** ./node_modules/color/index.js ***!
  \*************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const colorString = __webpack_require__(/*! color-string */ \"(ssr)/./node_modules/color-string/index.js\");\nconst convert = __webpack_require__(/*! color-convert */ \"(ssr)/./node_modules/color-convert/index.js\");\nconst skippedModels = [\n    // To be honest, I don't really feel like keyword belongs in color convert, but eh.\n    \"keyword\",\n    // Gray conflicts with some method names, and has its own method defined.\n    \"gray\",\n    // Shouldn't really be in color-convert either...\n    \"hex\"\n];\nconst hashedModelKeys = {};\nfor (const model of Object.keys(convert)){\n    hashedModelKeys[[\n        ...convert[model].labels\n    ].sort().join(\"\")] = model;\n}\nconst limiters = {};\nfunction Color(object, model) {\n    if (!(this instanceof Color)) {\n        return new Color(object, model);\n    }\n    if (model && model in skippedModels) {\n        model = null;\n    }\n    if (model && !(model in convert)) {\n        throw new Error(\"Unknown model: \" + model);\n    }\n    let i;\n    let channels;\n    if (object == null) {\n        this.model = \"rgb\";\n        this.color = [\n            0,\n            0,\n            0\n        ];\n        this.valpha = 1;\n    } else if (object instanceof Color) {\n        this.model = object.model;\n        this.color = [\n            ...object.color\n        ];\n        this.valpha = object.valpha;\n    } else if (typeof object === \"string\") {\n        const result = colorString.get(object);\n        if (result === null) {\n            throw new Error(\"Unable to parse color from string: \" + object);\n        }\n        this.model = result.model;\n        channels = convert[this.model].channels;\n        this.color = result.value.slice(0, channels);\n        this.valpha = typeof result.value[channels] === \"number\" ? result.value[channels] : 1;\n    } else if (object.length > 0) {\n        this.model = model || \"rgb\";\n        channels = convert[this.model].channels;\n        const newArray = Array.prototype.slice.call(object, 0, channels);\n        this.color = zeroArray(newArray, channels);\n        this.valpha = typeof object[channels] === \"number\" ? object[channels] : 1;\n    } else if (typeof object === \"number\") {\n        // This is always RGB - can be converted later on.\n        this.model = \"rgb\";\n        this.color = [\n            object >> 16 & 0xFF,\n            object >> 8 & 0xFF,\n            object & 0xFF\n        ];\n        this.valpha = 1;\n    } else {\n        this.valpha = 1;\n        const keys = Object.keys(object);\n        if (\"alpha\" in object) {\n            keys.splice(keys.indexOf(\"alpha\"), 1);\n            this.valpha = typeof object.alpha === \"number\" ? object.alpha : 0;\n        }\n        const hashedKeys = keys.sort().join(\"\");\n        if (!(hashedKeys in hashedModelKeys)) {\n            throw new Error(\"Unable to parse color from object: \" + JSON.stringify(object));\n        }\n        this.model = hashedModelKeys[hashedKeys];\n        const { labels } = convert[this.model];\n        const color = [];\n        for(i = 0; i < labels.length; i++){\n            color.push(object[labels[i]]);\n        }\n        this.color = zeroArray(color);\n    }\n    // Perform limitations (clamping, etc.)\n    if (limiters[this.model]) {\n        channels = convert[this.model].channels;\n        for(i = 0; i < channels; i++){\n            const limit = limiters[this.model][i];\n            if (limit) {\n                this.color[i] = limit(this.color[i]);\n            }\n        }\n    }\n    this.valpha = Math.max(0, Math.min(1, this.valpha));\n    if (Object.freeze) {\n        Object.freeze(this);\n    }\n}\nColor.prototype = {\n    toString () {\n        return this.string();\n    },\n    toJSON () {\n        return this[this.model]();\n    },\n    string (places) {\n        let self = this.model in colorString.to ? this : this.rgb();\n        self = self.round(typeof places === \"number\" ? places : 1);\n        const args = self.valpha === 1 ? self.color : [\n            ...self.color,\n            this.valpha\n        ];\n        return colorString.to[self.model](args);\n    },\n    percentString (places) {\n        const self = this.rgb().round(typeof places === \"number\" ? places : 1);\n        const args = self.valpha === 1 ? self.color : [\n            ...self.color,\n            this.valpha\n        ];\n        return colorString.to.rgb.percent(args);\n    },\n    array () {\n        return this.valpha === 1 ? [\n            ...this.color\n        ] : [\n            ...this.color,\n            this.valpha\n        ];\n    },\n    object () {\n        const result = {};\n        const { channels } = convert[this.model];\n        const { labels } = convert[this.model];\n        for(let i = 0; i < channels; i++){\n            result[labels[i]] = this.color[i];\n        }\n        if (this.valpha !== 1) {\n            result.alpha = this.valpha;\n        }\n        return result;\n    },\n    unitArray () {\n        const rgb = this.rgb().color;\n        rgb[0] /= 255;\n        rgb[1] /= 255;\n        rgb[2] /= 255;\n        if (this.valpha !== 1) {\n            rgb.push(this.valpha);\n        }\n        return rgb;\n    },\n    unitObject () {\n        const rgb = this.rgb().object();\n        rgb.r /= 255;\n        rgb.g /= 255;\n        rgb.b /= 255;\n        if (this.valpha !== 1) {\n            rgb.alpha = this.valpha;\n        }\n        return rgb;\n    },\n    round (places) {\n        places = Math.max(places || 0, 0);\n        return new Color([\n            ...this.color.map(roundToPlace(places)),\n            this.valpha\n        ], this.model);\n    },\n    alpha (value) {\n        if (value !== undefined) {\n            return new Color([\n                ...this.color,\n                Math.max(0, Math.min(1, value))\n            ], this.model);\n        }\n        return this.valpha;\n    },\n    // Rgb\n    red: getset(\"rgb\", 0, maxfn(255)),\n    green: getset(\"rgb\", 1, maxfn(255)),\n    blue: getset(\"rgb\", 2, maxfn(255)),\n    hue: getset([\n        \"hsl\",\n        \"hsv\",\n        \"hsl\",\n        \"hwb\",\n        \"hcg\"\n    ], 0, (value)=>(value % 360 + 360) % 360),\n    saturationl: getset(\"hsl\", 1, maxfn(100)),\n    lightness: getset(\"hsl\", 2, maxfn(100)),\n    saturationv: getset(\"hsv\", 1, maxfn(100)),\n    value: getset(\"hsv\", 2, maxfn(100)),\n    chroma: getset(\"hcg\", 1, maxfn(100)),\n    gray: getset(\"hcg\", 2, maxfn(100)),\n    white: getset(\"hwb\", 1, maxfn(100)),\n    wblack: getset(\"hwb\", 2, maxfn(100)),\n    cyan: getset(\"cmyk\", 0, maxfn(100)),\n    magenta: getset(\"cmyk\", 1, maxfn(100)),\n    yellow: getset(\"cmyk\", 2, maxfn(100)),\n    black: getset(\"cmyk\", 3, maxfn(100)),\n    x: getset(\"xyz\", 0, maxfn(95.047)),\n    y: getset(\"xyz\", 1, maxfn(100)),\n    z: getset(\"xyz\", 2, maxfn(108.833)),\n    l: getset(\"lab\", 0, maxfn(100)),\n    a: getset(\"lab\", 1),\n    b: getset(\"lab\", 2),\n    keyword (value) {\n        if (value !== undefined) {\n            return new Color(value);\n        }\n        return convert[this.model].keyword(this.color);\n    },\n    hex (value) {\n        if (value !== undefined) {\n            return new Color(value);\n        }\n        return colorString.to.hex(this.rgb().round().color);\n    },\n    hexa (value) {\n        if (value !== undefined) {\n            return new Color(value);\n        }\n        const rgbArray = this.rgb().round().color;\n        let alphaHex = Math.round(this.valpha * 255).toString(16).toUpperCase();\n        if (alphaHex.length === 1) {\n            alphaHex = \"0\" + alphaHex;\n        }\n        return colorString.to.hex(rgbArray) + alphaHex;\n    },\n    rgbNumber () {\n        const rgb = this.rgb().color;\n        return (rgb[0] & 0xFF) << 16 | (rgb[1] & 0xFF) << 8 | rgb[2] & 0xFF;\n    },\n    luminosity () {\n        // http://www.w3.org/TR/WCAG20/#relativeluminancedef\n        const rgb = this.rgb().color;\n        const lum = [];\n        for (const [i, element] of rgb.entries()){\n            const chan = element / 255;\n            lum[i] = chan <= 0.04045 ? chan / 12.92 : ((chan + 0.055) / 1.055) ** 2.4;\n        }\n        return 0.2126 * lum[0] + 0.7152 * lum[1] + 0.0722 * lum[2];\n    },\n    contrast (color2) {\n        // http://www.w3.org/TR/WCAG20/#contrast-ratiodef\n        const lum1 = this.luminosity();\n        const lum2 = color2.luminosity();\n        if (lum1 > lum2) {\n            return (lum1 + 0.05) / (lum2 + 0.05);\n        }\n        return (lum2 + 0.05) / (lum1 + 0.05);\n    },\n    level (color2) {\n        // https://www.w3.org/TR/WCAG/#contrast-enhanced\n        const contrastRatio = this.contrast(color2);\n        if (contrastRatio >= 7) {\n            return \"AAA\";\n        }\n        return contrastRatio >= 4.5 ? \"AA\" : \"\";\n    },\n    isDark () {\n        // YIQ equation from http://24ways.org/2010/calculating-color-contrast\n        const rgb = this.rgb().color;\n        const yiq = (rgb[0] * 2126 + rgb[1] * 7152 + rgb[2] * 722) / 10000;\n        return yiq < 128;\n    },\n    isLight () {\n        return !this.isDark();\n    },\n    negate () {\n        const rgb = this.rgb();\n        for(let i = 0; i < 3; i++){\n            rgb.color[i] = 255 - rgb.color[i];\n        }\n        return rgb;\n    },\n    lighten (ratio) {\n        const hsl = this.hsl();\n        hsl.color[2] += hsl.color[2] * ratio;\n        return hsl;\n    },\n    darken (ratio) {\n        const hsl = this.hsl();\n        hsl.color[2] -= hsl.color[2] * ratio;\n        return hsl;\n    },\n    saturate (ratio) {\n        const hsl = this.hsl();\n        hsl.color[1] += hsl.color[1] * ratio;\n        return hsl;\n    },\n    desaturate (ratio) {\n        const hsl = this.hsl();\n        hsl.color[1] -= hsl.color[1] * ratio;\n        return hsl;\n    },\n    whiten (ratio) {\n        const hwb = this.hwb();\n        hwb.color[1] += hwb.color[1] * ratio;\n        return hwb;\n    },\n    blacken (ratio) {\n        const hwb = this.hwb();\n        hwb.color[2] += hwb.color[2] * ratio;\n        return hwb;\n    },\n    grayscale () {\n        // http://en.wikipedia.org/wiki/Grayscale#Converting_color_to_grayscale\n        const rgb = this.rgb().color;\n        const value = rgb[0] * 0.3 + rgb[1] * 0.59 + rgb[2] * 0.11;\n        return Color.rgb(value, value, value);\n    },\n    fade (ratio) {\n        return this.alpha(this.valpha - this.valpha * ratio);\n    },\n    opaquer (ratio) {\n        return this.alpha(this.valpha + this.valpha * ratio);\n    },\n    rotate (degrees) {\n        const hsl = this.hsl();\n        let hue = hsl.color[0];\n        hue = (hue + degrees) % 360;\n        hue = hue < 0 ? 360 + hue : hue;\n        hsl.color[0] = hue;\n        return hsl;\n    },\n    mix (mixinColor, weight) {\n        // Ported from sass implementation in C\n        // https://github.com/sass/libsass/blob/0e6b4a2850092356aa3ece07c6b249f0221caced/functions.cpp#L209\n        if (!mixinColor || !mixinColor.rgb) {\n            throw new Error('Argument to \"mix\" was not a Color instance, but rather an instance of ' + typeof mixinColor);\n        }\n        const color1 = mixinColor.rgb();\n        const color2 = this.rgb();\n        const p = weight === undefined ? 0.5 : weight;\n        const w = 2 * p - 1;\n        const a = color1.alpha() - color2.alpha();\n        const w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2;\n        const w2 = 1 - w1;\n        return Color.rgb(w1 * color1.red() + w2 * color2.red(), w1 * color1.green() + w2 * color2.green(), w1 * color1.blue() + w2 * color2.blue(), color1.alpha() * p + color2.alpha() * (1 - p));\n    }\n};\n// Model conversion methods and static constructors\nfor (const model of Object.keys(convert)){\n    if (skippedModels.includes(model)) {\n        continue;\n    }\n    const { channels } = convert[model];\n    // Conversion methods\n    Color.prototype[model] = function(...args) {\n        if (this.model === model) {\n            return new Color(this);\n        }\n        if (args.length > 0) {\n            return new Color(args, model);\n        }\n        return new Color([\n            ...assertArray(convert[this.model][model].raw(this.color)),\n            this.valpha\n        ], model);\n    };\n    // 'static' construction methods\n    Color[model] = function(...args) {\n        let color = args[0];\n        if (typeof color === \"number\") {\n            color = zeroArray(args, channels);\n        }\n        return new Color(color, model);\n    };\n}\nfunction roundTo(number, places) {\n    return Number(number.toFixed(places));\n}\nfunction roundToPlace(places) {\n    return function(number) {\n        return roundTo(number, places);\n    };\n}\nfunction getset(model, channel, modifier) {\n    model = Array.isArray(model) ? model : [\n        model\n    ];\n    for (const m of model){\n        (limiters[m] || (limiters[m] = []))[channel] = modifier;\n    }\n    model = model[0];\n    return function(value) {\n        let result;\n        if (value !== undefined) {\n            if (modifier) {\n                value = modifier(value);\n            }\n            result = this[model]();\n            result.color[channel] = value;\n            return result;\n        }\n        result = this[model]().color[channel];\n        if (modifier) {\n            result = modifier(result);\n        }\n        return result;\n    };\n}\nfunction maxfn(max) {\n    return function(v) {\n        return Math.max(0, Math.min(max, v));\n    };\n}\nfunction assertArray(value) {\n    return Array.isArray(value) ? value : [\n        value\n    ];\n}\nfunction zeroArray(array, length) {\n    for(let i = 0; i < length; i++){\n        if (typeof array[i] !== \"number\") {\n            array[i] = 0;\n        }\n    }\n    return array;\n}\nmodule.exports = Color;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/color/index.js\n");

/***/ })

};
;