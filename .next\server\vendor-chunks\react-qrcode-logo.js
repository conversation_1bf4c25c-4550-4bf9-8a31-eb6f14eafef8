"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-qrcode-logo";
exports.ids = ["vendor-chunks/react-qrcode-logo"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-qrcode-logo/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/react-qrcode-logo/dist/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __extends = this && this.__extends || function() {\n    var extendStatics = function(d, b) {\n        extendStatics = Object.setPrototypeOf || ({\n            __proto__: []\n        }) instanceof Array && function(d, b) {\n            d.__proto__ = b;\n        } || function(d, b) {\n            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];\n        };\n        return extendStatics(d, b);\n    };\n    return function(d, b) {\n        extendStatics(d, b);\n        function __() {\n            this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n}();\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.QRCode = void 0;\nvar isEqual = __webpack_require__(/*! lodash.isequal */ \"(ssr)/./node_modules/lodash.isequal/index.js\");\nvar qrGenerator = __webpack_require__(/*! qrcode-generator */ \"(ssr)/./node_modules/qrcode-generator/qrcode.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar ReactDOM = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\nvar QRCode = /** @class */ function(_super) {\n    __extends(QRCode, _super);\n    function QRCode(props) {\n        var _this = _super.call(this, props) || this;\n        _this.canvas = React.createRef();\n        return _this;\n    }\n    QRCode.utf16to8 = function(str) {\n        var out = \"\", i, c;\n        var len = str.length;\n        for(i = 0; i < len; i++){\n            c = str.charCodeAt(i);\n            if (c >= 0x0001 && c <= 0x007F) {\n                out += str.charAt(i);\n            } else if (c > 0x07FF) {\n                out += String.fromCharCode(0xE0 | c >> 12 & 0x0F);\n                out += String.fromCharCode(0x80 | c >> 6 & 0x3F);\n                out += String.fromCharCode(0x80 | c >> 0 & 0x3F);\n            } else {\n                out += String.fromCharCode(0xC0 | c >> 6 & 0x1F);\n                out += String.fromCharCode(0x80 | c >> 0 & 0x3F);\n            }\n        }\n        return out;\n    };\n    /**\n     * Draw a rounded square in the canvas\n     */ QRCode.prototype.drawRoundedSquare = function(lineWidth, x, y, size, color, radii, fill, ctx) {\n        ctx.lineWidth = lineWidth;\n        ctx.fillStyle = color;\n        ctx.strokeStyle = color;\n        // Adjust coordinates so that the outside of the stroke is aligned to the edges\n        y += lineWidth / 2;\n        x += lineWidth / 2;\n        size -= lineWidth;\n        if (!Array.isArray(radii)) {\n            radii = [\n                radii,\n                radii,\n                radii,\n                radii\n            ];\n        }\n        // Radius should not be greater than half the size or less than zero\n        radii = radii.map(function(r) {\n            r = Math.min(r, size / 2);\n            return r < 0 ? 0 : r;\n        });\n        var rTopLeft = radii[0] || 0;\n        var rTopRight = radii[1] || 0;\n        var rBottomRight = radii[2] || 0;\n        var rBottomLeft = radii[3] || 0;\n        ctx.beginPath();\n        ctx.moveTo(x + rTopLeft, y);\n        ctx.lineTo(x + size - rTopRight, y);\n        if (rTopRight) ctx.quadraticCurveTo(x + size, y, x + size, y + rTopRight);\n        ctx.lineTo(x + size, y + size - rBottomRight);\n        if (rBottomRight) ctx.quadraticCurveTo(x + size, y + size, x + size - rBottomRight, y + size);\n        ctx.lineTo(x + rBottomLeft, y + size);\n        if (rBottomLeft) ctx.quadraticCurveTo(x, y + size, x, y + size - rBottomLeft);\n        ctx.lineTo(x, y + rTopLeft);\n        if (rTopLeft) ctx.quadraticCurveTo(x, y, x + rTopLeft, y);\n        ctx.closePath();\n        ctx.stroke();\n        if (fill) {\n            ctx.fill();\n        }\n    };\n    /**\n     * Draw a single positional pattern eye.\n     */ QRCode.prototype.drawPositioningPattern = function(ctx, cellSize, offset, row, col, color, radii) {\n        if (radii === void 0) {\n            radii = [\n                0,\n                0,\n                0,\n                0\n            ];\n        }\n        var lineWidth = Math.ceil(cellSize);\n        var radiiOuter;\n        var radiiInner;\n        if (typeof radii !== \"number\" && !Array.isArray(radii)) {\n            radiiOuter = radii.outer || 0;\n            radiiInner = radii.inner || 0;\n        } else {\n            radiiOuter = radii;\n            radiiInner = radiiOuter;\n        }\n        var colorOuter;\n        var colorInner;\n        if (typeof color !== \"string\") {\n            colorOuter = color.outer;\n            colorInner = color.inner;\n        } else {\n            colorOuter = color;\n            colorInner = color;\n        }\n        var y = row * cellSize + offset;\n        var x = col * cellSize + offset;\n        var size = cellSize * 7;\n        // Outer box\n        this.drawRoundedSquare(lineWidth, x, y, size, colorOuter, radiiOuter, false, ctx);\n        // Inner box\n        size = cellSize * 3;\n        y += cellSize * 2;\n        x += cellSize * 2;\n        this.drawRoundedSquare(lineWidth, x, y, size, colorInner, radiiInner, true, ctx);\n    };\n    ;\n    /**\n     * Is this dot inside a positional pattern zone.\n     */ QRCode.prototype.isInPositioninZone = function(col, row, zones) {\n        return zones.some(function(zone) {\n            return row >= zone.row && row <= zone.row + 7 && col >= zone.col && col <= zone.col + 7;\n        });\n    };\n    QRCode.prototype.transformPixelLengthIntoNumberOfCells = function(pixelLength, cellSize) {\n        return pixelLength / cellSize;\n    };\n    QRCode.prototype.isCoordinateInImage = function(col, row, dWidthLogo, dHeightLogo, dxLogo, dyLogo, cellSize, logoImage) {\n        if (logoImage) {\n            var numberOfCellsMargin = 2;\n            var firstRowOfLogo = this.transformPixelLengthIntoNumberOfCells(dxLogo, cellSize);\n            var firstColumnOfLogo = this.transformPixelLengthIntoNumberOfCells(dyLogo, cellSize);\n            var logoWidthInCells = this.transformPixelLengthIntoNumberOfCells(dWidthLogo, cellSize) - 1;\n            var logoHeightInCells = this.transformPixelLengthIntoNumberOfCells(dHeightLogo, cellSize) - 1;\n            return row >= firstRowOfLogo - numberOfCellsMargin && row <= firstRowOfLogo + logoWidthInCells + numberOfCellsMargin // check rows\n             && col >= firstColumnOfLogo - numberOfCellsMargin && col <= firstColumnOfLogo + logoHeightInCells + numberOfCellsMargin; // check cols\n        } else {\n            return false;\n        }\n    };\n    QRCode.prototype.shouldComponentUpdate = function(nextProps) {\n        return !isEqual(this.props, nextProps);\n    };\n    QRCode.prototype.componentDidMount = function() {\n        this.update();\n    };\n    QRCode.prototype.componentDidUpdate = function() {\n        this.update();\n    };\n    QRCode.prototype.update = function() {\n        var _a = this.props, value = _a.value, ecLevel = _a.ecLevel, enableCORS = _a.enableCORS, bgColor = _a.bgColor, fgColor = _a.fgColor, logoImage = _a.logoImage, logoOpacity = _a.logoOpacity, logoOnLoad = _a.logoOnLoad, removeQrCodeBehindLogo = _a.removeQrCodeBehindLogo, qrStyle = _a.qrStyle, eyeRadius = _a.eyeRadius, eyeColor = _a.eyeColor, logoPaddingStyle = _a.logoPaddingStyle;\n        // just make sure that these params are passed as numbers\n        var size = +this.props.size;\n        var quietZone = +this.props.quietZone;\n        var logoWidth = this.props.logoWidth ? +this.props.logoWidth : 0;\n        var logoHeight = this.props.logoHeight ? +this.props.logoHeight : 0;\n        var logoPadding = this.props.logoPadding ? +this.props.logoPadding : 0;\n        var qrCode = qrGenerator(0, ecLevel);\n        qrCode.addData(QRCode.utf16to8(value));\n        qrCode.make();\n        var canvas = ReactDOM.findDOMNode(this.canvas.current);\n        var ctx = canvas.getContext(\"2d\");\n        var canvasSize = size + 2 * quietZone;\n        var length = qrCode.getModuleCount();\n        var cellSize = size / length;\n        var scale = window.devicePixelRatio || 1;\n        canvas.height = canvas.width = canvasSize * scale;\n        ctx.scale(scale, scale);\n        ctx.fillStyle = bgColor;\n        ctx.fillRect(0, 0, canvasSize, canvasSize);\n        var offset = quietZone;\n        var positioningZones = [\n            {\n                row: 0,\n                col: 0\n            },\n            {\n                row: 0,\n                col: length - 7\n            },\n            {\n                row: length - 7,\n                col: 0\n            }\n        ];\n        ctx.strokeStyle = fgColor;\n        if (qrStyle === \"dots\") {\n            ctx.fillStyle = fgColor;\n            var radius = cellSize / 2;\n            for(var row = 0; row < length; row++){\n                for(var col = 0; col < length; col++){\n                    if (qrCode.isDark(row, col) && !this.isInPositioninZone(row, col, positioningZones)) {\n                        ctx.beginPath();\n                        ctx.arc(Math.round(col * cellSize) + radius + offset, Math.round(row * cellSize) + radius + offset, radius / 100 * 75, 0, 2 * Math.PI, false);\n                        ctx.closePath();\n                        ctx.fill();\n                    }\n                }\n            }\n        } else if (qrStyle === \"fluid\") {\n            var radius = Math.ceil(cellSize / 2);\n            for(var row = 0; row < length; row++){\n                for(var col = 0; col < length; col++){\n                    if (qrCode.isDark(row, col) && !this.isInPositioninZone(row, col, positioningZones)) {\n                        var roundedCorners = [\n                            false,\n                            false,\n                            false,\n                            false\n                        ]; // top-left, top-right, bottom-right, bottom-left\n                        if (row > 0 && !qrCode.isDark(row - 1, col) && col > 0 && !qrCode.isDark(row, col - 1)) roundedCorners[0] = true;\n                        if (row > 0 && !qrCode.isDark(row - 1, col) && col < length - 1 && !qrCode.isDark(row, col + 1)) roundedCorners[1] = true;\n                        if (row < length - 1 && !qrCode.isDark(row + 1, col) && col < length - 1 && !qrCode.isDark(row, col + 1)) roundedCorners[2] = true;\n                        if (row < length - 1 && !qrCode.isDark(row + 1, col) && col > 0 && !qrCode.isDark(row, col - 1)) roundedCorners[3] = true;\n                        var w = Math.ceil((col + 1) * cellSize) - Math.floor(col * cellSize);\n                        var h = Math.ceil((row + 1) * cellSize) - Math.floor(row * cellSize);\n                        ctx.fillStyle = fgColor;\n                        ctx.beginPath();\n                        ctx.arc(Math.round(col * cellSize) + radius + offset, Math.round(row * cellSize) + radius + offset, radius, 0, 2 * Math.PI, false);\n                        ctx.closePath();\n                        ctx.fill();\n                        if (!roundedCorners[0]) ctx.fillRect(Math.round(col * cellSize) + offset, Math.round(row * cellSize) + offset, w / 2, h / 2);\n                        if (!roundedCorners[1]) ctx.fillRect(Math.round(col * cellSize) + offset + Math.floor(w / 2), Math.round(row * cellSize) + offset, w / 2, h / 2);\n                        if (!roundedCorners[2]) ctx.fillRect(Math.round(col * cellSize) + offset + Math.floor(w / 2), Math.round(row * cellSize) + offset + Math.floor(h / 2), w / 2, h / 2);\n                        if (!roundedCorners[3]) ctx.fillRect(Math.round(col * cellSize) + offset, Math.round(row * cellSize) + offset + Math.floor(h / 2), w / 2, h / 2);\n                    }\n                }\n            }\n        } else {\n            for(var row = 0; row < length; row++){\n                for(var col = 0; col < length; col++){\n                    if (qrCode.isDark(row, col) && !this.isInPositioninZone(row, col, positioningZones)) {\n                        ctx.fillStyle = fgColor;\n                        var w = Math.ceil((col + 1) * cellSize) - Math.floor(col * cellSize);\n                        var h = Math.ceil((row + 1) * cellSize) - Math.floor(row * cellSize);\n                        ctx.fillRect(Math.round(col * cellSize) + offset, Math.round(row * cellSize) + offset, w, h);\n                    }\n                }\n            }\n        }\n        // Draw positioning patterns\n        for(var i = 0; i < 3; i++){\n            var _b = positioningZones[i], row = _b.row, col = _b.col;\n            var radii = eyeRadius;\n            var color = void 0;\n            if (Array.isArray(radii)) {\n                radii = radii[i];\n            }\n            if (typeof radii == \"number\") {\n                radii = [\n                    radii,\n                    radii,\n                    radii,\n                    radii\n                ];\n            }\n            if (!eyeColor) {\n                color = fgColor;\n            } else {\n                if (Array.isArray(eyeColor)) {\n                    color = eyeColor[i];\n                } else {\n                    color = eyeColor;\n                }\n            }\n            this.drawPositioningPattern(ctx, cellSize, offset, row, col, color, radii);\n        }\n        if (logoImage) {\n            var image_1 = new Image();\n            if (enableCORS) {\n                image_1.crossOrigin = \"Anonymous\";\n            }\n            image_1.onload = function() {\n                ctx.save();\n                var dWidthLogo = logoWidth || size * 0.2;\n                var dHeightLogo = logoHeight || dWidthLogo;\n                var dxLogo = (size - dWidthLogo) / 2;\n                var dyLogo = (size - dHeightLogo) / 2;\n                if (removeQrCodeBehindLogo || logoPadding) {\n                    ctx.beginPath();\n                    ctx.strokeStyle = bgColor;\n                    ctx.fillStyle = bgColor;\n                    var dWidthLogoPadding = dWidthLogo + 2 * logoPadding;\n                    var dHeightLogoPadding = dHeightLogo + 2 * logoPadding;\n                    var dxLogoPadding = dxLogo + offset - logoPadding;\n                    var dyLogoPadding = dyLogo + offset - logoPadding;\n                    if (logoPaddingStyle === \"circle\") {\n                        var dxCenterLogoPadding = dxLogoPadding + dWidthLogoPadding / 2;\n                        var dyCenterLogoPadding = dyLogoPadding + dHeightLogoPadding / 2;\n                        ctx.ellipse(dxCenterLogoPadding, dyCenterLogoPadding, dWidthLogoPadding / 2, dHeightLogoPadding / 2, 0, 0, 2 * Math.PI);\n                        ctx.stroke();\n                        ctx.fill();\n                    } else {\n                        ctx.fillRect(dxLogoPadding, dyLogoPadding, dWidthLogoPadding, dHeightLogoPadding);\n                    }\n                }\n                ctx.globalAlpha = logoOpacity;\n                ctx.drawImage(image_1, dxLogo + offset, dyLogo + offset, dWidthLogo, dHeightLogo);\n                ctx.restore();\n                if (logoOnLoad) {\n                    logoOnLoad();\n                }\n            };\n            image_1.src = logoImage;\n        }\n    };\n    QRCode.prototype.render = function() {\n        var _a;\n        var qrSize = +this.props.size + 2 * +this.props.quietZone;\n        return React.createElement(\"canvas\", {\n            id: (_a = this.props.id) !== null && _a !== void 0 ? _a : \"react-qrcode-logo\",\n            height: qrSize,\n            width: qrSize,\n            style: {\n                height: qrSize + \"px\",\n                width: qrSize + \"px\"\n            },\n            ref: this.canvas\n        });\n    };\n    QRCode.defaultProps = {\n        value: \"https://reactjs.org/\",\n        ecLevel: \"M\",\n        enableCORS: false,\n        size: 150,\n        quietZone: 10,\n        bgColor: \"#FFFFFF\",\n        fgColor: \"#000000\",\n        logoOpacity: 1,\n        qrStyle: \"squares\",\n        eyeRadius: [\n            0,\n            0,\n            0\n        ],\n        logoPaddingStyle: \"square\"\n    };\n    return QRCode;\n}(React.Component);\nexports.QRCode = QRCode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-qrcode-logo/dist/index.js\n");

/***/ })

};
;