/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/safe-buffer";
exports.ids = ["vendor-chunks/safe-buffer"];
exports.modules = {

/***/ "(ssr)/./node_modules/safe-buffer/index.js":
/*!*******************************************!*\
  !*** ./node_modules/safe-buffer/index.js ***!
  \*******************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */ /* eslint-disable node/no-deprecated-api */ var buffer = __webpack_require__(/*! buffer */ \"buffer\");\nvar Buffer = buffer.Buffer;\n// alternative to using Object.keys for old browsers\nfunction copyProps(src, dst) {\n    for(var key in src){\n        dst[key] = src[key];\n    }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n    module.exports = buffer;\n} else {\n    // Copy properties from require('buffer')\n    copyProps(buffer, exports);\n    exports.Buffer = SafeBuffer;\n}\nfunction SafeBuffer(arg, encodingOrOffset, length) {\n    return Buffer(arg, encodingOrOffset, length);\n}\nSafeBuffer.prototype = Object.create(Buffer.prototype);\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer);\nSafeBuffer.from = function(arg, encodingOrOffset, length) {\n    if (typeof arg === \"number\") {\n        throw new TypeError(\"Argument must not be a number\");\n    }\n    return Buffer(arg, encodingOrOffset, length);\n};\nSafeBuffer.alloc = function(size, fill, encoding) {\n    if (typeof size !== \"number\") {\n        throw new TypeError(\"Argument must be a number\");\n    }\n    var buf = Buffer(size);\n    if (fill !== undefined) {\n        if (typeof encoding === \"string\") {\n            buf.fill(fill, encoding);\n        } else {\n            buf.fill(fill);\n        }\n    } else {\n        buf.fill(0);\n    }\n    return buf;\n};\nSafeBuffer.allocUnsafe = function(size) {\n    if (typeof size !== \"number\") {\n        throw new TypeError(\"Argument must be a number\");\n    }\n    return Buffer(size);\n};\nSafeBuffer.allocUnsafeSlow = function(size) {\n    if (typeof size !== \"number\") {\n        throw new TypeError(\"Argument must be a number\");\n    }\n    return buffer.SlowBuffer(size);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/safe-buffer/index.js\n");

/***/ })

};
;