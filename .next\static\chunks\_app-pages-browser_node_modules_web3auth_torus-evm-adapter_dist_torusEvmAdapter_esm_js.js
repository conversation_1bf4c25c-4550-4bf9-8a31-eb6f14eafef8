"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_web3auth_torus-evm-adapter_dist_torusEvmAdapter_esm_js"],{

/***/ "(app-pages-browser)/./node_modules/@toruslabs/torus-embed/dist/torus.esm.js":
/*!***************************************************************!*\
  !*** ./node_modules/@toruslabs/torus-embed/dist/torus.esm.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BUTTON_POSITION: function() { return /* binding */ BUTTON_POSITION; },\n/* harmony export */   PAYMENT_PROVIDER: function() { return /* binding */ PAYMENT_PROVIDER; },\n/* harmony export */   SUPPORTED_PAYMENT_NETWORK: function() { return /* binding */ SUPPORTED_PAYMENT_NETWORK; },\n/* harmony export */   TORUS_BUILD_ENV: function() { return /* binding */ TORUS_BUILD_ENV; },\n/* harmony export */   TorusInpageProvider: function() { return /* binding */ TorusInpageProvider; },\n/* harmony export */   WALLET_OPENLOGIN_VERIFIER_MAP: function() { return /* binding */ WALLET_OPENLOGIN_VERIFIER_MAP; },\n/* harmony export */   WALLET_VERIFIERS: function() { return /* binding */ WALLET_VERIFIERS; },\n/* harmony export */   \"default\": function() { return /* binding */ Torus; }\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/objectSpread2 */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutProperties */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _toruslabs_http_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @toruslabs/http-helpers */ \"(app-pages-browser)/./node_modules/@toruslabs/http-helpers/dist/httpHelpers.esm.js\");\n/* harmony import */ var _toruslabs_openlogin_jrpc__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @toruslabs/openlogin-jrpc */ \"(app-pages-browser)/./node_modules/@toruslabs/openlogin-jrpc/dist/openloginJrpc.esm.js\");\n/* harmony import */ var lodash_merge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash.merge */ \"(app-pages-browser)/./node_modules/lodash.merge/index.js\");\n/* harmony import */ var lodash_merge__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_merge__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _metamask_rpc_errors__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @metamask/rpc-errors */ \"(app-pages-browser)/./node_modules/@metamask/rpc-errors/dist/errors.mjs\");\n/* harmony import */ var _metamask_rpc_errors__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @metamask/rpc-errors */ \"(app-pages-browser)/./node_modules/@metamask/rpc-errors/dist/classes.mjs\");\n/* harmony import */ var fast_deep_equal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! fast-deep-equal */ \"(app-pages-browser)/./node_modules/fast-deep-equal/index.js\");\n/* harmony import */ var fast_deep_equal__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(fast_deep_equal__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var pump__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! pump */ \"(app-pages-browser)/./node_modules/pump/index.js\");\n/* harmony import */ var pump__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(pump__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var loglevel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! loglevel */ \"(app-pages-browser)/./node_modules/loglevel/lib/loglevel.js\");\n/* harmony import */ var loglevel__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(loglevel__WEBPACK_IMPORTED_MODULE_8__);\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n\n\n\n\n\n\n\n\n\n\n\nconst WALLET_VERIFIERS = {\n  GOOGLE: \"google\",\n  FACEBOOK: \"facebook\",\n  TWITCH: \"twitch\",\n  REDDIT: \"reddit\",\n  DISCORD: \"discord\",\n  EMAIL_PASSWORDLESS: \"torus-auth0-email-passwordless\"\n};\nconst WALLET_OPENLOGIN_VERIFIER_MAP = {\n  [WALLET_VERIFIERS.GOOGLE]: \"tkey-google\",\n  [WALLET_VERIFIERS.FACEBOOK]: \"tkey-facebook\",\n  [WALLET_VERIFIERS.TWITCH]: \"tkey-twitch\",\n  [WALLET_VERIFIERS.REDDIT]: \"tkey-reddit\",\n  [WALLET_VERIFIERS.DISCORD]: \"tkey-discord\",\n  [WALLET_VERIFIERS.EMAIL_PASSWORDLESS]: \"tkey-auth0-email-passwordless\"\n};\nconst PAYMENT_PROVIDER = {\n  MOONPAY: \"moonpay\",\n  RAMPNETWORK: \"rampnetwork\",\n  MERCURYO: \"mercuryo\",\n  TRANSAK: \"transak\",\n  BANXA: \"banxa\"\n};\nconst SUPPORTED_PAYMENT_NETWORK = {\n  MAINNET: \"mainnet\",\n  MATIC: \"matic\",\n  BSC_MAINNET: \"bsc_mainnet\",\n  AVALANCHE_MAINNET: \"avalanche_mainnet\",\n  XDAI: \"xdai\",\n  ARBITRUM_MAINNET: \"arbitrum_mainnet\",\n  OPTIMISM_MAINNET: \"optimism_mainnet\"\n};\nconst TORUS_BUILD_ENV = {\n  PRODUCTION: \"production\",\n  DEVELOPMENT: \"development\",\n  BINANCE: \"binance\",\n  TESTING: \"testing\",\n  LRC: \"lrc\",\n  BETA: \"beta\",\n  BNB: \"bnb\",\n  POLYGON: \"polygon\",\n  ALPHA: \"alpha\"\n};\nconst BUTTON_POSITION = {\n  BOTTOM_LEFT: \"bottom-left\",\n  TOP_LEFT: \"top-left\",\n  BOTTOM_RIGHT: \"bottom-right\",\n  TOP_RIGHT: \"top-right\"\n};\n\n/**\n * From https://min-api.cryptocompare.com/data/v2/pair/mapping/fsym?fsym=BTC&extraParams=YourSite\n * GET https://min-api.cryptocompare.com/data/v2/pair/mapping/fsym?fsym=BTC\n * Then map over returned entries, picking tsym\n *\n * Last updated: Date of commit\n */\nconst CRYPTO_COMPARE_CURRENCIES = [\"ETH\", \"USDT\", \"USDC\", \"TUSD\", \"EOSDT\", \"USD\", \"DAI\", \"GUSD\", \"DKKT\", \"PAX\", \"ILS\", \"RUB\", \"BYN\", \"EUR\", \"GBP\", \"JPY\", \"KRW\", \"PLN\", \"MXN\", \"AUD\", \"BRL\", \"CAD\", \"CHF\", \"KPW\", \"LAK\", \"LBP\", \"LKR\", \"XOF\", \"CNHT\", \"DOGE\", \"UAH\", \"TRY\", \"HKD\", \"XJP\", \"SGD\", \"USC\", \"NZD\", \"NGN\", \"RUR\", \"COP\", \"GHS\", \"EGP\", \"IDR\", \"BHD\", \"CRC\", \"PEN\", \"AED\", \"DOP\", \"PKR\", \"HUF\", \"VND\", \"XAR\", \"LTC\", \"RON\", \"OMR\", \"MYR\", \"DKK\", \"UGX\", \"ZMW\", \"SAR\", \"SEK\", \"GEL\", \"RWF\", \"IRR\", \"TZS\", \"CNY\", \"VEF\", \"BDT\", \"HRK\", \"CLP\", \"THB\", \"XAF\", \"ARS\", \"UYU\", \"SZL\", \"KZT\", \"NOK\", \"KES\", \"PAB\", \"INR\", \"CZK\", \"MAD\", \"TWD\", \"PHP\", \"ZAR\", \"BOB\", \"CDF\", \"DASH\", \"VES\", \"ISK\", \"MWK\", \"BAM\", \"TTD\", \"XRP\", \"JOD\", \"RSD\", \"HNL\", \"BGN\", \"GTQ\", \"BWP\", \"XMR\", \"MMK\", \"QAR\", \"AOA\", \"KWD\", \"MUR\", \"WUSD\", \"WEUR\", \"WAVES\", \"WTRY\", \"LRD\", \"LSL\", \"LYD\", \"AWG\", \"MDL\", \"BTO\", \"EURS\", \"CHFT\", \"MKD\", \"MNT\", \"MOP\", \"MRO\", \"MVR\", \"VOLLAR\", \"CKUSD\", \"KHR\", \"VUV\", \"BITCNY\", \"QC\", \"BBD\", \"NAD\", \"NPR\", \"PGK\", \"PYG\", \"BIF\", \"BMD\", \"BND\", \"XLM\", \"BNB\", \"SCR\", \"BAT\", \"CRO\", \"HT\", \"KCS\", \"LEO\", \"LINK\", \"MKR\", \"NPXS\", \"OMG\", \"REP\", \"ZB\", \"ZIL\", \"ZRX\", \"BCH\", \"BZD\", \"CUP\", \"CVE\", \"DJF\", \"DZD\", \"ERN\", \"ETB\", \"FJD\", \"FKP\", \"BUSD\", \"ANCT\", \"ALL\", \"AMD\", \"ANG\", \"CNYX\", \"IQD\", \"UZS\", \"TND\", \"GGP\", \"XAU\", \"KGS\", \"GIP\", \"JMD\", \"ZEC\", \"USDP\", \"BSV\", \"EMC2\", \"SNT\", \"GTO\", \"POWR\", \"EUSD\", \"EURT\", \"BCY\", \"BTS\", \"ATM\", \"BLOCKPAY\", \"ARDR\", \"AMP\", \"B2X\", \"BITGOLD\", \"BITEUR\", \"ATB\", \"BITUSD\", \"AGRS\", \"DFXT\", \"HIKEN\", \"BIX\", \"KNC\", \"EOS\", \"COB\", \"COSS\", \"BMH\", \"NANO\", \"BDG\", \"BNT\", \"XVG\", \"LKK1Y\", \"LKK\", \"USDK\", \"EURN\", \"NZDT\", \"JSE\", \"GMD\", \"GNF\", \"GYD\", \"YER\", \"XPF\", \"HTG\", \"SLL\", \"SOS\", \"WST\", \"SVC\", \"SYP\", \"NEO\", \"KMF\", \"JUMP\", \"AYA\", \"BLAST\", \"WGR\", \"BCN\", \"BTG\", \"URALS\", \"INN\", \"USDQ\", \"CNH\", \"HUSD\", \"BKRW\", \"NZDX\", \"EURX\", \"CADX\", \"USDEX\", \"JPYX\", \"AUDX\", \"VNDC\", \"EON\", \"GBPX\", \"CHFX\", \"USDJ\", \"IDRT\", \"USDS\", \"USDN\", \"BIDR\", \"IDK\", \"BSD\", \"BTN\", \"KYD\", \"NIO\", \"SBD\", \"SDG\", \"SHP\", \"TOP\", \"XCD\", \"XCHF\", \"CNYT\", \"GYEN\", \"ZUSD\", \"GOLD\", \"TRX\", \"TRYB\", \"PLATC\", \"STRAX\", \"UST\", \"GLM\", \"VAI\", \"BRZ\", \"DDRST\", \"XAUT\", \"MIM\"];\n\n/**\n * currencies supported by the payment provider\n * Last updated: Date of commit\n */\nconst PROVIDER_SUPPORTED_FIAT_CURRENCIES = {\n  // https://integrations.simplex.com/supported_currencies\n  // https://support.moonpay.com/hc/en-gb/articles/360011931457-Which-fiat-currencies-are-supported-\n  [PAYMENT_PROVIDER.MOONPAY]: [\"AUD\", \"BGN\", \"BRL\", \"CAD\", \"CHF\", \"CNY\", \"COP\", \"CZK\", \"DKK\", \"DOP\", \"EGP\", \"EUR\", \"GBP\", \"HKD\", \"HRK\", \"IDR\", \"ILS\", \"JPY\", \"JOD\", \"KES\", \"KRW\", \"KWD\", \"LKR\", \"MAD\", \"MXN\", \"MYR\", \"NGN\", \"NOK\", \"NZD\", \"OMR\", \"PEN\", \"PKR\", \"PLN\", \"RON\", \"RUB\", \"SEK\", \"SGD\", \"THB\", \"TRY\", \"TWD\", \"USD\", \"VND\", \"ZAR\"],\n  // https://support.ramp.network/en/articles/471-supported-fiat-currencies\n  [PAYMENT_PROVIDER.RAMPNETWORK]: [\"USD\", \"EUR\", \"GBP\", \"BMD\", \"BAM\", \"BWP\", \"BRL\", \"BGN\", \"COP\", \"CRC\", \"CZK\", \"DKK\", \"DOP\", \"GEL\", \"GTQ\", \"HNL\", \"HUF\", \"ISK\", \"INR\", \"ILS\", \"KZT\", \"KES\", \"KWD\", \"LAK\", \"MKD\", \"MYR\", \"MXN\", \"MDL\", \"MZN\", \"NZD\", \"NGN\", \"PYG\", \"PEN\", \"PLN\", \"RON\", \"RSD\", \"SGD\", \"ZAR\", \"LKR\", \"SEK\", \"CHF\", \"TJS\", \"THB\", \"UYU\"],\n  // https://help.mercuryo.io/en/articles/6121246-which-fiat-currencies-are-supported\n  // RUB / UAH currently not supported\n  [PAYMENT_PROVIDER.MERCURYO]: [\"EUR\", \"USD\", \"GBP\", \"TRY\", \"JPY\", \"BRL\", \"NGN\", \"VND\", \"MXN\", \"KRW\", \"PLN\", \"SEK\", \"CHF\", \"CAD\", \"CZK\", \"DKK\", \"BGN\", \"HKD\", \"AUD\", \"INR\"],\n  /**\n   * https://support.transak.com/hc/en-us/articles/360020615578-Credit-and-Debit-Card-Payments-through-Transak\n   * or\n   * https://transak.stoplight.io/docs/transak-docs/b3A6OTk1ODQ0-2-get-fiat-currencies\n   */\n  [PAYMENT_PROVIDER.TRANSAK]: [\"ARS\", \"AUD\", \"BBD\", \"BGN\", \"BMD\", \"BRL\", \"CAD\", \"CHF\", \"CLP\", \"CRC\", \"CZK\", \"DKK\", \"DOP\", \"EUR\", \"FJD\", \"FKP\", \"GBP\", \"GIP\", \"HRK\", \"HUF\", \"IDR\", \"ILS\", \"ISK\", \"JMD\", \"JPY\", \"KES\", \"KRW\", \"MDL\", \"MXN\", \"MYR\", \"NOK\", \"NZD\", \"PEN\", \"PHP\", \"PLN\", \"PYG\", \"RON\", \"SEK\", \"SGD\", \"THB\", \"TRY\", \"TZS\", \"USD\", \"ZAR\"],\n  [PAYMENT_PROVIDER.BANXA]: [\"AUD\", \"CAD\", \"CZK\", \"DKK\", \"EUR\", \"GBP\", \"HKD\", \"JPY\", \"NOK\", \"NZD\", \"NZD\", \"PLN\", \"RUB\", \"SEK\", \"SGD\", \"TRY\", \"USD\"]\n};\nconst cryptoCompareCurrenciesSet = new Set(CRYPTO_COMPARE_CURRENCIES);\n/**\n * Fiat currencies that we support\n */\nfunction supportedFiatCurrencies(provider) {\n  const providerSupportedFiatCurrencies = PROVIDER_SUPPORTED_FIAT_CURRENCIES[provider];\n  return providerSupportedFiatCurrencies.filter(currency => cryptoCompareCurrenciesSet.has(currency));\n}\n\nconst paymentProviders$1 = {\n  [PAYMENT_PROVIDER.MOONPAY]: {\n    line1: \"Credit/ Debit Card/ Apple Pay\",\n    line2: \"4.5% or 5 USD\",\n    line3: \"2,000€/day, 10,000€/mo\",\n    supportPage: \"https://help.moonpay.io/en/\",\n    minOrderValue: 24.99,\n    maxOrderValue: 50000,\n    validCurrencies: supportedFiatCurrencies(PAYMENT_PROVIDER.MOONPAY),\n    validCryptoCurrenciesByChain: {\n      [SUPPORTED_PAYMENT_NETWORK.MAINNET]: [{\n        value: \"aave\",\n        display: \"AAVE\"\n      }, {\n        value: \"bat\",\n        display: \"BAT\"\n      }, {\n        value: \"dai\",\n        display: \"DAI\"\n      }, {\n        value: \"eth\",\n        display: \"ETH\"\n      }, {\n        value: \"mkr\",\n        display: \"MKR\"\n      }, {\n        value: \"matic\",\n        display: \"MATIC\"\n      }, {\n        value: \"usdt\",\n        display: \"USDT\"\n      }, {\n        value: \"uni\",\n        display: \"UNI\"\n      }, {\n        value: \"usdc\",\n        display: \"USDC\"\n      }, {\n        value: \"weth\",\n        display: \"WETH\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.MATIC]: [{\n        value: \"eth_polygon\",\n        display: \"ETH\"\n      }, {\n        value: \"matic_polygon\",\n        display: \"MATIC\"\n      }, {\n        value: \"usdc_polygon\",\n        display: \"USDC\"\n      }, {\n        value: \"usdt_polygon\",\n        display: \"USDT\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.BSC_MAINNET]: [{\n        value: \"bnb_bsc\",\n        display: \"BNB\"\n      }, {\n        value: \"busd_bsc\",\n        display: \"BUSD\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.AVALANCHE_MAINNET]: [{\n        value: \"avax_cchain\",\n        display: \"AVAX\"\n      }, {\n        value: \"usdc_cchain\",\n        display: \"USDC\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.ARBITRUM_MAINNET]: [{\n        value: \"eth_arbitrum\",\n        display: \"ETH\"\n      }, {\n        value: \"usdc_arbitrum\",\n        display: \"USDC\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.OPTIMISM_MAINNET]: [{\n        value: \"eth_optimism\",\n        display: \"ETH\"\n      }, {\n        value: \"usdc_optimism\",\n        display: \"USDC\"\n      }]\n    },\n    includeFees: true,\n    api: true,\n    enforceMax: false\n  },\n  [PAYMENT_PROVIDER.RAMPNETWORK]: {\n    line1: \"Debit Card/ <br>Apple Pay/ Bank transfer\",\n    line2: \"0.49% - 2.9%\",\n    line3: \"5,000€/purchase, 20,000€/mo\",\n    supportPage: \"https://instant.ramp.network/\",\n    minOrderValue: 50,\n    maxOrderValue: 20000,\n    validCurrencies: supportedFiatCurrencies(PAYMENT_PROVIDER.RAMPNETWORK),\n    validCryptoCurrenciesByChain: {\n      [SUPPORTED_PAYMENT_NETWORK.MAINNET]: [{\n        value: \"ETH\",\n        display: \"ETH\"\n      }, {\n        value: \"DAI\",\n        display: \"DAI\"\n      }, {\n        value: \"BAT\",\n        display: \"BAT\"\n      }, {\n        value: \"USDC\",\n        display: \"USDC\"\n      }, {\n        value: \"USDT\",\n        display: \"USDT\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.MATIC]: [{\n        value: \"MATIC_BAT\",\n        display: \"BAT\"\n      }, {\n        value: \"MATIC_DAI\",\n        display: \"DAI\"\n      }, {\n        value: \"MATIC_MATIC\",\n        display: \"MATIC\"\n      }, {\n        value: \"MATIC_USDC\",\n        display: \"USDC\"\n      }, {\n        value: \"MATIC_USDT\",\n        display: \"USDT\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.AVALANCHE_MAINNET]: [{\n        value: \"AVAX_AVAX\",\n        display: \"AVAX\"\n      }, {\n        value: \"AVAX_USDC\",\n        display: \"USDC\"\n      }, {\n        value: \"AVAX_USDT\",\n        display: \"USDT\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.ARBITRUM_MAINNET]: [{\n        value: \"ARBITRUM_ETH\",\n        display: \"ETH\"\n      }, {\n        value: \"ARBITRUM_USDC.e\t\",\n        display: \"USDC\"\n      }, {\n        value: \"ARBITRUM_USDT\",\n        display: \"USDT\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.OPTIMISM_MAINNET]: [{\n        value: \"OPTIMISM_DAI\",\n        display: \"DAI\"\n      }, {\n        value: \"OPTIMISM_OPTIMISM\",\n        display: \"OPTIMISM\"\n      }, {\n        value: \"OPTIMISM_USDC\",\n        display: \"USDC\"\n      }, {\n        value: \"OPTIMISM_USDT\",\n        display: \"USDT\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.BSC_MAINNET]: [{\n        value: \"BSC_BNB\",\n        display: \"BNB\"\n      }, {\n        value: \"BSC_BUSD\",\n        display: \"BUSD\"\n      }]\n    },\n    includeFees: true,\n    api: true,\n    receiveHint: \"walletTopUp.receiveHintRamp\",\n    enforceMax: false\n  },\n  [PAYMENT_PROVIDER.MERCURYO]: {\n    line1: \"Credit/ Debit Card/ Apple Pay\",\n    line2: \"3.95% or 4 USD\",\n    line3: \"10,000€/day, 25,000€/mo\",\n    supportPage: \"mailto:<EMAIL>\",\n    minOrderValue: 30,\n    maxOrderValue: 5000,\n    validCurrencies: supportedFiatCurrencies(PAYMENT_PROVIDER.MERCURYO),\n    validCryptoCurrenciesByChain: {\n      [SUPPORTED_PAYMENT_NETWORK.MAINNET]: [{\n        value: \"ETH\",\n        display: \"ETH\"\n      }, {\n        value: \"BAT\",\n        display: \"BAT\"\n      }, {\n        value: \"USDT\",\n        display: \"USDT\"\n      }, {\n        value: \"DAI\",\n        display: \"DAI\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.BSC_MAINNET]: [{\n        value: \"BNB\",\n        display: \"BNB\"\n      }, {\n        value: \"BUSD\",\n        display: \"BUSD\"\n      }, {\n        value: \"1INCH\",\n        display: \"1INCH\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.AVALANCHE_MAINNET]: [{\n        value: \"AVAX\",\n        display: \"AVAX\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.MATIC]: [{\n        value: \"MATIC\",\n        display: \"MATIC\"\n      }]\n    },\n    includeFees: true,\n    api: true,\n    enforceMax: false\n  },\n  [PAYMENT_PROVIDER.TRANSAK]: {\n    line1: \"Apple & Google Pay / Credit/Debit Card<br/>Bangkok Bank Mobile & iPay<br/>Bank Transfer (sepa/gbp) / SCB Mobile & Easy\",\n    line2: \"0.99% - 5.5% or 5 USD\",\n    line3: \"$5,000/day, $28,000/mo\",\n    supportPage: \"https://support.transak.com/hc/en-US\",\n    minOrderValue: 30,\n    maxOrderValue: 500,\n    validCurrencies: supportedFiatCurrencies(PAYMENT_PROVIDER.TRANSAK),\n    validCryptoCurrenciesByChain: {\n      [SUPPORTED_PAYMENT_NETWORK.MAINNET]: [{\n        value: \"1INCH\",\n        display: \"1INCH\"\n      }, {\n        value: \"BAT\",\n        display: \"BAT\"\n      }, {\n        value: \"AAVE\",\n        display: \"AAVE\"\n      }, {\n        value: \"DAI\",\n        display: \"DAI\"\n      }, {\n        value: \"ETH\",\n        display: \"ETH\"\n      }, {\n        value: \"USDC\",\n        display: \"USDC\"\n      }, {\n        value: \"USDT\",\n        display: \"USDT\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.MATIC]: [{\n        value: \"BAT\",\n        display: \"BAT\"\n      }, {\n        value: \"AAVE\",\n        display: \"AAVE\"\n      }, {\n        value: \"DAI\",\n        display: \"DAI\"\n      }, {\n        value: \"MATIC\",\n        display: \"MATIC\"\n      }, {\n        value: \"USDC\",\n        display: \"USDC\"\n      }, {\n        value: \"USDT\",\n        display: \"USDT\"\n      }, {\n        value: \"WETH\",\n        display: \"WETH\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.BSC_MAINNET]: [{\n        value: \"BAT\",\n        display: \"BAT\"\n      }, {\n        value: \"BNB\",\n        display: \"BNB\"\n      }, {\n        value: \"BUSD\",\n        display: \"BUSD\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.AVALANCHE_MAINNET]: [{\n        value: \"AVAX\",\n        display: \"AVAX\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.OPTIMISM_MAINNET]: [{\n        value: \"ETH\",\n        display: \"ETH\"\n      }, {\n        value: \"USDC\",\n        display: \"USDC\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.ARBITRUM_MAINNET]: [{\n        value: \"USDC\",\n        display: \"USDC\"\n      }, {\n        value: \"ETH\",\n        display: \"ETH\"\n      }]\n    },\n    includeFees: true,\n    enforceMax: true\n  },\n  [PAYMENT_PROVIDER.BANXA]: {\n    line1: \"Debit Card/ <br>Apple Pay/ Bank transfer\",\n    line2: \"0.49% - 2.9%\",\n    line3: \"5,000€/purchase, 20,000€/mo\",\n    supportPage: \"https://support.banxa.com\",\n    minOrderValue: 20,\n    maxOrderValue: 15000,\n    validCurrencies: supportedFiatCurrencies(PAYMENT_PROVIDER.BANXA),\n    validCryptoCurrenciesByChain: {\n      [SUPPORTED_PAYMENT_NETWORK.MAINNET]: [{\n        value: \"ETH\",\n        display: \"ETH\"\n      }, {\n        value: \"DAI\",\n        display: \"DAI\"\n      }, {\n        value: \"MKR\",\n        display: \"MKR\"\n      }, {\n        value: \"USDT\",\n        display: \"USDT\"\n      }, {\n        value: \"BUSD\",\n        display: \"BUSD\"\n      }, {\n        value: \"USDC\",\n        display: \"USDC\"\n      }, {\n        value: \"BAT\",\n        display: \"BAT\"\n      }, {\n        value: \"AAVE\",\n        display: \"AAVE\"\n      }, {\n        value: \"COMP\",\n        display: \"COMP\"\n      }, {\n        value: \"UNI\",\n        display: \"UNI\"\n      }],\n      [SUPPORTED_PAYMENT_NETWORK.MATIC]: [{\n        value: \"MATIC\",\n        display: \"MATIC\"\n      }]\n      // [BSC_MAINNET]: [{ value: 'BNB', display: 'BNB' }],\n    },\n    includeFees: true,\n    enforceMax: true\n  }\n};\nconst translations = {\n  en: {\n    embed: {\n      continue: \"Continue\",\n      actionRequired: \"Authorization required\",\n      pendingAction: \"Click continue to proceed with your request in a popup\",\n      cookiesRequired: \"Cookies Required\",\n      enableCookies: \"Please enable cookies in your browser preferences to access Torus\",\n      clickHere: \"More Info\"\n    }\n  },\n  de: {\n    embed: {\n      continue: \"Fortsetzen\",\n      actionRequired: \"Autorisierung erforderlich\",\n      pendingAction: \"Klicken Sie in einem Popup auf Weiter, um mit Ihrer Anfrage fortzufahren\",\n      cookiesRequired: \"Cookies benötigt\",\n      enableCookies: \"Bitte aktivieren Sie Cookies in Ihren Browsereinstellungen, um auf Torus zuzugreifen\",\n      clickHere: \"Mehr Info\"\n    }\n  },\n  ja: {\n    embed: {\n      continue: \"継続する\",\n      actionRequired: \"認証が必要です\",\n      pendingAction: \"続行をクリックして、ポップアップでリクエストを続行します\",\n      cookiesRequired: \"必要なクッキー\",\n      enableCookies: \"Torusにアクセスするには、ブラウザの設定でCookieを有効にしてください。\",\n      clickHere: \"詳しくは\"\n    }\n  },\n  ko: {\n    embed: {\n      continue: \"계속하다\",\n      actionRequired: \"승인 필요\",\n      pendingAction: \"팝업에서 요청을 진행하려면 계속을 클릭하십시오.\",\n      cookiesRequired: \"쿠키 필요\",\n      enableCookies: \"브라우저 환경 설정에서 쿠키를 활성화하여 Torus에 액세스하십시오.\",\n      clickHere: \"더 많은 정보\"\n    }\n  },\n  zh: {\n    embed: {\n      continue: \"继续\",\n      actionRequired: \"需要授权\",\n      pendingAction: \"单击继续以在弹出窗口中继续您的请求\",\n      cookiesRequired: \"必填Cookie\",\n      enableCookies: \"请在您的浏览器首选项中启用cookie以访问Torus。\",\n      clickHere: \"更多信息\"\n    }\n  }\n};\nvar configuration = {\n  supportedVerifierList: Object.values(WALLET_VERIFIERS),\n  paymentProviders: paymentProviders$1,\n  api: \"https://api.tor.us\",\n  translations,\n  prodTorusUrl: \"\",\n  localStorageKeyPrefix: `torus-`\n};\n\nconst htmlToElement = html => {\n  const template = window.document.createElement(\"template\");\n  const trimmedHtml = html.trim(); // Never return a text node of whitespace as the result\n  template.innerHTML = trimmedHtml;\n  return template.content.firstChild;\n};\nconst handleStream = (handle, eventName, handler) => {\n  const handlerWrapper = chunk => {\n    handler(chunk);\n    handle.removeListener(eventName, handlerWrapper);\n  };\n  handle.on(eventName, handlerWrapper);\n};\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\nfunction isStream(stream) {\n  return stream !== null && typeof stream === \"object\" && typeof stream.pipe === \"function\";\n}\nfunction isWritableStream(stream) {\n  return isStream(stream) && stream.writable !== false && typeof stream._write === \"function\" && typeof stream._writableState === \"object\";\n}\nfunction isReadableStream(stream) {\n  return isStream(stream) && stream.readable !== false && typeof stream._read === \"function\" && typeof stream._readableState === \"object\";\n}\nfunction isDuplexStream(stream) {\n  return isWritableStream(stream) && isReadableStream(stream);\n}\n\nvar log = loglevel__WEBPACK_IMPORTED_MODULE_8___default().getLogger(\"torus-embed\");\n\nvar messages = {\n  errors: {\n    disconnected: () => \"Torus: Lost connection to Torus.\",\n    permanentlyDisconnected: () => \"Torus: Disconnected from iframe. Page reload required.\",\n    sendSiteMetadata: () => \"Torus: Failed to send site metadata. This is an internal error, please report this bug.\",\n    unsupportedSync: method => `Torus: The Torus Ethereum provider does not support synchronous methods like ${method} without a callback parameter.`,\n    invalidDuplexStream: () => \"Must provide a Node.js-style duplex stream.\",\n    invalidOptions: (maxEventListeners, shouldSendMetadata) => `Invalid options. Received: { maxEventListeners: ${maxEventListeners}, shouldSendMetadata: ${shouldSendMetadata} }`,\n    invalidRequestArgs: () => `Expected a single, non-array, object argument.`,\n    invalidRequestMethod: () => `'args.method' must be a non-empty string.`,\n    invalidRequestParams: () => `'args.params' must be an object or array if provided.`,\n    invalidLoggerObject: () => `'args.logger' must be an object if provided.`,\n    invalidLoggerMethod: method => `'args.logger' must include required method '${method}'.`\n  },\n  info: {\n    connected: chainId => `Torus: Connected to chain with ID \"${chainId}\".`\n  },\n  warnings: {\n    // deprecated methods\n    enableDeprecation: 'Torus: \"\"ethereum.enable()\" is deprecated and may be removed in the future. ' + 'Please use \"ethereum.send(\"eth_requestAccounts\")\" instead. For more information, see: https://eips.ethereum.org/EIPS/eip-1102',\n    sendDeprecation: 'Torus: \"ethereum.send(...)\" is deprecated and may be removed in the future.' + ' Please use \"ethereum.sendAsync(...)\" or \"ethereum.request(...)\" instead.\\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193'\n  }\n};\n\nconst {\n  paymentProviders\n} = configuration;\nconst validatePaymentProvider = (provider, params) => {\n  const errors = {};\n  if (!provider) {\n    return {\n      errors,\n      isValid: true\n    };\n  }\n  if (provider && !paymentProviders[provider]) {\n    errors.provider = \"Invalid Provider\";\n    return {\n      errors,\n      isValid: Object.keys(errors).length === 0\n    };\n  }\n  const selectedProvider = paymentProviders[provider];\n  const selectedParams = params || {};\n\n  // set default values\n  // if (!selectedParams.selectedCurrency) [selectedParams.selectedCurrency] = selectedProvider.validCurrencies\n  // if (!selectedParams.fiatValue) selectedParams.fiatValue = selectedProvider.minOrderValue\n  // if (!selectedParams.selectedCryptoCurrency) [selectedParams.selectedCryptoCurrency] = selectedProvider.validCryptoCurrencies\n\n  // validations\n  if (selectedParams.fiatValue) {\n    const requestedOrderAmount = +parseFloat(selectedParams.fiatValue.toString()) || 0;\n    if (requestedOrderAmount < selectedProvider.minOrderValue) errors.fiatValue = \"Requested amount is lower than supported\";\n    if (requestedOrderAmount > selectedProvider.maxOrderValue && selectedProvider.enforceMax) errors.fiatValue = \"Requested amount is higher than supported\";\n  }\n  if (selectedParams.selectedCurrency && !selectedProvider.validCurrencies.includes(selectedParams.selectedCurrency)) {\n    errors.selectedCurrency = \"Unsupported currency\";\n  }\n  if (selectedParams.selectedCryptoCurrency) {\n    const validCryptoCurrenciesByChain = Object.values(selectedProvider.validCryptoCurrenciesByChain).flat().map(currency => currency.value);\n    const finalCryptoCurrency = provider === PAYMENT_PROVIDER.MOONPAY ? selectedParams.selectedCryptoCurrency.toLowerCase() : selectedParams.selectedCryptoCurrency;\n    if (validCryptoCurrenciesByChain && !validCryptoCurrenciesByChain.includes(finalCryptoCurrency)) errors.selectedCryptoCurrency = \"Unsupported cryptoCurrency\";\n  }\n  return {\n    errors,\n    isValid: Object.keys(errors).length === 0\n  };\n};\n\n// utility functions\n\n/**\n * json-rpc-engine middleware that logs RPC errors and and validates req.method.\n *\n * @param log - The logging API to use.\n * @returns  json-rpc-engine middleware function\n */\nfunction createErrorMiddleware() {\n  return (req, res, next) => {\n    // json-rpc-engine will terminate the request when it notices this error\n    if (typeof req.method !== \"string\" || !req.method) {\n      res.error = _metamask_rpc_errors__WEBPACK_IMPORTED_MODULE_9__.rpcErrors.invalidRequest({\n        message: `The request 'method' must be a non-empty string.`,\n        data: (0,_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, req || {}), {}, {\n          cause: \"The request 'method' must be a non-empty string.\"\n        })\n      });\n    }\n    next(done => {\n      const {\n        error\n      } = res;\n      if (!error) {\n        return done();\n      }\n      log.error(`MetaMask - RPC Error: ${error.message}`, error);\n      return done();\n    });\n  };\n}\n\n/**\n * Logs a stream disconnection error. Emits an 'error' if given an\n * EventEmitter that has listeners for the 'error' event.\n *\n * @param log - The logging API to use.\n * @param remoteLabel - The label of the disconnected stream.\n * @param error - The associated error to log.\n * @param emitter - The logging API to use.\n */\nfunction logStreamDisconnectWarning(remoteLabel, error, emitter) {\n  let warningMsg = `MetaMask: Lost connection to \"${remoteLabel}\".`;\n  if (error !== null && error !== void 0 && error.stack) {\n    warningMsg += `\\n${error.stack}`;\n  }\n  log.warn(warningMsg);\n  if (emitter && emitter.listenerCount(\"error\") > 0) {\n    emitter.emit(\"error\", warningMsg);\n  }\n}\nconst getPreopenInstanceId = () => Math.random().toString(36).slice(2);\nconst getTorusUrl = async (buildEnv, integrity) => {\n  let torusUrl;\n  let logLevel;\n  // Do not change this line\n  const version = \"4.1.3\";\n  let versionUsed = integrity.version || version;\n  try {\n    if ((buildEnv === \"binance\" || buildEnv === \"production\") && !integrity.version) {\n      let response;\n      if (!configuration.prodTorusUrl) response = await (0,_toruslabs_http_helpers__WEBPACK_IMPORTED_MODULE_3__.get)(`${configuration.api}/latestversion?name=@toruslabs/torus-embed&version=${version}`, {}, {\n        useAPIKey: true\n      });else response = {\n        data: configuration.prodTorusUrl\n      };\n      versionUsed = response.data;\n      // eslint-disable-next-line require-atomic-updates\n      configuration.prodTorusUrl = response.data;\n    }\n  } catch (error) {\n    log.error(error, \"unable to fetch latest version\");\n  }\n  log.info(\"version used: \", versionUsed);\n  switch (buildEnv) {\n    case \"binance\":\n      torusUrl = `https://binance.tor.us/v${versionUsed}`;\n      logLevel = \"info\";\n      break;\n    case \"testing\":\n      torusUrl = \"https://testing.tor.us\";\n      logLevel = \"debug\";\n      break;\n    case \"bnb\":\n      torusUrl = \"https://bnb.tor.us\";\n      logLevel = \"error\";\n      break;\n    case \"polygon\":\n      torusUrl = \"https://polygon.tor.us\";\n      logLevel = \"error\";\n      break;\n    case \"lrc\":\n      torusUrl = \"https://lrc.tor.us\";\n      logLevel = \"debug\";\n      break;\n    case \"beta\":\n      torusUrl = \"https://beta.tor.us\";\n      logLevel = \"debug\";\n      break;\n    case \"development\":\n      torusUrl = \"http://localhost:4050\";\n      logLevel = \"debug\";\n      break;\n    case \"alpha\":\n      torusUrl = \"https://alpha.tor.us\";\n      logLevel = \"debug\";\n      break;\n    default:\n      torusUrl = `https://app.tor.us/v${versionUsed}`;\n      logLevel = \"error\";\n      break;\n  }\n  return {\n    torusUrl,\n    logLevel\n  };\n};\nconst getUserLanguage = () => {\n  let userLanguage = window.navigator.language || \"en-US\";\n  const userLanguages = userLanguage.split(\"-\");\n  userLanguage = Object.prototype.hasOwnProperty.call(configuration.translations, userLanguages[0]) ? userLanguages[0] : \"en\";\n  return userLanguage;\n};\nconst EMITTED_NOTIFICATIONS = [\"eth_subscription\" // per eth-json-rpc-filters/subscriptionManager\n];\nconst NOOP = () => {\n  // empty function\n};\nconst FEATURES_PROVIDER_CHANGE_WINDOW = \"directories=0,titlebar=0,toolbar=0,status=0,location=0,menubar=0,height=660,width=375\";\nconst FEATURES_DEFAULT_WALLET_WINDOW = \"directories=0,titlebar=0,toolbar=0,status=0,location=0,menubar=0,height=740,width=1315\";\nconst FEATURES_CONFIRM_WINDOW = \"directories=0,titlebar=0,toolbar=0,status=0,location=0,menubar=0,height=700,width=450\";\nfunction getPopupFeatures() {\n  // Fixes dual-screen position                             Most browsers      Firefox\n  const dualScreenLeft = window.screenLeft !== undefined ? window.screenLeft : window.screenX;\n  const dualScreenTop = window.screenTop !== undefined ? window.screenTop : window.screenY;\n  const w = 1200;\n  const h = 700;\n  const width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : window.screen.width;\n  const height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : window.screen.height;\n  const systemZoom = 1; // No reliable estimate\n\n  const left = Math.abs((width - w) / 2 / systemZoom + dualScreenLeft);\n  const top = Math.abs((height - h) / 2 / systemZoom + dualScreenTop);\n  const features = `titlebar=0,toolbar=0,status=0,location=0,menubar=0,height=${h / systemZoom},width=${w / systemZoom},top=${top},left=${left}`;\n  return features;\n}\n\n_toruslabs_openlogin_jrpc__WEBPACK_IMPORTED_MODULE_4__.SafeEventEmitter.defaultMaxListeners = 100;\n\n// resolve response.result, reject errors\nconst getRpcPromiseCallback = function (resolve, reject) {\n  let unwrapResult = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  return (error, response) => {\n    if (error || response.error) {\n      return reject(error || response.error);\n    }\n    return !unwrapResult || Array.isArray(response) ? resolve(response) : resolve(response.result);\n  };\n};\nclass TorusInpageProvider extends _toruslabs_openlogin_jrpc__WEBPACK_IMPORTED_MODULE_4__.SafeEventEmitter {\n  constructor(connectionStream) {\n    let {\n      maxEventListeners = 100,\n      shouldSendMetadata = true,\n      jsonRpcStreamName = \"provider\"\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    super();\n    /**\n     * The chain ID of the currently connected Ethereum chain.\n     * See [chainId.network]{@link https://chainid.network} for more information.\n     */\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"chainId\", void 0);\n    /**\n     * The user's currently selected Ethereum address.\n     * If null, MetaMask is either locked or the user has not permitted any\n     * addresses to be viewed.\n     */\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"selectedAddress\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"_rpcEngine\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"networkVersion\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"shouldSendMetadata\", void 0);\n    /**\n     * Indicating that this provider is a MetaMask provider.\n     */\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"isTorus\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"tryPreopenHandle\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"enable\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"_state\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"_jsonRpcConnection\", void 0);\n    if (!isDuplexStream(connectionStream)) {\n      throw new Error(messages.errors.invalidDuplexStream());\n    }\n    this.isTorus = true;\n    this.setMaxListeners(maxEventListeners);\n\n    // private state\n    this._state = (0,_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, TorusInpageProvider._defaultState);\n\n    // public state\n    this.selectedAddress = null;\n    this.networkVersion = null;\n    this.chainId = null;\n    this.shouldSendMetadata = shouldSendMetadata;\n\n    // bind functions (to prevent e.g. web3@1.x from making unbound calls)\n    this._handleAccountsChanged = this._handleAccountsChanged.bind(this);\n    this._handleChainChanged = this._handleChainChanged.bind(this);\n    this._handleUnlockStateChanged = this._handleUnlockStateChanged.bind(this);\n    this._handleConnect = this._handleConnect.bind(this);\n    this._handleDisconnect = this._handleDisconnect.bind(this);\n    this._handleStreamDisconnect = this._handleStreamDisconnect.bind(this);\n    this._sendSync = this._sendSync.bind(this);\n    this._rpcRequest = this._rpcRequest.bind(this);\n    this._initializeState = this._initializeState.bind(this);\n    this.request = this.request.bind(this);\n    this.send = this.send.bind(this);\n    this.sendAsync = this.sendAsync.bind(this);\n    // this.enable = this.enable.bind(this);\n\n    // setup connectionStream multiplexing\n    const mux = new _toruslabs_openlogin_jrpc__WEBPACK_IMPORTED_MODULE_4__.ObjectMultiplex();\n    pump__WEBPACK_IMPORTED_MODULE_7___default()(connectionStream, mux, connectionStream, this._handleStreamDisconnect.bind(this, \"MetaMask\"));\n\n    // ignore phishing warning message (handled elsewhere)\n    mux.ignoreStream(\"phishing\");\n\n    // setup own event listeners\n\n    // EIP-1193 connect\n    this.on(\"connect\", () => {\n      this._state.isConnected = true;\n    });\n\n    // connect to async provider\n\n    const jsonRpcConnection = (0,_toruslabs_openlogin_jrpc__WEBPACK_IMPORTED_MODULE_4__.createStreamMiddleware)();\n    pump__WEBPACK_IMPORTED_MODULE_7___default()(jsonRpcConnection.stream, mux.createStream(jsonRpcStreamName), jsonRpcConnection.stream, this._handleStreamDisconnect.bind(this, \"MetaMask RpcProvider\"));\n\n    // handle RPC requests via dapp-side rpc engine\n    const rpcEngine = new _toruslabs_openlogin_jrpc__WEBPACK_IMPORTED_MODULE_4__.JRPCEngine();\n    rpcEngine.push((0,_toruslabs_openlogin_jrpc__WEBPACK_IMPORTED_MODULE_4__.createIdRemapMiddleware)());\n    rpcEngine.push(createErrorMiddleware());\n    rpcEngine.push(jsonRpcConnection.middleware);\n    this._rpcEngine = rpcEngine;\n\n    // json rpc notification listener\n    jsonRpcConnection.events.on(\"notification\", payload => {\n      const {\n        method,\n        params\n      } = payload;\n      if (method === \"wallet_accountsChanged\") {\n        this._handleAccountsChanged(params);\n      } else if (method === \"wallet_unlockStateChanged\") {\n        this._handleUnlockStateChanged(params);\n      } else if (method === \"wallet_chainChanged\") {\n        this._handleChainChanged(params);\n      } else if (EMITTED_NOTIFICATIONS.includes(payload.method)) {\n        // EIP 1193 subscriptions, per eth-json-rpc-filters/subscriptionManager\n        this.emit(\"data\", payload); // deprecated\n        this.emit(\"notification\", params.result);\n        this.emit(\"message\", {\n          type: method,\n          data: params\n        });\n      }\n\n      // Backward compatibility for older non EIP 1193 subscriptions\n      // this.emit('data', null, payload)\n    });\n  }\n\n  /**\n   * Returns whether the inpage provider is connected to Torus.\n   */\n  isConnected() {\n    return this._state.isConnected;\n  }\n\n  /**\n   * Submits an RPC request for the given method, with the given params.\n   * Resolves with the result of the method call, or rejects on error.\n   *\n   * @param args - The RPC request arguments.\n   * @returns A Promise that resolves with the result of the RPC method,\n   * or rejects if an error is encountered.\n   */\n  async request(args) {\n    if (!args || typeof args !== \"object\" || Array.isArray(args)) {\n      throw _metamask_rpc_errors__WEBPACK_IMPORTED_MODULE_9__.rpcErrors.invalidRequest({\n        message: messages.errors.invalidRequestArgs(),\n        data: (0,_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, args || {}), {}, {\n          cause: messages.errors.invalidRequestArgs()\n        })\n      });\n    }\n    const {\n      method,\n      params\n    } = args;\n    if (typeof method !== \"string\" || method.length === 0) {\n      throw _metamask_rpc_errors__WEBPACK_IMPORTED_MODULE_9__.rpcErrors.invalidRequest({\n        message: messages.errors.invalidRequestMethod(),\n        data: (0,_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, args || {}), {}, {\n          cause: messages.errors.invalidRequestArgs()\n        })\n      });\n    }\n    if (params !== undefined && !Array.isArray(params) && (typeof params !== \"object\" || params === null)) {\n      throw _metamask_rpc_errors__WEBPACK_IMPORTED_MODULE_9__.rpcErrors.invalidRequest({\n        message: messages.errors.invalidRequestParams(),\n        data: (0,_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, args || {}), {}, {\n          cause: messages.errors.invalidRequestArgs()\n        })\n      });\n    }\n    return new Promise((resolve, reject) => {\n      this._rpcRequest({\n        method,\n        params\n      }, getRpcPromiseCallback(resolve, reject));\n    });\n  }\n\n  /**\n   * Submits an RPC request per the given JSON-RPC request object.\n   *\n   * @param payload - The RPC request object.\n   * @param cb - The callback function.\n   */\n  sendAsync(payload, callback) {\n    this._rpcRequest(payload, callback);\n  }\n\n  // Private Methods\n  //= ===================\n  /**\n   * Constructor helper.\n   * Populates initial state by calling 'wallet_getProviderState' and emits\n   * necessary events.\n   */\n  async _initializeState() {\n    try {\n      const {\n        accounts,\n        chainId,\n        isUnlocked,\n        networkVersion\n      } = await this.request({\n        method: \"wallet_getProviderState\"\n      });\n\n      // indicate that we've connected, for EIP-1193 compliance\n      this.emit(\"connect\", {\n        chainId\n      });\n      this._handleChainChanged({\n        chainId,\n        networkVersion\n      });\n      this._handleUnlockStateChanged({\n        accounts,\n        isUnlocked\n      });\n      this._handleAccountsChanged(accounts);\n    } catch (error) {\n      log.error(\"MetaMask: Failed to get initial state. Please report this bug.\", error);\n    } finally {\n      log.info(\"initialized state\");\n      this._state.initialized = true;\n      this.emit(\"_initialized\");\n    }\n  }\n\n  /**\n   * Internal RPC method. Forwards requests to background via the RPC engine.\n   * Also remap ids inbound and outbound.\n   *\n   * @param payload - The RPC request object.\n   * @param callback - The consumer's callback.\n   * @param isInternal - false - Whether the request is internal.\n   */\n  _rpcRequest(payload, callback) {\n    let isInternal = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    let cb = callback;\n    const _payload = payload;\n    if (!Array.isArray(_payload)) {\n      if (!_payload.jsonrpc) {\n        _payload.jsonrpc = \"2.0\";\n      }\n      if (_payload.method === \"eth_accounts\" || _payload.method === \"eth_requestAccounts\") {\n        // handle accounts changing\n        cb = (err, res) => {\n          this._handleAccountsChanged(res.result || [], _payload.method === \"eth_accounts\", isInternal);\n          callback(err, res);\n        };\n      } else if (_payload.method === \"wallet_getProviderState\") {\n        this._rpcEngine.handle(payload, cb);\n        return;\n      }\n    }\n    this.tryPreopenHandle(_payload, cb);\n  }\n\n  /**\n   * Submits an RPC request for the given method, with the given params.\n   *\n   * @deprecated Use \"request\" instead.\n   * @param method - The method to request.\n   * @param params - Any params for the method.\n   * @returns A Promise that resolves with the JSON-RPC response object for the\n   * request.\n   */\n\n  /**\n   * Submits an RPC request per the given JSON-RPC request object.\n   *\n   * @deprecated Use \"request\" instead.\n   * @param payload - A JSON-RPC request object.\n   * @param callback - An error-first callback that will receive the JSON-RPC\n   * response object.\n   */\n\n  /**\n   * Accepts a JSON-RPC request object, and synchronously returns the cached result\n   * for the given method. Only supports 4 specific RPC methods.\n   *\n   * @deprecated Use \"request\" instead.\n   * @param payload - A JSON-RPC request object.\n   * @returns A JSON-RPC response object.\n   */\n\n  send(methodOrPayload, callbackOrArgs) {\n    if (typeof methodOrPayload === \"string\" && (!callbackOrArgs || Array.isArray(callbackOrArgs))) {\n      return new Promise((resolve, reject) => {\n        try {\n          this._rpcRequest({\n            method: methodOrPayload,\n            params: callbackOrArgs\n          }, getRpcPromiseCallback(resolve, reject, false));\n        } catch (error) {\n          reject(error);\n        }\n      });\n    }\n    if (methodOrPayload && typeof methodOrPayload === \"object\" && typeof callbackOrArgs === \"function\") {\n      return this._rpcRequest(methodOrPayload, callbackOrArgs);\n    }\n    return this._sendSync(methodOrPayload);\n  }\n\n  /**\n   * DEPRECATED.\n   * Internal backwards compatibility method, used in send.\n   */\n  _sendSync(payload) {\n    let result;\n    switch (payload.method) {\n      case \"eth_accounts\":\n        result = this.selectedAddress ? [this.selectedAddress] : [];\n        break;\n      case \"eth_coinbase\":\n        result = this.selectedAddress || null;\n        break;\n      case \"eth_uninstallFilter\":\n        this._rpcRequest(payload, NOOP);\n        result = true;\n        break;\n      case \"net_version\":\n        result = this.networkVersion || null;\n        break;\n      default:\n        throw new Error(messages.errors.unsupportedSync(payload.method));\n    }\n    return {\n      id: payload.id,\n      jsonrpc: payload.jsonrpc,\n      result\n    };\n  }\n\n  /**\n   * When the provider becomes connected, updates internal state and emits\n   * required events. Idempotent.\n   *\n   * @param chainId - The ID of the newly connected chain.\n   * emits MetaMaskInpageProvider#connect\n   */\n  _handleConnect(chainId) {\n    if (!this._state.isConnected) {\n      this._state.isConnected = true;\n      this.emit(\"connect\", {\n        chainId\n      });\n      log.debug(messages.info.connected(chainId));\n    }\n  }\n\n  /**\n   * When the provider becomes disconnected, updates internal state and emits\n   * required events. Idempotent with respect to the isRecoverable parameter.\n   *\n   * Error codes per the CloseEvent status codes as required by EIP-1193:\n   * https://developer.mozilla.org/en-US/docs/Web/API/CloseEvent#Status_codes\n   *\n   * @param isRecoverable - Whether the disconnection is recoverable.\n   * @param errorMessage - A custom error message.\n   * emits MetaMaskInpageProvider#disconnect\n   */\n  _handleDisconnect(isRecoverable, errorMessage) {\n    if (this._state.isConnected || !this._state.isPermanentlyDisconnected && !isRecoverable) {\n      this._state.isConnected = false;\n      let error;\n      if (isRecoverable) {\n        error = new _metamask_rpc_errors__WEBPACK_IMPORTED_MODULE_10__.EthereumProviderError(1013,\n        // Try again later\n        errorMessage || messages.errors.disconnected());\n        log.debug(error);\n      } else {\n        error = new _metamask_rpc_errors__WEBPACK_IMPORTED_MODULE_10__.EthereumProviderError(1011,\n        // Internal error\n        errorMessage || messages.errors.permanentlyDisconnected());\n        log.error(error);\n        this.chainId = null;\n        this._state.accounts = null;\n        this.selectedAddress = null;\n        this._state.isUnlocked = false;\n        this._state.isPermanentlyDisconnected = true;\n      }\n      this.emit(\"disconnect\", error);\n    }\n  }\n\n  /**\n   * Called when connection is lost to critical streams.\n   *\n   * emits MetamaskInpageProvider#disconnect\n   */\n  _handleStreamDisconnect(streamName, error) {\n    logStreamDisconnectWarning(streamName, error, this);\n    this._handleDisconnect(false, error ? error.message : undefined);\n  }\n\n  /**\n   * Called when accounts may have changed.\n   */\n  _handleAccountsChanged(accounts) {\n    let isEthAccounts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    let isInternal = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    // defensive programming\n    let finalAccounts = accounts;\n    if (!Array.isArray(finalAccounts)) {\n      log.error(\"MetaMask: Received non-array accounts parameter. Please report this bug.\", finalAccounts);\n      finalAccounts = [];\n    }\n    for (const account of accounts) {\n      if (typeof account !== \"string\") {\n        log.error(\"MetaMask: Received non-string account. Please report this bug.\", accounts);\n        finalAccounts = [];\n        break;\n      }\n    }\n\n    // emit accountsChanged if anything about the accounts array has changed\n    if (!fast_deep_equal__WEBPACK_IMPORTED_MODULE_6___default()(this._state.accounts, finalAccounts)) {\n      // we should always have the correct accounts even before eth_accounts\n      // returns, except in cases where isInternal is true\n      if (isEthAccounts && Array.isArray(this._state.accounts) && this._state.accounts.length > 0 && !isInternal) {\n        log.error('MetaMask: \"eth_accounts\" unexpectedly updated accounts. Please report this bug.', finalAccounts);\n      }\n      this._state.accounts = finalAccounts;\n      this.emit(\"accountsChanged\", finalAccounts);\n    }\n\n    // handle selectedAddress\n    if (this.selectedAddress !== finalAccounts[0]) {\n      this.selectedAddress = finalAccounts[0] || null;\n    }\n  }\n\n  /**\n   * Upon receipt of a new chainId and networkVersion, emits corresponding\n   * events and sets relevant public state.\n   * Does nothing if neither the chainId nor the networkVersion are different\n   * from existing values.\n   *\n   * emits MetamaskInpageProvider#chainChanged\n   * @param networkInfo - An object with network info.\n   */\n  _handleChainChanged() {\n    let {\n      chainId,\n      networkVersion\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (!chainId || !networkVersion) {\n      log.error(\"MetaMask: Received invalid network parameters. Please report this bug.\", {\n        chainId,\n        networkVersion\n      });\n      return;\n    }\n    if (networkVersion === \"loading\") {\n      this._handleDisconnect(true);\n    } else {\n      this._handleConnect(chainId);\n      if (chainId !== this.chainId) {\n        this.chainId = chainId;\n        if (this._state.initialized) {\n          this.emit(\"chainChanged\", this.chainId);\n        }\n      }\n    }\n  }\n\n  /**\n   * Upon receipt of a new isUnlocked state, sets relevant public state.\n   * Calls the accounts changed handler with the received accounts, or an empty\n   * array.\n   *\n   * Does nothing if the received value is equal to the existing value.\n   * There are no lock/unlock events.\n   *\n   * @param opts - Options bag.\n   */\n  _handleUnlockStateChanged() {\n    let {\n      accounts,\n      isUnlocked\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (typeof isUnlocked !== \"boolean\") {\n      log.error(\"MetaMask: Received invalid isUnlocked parameter. Please report this bug.\", {\n        isUnlocked\n      });\n      return;\n    }\n    if (isUnlocked !== this._state.isUnlocked) {\n      this._state.isUnlocked = isUnlocked;\n      this._handleAccountsChanged(accounts || []);\n    }\n  }\n}\n(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(TorusInpageProvider, \"_defaultState\", {\n  accounts: null,\n  isConnected: false,\n  isUnlocked: false,\n  initialized: false,\n  isPermanentlyDisconnected: false,\n  hasEmittedConnection: false\n});\n\nclass PopupHandler extends _toruslabs_openlogin_jrpc__WEBPACK_IMPORTED_MODULE_4__.SafeEventEmitter {\n  constructor(_ref) {\n    let {\n      url,\n      target,\n      features,\n      timeout = 30000\n    } = _ref;\n    super();\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"url\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"target\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"features\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"window\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"windowTimer\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"iClosedWindow\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"timeout\", void 0);\n    this.url = url;\n    this.target = target || \"_blank\";\n    this.features = features || getPopupFeatures();\n    this.window = undefined;\n    this.windowTimer = undefined;\n    this.iClosedWindow = false;\n    this.timeout = timeout;\n    this._setupTimer();\n  }\n  _setupTimer() {\n    this.windowTimer = Number(setInterval(() => {\n      if (this.window && this.window.closed) {\n        clearInterval(this.windowTimer);\n        setTimeout(() => {\n          if (!this.iClosedWindow) {\n            this.emit(\"close\");\n          }\n          this.iClosedWindow = false;\n          this.window = undefined;\n        }, this.timeout);\n      }\n      if (this.window === undefined) clearInterval(this.windowTimer);\n    }, 500));\n  }\n  open() {\n    var _this$window;\n    this.window = window.open(this.url.href, this.target, this.features);\n    if ((_this$window = this.window) !== null && _this$window !== void 0 && _this$window.focus) this.window.focus();\n  }\n  close() {\n    this.iClosedWindow = true;\n    if (this.window) this.window.close();\n  }\n  redirect(locationReplaceOnRedirect) {\n    if (locationReplaceOnRedirect) {\n      window.location.replace(this.url.href);\n    } else {\n      window.location.href = this.url.href;\n    }\n  }\n}\n\n/**\n * Returns whether the given image URL exists\n * @param url - the url of the image\n * @returns - whether the image exists\n */\nfunction imgExists(url) {\n  return new Promise((resolve, reject) => {\n    try {\n      const img = document.createElement(\"img\");\n      img.onload = () => resolve(true);\n      img.onerror = () => resolve(false);\n      img.src = url;\n    } catch (e) {\n      reject(e);\n    }\n  });\n}\n\n/**\n * Extracts a name for the site from the DOM\n */\nconst getSiteName = window => {\n  const {\n    document\n  } = window;\n  const siteName = document.querySelector('head > meta[property=\"og:site_name\"]');\n  if (siteName) {\n    return siteName.content;\n  }\n  const metaTitle = document.querySelector('head > meta[name=\"title\"]');\n  if (metaTitle) {\n    return metaTitle.content;\n  }\n  if (document.title && document.title.length > 0) {\n    return document.title;\n  }\n  return window.location.hostname;\n};\n\n/**\n * Extracts an icon for the site from the DOM\n */\nasync function getSiteIcon(window) {\n  const {\n    document\n  } = window;\n\n  // Use the site's favicon if it exists\n  let icon = document.querySelector('head > link[rel=\"shortcut icon\"]');\n  if (icon && (await imgExists(icon.href))) {\n    return icon.href;\n  }\n\n  // Search through available icons in no particular order\n  icon = Array.from(document.querySelectorAll('head > link[rel=\"icon\"]')).find(_icon => Boolean(_icon.href));\n  if (icon && (await imgExists(icon.href))) {\n    return icon.href;\n  }\n  return null;\n}\n\n/**\n * Gets site metadata and returns it\n *\n */\nconst getSiteMetadata = async () => ({\n  name: getSiteName(window),\n  icon: await getSiteIcon(window)\n});\n\n/**\n * Sends site metadata over an RPC request.\n */\nasync function sendSiteMetadata(engine) {\n  try {\n    const domainMetadata = await getSiteMetadata();\n    // call engine.handle directly to avoid normal RPC request handling\n    engine.handle({\n      jsonrpc: \"2.0\",\n      id: getPreopenInstanceId(),\n      method: \"wallet_sendDomainMetadata\",\n      params: domainMetadata\n    }, NOOP);\n  } catch (error) {\n    log.error({\n      message: messages.errors.sendSiteMetadata(),\n      originalError: error\n    });\n  }\n}\n\nconst _excluded = [\"host\", \"chainId\", \"networkName\"];\nconst UNSAFE_METHODS = [\"eth_sendTransaction\", \"eth_signTypedData\", \"eth_signTypedData_v3\", \"eth_signTypedData_v4\", \"personal_sign\", \"eth_getEncryptionPublicKey\", \"eth_decrypt\", \"wallet_addEthereumChain\", \"wallet_switchEthereumChain\"];\n\n// preload for iframe doesn't work https://bugs.chromium.org/p/chromium/issues/detail?id=593267\n(async function preLoadIframe() {\n  try {\n    if (typeof document === \"undefined\") return;\n    const torusIframeHtml = document.createElement(\"link\");\n    const {\n      torusUrl\n    } = await getTorusUrl(\"production\", {\n      version: \"\"\n    });\n    torusIframeHtml.href = `${torusUrl}/popup`;\n    torusIframeHtml.crossOrigin = \"anonymous\";\n    torusIframeHtml.type = \"text/html\";\n    torusIframeHtml.rel = \"prefetch\";\n    if (torusIframeHtml.relList && torusIframeHtml.relList.supports) {\n      if (torusIframeHtml.relList.supports(\"prefetch\")) {\n        document.head.appendChild(torusIframeHtml);\n      }\n    }\n  } catch (error) {\n    log.warn(error);\n  }\n})();\nclass Torus {\n  constructor() {\n    let {\n      buttonPosition = BUTTON_POSITION.BOTTOM_LEFT,\n      buttonSize = 56,\n      modalZIndex = 99999,\n      apiKey = \"torus-default\"\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"buttonPosition\", BUTTON_POSITION.BOTTOM_LEFT);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"buttonSize\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"torusUrl\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"torusIframe\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"styleLink\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"isLoggedIn\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"isInitialized\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"torusWidgetVisibility\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"torusAlert\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"apiKey\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"modalZIndex\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"alertZIndex\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"torusAlertContainer\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"isIframeFullScreen\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"whiteLabel\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"requestedVerifier\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"currentVerifier\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"embedTranslations\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"ethereum\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"provider\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"communicationMux\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"isLoginCallback\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"paymentProviders\", configuration.paymentProviders);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"loginHint\", \"\");\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"useWalletConnect\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, \"isCustomLogin\", false);\n    this.buttonPosition = buttonPosition;\n    this.buttonSize = buttonSize;\n    this.torusUrl = \"\";\n    this.isLoggedIn = false; // ethereum.enable working\n    this.isInitialized = false; // init done\n    this.torusWidgetVisibility = true;\n    this.requestedVerifier = \"\";\n    this.currentVerifier = \"\";\n    this.apiKey = apiKey;\n    (0,_toruslabs_http_helpers__WEBPACK_IMPORTED_MODULE_3__.setAPIKey)(apiKey);\n    this.modalZIndex = modalZIndex;\n    this.alertZIndex = modalZIndex + 1000;\n    this.isIframeFullScreen = false;\n  }\n  async init() {\n    let {\n      buildEnv = TORUS_BUILD_ENV.PRODUCTION,\n      enableLogging = false,\n      network = {\n        host: \"mainnet\",\n        chainId: null,\n        networkName: \"\",\n        blockExplorer: \"\",\n        ticker: \"\",\n        tickerName: \"\"\n      },\n      loginConfig = {},\n      showTorusButton = true,\n      integrity = {\n        version: \"\"\n      },\n      whiteLabel,\n      useWalletConnect = false,\n      mfaLevel = \"default\"\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (this.isInitialized) throw new Error(\"Already initialized\");\n    const {\n      torusUrl,\n      logLevel\n    } = await getTorusUrl(buildEnv, integrity);\n    log.info(torusUrl, \"url loaded\");\n    this.torusUrl = torusUrl;\n    this.whiteLabel = whiteLabel;\n    this.useWalletConnect = useWalletConnect;\n    this.isCustomLogin = !!(loginConfig && Object.keys(loginConfig).length > 0) || !!(whiteLabel && Object.keys(whiteLabel).length > 0);\n    log.setDefaultLevel(logLevel);\n    if (enableLogging) log.enableAll();else log.disableAll();\n    this.torusWidgetVisibility = showTorusButton;\n    const torusIframeUrl = new URL(torusUrl);\n    if (torusIframeUrl.pathname.endsWith(\"/\")) torusIframeUrl.pathname += \"popup\";else torusIframeUrl.pathname += \"/popup\";\n    torusIframeUrl.hash = `#isCustomLogin=${this.isCustomLogin}`;\n\n    // Iframe code\n    this.torusIframe = htmlToElement(`<iframe\n        id=\"torusIframe\"\n        allow=${useWalletConnect ? \"camera\" : \"\"}\n        class=\"torusIframe\"\n        src=\"${torusIframeUrl.href}\"\n        style=\"display: none; position: fixed; top: 0; right: 0; width: 100%; color-scheme: none;\n        height: 100%; border: none; border-radius: 0; z-index: ${this.modalZIndex}\"\n      ></iframe>`);\n    this.torusAlertContainer = htmlToElement('<div id=\"torusAlertContainer\"></div>');\n    this.torusAlertContainer.style.display = \"none\";\n    this.torusAlertContainer.style.setProperty(\"z-index\", this.alertZIndex.toString());\n    const link = window.document.createElement(\"link\");\n    link.setAttribute(\"rel\", \"stylesheet\");\n    link.setAttribute(\"type\", \"text/css\");\n    link.setAttribute(\"href\", `${torusUrl}/css/widget.css`);\n    this.styleLink = link;\n    const {\n      defaultLanguage = getUserLanguage(),\n      customTranslations = {}\n    } = this.whiteLabel || {};\n    const mergedTranslations = lodash_merge__WEBPACK_IMPORTED_MODULE_5___default()(configuration.translations, customTranslations);\n    const languageTranslations = mergedTranslations[defaultLanguage] || configuration.translations[getUserLanguage()];\n    this.embedTranslations = languageTranslations.embed;\n    return new Promise((resolve, reject) => {\n      this.torusIframe.onload = async () => {\n        // only do this if iframe is not full screen\n        this._setupWeb3();\n        const initStream = this.communicationMux.getStream(\"init_stream\");\n        initStream.on(\"data\", chunk => {\n          const {\n            name,\n            data,\n            error\n          } = chunk;\n          if (name === \"init_complete\" && data.success) {\n            // resolve promise\n            this.isInitialized = true;\n            this._displayIframe(this.isIframeFullScreen);\n            resolve(undefined);\n          } else if (error) {\n            reject(new Error(error));\n          }\n        });\n        initStream.write({\n          name: \"init_stream\",\n          data: {\n            loginConfig,\n            whiteLabel: this.whiteLabel,\n            buttonPosition: this.buttonPosition,\n            buttonSize: this.buttonSize,\n            torusWidgetVisibility: this.torusWidgetVisibility,\n            apiKey: this.apiKey,\n            network,\n            mfaLevel\n          }\n        });\n      };\n      window.document.head.appendChild(this.styleLink);\n      window.document.body.appendChild(this.torusIframe);\n      window.document.body.appendChild(this.torusAlertContainer);\n    });\n  }\n  login() {\n    let {\n      verifier = \"\",\n      login_hint: loginHint = \"\"\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (!this.isInitialized) throw new Error(\"Call init() first\");\n    this.requestedVerifier = verifier;\n    this.loginHint = loginHint;\n    return this.ethereum.enable();\n  }\n  logout() {\n    return new Promise((resolve, reject) => {\n      if (!this.isLoggedIn) {\n        reject(new Error(\"User has not logged in yet\"));\n        return;\n      }\n      const logOutStream = this.communicationMux.getStream(\"logout\");\n      logOutStream.write({\n        name: \"logOut\"\n      });\n      const statusStream = this.communicationMux.getStream(\"status\");\n      const statusStreamHandler = arg => {\n        const status = arg;\n        if (!status.loggedIn) {\n          this.isLoggedIn = false;\n          this.currentVerifier = \"\";\n          this.requestedVerifier = \"\";\n          resolve();\n        } else reject(new Error(\"Some Error Occured\"));\n      };\n      handleStream(statusStream, \"data\", statusStreamHandler);\n    });\n  }\n  async cleanUp() {\n    if (this.isLoggedIn) {\n      await this.logout();\n    }\n    this.clearInit();\n  }\n  clearInit() {\n    function isElement(element) {\n      return element instanceof Element || element instanceof HTMLDocument;\n    }\n    if (isElement(this.styleLink) && window.document.body.contains(this.styleLink)) {\n      this.styleLink.remove();\n      this.styleLink = undefined;\n    }\n    if (isElement(this.torusIframe) && window.document.body.contains(this.torusIframe)) {\n      this.torusIframe.remove();\n      this.torusIframe = undefined;\n    }\n    if (isElement(this.torusAlertContainer) && window.document.body.contains(this.torusAlertContainer)) {\n      this.torusAlert = undefined;\n      this.torusAlertContainer.remove();\n      this.torusAlertContainer = undefined;\n    }\n    this.isInitialized = false;\n  }\n  hideTorusButton() {\n    this.torusWidgetVisibility = false;\n    this._sendWidgetVisibilityStatus(false);\n    this._displayIframe();\n  }\n  showTorusButton() {\n    this.torusWidgetVisibility = true;\n    this._sendWidgetVisibilityStatus(true);\n    this._displayIframe();\n  }\n  setProvider(_ref) {\n    let {\n        host = \"mainnet\",\n        chainId = null,\n        networkName = \"\"\n      } = _ref,\n      rest = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref, _excluded);\n    return new Promise((resolve, reject) => {\n      const providerChangeStream = this.communicationMux.getStream(\"provider_change\");\n      const handler = arg => {\n        const chunk = arg;\n        const {\n          err,\n          success\n        } = chunk.data;\n        log.info(chunk);\n        if (err) {\n          reject(err);\n        } else if (success) {\n          resolve();\n        } else reject(new Error(\"some error occurred\"));\n      };\n      handleStream(providerChangeStream, \"data\", handler);\n      const preopenInstanceId = getPreopenInstanceId();\n      this._handleWindow(preopenInstanceId, {\n        target: \"_blank\",\n        features: FEATURES_PROVIDER_CHANGE_WINDOW\n      });\n      providerChangeStream.write({\n        name: \"show_provider_change\",\n        data: {\n          network: (0,_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            host,\n            chainId,\n            networkName\n          }, rest),\n          preopenInstanceId,\n          override: false\n        }\n      });\n    });\n  }\n  showWallet(path) {\n    let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const showWalletStream = this.communicationMux.getStream(\"show_wallet\");\n    const finalPath = path ? `/${path}` : \"\";\n    showWalletStream.write({\n      name: \"show_wallet\",\n      data: {\n        path: finalPath\n      }\n    });\n    const showWalletHandler = arg => {\n      const chunk = arg;\n      if (chunk.name === \"show_wallet_instance\") {\n        // Let the error propogate up (hence, no try catch)\n        const {\n          instanceId\n        } = chunk.data;\n        const finalUrl = new URL(`${this.torusUrl}/wallet${finalPath}`);\n        // Using URL constructor to prevent js injection and allow parameter validation.!\n        finalUrl.searchParams.append(\"integrity\", \"true\");\n        finalUrl.searchParams.append(\"instanceId\", instanceId);\n        Object.keys(params).forEach(x => {\n          finalUrl.searchParams.append(x, params[x]);\n        });\n        finalUrl.hash = `#isCustomLogin=${this.isCustomLogin}`;\n        const walletWindow = new PopupHandler({\n          url: finalUrl,\n          features: FEATURES_DEFAULT_WALLET_WINDOW\n        });\n        walletWindow.open();\n      }\n    };\n    handleStream(showWalletStream, \"data\", showWalletHandler);\n  }\n  async getPublicAddress(_ref2) {\n    let {\n      verifier,\n      verifierId,\n      isExtended = false\n    } = _ref2;\n    if (!configuration.supportedVerifierList.includes(verifier) || !WALLET_OPENLOGIN_VERIFIER_MAP[verifier]) throw new Error(\"Unsupported verifier\");\n    const walletVerifier = verifier;\n    const openloginVerifier = WALLET_OPENLOGIN_VERIFIER_MAP[verifier];\n    const url = new URL(`https://api.tor.us/lookup/torus`);\n    url.searchParams.append(\"verifier\", openloginVerifier);\n    url.searchParams.append(\"verifierId\", verifierId);\n    url.searchParams.append(\"walletVerifier\", walletVerifier);\n    url.searchParams.append(\"network\", \"mainnet\");\n    url.searchParams.append(\"isExtended\", isExtended.toString());\n    return (0,_toruslabs_http_helpers__WEBPACK_IMPORTED_MODULE_3__.get)(url.href, {\n      headers: {\n        \"Content-Type\": \"application/json; charset=utf-8\"\n      }\n    }, {\n      useAPIKey: true\n    });\n  }\n  getUserInfo(message) {\n    return new Promise((resolve, reject) => {\n      if (this.isLoggedIn) {\n        const userInfoAccessStream = this.communicationMux.getStream(\"user_info_access\");\n        userInfoAccessStream.write({\n          name: \"user_info_access_request\"\n        });\n        const userInfoAccessHandler = arg => {\n          const chunk = arg;\n          const {\n            name,\n            data: {\n              approved,\n              payload,\n              rejected,\n              newRequest\n            }\n          } = chunk;\n          if (name === \"user_info_access_response\") {\n            if (approved) {\n              resolve(payload);\n            } else if (rejected) {\n              reject(new Error(\"User rejected the request\"));\n            } else if (newRequest) {\n              const userInfoStream = this.communicationMux.getStream(\"user_info\");\n              const userInfoHandler = arg2 => {\n                const handlerChunk = arg2;\n                if (handlerChunk.name === \"user_info_response\") {\n                  if (handlerChunk.data.approved) {\n                    resolve(handlerChunk.data.payload);\n                  } else {\n                    reject(new Error(\"User rejected the request\"));\n                  }\n                }\n              };\n              handleStream(userInfoStream, \"data\", userInfoHandler);\n              const preopenInstanceId = getPreopenInstanceId();\n              this._handleWindow(preopenInstanceId, {\n                target: \"_blank\",\n                features: FEATURES_PROVIDER_CHANGE_WINDOW\n              });\n              userInfoStream.write({\n                name: \"user_info_request\",\n                data: {\n                  message,\n                  preopenInstanceId\n                }\n              });\n            }\n          }\n        };\n        handleStream(userInfoAccessStream, \"data\", userInfoAccessHandler);\n      } else reject(new Error(\"User has not logged in yet\"));\n    });\n  }\n  initiateTopup(provider, params) {\n    return new Promise((resolve, reject) => {\n      if (this.isInitialized) {\n        const {\n          errors,\n          isValid\n        } = validatePaymentProvider(provider, params);\n        if (!isValid) {\n          reject(new Error(JSON.stringify(errors)));\n          return;\n        }\n        const topupStream = this.communicationMux.getStream(\"topup\");\n        const topupHandler = arg => {\n          const chunk = arg;\n          if (chunk.name === \"topup_response\") {\n            if (chunk.data.success) {\n              resolve(chunk.data.success);\n            } else {\n              reject(new Error(chunk.data.error));\n            }\n          }\n        };\n        handleStream(topupStream, \"data\", topupHandler);\n        const preopenInstanceId = getPreopenInstanceId();\n        this._handleWindow(preopenInstanceId);\n        topupStream.write({\n          name: \"topup_request\",\n          data: {\n            provider,\n            params,\n            preopenInstanceId\n          }\n        });\n      } else reject(new Error(\"Torus is not initialized yet\"));\n    });\n  }\n  async loginWithPrivateKey(loginParams) {\n    const {\n      privateKey,\n      userInfo\n    } = loginParams;\n    return new Promise((resolve, reject) => {\n      if (this.isInitialized) {\n        if (Buffer.from(privateKey, \"hex\").length !== 32) {\n          reject(new Error(\"Invalid private key, Please provide a 32 byte valid secp25k1 private key\"));\n          return;\n        }\n        const loginPrivKeyStream = this.communicationMux.getStream(\"login_with_private_key\");\n        const loginHandler = arg => {\n          const chunk = arg;\n          if (chunk.name === \"login_with_private_key_response\") {\n            if (chunk.data.success) {\n              resolve(chunk.data.success);\n            } else {\n              reject(new Error(chunk.data.error));\n            }\n          }\n        };\n        handleStream(loginPrivKeyStream, \"data\", loginHandler);\n        loginPrivKeyStream.write({\n          name: \"login_with_private_key_request\",\n          data: {\n            privateKey,\n            userInfo\n          }\n        });\n      } else reject(new Error(\"Torus is not initialized yet\"));\n    });\n  }\n  async showWalletConnectScanner() {\n    if (!this.useWalletConnect) throw new Error(\"Set `useWalletConnect` as true in init function options to use wallet connect scanner\");\n    return new Promise((resolve, reject) => {\n      if (this.isLoggedIn) {\n        const walletConnectStream = this.communicationMux.getStream(\"wallet_connect_stream\");\n        const walletConnectHandler = arg => {\n          const chunk = arg;\n          if (chunk.name === \"wallet_connect_stream_res\") {\n            if (chunk.data.success) {\n              resolve(chunk.data.success);\n            } else {\n              reject(new Error(chunk.data.error));\n            }\n            this._displayIframe();\n          }\n        };\n        handleStream(walletConnectStream, \"data\", walletConnectHandler);\n        walletConnectStream.write({\n          name: \"wallet_connect_stream_req\"\n        });\n        this._displayIframe(true);\n      } else reject(new Error(\"User has not logged in yet\"));\n    });\n  }\n  _handleWindow(preopenInstanceId) {\n    let {\n      url,\n      target,\n      features\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (preopenInstanceId) {\n      const windowStream = this.communicationMux.getStream(\"window\");\n      const finalUrl = new URL(url || `${this.torusUrl}/redirect?preopenInstanceId=${preopenInstanceId}`);\n      if (finalUrl.hash) finalUrl.hash += `&isCustomLogin=${this.isCustomLogin}`;else finalUrl.hash = `#isCustomLogin=${this.isCustomLogin}`;\n      const handledWindow = new PopupHandler({\n        url: finalUrl,\n        target,\n        features\n      });\n      handledWindow.open();\n      if (!handledWindow.window) {\n        this._createPopupBlockAlert(preopenInstanceId, finalUrl.href);\n        return;\n      }\n      windowStream.write({\n        name: \"opened_window\",\n        data: {\n          preopenInstanceId\n        }\n      });\n      const closeHandler = _ref3 => {\n        let {\n          preopenInstanceId: receivedId,\n          close\n        } = _ref3;\n        if (receivedId === preopenInstanceId && close) {\n          handledWindow.close();\n          windowStream.removeListener(\"data\", closeHandler);\n        }\n      };\n      windowStream.on(\"data\", closeHandler);\n      handledWindow.once(\"close\", () => {\n        windowStream.write({\n          data: {\n            preopenInstanceId,\n            closed: true\n          }\n        });\n        windowStream.removeListener(\"data\", closeHandler);\n      });\n    }\n  }\n  _setEmbedWhiteLabel(element) {\n    // Set whitelabel\n    const {\n      theme\n    } = this.whiteLabel || {};\n    if (theme) {\n      const {\n        isDark = false,\n        colors = {}\n      } = theme;\n      if (isDark) element.classList.add(\"torus-dark\");\n      if (colors.torusBrand1) element.style.setProperty(\"--torus-brand-1\", colors.torusBrand1);\n      if (colors.torusGray2) element.style.setProperty(\"--torus-gray-2\", colors.torusGray2);\n    }\n  }\n  _getLogoUrl() {\n    var _this$whiteLabel;\n    let logoUrl = `${this.torusUrl}/images/torus_icon-blue.svg`;\n    if ((_this$whiteLabel = this.whiteLabel) !== null && _this$whiteLabel !== void 0 && (_this$whiteLabel = _this$whiteLabel.theme) !== null && _this$whiteLabel !== void 0 && _this$whiteLabel.isDark) {\n      var _this$whiteLabel2;\n      logoUrl = ((_this$whiteLabel2 = this.whiteLabel) === null || _this$whiteLabel2 === void 0 ? void 0 : _this$whiteLabel2.logoLight) || logoUrl;\n    } else {\n      var _this$whiteLabel3;\n      logoUrl = ((_this$whiteLabel3 = this.whiteLabel) === null || _this$whiteLabel3 === void 0 ? void 0 : _this$whiteLabel3.logoDark) || logoUrl;\n    }\n    return logoUrl;\n  }\n  _sendWidgetVisibilityStatus(status) {\n    const torusWidgetVisibilityStream = this.communicationMux.getStream(\"torus-widget-visibility\");\n    torusWidgetVisibilityStream.write({\n      data: status\n    });\n  }\n  _displayIframe() {\n    let isFull = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    const style = {};\n    const size = this.buttonSize + 14; // 15px padding\n    // set phase\n    if (!isFull) {\n      style.display = this.torusWidgetVisibility ? \"block\" : \"none\";\n      style.height = `${size}px`;\n      style.width = `${size}px`;\n      switch (this.buttonPosition) {\n        case BUTTON_POSITION.TOP_LEFT:\n          style.top = \"0px\";\n          style.left = \"0px\";\n          style.right = \"auto\";\n          style.bottom = \"auto\";\n          break;\n        case BUTTON_POSITION.TOP_RIGHT:\n          style.top = \"0px\";\n          style.right = \"0px\";\n          style.left = \"auto\";\n          style.bottom = \"auto\";\n          break;\n        case BUTTON_POSITION.BOTTOM_RIGHT:\n          style.bottom = \"0px\";\n          style.right = \"0px\";\n          style.top = \"auto\";\n          style.left = \"auto\";\n          break;\n        case BUTTON_POSITION.BOTTOM_LEFT:\n        default:\n          style.bottom = \"0px\";\n          style.left = \"0px\";\n          style.top = \"auto\";\n          style.right = \"auto\";\n          break;\n      }\n    } else {\n      style.display = \"block\";\n      style.width = \"100%\";\n      style.height = \"100%\";\n      style.top = \"0px\";\n      style.right = \"0px\";\n      style.left = \"0px\";\n      style.bottom = \"0px\";\n    }\n    Object.assign(this.torusIframe.style, style);\n    this.isIframeFullScreen = isFull;\n  }\n  _setupWeb3() {\n    log.info(\"setupWeb3 running\");\n    // setup background connection\n    const metamaskStream = new _toruslabs_openlogin_jrpc__WEBPACK_IMPORTED_MODULE_4__.BasePostMessageStream({\n      name: \"embed_metamask\",\n      target: \"iframe_metamask\",\n      targetWindow: this.torusIframe.contentWindow,\n      targetOrigin: new URL(this.torusUrl).origin\n    });\n\n    // Due to compatibility reasons, we should not set up multiplexing on window.metamaskstream\n    // because the MetamaskInpageProvider also attempts to do so.\n    // We create another LocalMessageDuplexStream for communication between dapp <> iframe\n    const communicationStream = new _toruslabs_openlogin_jrpc__WEBPACK_IMPORTED_MODULE_4__.BasePostMessageStream({\n      name: \"embed_comm\",\n      target: \"iframe_comm\",\n      targetWindow: this.torusIframe.contentWindow,\n      targetOrigin: new URL(this.torusUrl).origin\n    });\n\n    // Backward compatibility with Gotchi :)\n    // window.metamaskStream = this.communicationStream\n\n    // compose the inpage provider\n    const inpageProvider = new TorusInpageProvider(metamaskStream);\n\n    // detect eth_requestAccounts and pipe to enable for now\n    const detectAccountRequestPrototypeModifier = m => {\n      // @ts-ignore\n      const originalMethod = inpageProvider[m];\n      // @ts-ignore\n      inpageProvider[m] = function providerFunc(method) {\n        if (method && method === \"eth_requestAccounts\") {\n          return inpageProvider.enable();\n        }\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        return originalMethod.apply(this, [method, ...args]);\n      };\n    };\n    detectAccountRequestPrototypeModifier(\"send\");\n    detectAccountRequestPrototypeModifier(\"sendAsync\");\n    inpageProvider.enable = () => {\n      return new Promise((resolve, reject) => {\n        // If user is already logged in, we assume they have given access to the website\n        inpageProvider.sendAsync({\n          jsonrpc: \"2.0\",\n          id: getPreopenInstanceId(),\n          method: \"eth_requestAccounts\",\n          params: []\n        }, (err, response) => {\n          const {\n            result: res\n          } = response || {};\n          if (err) {\n            setTimeout(() => {\n              reject(err);\n            }, 50);\n          } else if (Array.isArray(res) && res.length > 0) {\n            // If user is already rehydrated, resolve this\n            // else wait for something to be written to status stream\n            const handleLoginCb = () => {\n              if (this.requestedVerifier !== \"\" && this.currentVerifier !== this.requestedVerifier) {\n                const {\n                  requestedVerifier\n                } = this;\n                // eslint-disable-next-line promise/no-promise-in-callback\n                this.logout()\n                // eslint-disable-next-line promise/always-return\n                .then(_ => {\n                  this.requestedVerifier = requestedVerifier;\n                  this._showLoginPopup(true, resolve, reject);\n                }).catch(error => reject(error));\n              } else {\n                resolve(res);\n              }\n            };\n            if (this.isLoggedIn) {\n              handleLoginCb();\n            } else {\n              this.isLoginCallback = handleLoginCb;\n            }\n          } else {\n            // set up listener for login\n            this._showLoginPopup(true, resolve, reject);\n          }\n        });\n      });\n    };\n    inpageProvider.tryPreopenHandle = (payload, cb) => {\n      const _payload = payload;\n      if (!Array.isArray(_payload) && UNSAFE_METHODS.includes(_payload.method)) {\n        const preopenInstanceId = getPreopenInstanceId();\n        this._handleWindow(preopenInstanceId, {\n          target: \"_blank\",\n          features: FEATURES_CONFIRM_WINDOW\n        });\n        _payload.preopenInstanceId = preopenInstanceId;\n      }\n      inpageProvider._rpcEngine.handle(_payload, cb);\n    };\n\n    // Work around for web3@1.0 deleting the bound `sendAsync` but not the unbound\n    // `sendAsync` method on the prototype, causing `this` reference issues with drizzle\n    const proxiedInpageProvider = new Proxy(inpageProvider, {\n      // straight up lie that we deleted the property so that it doesnt\n      // throw an error in strict mode\n      deleteProperty: () => true\n    });\n    this.ethereum = proxiedInpageProvider;\n    const communicationMux = (0,_toruslabs_openlogin_jrpc__WEBPACK_IMPORTED_MODULE_4__.setupMultiplex)(communicationStream);\n    this.communicationMux = communicationMux;\n    const windowStream = communicationMux.getStream(\"window\");\n    windowStream.on(\"data\", chunk => {\n      if (chunk.name === \"create_window\") {\n        // url is the url we need to open\n        // we can pass the final url upfront so that it removes the step of redirecting to /redirect and waiting for finalUrl\n        this._createPopupBlockAlert(chunk.data.preopenInstanceId, chunk.data.url);\n      }\n    });\n\n    // show torus widget if button clicked\n    const widgetStream = communicationMux.getStream(\"widget\");\n    widgetStream.on(\"data\", chunk => {\n      const {\n        data\n      } = chunk;\n      this._displayIframe(data);\n    });\n\n    // Show torus button if wallet has been hydrated/detected\n    const statusStream = communicationMux.getStream(\"status\");\n    statusStream.on(\"data\", status => {\n      // login\n      if (status.loggedIn) {\n        this.isLoggedIn = status.loggedIn;\n        this.currentVerifier = status.verifier;\n      } // logout\n      else this._displayIframe();\n      if (this.isLoginCallback) {\n        this.isLoginCallback();\n        delete this.isLoginCallback;\n      }\n    });\n    this.provider = proxiedInpageProvider;\n    if (this.provider.shouldSendMetadata) sendSiteMetadata(this.provider._rpcEngine);\n    inpageProvider._initializeState();\n    log.debug(\"Torus - injected provider\");\n  }\n  _showLoginPopup(calledFromEmbed, resolve, reject) {\n    const loginHandler = arg => {\n      const data = arg;\n      const {\n        err,\n        selectedAddress\n      } = data;\n      if (err) {\n        log.error(err);\n        if (reject) reject(err);\n      }\n      // returns an array (cause accounts expects it)\n      else if (resolve) resolve([selectedAddress]);\n      if (this.isIframeFullScreen) this._displayIframe();\n    };\n    const oauthStream = this.communicationMux.getStream(\"oauth\");\n    if (!this.requestedVerifier) {\n      this._displayIframe(true);\n      handleStream(oauthStream, \"data\", loginHandler);\n      oauthStream.write({\n        name: \"oauth_modal\",\n        data: {\n          calledFromEmbed\n        }\n      });\n    } else {\n      handleStream(oauthStream, \"data\", loginHandler);\n      const preopenInstanceId = getPreopenInstanceId();\n      this._handleWindow(preopenInstanceId);\n      oauthStream.write({\n        name: \"oauth\",\n        data: {\n          calledFromEmbed,\n          verifier: this.requestedVerifier,\n          preopenInstanceId,\n          login_hint: this.loginHint\n        }\n      });\n    }\n  }\n  _createPopupBlockAlert(preopenInstanceId, url) {\n    const logoUrl = this._getLogoUrl();\n    const torusAlert = htmlToElement('<div id=\"torusAlert\" class=\"torus-alert--v2\">' + `<div id=\"torusAlert__logo\"><img src=\"${logoUrl}\" /></div>` + \"<div>\" + `<h1 id=\"torusAlert__title\">${this.embedTranslations.actionRequired}</h1>` + `<p id=\"torusAlert__desc\">${this.embedTranslations.pendingAction}</p>` + \"</div>\" + \"</div>\");\n    const successAlert = htmlToElement(`<div><a id=\"torusAlert__btn\">${this.embedTranslations.continue}</a></div>`);\n    const btnContainer = htmlToElement('<div id=\"torusAlert__btn-container\"></div>');\n    btnContainer.appendChild(successAlert);\n    torusAlert.appendChild(btnContainer);\n    this._setEmbedWhiteLabel(torusAlert);\n    this.torusAlertContainer.style.display = \"block\";\n    this.torusAlertContainer.appendChild(torusAlert);\n    successAlert.addEventListener(\"click\", () => {\n      this._handleWindow(preopenInstanceId, {\n        url,\n        target: \"_blank\",\n        features: FEATURES_CONFIRM_WINDOW\n      });\n      torusAlert.remove();\n      if (this.torusAlertContainer.children.length === 0) this.torusAlertContainer.style.display = \"none\";\n    });\n  }\n}\n\n\n//# sourceMappingURL=torus.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@toruslabs/torus-embed/dist/torus.esm.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@web3auth/base-evm-adapter/dist/baseEvmAdapter.esm.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@web3auth/base-evm-adapter/dist/baseEvmAdapter.esm.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseEvmAdapter: function() { return /* binding */ BaseEvmAdapter; }\n/* harmony export */ });\n/* harmony import */ var _web3auth_base__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @web3auth/base */ \"(app-pages-browser)/./node_modules/@web3auth/base/dist/base.esm.js\");\n\n\nclass BaseEvmAdapter extends _web3auth_base__WEBPACK_IMPORTED_MODULE_0__.BaseAdapter {\n  async init(_) {\n    if (!this.chainConfig) this.chainConfig = (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.getChainConfig)(_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.CHAIN_NAMESPACES.EIP155, 1);\n  }\n  async authenticateUser() {\n    if (!this.provider || this.status !== _web3auth_base__WEBPACK_IMPORTED_MODULE_0__.ADAPTER_STATUS.CONNECTED) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_0__.WalletLoginError.notConnectedError();\n    const {\n      chainNamespace,\n      chainId\n    } = this.chainConfig;\n    const accounts = await this.provider.request({\n      method: \"eth_accounts\"\n    });\n    if (accounts && accounts.length > 0) {\n      const existingToken = (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.getSavedToken)(accounts[0], this.name);\n      if (existingToken) {\n        const isExpired = (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.checkIfTokenIsExpired)(existingToken);\n        if (!isExpired) {\n          return {\n            idToken: existingToken\n          };\n        }\n      }\n      const payload = {\n        domain: window.location.origin,\n        uri: window.location.href,\n        address: accounts[0],\n        chainId: parseInt(chainId, 16),\n        version: \"1\",\n        nonce: Math.random().toString(36).slice(2),\n        issuedAt: new Date().toISOString()\n      };\n      const challenge = await (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.signChallenge)(payload, chainNamespace);\n      const signedMessage = await this.provider.request({\n        method: \"personal_sign\",\n        params: [challenge, accounts[0]]\n      });\n      const idToken = await (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.verifySignedChallenge)(chainNamespace, signedMessage, challenge, this.name, this.sessionTime, this.clientId, this.web3AuthNetwork);\n      (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.saveToken)(accounts[0], this.name, idToken);\n      return {\n        idToken\n      };\n    }\n    throw _web3auth_base__WEBPACK_IMPORTED_MODULE_0__.WalletLoginError.notConnectedError(\"Not connected with wallet, Please login/connect first\");\n  }\n  async disconnectSession() {\n    super.checkDisconnectionRequirements();\n    const accounts = await this.provider.request({\n      method: \"eth_accounts\"\n    });\n    if (accounts && accounts.length > 0) {\n      (0,_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.clearToken)(accounts[0], this.name);\n    }\n  }\n  async disconnect() {\n    this.rehydrated = false;\n    this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_0__.ADAPTER_EVENTS.DISCONNECTED);\n  }\n}\n\n\n//# sourceMappingURL=baseEvmAdapter.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/base-evm-adapter/dist/baseEvmAdapter.esm.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@web3auth/torus-evm-adapter/dist/torusEvmAdapter.esm.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@web3auth/torus-evm-adapter/dist/torusEvmAdapter.esm.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TorusWalletAdapter: function() { return /* binding */ TorusWalletAdapter; }\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/objectSpread2 */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _toruslabs_torus_embed__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @toruslabs/torus-embed */ \"(app-pages-browser)/./node_modules/@toruslabs/torus-embed/dist/torus.esm.js\");\n/* harmony import */ var _web3auth_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @web3auth/base */ \"(app-pages-browser)/./node_modules/@web3auth/base/dist/base.esm.js\");\n/* harmony import */ var _web3auth_base_evm_adapter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @web3auth/base-evm-adapter */ \"(app-pages-browser)/./node_modules/@web3auth/base-evm-adapter/dist/baseEvmAdapter.esm.js\");\n\n\n\n\n\n\nclass TorusWalletAdapter extends _web3auth_base_evm_adapter__WEBPACK_IMPORTED_MODULE_4__.BaseEvmAdapter {\n  constructor() {\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    super(params);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this, \"name\", _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.WALLET_ADAPTERS.TORUS_EVM);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this, \"adapterNamespace\", _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_NAMESPACES.EIP155);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this, \"currentChainNamespace\", _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.CHAIN_NAMESPACES.EIP155);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this, \"type\", _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_CATEGORY.EXTERNAL);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this, \"status\", _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_STATUS.NOT_READY);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this, \"torusInstance\", null);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this, \"torusWalletOptions\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this, \"initParams\", void 0);\n    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this, \"loginSettings\", {});\n    this.torusWalletOptions = params.adapterSettings || {};\n    this.initParams = params.initParams || {};\n    this.loginSettings = params.loginSettings || {};\n  }\n  get provider() {\n    if (this.status !== _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_STATUS.NOT_READY && this.torusInstance) {\n      return this.torusInstance.provider;\n    }\n    return null;\n  }\n  set provider(_) {\n    throw new Error(\"Not implemented\");\n  }\n  async init() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    await super.init(options);\n    super.checkInitializationRequirements();\n    const {\n      chainId,\n      blockExplorer,\n      displayName,\n      rpcTarget,\n      ticker,\n      tickerName\n    } = this.chainConfig;\n    const network = {\n      chainId: Number.parseInt(chainId, 16),\n      host: rpcTarget,\n      blockExplorer,\n      networkName: displayName,\n      ticker,\n      tickerName\n      // decimals: decimals || 18,\n    };\n    this.torusInstance = new _toruslabs_torus_embed__WEBPACK_IMPORTED_MODULE_2__[\"default\"](this.torusWalletOptions);\n    _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.log.debug(\"initializing torus evm adapter init\");\n    await this.torusInstance.init((0,_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      showTorusButton: false\n    }, this.initParams), {}, {\n      network\n    }));\n    this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_STATUS.READY;\n    this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_EVENTS.READY, _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.WALLET_ADAPTERS.TORUS_EVM);\n    try {\n      _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.log.debug(\"initializing torus evm adapter\");\n      if (options.autoConnect) {\n        this.rehydrated = true;\n        await this.connect();\n      }\n    } catch (error) {\n      _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.log.error(\"Failed to connect with torus evm provider\", error);\n      this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_EVENTS.ERRORED, error);\n    }\n  }\n  async connect() {\n    super.checkConnectionRequirements();\n    if (!this.torusInstance) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.WalletInitializationError.notReady(\"Torus wallet is not initialized\");\n    this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_STATUS.CONNECTING;\n    this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_EVENTS.CONNECTING, {\n      adapter: _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.WALLET_ADAPTERS.TORUS_EVM\n    });\n    try {\n      await this.torusInstance.login(this.loginSettings);\n      const chainId = await this.torusInstance.provider.request({\n        method: \"eth_chainId\"\n      });\n      if (chainId && parseInt(chainId) !== parseInt(this.chainConfig.chainId, 16)) {\n        const {\n          chainId: _chainId,\n          blockExplorer,\n          displayName,\n          rpcTarget,\n          ticker,\n          tickerName\n        } = this.chainConfig;\n        const network = {\n          chainId: Number.parseInt(_chainId, 16),\n          host: rpcTarget,\n          blockExplorer,\n          networkName: displayName,\n          tickerName,\n          ticker\n        };\n        // in some cases when user manually switches chain and relogin then adapter will not connect to initially passed\n        // chainConfig but will connect to the one that user switched to.\n        // So here trying to switch network to the one that was initially passed in chainConfig.\n        await this.torusInstance.setProvider((0,_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, network));\n        const updatedChainID = await this.torusInstance.ethereum.request({\n          method: \"eth_chainId\"\n        });\n        if (updatedChainID && parseInt(updatedChainID) !== parseInt(this.chainConfig.chainId, 16)) {\n          throw _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.WalletInitializationError.fromCode(5000, `Not connected to correct chainId. Expected: ${this.chainConfig.chainId}, Current: ${updatedChainID}`);\n        }\n      }\n      this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_STATUS.CONNECTED;\n      this.torusInstance.showTorusButton();\n      this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_STATUS.CONNECTED, {\n        adapter: _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.WALLET_ADAPTERS.TORUS_EVM,\n        reconnected: this.rehydrated\n      });\n      return this.provider;\n    } catch (error) {\n      // ready again to be connected\n      this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_STATUS.READY;\n      this.rehydrated = false;\n      this.emit(_web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_STATUS.ERRORED, error);\n      throw error instanceof _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.Web3AuthError ? error : _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.WalletLoginError.connectionError(\"Failed to login with torus wallet\");\n    }\n  }\n  async disconnect() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      cleanup: false\n    };\n    await super.disconnectSession();\n    if (!this.torusInstance) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.WalletInitializationError.notReady(\"Torus wallet is not initialized\");\n    await this.torusInstance.logout();\n    this.torusInstance.hideTorusButton();\n    if (options.cleanup) {\n      this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_STATUS.NOT_READY;\n      this.torusInstance = null;\n    } else {\n      // ready to be connected again\n      this.status = _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_STATUS.READY;\n    }\n    await super.disconnect();\n  }\n  async getUserInfo() {\n    if (this.status !== _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.ADAPTER_STATUS.CONNECTED) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.WalletLoginError.notConnectedError(\"Not connected with wallet\");\n    if (!this.torusInstance) throw _web3auth_base__WEBPACK_IMPORTED_MODULE_3__.WalletInitializationError.notReady(\"Torus wallet is not initialized\");\n    const userInfo = await this.torusInstance.getUserInfo(\"\");\n    return userInfo;\n  }\n  async addChain(chainConfig) {\n    let init = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    super.checkAddChainRequirements(chainConfig, init);\n    // TODO: add these in torus wallet.\n    // await this.torusInstance?.provider.request({\n    //   method: \"wallet_addEthereumChain\",\n    //   params: [\n    //     {\n    //       chainId: chainConfig.chainId,\n    //       chainName: chainConfig.displayName,\n    //       rpcUrls: [chainConfig.rpcTarget],\n    //       blockExplorerUrls: [chainConfig.blockExplorer],\n    //       nativeCurrency: {\n    //         name: chainConfig.tickerName,\n    //         symbol: chainConfig.ticker,\n    //         decimals: chainConfig.decimals || 18,\n    //       },\n    //     },\n    //   ],\n    // });\n    this.addChainConfig(chainConfig);\n  }\n  async switchChain(params) {\n    var _this$torusInstance;\n    let init = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    super.checkSwitchChainRequirements(params, init);\n    // TODO: add these in torus wallet.\n    // await this.torusInstance?.provider.request({\n    //   method: \"wallet_switchEthereumChain\",\n    //   params: [{ chainId: params.chainId }],\n    // });\n    const chainConfig = this.getChainConfig(params.chainId);\n    await ((_this$torusInstance = this.torusInstance) === null || _this$torusInstance === void 0 ? void 0 : _this$torusInstance.setProvider({\n      host: chainConfig.rpcTarget,\n      chainId: parseInt(chainConfig.chainId, 16),\n      networkName: chainConfig.displayName,\n      blockExplorer: chainConfig.blockExplorer,\n      ticker: chainConfig.ticker,\n      tickerName: chainConfig.tickerName\n    }));\n    this.setAdapterSettings({\n      chainConfig: this.getChainConfig(params.chainId)\n    });\n  }\n}\n\n\n//# sourceMappingURL=torusEvmAdapter.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/torus-evm-adapter/dist/torusEvmAdapter.esm.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/fast-deep-equal/index.js":
/*!***********************************************!*\
  !*** ./node_modules/fast-deep-equal/index.js ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n\n// do not edit .js files directly - edit src/index.jst\n\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fast-deep-equal/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ _objectWithoutProperties; }\n/* harmony export */ });\n/* harmony import */ var _objectWithoutPropertiesLoose_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./objectWithoutPropertiesLoose.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = (0,_objectWithoutPropertiesLoose_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RXaXRob3V0UHJvcGVydGllcy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE2RTtBQUM3RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsNEVBQTRCO0FBQ3BDO0FBQ0E7QUFDQSxnQkFBZ0IsY0FBYyx3Q0FBd0M7QUFDdEU7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RXaXRob3V0UHJvcGVydGllcy5qcz82YmY5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBvYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlIGZyb20gXCIuL29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UuanNcIjtcbmZ1bmN0aW9uIF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhlLCB0KSB7XG4gIGlmIChudWxsID09IGUpIHJldHVybiB7fTtcbiAgdmFyIG8sXG4gICAgcixcbiAgICBpID0gb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZShlLCB0KTtcbiAgaWYgKE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMpIHtcbiAgICB2YXIgbiA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMoZSk7XG4gICAgZm9yIChyID0gMDsgciA8IG4ubGVuZ3RoOyByKyspIG8gPSBuW3JdLCAtMSA9PT0gdC5pbmRleE9mKG8pICYmIHt9LnByb3BlcnR5SXNFbnVtZXJhYmxlLmNhbGwoZSwgbykgJiYgKGlbb10gPSBlW29dKTtcbiAgfVxuICByZXR1cm4gaTtcbn1cbmV4cG9ydCB7IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyBhcyBkZWZhdWx0IH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ _objectWithoutPropertiesLoose; }\n/* harmony export */ });\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZS5qcz8wNGMwIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlKHIsIGUpIHtcbiAgaWYgKG51bGwgPT0gcikgcmV0dXJuIHt9O1xuICB2YXIgdCA9IHt9O1xuICBmb3IgKHZhciBuIGluIHIpIGlmICh7fS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHIsIG4pKSB7XG4gICAgaWYgKC0xICE9PSBlLmluZGV4T2YobikpIGNvbnRpbnVlO1xuICAgIHRbbl0gPSByW25dO1xuICB9XG4gIHJldHVybiB0O1xufVxuZXhwb3J0IHsgX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UgYXMgZGVmYXVsdCB9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\n"));

/***/ })

}]);