/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/is-arrayish";
exports.ids = ["vendor-chunks/is-arrayish"];
exports.modules = {

/***/ "(ssr)/./node_modules/is-arrayish/index.js":
/*!*******************************************!*\
  !*** ./node_modules/is-arrayish/index.js ***!
  \*******************************************/
/***/ ((module) => {

eval("module.exports = function isArrayish(obj) {\n    if (!obj || typeof obj === \"string\") {\n        return false;\n    }\n    return obj instanceof Array || Array.isArray(obj) || obj.length >= 0 && (obj.splice instanceof Function || Object.getOwnPropertyDescriptor(obj, obj.length - 1) && obj.constructor.name !== \"String\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvaXMtYXJyYXlpc2gvaW5kZXguanM/YmU0MSJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIGlzQXJyYXlpc2gob2JqKSB7XG5cdGlmICghb2JqIHx8IHR5cGVvZiBvYmogPT09ICdzdHJpbmcnKSB7XG5cdFx0cmV0dXJuIGZhbHNlO1xuXHR9XG5cblx0cmV0dXJuIG9iaiBpbnN0YW5jZW9mIEFycmF5IHx8IEFycmF5LmlzQXJyYXkob2JqKSB8fFxuXHRcdChvYmoubGVuZ3RoID49IDAgJiYgKG9iai5zcGxpY2UgaW5zdGFuY2VvZiBGdW5jdGlvbiB8fFxuXHRcdFx0KE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3Iob2JqLCAob2JqLmxlbmd0aCAtIDEpKSAmJiBvYmouY29uc3RydWN0b3IubmFtZSAhPT0gJ1N0cmluZycpKSk7XG59O1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJpc0FycmF5aXNoIiwib2JqIiwiQXJyYXkiLCJpc0FycmF5IiwibGVuZ3RoIiwic3BsaWNlIiwiRnVuY3Rpb24iLCJPYmplY3QiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiLCJjb25zdHJ1Y3RvciIsIm5hbWUiXSwibWFwcGluZ3MiOiJBQUFBQSxPQUFPQyxPQUFPLEdBQUcsU0FBU0MsV0FBV0MsR0FBRztJQUN2QyxJQUFJLENBQUNBLE9BQU8sT0FBT0EsUUFBUSxVQUFVO1FBQ3BDLE9BQU87SUFDUjtJQUVBLE9BQU9BLGVBQWVDLFNBQVNBLE1BQU1DLE9BQU8sQ0FBQ0YsUUFDM0NBLElBQUlHLE1BQU0sSUFBSSxLQUFNSCxDQUFBQSxJQUFJSSxNQUFNLFlBQVlDLFlBQ3pDQyxPQUFPQyx3QkFBd0IsQ0FBQ1AsS0FBTUEsSUFBSUcsTUFBTSxHQUFHLE1BQU9ILElBQUlRLFdBQVcsQ0FBQ0MsSUFBSSxLQUFLLFFBQVE7QUFDL0YiLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaXMtYXJyYXlpc2gvaW5kZXguanMiLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/is-arrayish/index.js\n");

/***/ })

};
;