/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lodash.clonedeep";
exports.ids = ["vendor-chunks/lodash.clonedeep"];
exports.modules = {

/***/ "(ssr)/./node_modules/lodash.clonedeep/index.js":
/*!************************************************!*\
  !*** ./node_modules/lodash.clonedeep/index.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* module decorator */ module = __webpack_require__.nmd(module);\n/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors\n */ /** Used as the size to enable large array optimizations. */ var LARGE_ARRAY_SIZE = 200;\n/** Used to stand-in for `undefined` hash values. */ var HASH_UNDEFINED = \"__lodash_hash_undefined__\";\n/** Used as references for various `Number` constants. */ var MAX_SAFE_INTEGER = 9007199254740991;\n/** `Object#toString` result references. */ var argsTag = \"[object Arguments]\", arrayTag = \"[object Array]\", boolTag = \"[object Boolean]\", dateTag = \"[object Date]\", errorTag = \"[object Error]\", funcTag = \"[object Function]\", genTag = \"[object GeneratorFunction]\", mapTag = \"[object Map]\", numberTag = \"[object Number]\", objectTag = \"[object Object]\", promiseTag = \"[object Promise]\", regexpTag = \"[object RegExp]\", setTag = \"[object Set]\", stringTag = \"[object String]\", symbolTag = \"[object Symbol]\", weakMapTag = \"[object WeakMap]\";\nvar arrayBufferTag = \"[object ArrayBuffer]\", dataViewTag = \"[object DataView]\", float32Tag = \"[object Float32Array]\", float64Tag = \"[object Float64Array]\", int8Tag = \"[object Int8Array]\", int16Tag = \"[object Int16Array]\", int32Tag = \"[object Int32Array]\", uint8Tag = \"[object Uint8Array]\", uint8ClampedTag = \"[object Uint8ClampedArray]\", uint16Tag = \"[object Uint16Array]\", uint32Tag = \"[object Uint32Array]\";\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */ var reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n/** Used to match `RegExp` flags from their coerced string values. */ var reFlags = /\\w*$/;\n/** Used to detect host constructors (Safari). */ var reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n/** Used to detect unsigned integer values. */ var reIsUint = /^(?:0|[1-9]\\d*)$/;\n/** Used to identify `toStringTag` values supported by `_.clone`. */ var cloneableTags = {};\ncloneableTags[argsTag] = cloneableTags[arrayTag] = cloneableTags[arrayBufferTag] = cloneableTags[dataViewTag] = cloneableTags[boolTag] = cloneableTags[dateTag] = cloneableTags[float32Tag] = cloneableTags[float64Tag] = cloneableTags[int8Tag] = cloneableTags[int16Tag] = cloneableTags[int32Tag] = cloneableTags[mapTag] = cloneableTags[numberTag] = cloneableTags[objectTag] = cloneableTags[regexpTag] = cloneableTags[setTag] = cloneableTags[stringTag] = cloneableTags[symbolTag] = cloneableTags[uint8Tag] = cloneableTags[uint8ClampedTag] = cloneableTags[uint16Tag] = cloneableTags[uint32Tag] = true;\ncloneableTags[errorTag] = cloneableTags[funcTag] = cloneableTags[weakMapTag] = false;\n/** Detect free variable `global` from Node.js. */ var freeGlobal = typeof global == \"object\" && global && global.Object === Object && global;\n/** Detect free variable `self`. */ var freeSelf = typeof self == \"object\" && self && self.Object === Object && self;\n/** Used as a reference to the global object. */ var root = freeGlobal || freeSelf || Function(\"return this\")();\n/** Detect free variable `exports`. */ var freeExports =  true && exports && !exports.nodeType && exports;\n/** Detect free variable `module`. */ var freeModule = freeExports && \"object\" == \"object\" && module && !module.nodeType && module;\n/** Detect the popular CommonJS extension `module.exports`. */ var moduleExports = freeModule && freeModule.exports === freeExports;\n/**\n * Adds the key-value `pair` to `map`.\n *\n * @private\n * @param {Object} map The map to modify.\n * @param {Array} pair The key-value pair to add.\n * @returns {Object} Returns `map`.\n */ function addMapEntry(map, pair) {\n    // Don't return `map.set` because it's not chainable in IE 11.\n    map.set(pair[0], pair[1]);\n    return map;\n}\n/**\n * Adds `value` to `set`.\n *\n * @private\n * @param {Object} set The set to modify.\n * @param {*} value The value to add.\n * @returns {Object} Returns `set`.\n */ function addSetEntry(set, value) {\n    // Don't return `set.add` because it's not chainable in IE 11.\n    set.add(value);\n    return set;\n}\n/**\n * A specialized version of `_.forEach` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns `array`.\n */ function arrayEach(array, iteratee) {\n    var index = -1, length = array ? array.length : 0;\n    while(++index < length){\n        if (iteratee(array[index], index, array) === false) {\n            break;\n        }\n    }\n    return array;\n}\n/**\n * Appends the elements of `values` to `array`.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to append.\n * @returns {Array} Returns `array`.\n */ function arrayPush(array, values) {\n    var index = -1, length = values.length, offset = array.length;\n    while(++index < length){\n        array[offset + index] = values[index];\n    }\n    return array;\n}\n/**\n * A specialized version of `_.reduce` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @param {boolean} [initAccum] Specify using the first element of `array` as\n *  the initial value.\n * @returns {*} Returns the accumulated value.\n */ function arrayReduce(array, iteratee, accumulator, initAccum) {\n    var index = -1, length = array ? array.length : 0;\n    if (initAccum && length) {\n        accumulator = array[++index];\n    }\n    while(++index < length){\n        accumulator = iteratee(accumulator, array[index], index, array);\n    }\n    return accumulator;\n}\n/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */ function baseTimes(n, iteratee) {\n    var index = -1, result = Array(n);\n    while(++index < n){\n        result[index] = iteratee(index);\n    }\n    return result;\n}\n/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */ function getValue(object, key) {\n    return object == null ? undefined : object[key];\n}\n/**\n * Checks if `value` is a host object in IE < 9.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a host object, else `false`.\n */ function isHostObject(value) {\n    // Many host objects are `Object` objects that can coerce to strings\n    // despite having improperly defined `toString` methods.\n    var result = false;\n    if (value != null && typeof value.toString != \"function\") {\n        try {\n            result = !!(value + \"\");\n        } catch (e) {}\n    }\n    return result;\n}\n/**\n * Converts `map` to its key-value pairs.\n *\n * @private\n * @param {Object} map The map to convert.\n * @returns {Array} Returns the key-value pairs.\n */ function mapToArray(map) {\n    var index = -1, result = Array(map.size);\n    map.forEach(function(value, key) {\n        result[++index] = [\n            key,\n            value\n        ];\n    });\n    return result;\n}\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */ function overArg(func, transform) {\n    return function(arg) {\n        return func(transform(arg));\n    };\n}\n/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */ function setToArray(set) {\n    var index = -1, result = Array(set.size);\n    set.forEach(function(value) {\n        result[++index] = value;\n    });\n    return result;\n}\n/** Used for built-in method references. */ var arrayProto = Array.prototype, funcProto = Function.prototype, objectProto = Object.prototype;\n/** Used to detect overreaching core-js shims. */ var coreJsData = root[\"__core-js_shared__\"];\n/** Used to detect methods masquerading as native. */ var maskSrcKey = function() {\n    var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || \"\");\n    return uid ? \"Symbol(src)_1.\" + uid : \"\";\n}();\n/** Used to resolve the decompiled source of functions. */ var funcToString = funcProto.toString;\n/** Used to check objects for own properties. */ var hasOwnProperty = objectProto.hasOwnProperty;\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */ var objectToString = objectProto.toString;\n/** Used to detect if a method is native. */ var reIsNative = RegExp(\"^\" + funcToString.call(hasOwnProperty).replace(reRegExpChar, \"\\\\$&\").replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, \"$1.*?\") + \"$\");\n/** Built-in value references. */ var Buffer = moduleExports ? root.Buffer : undefined, Symbol = root.Symbol, Uint8Array = root.Uint8Array, getPrototype = overArg(Object.getPrototypeOf, Object), objectCreate = Object.create, propertyIsEnumerable = objectProto.propertyIsEnumerable, splice = arrayProto.splice;\n/* Built-in method references for those with the same name as other `lodash` methods. */ var nativeGetSymbols = Object.getOwnPropertySymbols, nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined, nativeKeys = overArg(Object.keys, Object);\n/* Built-in method references that are verified to be native. */ var DataView = getNative(root, \"DataView\"), Map = getNative(root, \"Map\"), Promise = getNative(root, \"Promise\"), Set = getNative(root, \"Set\"), WeakMap = getNative(root, \"WeakMap\"), nativeCreate = getNative(Object, \"create\");\n/** Used to detect maps, sets, and weakmaps. */ var dataViewCtorString = toSource(DataView), mapCtorString = toSource(Map), promiseCtorString = toSource(Promise), setCtorString = toSource(Set), weakMapCtorString = toSource(WeakMap);\n/** Used to convert symbols to primitives and strings. */ var symbolProto = Symbol ? Symbol.prototype : undefined, symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function Hash(entries) {\n    var index = -1, length = entries ? entries.length : 0;\n    this.clear();\n    while(++index < length){\n        var entry = entries[index];\n        this.set(entry[0], entry[1]);\n    }\n}\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */ function hashClear() {\n    this.__data__ = nativeCreate ? nativeCreate(null) : {};\n}\n/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function hashDelete(key) {\n    return this.has(key) && delete this.__data__[key];\n}\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function hashGet(key) {\n    var data = this.__data__;\n    if (nativeCreate) {\n        var result = data[key];\n        return result === HASH_UNDEFINED ? undefined : result;\n    }\n    return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function hashHas(key) {\n    var data = this.__data__;\n    return nativeCreate ? data[key] !== undefined : hasOwnProperty.call(data, key);\n}\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */ function hashSet(key, value) {\n    var data = this.__data__;\n    data[key] = nativeCreate && value === undefined ? HASH_UNDEFINED : value;\n    return this;\n}\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype[\"delete\"] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function ListCache(entries) {\n    var index = -1, length = entries ? entries.length : 0;\n    this.clear();\n    while(++index < length){\n        var entry = entries[index];\n        this.set(entry[0], entry[1]);\n    }\n}\n/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */ function listCacheClear() {\n    this.__data__ = [];\n}\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function listCacheDelete(key) {\n    var data = this.__data__, index = assocIndexOf(data, key);\n    if (index < 0) {\n        return false;\n    }\n    var lastIndex = data.length - 1;\n    if (index == lastIndex) {\n        data.pop();\n    } else {\n        splice.call(data, index, 1);\n    }\n    return true;\n}\n/**\n * Gets the list cache value for `key`.\n *\n * @private\n * @name get\n * @memberOf ListCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function listCacheGet(key) {\n    var data = this.__data__, index = assocIndexOf(data, key);\n    return index < 0 ? undefined : data[index][1];\n}\n/**\n * Checks if a list cache value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf ListCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function listCacheHas(key) {\n    return assocIndexOf(this.__data__, key) > -1;\n}\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */ function listCacheSet(key, value) {\n    var data = this.__data__, index = assocIndexOf(data, key);\n    if (index < 0) {\n        data.push([\n            key,\n            value\n        ]);\n    } else {\n        data[index][1] = value;\n    }\n    return this;\n}\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype[\"delete\"] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function MapCache(entries) {\n    var index = -1, length = entries ? entries.length : 0;\n    this.clear();\n    while(++index < length){\n        var entry = entries[index];\n        this.set(entry[0], entry[1]);\n    }\n}\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */ function mapCacheClear() {\n    this.__data__ = {\n        \"hash\": new Hash,\n        \"map\": new (Map || ListCache),\n        \"string\": new Hash\n    };\n}\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function mapCacheDelete(key) {\n    return getMapData(this, key)[\"delete\"](key);\n}\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function mapCacheGet(key) {\n    return getMapData(this, key).get(key);\n}\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function mapCacheHas(key) {\n    return getMapData(this, key).has(key);\n}\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */ function mapCacheSet(key, value) {\n    getMapData(this, key).set(key, value);\n    return this;\n}\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype[\"delete\"] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function Stack(entries) {\n    this.__data__ = new ListCache(entries);\n}\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */ function stackClear() {\n    this.__data__ = new ListCache;\n}\n/**\n * Removes `key` and its value from the stack.\n *\n * @private\n * @name delete\n * @memberOf Stack\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function stackDelete(key) {\n    return this.__data__[\"delete\"](key);\n}\n/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function stackGet(key) {\n    return this.__data__.get(key);\n}\n/**\n * Checks if a stack value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Stack\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function stackHas(key) {\n    return this.__data__.has(key);\n}\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */ function stackSet(key, value) {\n    var cache = this.__data__;\n    if (cache instanceof ListCache) {\n        var pairs = cache.__data__;\n        if (!Map || pairs.length < LARGE_ARRAY_SIZE - 1) {\n            pairs.push([\n                key,\n                value\n            ]);\n            return this;\n        }\n        cache = this.__data__ = new MapCache(pairs);\n    }\n    cache.set(key, value);\n    return this;\n}\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype[\"delete\"] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */ function arrayLikeKeys(value, inherited) {\n    // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n    // Safari 9 makes `arguments.length` enumerable in strict mode.\n    var result = isArray(value) || isArguments(value) ? baseTimes(value.length, String) : [];\n    var length = result.length, skipIndexes = !!length;\n    for(var key in value){\n        if ((inherited || hasOwnProperty.call(value, key)) && !(skipIndexes && (key == \"length\" || isIndex(key, length)))) {\n            result.push(key);\n        }\n    }\n    return result;\n}\n/**\n * Assigns `value` to `key` of `object` if the existing value is not equivalent\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */ function assignValue(object, key, value) {\n    var objValue = object[key];\n    if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) || value === undefined && !(key in object)) {\n        object[key] = value;\n    }\n}\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */ function assocIndexOf(array, key) {\n    var length = array.length;\n    while(length--){\n        if (eq(array[length][0], key)) {\n            return length;\n        }\n    }\n    return -1;\n}\n/**\n * The base implementation of `_.assign` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */ function baseAssign(object, source) {\n    return object && copyObject(source, keys(source), object);\n}\n/**\n * The base implementation of `_.clone` and `_.cloneDeep` which tracks\n * traversed objects.\n *\n * @private\n * @param {*} value The value to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @param {boolean} [isFull] Specify a clone including symbols.\n * @param {Function} [customizer] The function to customize cloning.\n * @param {string} [key] The key of `value`.\n * @param {Object} [object] The parent object of `value`.\n * @param {Object} [stack] Tracks traversed objects and their clone counterparts.\n * @returns {*} Returns the cloned value.\n */ function baseClone(value, isDeep, isFull, customizer, key, object, stack) {\n    var result;\n    if (customizer) {\n        result = object ? customizer(value, key, object, stack) : customizer(value);\n    }\n    if (result !== undefined) {\n        return result;\n    }\n    if (!isObject(value)) {\n        return value;\n    }\n    var isArr = isArray(value);\n    if (isArr) {\n        result = initCloneArray(value);\n        if (!isDeep) {\n            return copyArray(value, result);\n        }\n    } else {\n        var tag = getTag(value), isFunc = tag == funcTag || tag == genTag;\n        if (isBuffer(value)) {\n            return cloneBuffer(value, isDeep);\n        }\n        if (tag == objectTag || tag == argsTag || isFunc && !object) {\n            if (isHostObject(value)) {\n                return object ? value : {};\n            }\n            result = initCloneObject(isFunc ? {} : value);\n            if (!isDeep) {\n                return copySymbols(value, baseAssign(result, value));\n            }\n        } else {\n            if (!cloneableTags[tag]) {\n                return object ? value : {};\n            }\n            result = initCloneByTag(value, tag, baseClone, isDeep);\n        }\n    }\n    // Check for circular references and return its corresponding clone.\n    stack || (stack = new Stack);\n    var stacked = stack.get(value);\n    if (stacked) {\n        return stacked;\n    }\n    stack.set(value, result);\n    if (!isArr) {\n        var props = isFull ? getAllKeys(value) : keys(value);\n    }\n    arrayEach(props || value, function(subValue, key) {\n        if (props) {\n            key = subValue;\n            subValue = value[key];\n        }\n        // Recursively populate clone (susceptible to call stack limits).\n        assignValue(result, key, baseClone(subValue, isDeep, isFull, customizer, key, value, stack));\n    });\n    return result;\n}\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} prototype The object to inherit from.\n * @returns {Object} Returns the new object.\n */ function baseCreate(proto) {\n    return isObject(proto) ? objectCreate(proto) : {};\n}\n/**\n * The base implementation of `getAllKeys` and `getAllKeysIn` which uses\n * `keysFunc` and `symbolsFunc` to get the enumerable property names and\n * symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @param {Function} symbolsFunc The function to get the symbols of `object`.\n * @returns {Array} Returns the array of property names and symbols.\n */ function baseGetAllKeys(object, keysFunc, symbolsFunc) {\n    var result = keysFunc(object);\n    return isArray(object) ? result : arrayPush(result, symbolsFunc(object));\n}\n/**\n * The base implementation of `getTag`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */ function baseGetTag(value) {\n    return objectToString.call(value);\n}\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */ function baseIsNative(value) {\n    if (!isObject(value) || isMasked(value)) {\n        return false;\n    }\n    var pattern = isFunction(value) || isHostObject(value) ? reIsNative : reIsHostCtor;\n    return pattern.test(toSource(value));\n}\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */ function baseKeys(object) {\n    if (!isPrototype(object)) {\n        return nativeKeys(object);\n    }\n    var result = [];\n    for(var key in Object(object)){\n        if (hasOwnProperty.call(object, key) && key != \"constructor\") {\n            result.push(key);\n        }\n    }\n    return result;\n}\n/**\n * Creates a clone of  `buffer`.\n *\n * @private\n * @param {Buffer} buffer The buffer to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Buffer} Returns the cloned buffer.\n */ function cloneBuffer(buffer, isDeep) {\n    if (isDeep) {\n        return buffer.slice();\n    }\n    var result = new buffer.constructor(buffer.length);\n    buffer.copy(result);\n    return result;\n}\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */ function cloneArrayBuffer(arrayBuffer) {\n    var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n    new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n    return result;\n}\n/**\n * Creates a clone of `dataView`.\n *\n * @private\n * @param {Object} dataView The data view to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned data view.\n */ function cloneDataView(dataView, isDeep) {\n    var buffer = isDeep ? cloneArrayBuffer(dataView.buffer) : dataView.buffer;\n    return new dataView.constructor(buffer, dataView.byteOffset, dataView.byteLength);\n}\n/**\n * Creates a clone of `map`.\n *\n * @private\n * @param {Object} map The map to clone.\n * @param {Function} cloneFunc The function to clone values.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned map.\n */ function cloneMap(map, isDeep, cloneFunc) {\n    var array = isDeep ? cloneFunc(mapToArray(map), true) : mapToArray(map);\n    return arrayReduce(array, addMapEntry, new map.constructor);\n}\n/**\n * Creates a clone of `regexp`.\n *\n * @private\n * @param {Object} regexp The regexp to clone.\n * @returns {Object} Returns the cloned regexp.\n */ function cloneRegExp(regexp) {\n    var result = new regexp.constructor(regexp.source, reFlags.exec(regexp));\n    result.lastIndex = regexp.lastIndex;\n    return result;\n}\n/**\n * Creates a clone of `set`.\n *\n * @private\n * @param {Object} set The set to clone.\n * @param {Function} cloneFunc The function to clone values.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned set.\n */ function cloneSet(set, isDeep, cloneFunc) {\n    var array = isDeep ? cloneFunc(setToArray(set), true) : setToArray(set);\n    return arrayReduce(array, addSetEntry, new set.constructor);\n}\n/**\n * Creates a clone of the `symbol` object.\n *\n * @private\n * @param {Object} symbol The symbol object to clone.\n * @returns {Object} Returns the cloned symbol object.\n */ function cloneSymbol(symbol) {\n    return symbolValueOf ? Object(symbolValueOf.call(symbol)) : {};\n}\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */ function cloneTypedArray(typedArray, isDeep) {\n    var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n    return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\n/**\n * Copies the values of `source` to `array`.\n *\n * @private\n * @param {Array} source The array to copy values from.\n * @param {Array} [array=[]] The array to copy values to.\n * @returns {Array} Returns `array`.\n */ function copyArray(source, array) {\n    var index = -1, length = source.length;\n    array || (array = Array(length));\n    while(++index < length){\n        array[index] = source[index];\n    }\n    return array;\n}\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */ function copyObject(source, props, object, customizer) {\n    object || (object = {});\n    var index = -1, length = props.length;\n    while(++index < length){\n        var key = props[index];\n        var newValue = customizer ? customizer(object[key], source[key], key, object, source) : undefined;\n        assignValue(object, key, newValue === undefined ? source[key] : newValue);\n    }\n    return object;\n}\n/**\n * Copies own symbol properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy symbols from.\n * @param {Object} [object={}] The object to copy symbols to.\n * @returns {Object} Returns `object`.\n */ function copySymbols(source, object) {\n    return copyObject(source, getSymbols(source), object);\n}\n/**\n * Creates an array of own enumerable property names and symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names and symbols.\n */ function getAllKeys(object) {\n    return baseGetAllKeys(object, keys, getSymbols);\n}\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */ function getMapData(map, key) {\n    var data = map.__data__;\n    return isKeyable(key) ? data[typeof key == \"string\" ? \"string\" : \"hash\"] : data.map;\n}\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */ function getNative(object, key) {\n    var value = getValue(object, key);\n    return baseIsNative(value) ? value : undefined;\n}\n/**\n * Creates an array of the own enumerable symbol properties of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */ var getSymbols = nativeGetSymbols ? overArg(nativeGetSymbols, Object) : stubArray;\n/**\n * Gets the `toStringTag` of `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */ var getTag = baseGetTag;\n// Fallback for data views, maps, sets, and weak maps in IE 11,\n// for data views in Edge < 14, and promises in Node.js.\nif (DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag || Map && getTag(new Map) != mapTag || Promise && getTag(Promise.resolve()) != promiseTag || Set && getTag(new Set) != setTag || WeakMap && getTag(new WeakMap) != weakMapTag) {\n    getTag = function(value) {\n        var result = objectToString.call(value), Ctor = result == objectTag ? value.constructor : undefined, ctorString = Ctor ? toSource(Ctor) : undefined;\n        if (ctorString) {\n            switch(ctorString){\n                case dataViewCtorString:\n                    return dataViewTag;\n                case mapCtorString:\n                    return mapTag;\n                case promiseCtorString:\n                    return promiseTag;\n                case setCtorString:\n                    return setTag;\n                case weakMapCtorString:\n                    return weakMapTag;\n            }\n        }\n        return result;\n    };\n}\n/**\n * Initializes an array clone.\n *\n * @private\n * @param {Array} array The array to clone.\n * @returns {Array} Returns the initialized clone.\n */ function initCloneArray(array) {\n    var length = array.length, result = array.constructor(length);\n    // Add properties assigned by `RegExp#exec`.\n    if (length && typeof array[0] == \"string\" && hasOwnProperty.call(array, \"index\")) {\n        result.index = array.index;\n        result.input = array.input;\n    }\n    return result;\n}\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */ function initCloneObject(object) {\n    return typeof object.constructor == \"function\" && !isPrototype(object) ? baseCreate(getPrototype(object)) : {};\n}\n/**\n * Initializes an object clone based on its `toStringTag`.\n *\n * **Note:** This function only supports cloning values with tags of\n * `Boolean`, `Date`, `Error`, `Number`, `RegExp`, or `String`.\n *\n * @private\n * @param {Object} object The object to clone.\n * @param {string} tag The `toStringTag` of the object to clone.\n * @param {Function} cloneFunc The function to clone values.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the initialized clone.\n */ function initCloneByTag(object, tag, cloneFunc, isDeep) {\n    var Ctor = object.constructor;\n    switch(tag){\n        case arrayBufferTag:\n            return cloneArrayBuffer(object);\n        case boolTag:\n        case dateTag:\n            return new Ctor(+object);\n        case dataViewTag:\n            return cloneDataView(object, isDeep);\n        case float32Tag:\n        case float64Tag:\n        case int8Tag:\n        case int16Tag:\n        case int32Tag:\n        case uint8Tag:\n        case uint8ClampedTag:\n        case uint16Tag:\n        case uint32Tag:\n            return cloneTypedArray(object, isDeep);\n        case mapTag:\n            return cloneMap(object, isDeep, cloneFunc);\n        case numberTag:\n        case stringTag:\n            return new Ctor(object);\n        case regexpTag:\n            return cloneRegExp(object);\n        case setTag:\n            return cloneSet(object, isDeep, cloneFunc);\n        case symbolTag:\n            return cloneSymbol(object);\n    }\n}\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */ function isIndex(value, length) {\n    length = length == null ? MAX_SAFE_INTEGER : length;\n    return !!length && (typeof value == \"number\" || reIsUint.test(value)) && value > -1 && value % 1 == 0 && value < length;\n}\n/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */ function isKeyable(value) {\n    var type = typeof value;\n    return type == \"string\" || type == \"number\" || type == \"symbol\" || type == \"boolean\" ? value !== \"__proto__\" : value === null;\n}\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */ function isMasked(func) {\n    return !!maskSrcKey && maskSrcKey in func;\n}\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */ function isPrototype(value) {\n    var Ctor = value && value.constructor, proto = typeof Ctor == \"function\" && Ctor.prototype || objectProto;\n    return value === proto;\n}\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to process.\n * @returns {string} Returns the source code.\n */ function toSource(func) {\n    if (func != null) {\n        try {\n            return funcToString.call(func);\n        } catch (e) {}\n        try {\n            return func + \"\";\n        } catch (e) {}\n    }\n    return \"\";\n}\n/**\n * This method is like `_.clone` except that it recursively clones `value`.\n *\n * @static\n * @memberOf _\n * @since 1.0.0\n * @category Lang\n * @param {*} value The value to recursively clone.\n * @returns {*} Returns the deep cloned value.\n * @see _.clone\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var deep = _.cloneDeep(objects);\n * console.log(deep[0] === objects[0]);\n * // => false\n */ function cloneDeep(value) {\n    return baseClone(value, true, true);\n}\n/**\n * Performs a\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * comparison between two values to determine if they are equivalent.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.eq(object, object);\n * // => true\n *\n * _.eq(object, other);\n * // => false\n *\n * _.eq('a', 'a');\n * // => true\n *\n * _.eq('a', Object('a'));\n * // => false\n *\n * _.eq(NaN, NaN);\n * // => true\n */ function eq(value, other) {\n    return value === other || value !== value && other !== other;\n}\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */ function isArguments(value) {\n    // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n    return isArrayLikeObject(value) && hasOwnProperty.call(value, \"callee\") && (!propertyIsEnumerable.call(value, \"callee\") || objectToString.call(value) == argsTag);\n}\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */ var isArray = Array.isArray;\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */ function isArrayLike(value) {\n    return value != null && isLength(value.length) && !isFunction(value);\n}\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */ function isArrayLikeObject(value) {\n    return isObjectLike(value) && isArrayLike(value);\n}\n/**\n * Checks if `value` is a buffer.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\n * @example\n *\n * _.isBuffer(new Buffer(2));\n * // => true\n *\n * _.isBuffer(new Uint8Array(2));\n * // => false\n */ var isBuffer = nativeIsBuffer || stubFalse;\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */ function isFunction(value) {\n    // The use of `Object#toString` avoids issues with the `typeof` operator\n    // in Safari 8-9 which returns 'object' for typed array and other constructors.\n    var tag = isObject(value) ? objectToString.call(value) : \"\";\n    return tag == funcTag || tag == genTag;\n}\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */ function isLength(value) {\n    return typeof value == \"number\" && value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */ function isObject(value) {\n    var type = typeof value;\n    return !!value && (type == \"object\" || type == \"function\");\n}\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */ function isObjectLike(value) {\n    return !!value && typeof value == \"object\";\n}\n/**\n * Creates an array of the own enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects. See the\n * [ES spec](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * for more details.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keys(new Foo);\n * // => ['a', 'b'] (iteration order is not guaranteed)\n *\n * _.keys('hi');\n * // => ['0', '1']\n */ function keys(object) {\n    return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);\n}\n/**\n * This method returns a new empty array.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {Array} Returns the new empty array.\n * @example\n *\n * var arrays = _.times(2, _.stubArray);\n *\n * console.log(arrays);\n * // => [[], []]\n *\n * console.log(arrays[0] === arrays[1]);\n * // => false\n */ function stubArray() {\n    return [];\n}\n/**\n * This method returns `false`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `false`.\n * @example\n *\n * _.times(2, _.stubFalse);\n * // => [false, false]\n */ function stubFalse() {\n    return false;\n}\nmodule.exports = cloneDeep;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lodash.clonedeep/index.js\n");

/***/ })

};
;