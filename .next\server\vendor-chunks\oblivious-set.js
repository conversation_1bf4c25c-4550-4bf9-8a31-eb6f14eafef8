"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oblivious-set";
exports.ids = ["vendor-chunks/oblivious-set"];
exports.modules = {

/***/ "(ssr)/./node_modules/oblivious-set/dist/es/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/oblivious-set/dist/es/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ObliviousSet: () => (/* binding */ ObliviousSet),\n/* harmony export */   now: () => (/* binding */ now),\n/* harmony export */   removeTooOldValues: () => (/* binding */ removeTooOldValues)\n/* harmony export */ });\n/**\n * this is a set which automatically forgets\n * a given entry when a new entry is set and the ttl\n * of the old one is over\n */ var ObliviousSet = /** @class */ function() {\n    function ObliviousSet(ttl) {\n        this.ttl = ttl;\n        this.map = new Map();\n        /**\n         * Creating calls to setTimeout() is expensive,\n         * so we only do that if there is not timeout already open.\n         */ this._to = false;\n    }\n    ObliviousSet.prototype.has = function(value) {\n        return this.map.has(value);\n    };\n    ObliviousSet.prototype.add = function(value) {\n        var _this = this;\n        this.map.set(value, now());\n        /**\n         * When a new value is added,\n         * start the cleanup at the next tick\n         * to not block the cpu for more important stuff\n         * that might happen.\n         */ if (!this._to) {\n            this._to = true;\n            setTimeout(function() {\n                _this._to = false;\n                removeTooOldValues(_this);\n            }, 0);\n        }\n    };\n    ObliviousSet.prototype.clear = function() {\n        this.map.clear();\n    };\n    return ObliviousSet;\n}();\n\n/**\n * Removes all entries from the set\n * where the TTL has expired\n */ function removeTooOldValues(obliviousSet) {\n    var olderThen = now() - obliviousSet.ttl;\n    var iterator = obliviousSet.map[Symbol.iterator]();\n    /**\n     * Because we can assume the new values are added at the bottom,\n     * we start from the top and stop as soon as we reach a non-too-old value.\n     */ while(true){\n        var next = iterator.next().value;\n        if (!next) {\n            return; // no more elements\n        }\n        var value = next[0];\n        var time = next[1];\n        if (time < olderThen) {\n            obliviousSet.map.delete(value);\n        } else {\n            // We reached a value that is not old enough\n            return;\n        }\n    }\n}\nfunction now() {\n    return new Date().getTime();\n} //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/oblivious-set/dist/es/index.js\n");

/***/ })

};
;