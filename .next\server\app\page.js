/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Csrc%5Cproviders%5CWeb3AuthProvider.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Csrc%5Cproviders%5CWeb3AuthProvider.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/Web3AuthProvider.tsx */ \"(ssr)/./src/providers/Web3AuthProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RSUzQSU1Q2RhcHAlRTUlQkMlODAlRTUlOEYlOTElRTglQjclQUYlRTclQkElQkYlNUNBSSUyMCUyQiUyMFdlYjMlMjAlRTUlODglOUIlRTQlQkQlOUMlRTUlQjklQjMlRTUlOEYlQjAlNUNub2RlX21vZHVsZXMlNUNyZWFjdC1ob3QtdG9hc3QlNUNkaXN0JTVDaW5kZXgubWpzJm1vZHVsZXM9RSUzQSU1Q2RhcHAlRTUlQkMlODAlRTUlOEYlOTElRTglQjclQUYlRTclQkElQkYlNUNBSSUyMCUyQiUyMFdlYjMlMjAlRTUlODglOUIlRTQlQkQlOUMlRTUlQjklQjMlRTUlOEYlQjAlNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUUlM0ElNUNkYXBwJUU1JUJDJTgwJUU1JThGJTkxJUU4JUI3JUFGJUU3JUJBJUJGJTVDQUklMjAlMkIlMjBXZWIzJTIwJUU1JTg4JTlCJUU0JUJEJTlDJUU1JUI5JUIzJUU1JThGJUIwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9RSUzQSU1Q2RhcHAlRTUlQkMlODAlRTUlOEYlOTElRTglQjclQUYlRTclQkElQkYlNUNBSSUyMCUyQiUyMFdlYjMlMjAlRTUlODglOUIlRTQlQkQlOUMlRTUlQjklQjMlRTUlOEYlQjAlNUNzcmMlNUNwcm92aWRlcnMlNUNXZWIzQXV0aFByb3ZpZGVyLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc01BQWlIO0FBQ2pIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLz8zMzU3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcZGFwcOW8gOWPkei3r+e6v1xcXFxBSSArIFdlYjMg5Yib5L2c5bmz5Y+wXFxcXG5vZGVfbW9kdWxlc1xcXFxyZWFjdC1ob3QtdG9hc3RcXFxcZGlzdFxcXFxpbmRleC5tanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGRhcHDlvIDlj5Hot6/nur9cXFxcQUkgKyBXZWIzIOWIm+S9nOW5s+WPsFxcXFxzcmNcXFxccHJvdmlkZXJzXFxcXFdlYjNBdXRoUHJvdmlkZXIudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Csrc%5Cproviders%5CWeb3AuthProvider.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RSUzQSU1Q2RhcHAlRTUlQkMlODAlRTUlOEYlOTElRTglQjclQUYlRTclQkElQkYlNUNBSSUyMCUyQiUyMFdlYjMlMjAlRTUlODglOUIlRTQlQkQlOUMlRTUlQjklQjMlRTUlOEYlQjAlNUNzcmMlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vP2ExZTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxkYXBw5byA5Y+R6Lev57q/XFxcXEFJICsgV2ViMyDliJvkvZzlubPlj7BcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useWeb3Auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useWeb3Auth */ \"(ssr)/./src/hooks/useWeb3Auth.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Coins_Music_Palette_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,Music,Palette,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Coins_Music_Palette_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,Music,Palette,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Coins_Music_Palette_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,Music,Palette,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Coins_Music_Palette_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,Music,Palette,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction HomePage() {\n    const { user, login, logout, isLoading } = (0,_hooks_useWeb3Auth__WEBPACK_IMPORTED_MODULE_1__.useWeb3Auth)();\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_Music_Palette_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 14,\n                columnNumber: 13\n            }, this),\n            title: \"AI文字创作\",\n            description: \"使用先进的AI技术生成故事、诗歌、文案等文字内容\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_Music_Palette_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 19,\n                columnNumber: 13\n            }, this),\n            title: \"AI图片生成\",\n            description: \"创造独特的艺术画作、插画和数字艺术品\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_Music_Palette_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 24,\n                columnNumber: 13\n            }, this),\n            title: \"AI音乐创作\",\n            description: \"生成原创背景音乐和音效，为作品增添声音魅力\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_Music_Palette_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 29,\n                columnNumber: 13\n            }, this),\n            title: \"NFT铸造\",\n            description: \"一键将创作转化为NFT，在Solana链上永久保存\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen gradient-bg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"p-6 flex justify-between items-center glass-effect\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"AI + Web3 创作平台\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white\",\n                                    children: [\n                                        \"欢迎, \",\n                                        user.name || \"创作者\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: logout,\n                                    variant: \"outline\",\n                                    children: \"退出登录\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: login,\n                            disabled: isLoading,\n                            className: \"bg-white text-purple-600 hover:bg-gray-100\",\n                            children: isLoading ? \"连接中...\" : \"连接钱包\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold text-white mb-6\",\n                                children: \"创作的未来，从这里开始\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-white/80 mb-8 max-w-2xl mx-auto\",\n                                children: \"结合AI创作能力与Web3技术，让每个人都能成为数字艺术家， 创作独特内容并通过NFT获得收益\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            !user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: login,\n                                disabled: isLoading,\n                                size: \"lg\",\n                                className: \"bg-white text-purple-600 hover:bg-gray-100 text-lg px-8 py-4\",\n                                children: isLoading ? \"连接中...\" : \"开始创作之旅\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\",\n                        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: index * 0.1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"glass-effect border-white/20 text-white h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-purple-300 mb-2\",\n                                                    children: feature.icon\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                className: \"text-white/70\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this)\n                            }, index, false, {\n                                fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass-effect border-white/20 text-white max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"钱包信息\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-white/70 mb-2\",\n                                            children: [\n                                                \"钱包地址: \",\n                                                user.publicKey?.slice(0, 8),\n                                                \"...\",\n                                                user.publicKey?.slice(-8)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"w-full bg-purple-600 hover:bg-purple-700\",\n                                            children: \"开始创作\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9jYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFFRTtBQUVoQyxNQUFNRSxxQkFBT0YsNkNBQWdCLENBRzNCLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUNDRCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FDWCw0REFDQUc7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkgsS0FBS00sV0FBVyxHQUFHO0FBRW5CLE1BQU1DLDJCQUFhVCw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQUlELEtBQUtBO1FBQUtGLFdBQVdILDhDQUFFQSxDQUFDLGlDQUFpQ0c7UUFBYSxHQUFHQyxLQUFLOzs7Ozs7QUFFckZJLFdBQVdELFdBQVcsR0FBRztBQUV6QixNQUFNRSwwQkFBWVYsNkNBQWdCLENBR2hDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDSztRQUNDTCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FDWCxzREFDQUc7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkssVUFBVUYsV0FBVyxHQUFHO0FBRXhCLE1BQU1JLGdDQUFrQlosNkNBQWdCLENBR3RDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDTztRQUNDUCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FBQyxpQ0FBaUNHO1FBQzlDLEdBQUdDLEtBQUs7Ozs7OztBQUdiTyxnQkFBZ0JKLFdBQVcsR0FBRztBQUU5QixNQUFNTSw0QkFBY2QsNkNBQWdCLENBR2xDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUFJRCxLQUFLQTtRQUFLRixXQUFXSCw4Q0FBRUEsQ0FBQyxZQUFZRztRQUFhLEdBQUdDLEtBQUs7Ozs7OztBQUVoRVMsWUFBWU4sV0FBVyxHQUFHO0FBRTFCLE1BQU1PLDJCQUFhZiw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQUlELEtBQUtBO1FBQUtGLFdBQVdILDhDQUFFQSxDQUFDLDhCQUE4Qkc7UUFBYSxHQUFHQyxLQUFLOzs7Ozs7QUFFbEZVLFdBQVdQLFdBQVcsR0FBRztBQUV1RCIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXdlYjMtY3JlYXRvci1wbGF0Zm9ybS8uL3NyYy9jb21wb25lbnRzL3VpL2NhcmQudHN4P2U3ZDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBDYXJkID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwicm91bmRlZC1sZyBib3JkZXIgYmctY2FyZCB0ZXh0LWNhcmQtZm9yZWdyb3VuZCBzaGFkb3ctc21cIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmQuZGlzcGxheU5hbWUgPSBcIkNhcmRcIlxuXG5jb25zdCBDYXJkSGVhZGVyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2IHJlZj17cmVmfSBjbGFzc05hbWU9e2NuKFwiZmxleCBmbGV4LWNvbCBzcGFjZS15LTEuNSBwLTZcIiwgY2xhc3NOYW1lKX0gey4uLnByb3BzfSAvPlxuKSlcbkNhcmRIZWFkZXIuZGlzcGxheU5hbWUgPSBcIkNhcmRIZWFkZXJcIlxuXG5jb25zdCBDYXJkVGl0bGUgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MUGFyYWdyYXBoRWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTEhlYWRpbmdFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8aDNcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIGxlYWRpbmctbm9uZSB0cmFja2luZy10aWdodFwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZFRpdGxlLmRpc3BsYXlOYW1lID0gXCJDYXJkVGl0bGVcIlxuXG5jb25zdCBDYXJkRGVzY3JpcHRpb24gPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MUGFyYWdyYXBoRWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTFBhcmFncmFwaEVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxwXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmREZXNjcmlwdGlvbi5kaXNwbGF5TmFtZSA9IFwiQ2FyZERlc2NyaXB0aW9uXCJcblxuY29uc3QgQ2FyZENvbnRlbnQgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXYgcmVmPXtyZWZ9IGNsYXNzTmFtZT17Y24oXCJwLTYgcHQtMFwiLCBjbGFzc05hbWUpfSB7Li4ucHJvcHN9IC8+XG4pKVxuQ2FyZENvbnRlbnQuZGlzcGxheU5hbWUgPSBcIkNhcmRDb250ZW50XCJcblxuY29uc3QgQ2FyZEZvb3RlciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdiByZWY9e3JlZn0gY2xhc3NOYW1lPXtjbihcImZsZXggaXRlbXMtY2VudGVyIHAtNiBwdC0wXCIsIGNsYXNzTmFtZSl9IHsuLi5wcm9wc30gLz5cbikpXG5DYXJkRm9vdGVyLmRpc3BsYXlOYW1lID0gXCJDYXJkRm9vdGVyXCJcblxuZXhwb3J0IHsgQ2FyZCwgQ2FyZEhlYWRlciwgQ2FyZEZvb3RlciwgQ2FyZFRpdGxlLCBDYXJkRGVzY3JpcHRpb24sIENhcmRDb250ZW50IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiQ2FyZCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsImRpdiIsImRpc3BsYXlOYW1lIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsImgzIiwiQ2FyZERlc2NyaXB0aW9uIiwicCIsIkNhcmRDb250ZW50IiwiQ2FyZEZvb3RlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useWeb3Auth.ts":
/*!**********************************!*\
  !*** ./src/hooks/useWeb3Auth.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWeb3Auth: () => (/* binding */ useWeb3Auth)\n/* harmony export */ });\n/* harmony import */ var _providers_Web3AuthProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/providers/Web3AuthProvider */ \"(ssr)/./src/providers/Web3AuthProvider.tsx\");\n\nconst useWeb3Auth = ()=>{\n    return (0,_providers_Web3AuthProvider__WEBPACK_IMPORTED_MODULE_0__.useWeb3AuthContext)();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlV2ViM0F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBaUU7QUFFMUQsTUFBTUMsY0FBYztJQUN6QixPQUFPRCwrRUFBa0JBO0FBQzNCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9zcmMvaG9va3MvdXNlV2ViM0F1dGgudHM/NTM4ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VXZWIzQXV0aENvbnRleHQgfSBmcm9tICdAL3Byb3ZpZGVycy9XZWIzQXV0aFByb3ZpZGVyJ1xuXG5leHBvcnQgY29uc3QgdXNlV2ViM0F1dGggPSAoKSA9PiB7XG4gIHJldHVybiB1c2VXZWIzQXV0aENvbnRleHQoKVxufVxuIl0sIm5hbWVzIjpbInVzZVdlYjNBdXRoQ29udGV4dCIsInVzZVdlYjNBdXRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useWeb3Auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXdlYjMtY3JlYXRvci1wbGF0Zm9ybS8uL3NyYy9saWIvdXRpbHMudHM/N2MxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/web3auth.ts":
/*!*****************************!*\
  !*** ./src/lib/web3auth.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getWeb3AuthProvider: () => (/* binding */ getWeb3AuthProvider),\n/* harmony export */   web3auth: () => (/* binding */ web3auth)\n/* harmony export */ });\n/* harmony import */ var _web3auth_modal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @web3auth/modal */ \"(ssr)/./node_modules/@web3auth/modal/dist/modal.esm.js\");\n/* harmony import */ var _web3auth_base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @web3auth/base */ \"(ssr)/./node_modules/@web3auth/base/dist/base.esm.js\");\n/* harmony import */ var _web3auth_solana_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @web3auth/solana-provider */ \"(ssr)/./node_modules/@web3auth/solana-provider/dist/solanaProvider.esm.js\");\n\n\n\nconst clientId = \"BDdeo2-13Ghj7CScGuPeEU7a_ogZsOWuxv95yMPLEB75-e07zaCGfEgymbWUb8y3C8zLYiqb1V_e_Ud6uNOmsfY\";\nconst chainConfig = {\n    chainNamespace: _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.CHAIN_NAMESPACES.SOLANA,\n    chainId: \"0x3\",\n    rpcTarget: \"https://api.devnet.solana.com\",\n    displayName: \"Solana Devnet\",\n    blockExplorer: \"https://explorer.solana.com/?cluster=devnet\",\n    ticker: \"SOL\",\n    tickerName: \"Solana\"\n};\nconst privateKeyProvider = new _web3auth_solana_provider__WEBPACK_IMPORTED_MODULE_2__.SolanaPrivateKeyProvider({\n    config: {\n        chainConfig\n    }\n});\nconst web3auth = new _web3auth_modal__WEBPACK_IMPORTED_MODULE_0__.Web3Auth({\n    clientId,\n    web3AuthNetwork: _web3auth_base__WEBPACK_IMPORTED_MODULE_1__.WEB3AUTH_NETWORK.SAPPHIRE_DEVNET,\n    privateKeyProvider,\n    uiConfig: {\n        appName: \"AI + Web3 创作平台\",\n        appUrl: \"http://localhost:3000\",\n        theme: {\n            primary: \"#7c3aed\"\n        },\n        mode: \"light\",\n        logoLight: \"https://web3auth.io/images/web3auth-logo.svg\",\n        logoDark: \"https://web3auth.io/images/web3auth-logo---Dark.svg\",\n        defaultLanguage: \"zh\",\n        loginGridCol: 3,\n        primaryButton: \"socialLogin\"\n    }\n});\nconst getWeb3AuthProvider = ()=>{\n    return web3auth.provider;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/web3auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/providers/Web3AuthProvider.tsx":
/*!********************************************!*\
  !*** ./src/providers/Web3AuthProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Web3AuthProvider: () => (/* binding */ Web3AuthProvider),\n/* harmony export */   useWeb3AuthContext: () => (/* binding */ useWeb3AuthContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_web3auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/web3auth */ \"(ssr)/./src/lib/web3auth.ts\");\n/* harmony import */ var _solana_web3_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @solana/web3.js */ \"(ssr)/./node_modules/@solana/web3.js/lib/index.esm.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ useWeb3AuthContext,Web3AuthProvider auto */ \n\n\n\n\nconst Web3AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst useWeb3AuthContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(Web3AuthContext);\n    if (!context) {\n        throw new Error(\"useWeb3AuthContext must be used within Web3AuthProvider\");\n    }\n    return context;\n};\nconst Web3AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const connection = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_3__.Connection(\"https://api.devnet.solana.com\", \"confirmed\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const init = async ()=>{\n            try {\n                await _lib_web3auth__WEBPACK_IMPORTED_MODULE_2__.web3auth.initModal();\n                setProvider(_lib_web3auth__WEBPACK_IMPORTED_MODULE_2__.web3auth.provider);\n                if (_lib_web3auth__WEBPACK_IMPORTED_MODULE_2__.web3auth.connected) {\n                    await setUserInfo();\n                }\n            } catch (error) {\n                console.error(\"Web3Auth初始化失败:\", error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(\"Web3Auth初始化失败\");\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        init();\n    }, []);\n    const setUserInfo = async ()=>{\n        if (!_lib_web3auth__WEBPACK_IMPORTED_MODULE_2__.web3auth.provider) return;\n        try {\n            const userInfo = await _lib_web3auth__WEBPACK_IMPORTED_MODULE_2__.web3auth.getUserInfo();\n            const accounts = await _lib_web3auth__WEBPACK_IMPORTED_MODULE_2__.web3auth.provider.request({\n                method: \"getAccounts\"\n            });\n            if (accounts.length > 0) {\n                const publicKey = accounts[0];\n                const balance = await getBalance();\n                setUser({\n                    name: userInfo.name,\n                    email: userInfo.email,\n                    profileImage: userInfo.profileImage,\n                    publicKey,\n                    balance\n                });\n            }\n        } catch (error) {\n            console.error(\"获取用户信息失败:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(\"获取用户信息失败\");\n        }\n    };\n    const login = async ()=>{\n        if (!_lib_web3auth__WEBPACK_IMPORTED_MODULE_2__.web3auth) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(\"Web3Auth未初始化\");\n            return;\n        }\n        try {\n            setIsLoading(true);\n            const web3authProvider = await _lib_web3auth__WEBPACK_IMPORTED_MODULE_2__.web3auth.connect();\n            setProvider(web3authProvider);\n            await setUserInfo();\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"登录成功！\");\n        } catch (error) {\n            console.error(\"登录失败:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(\"登录失败，请重试\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        if (!_lib_web3auth__WEBPACK_IMPORTED_MODULE_2__.web3auth) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(\"Web3Auth未初始化\");\n            return;\n        }\n        try {\n            setIsLoading(true);\n            await _lib_web3auth__WEBPACK_IMPORTED_MODULE_2__.web3auth.logout();\n            setProvider(null);\n            setUser(null);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"已退出登录\");\n        } catch (error) {\n            console.error(\"退出登录失败:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(\"退出登录失败\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const getBalance = async ()=>{\n        if (!provider || !user?.publicKey) return 0;\n        try {\n            const publicKey = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_3__.PublicKey(user.publicKey);\n            const balance = await connection.getBalance(publicKey);\n            return balance / 1e9 // 转换为SOL\n            ;\n        } catch (error) {\n            console.error(\"获取余额失败:\", error);\n            return 0;\n        }\n    };\n    const value = {\n        user,\n        provider,\n        isLoading,\n        login,\n        logout,\n        getBalance\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Web3AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\providers\\\\Web3AuthProvider.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcHJvdmlkZXJzL1dlYjNBdXRoUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFaUY7QUFDeEM7QUFFYztBQUNwQjtBQW1CbkMsTUFBTVEsZ0NBQWtCUixvREFBYUEsQ0FBNkI7QUFFM0QsTUFBTVMscUJBQXFCO0lBQ2hDLE1BQU1DLFVBQVVULGlEQUFVQSxDQUFDTztJQUMzQixJQUFJLENBQUNFLFNBQVM7UUFDWixNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNULEVBQUM7QUFNTSxNQUFNRSxtQkFBbUIsQ0FBQyxFQUFFQyxRQUFRLEVBQXlCO0lBQ2xFLE1BQU0sQ0FBQ0MsTUFBTUMsUUFBUSxHQUFHWiwrQ0FBUUEsQ0FBYztJQUM5QyxNQUFNLENBQUNhLFVBQVVDLFlBQVksR0FBR2QsK0NBQVFBLENBQW1CO0lBQzNELE1BQU0sQ0FBQ2UsV0FBV0MsYUFBYSxHQUFHaEIsK0NBQVFBLENBQUM7SUFFM0MsTUFBTWlCLGFBQWEsSUFBSWYsdURBQVVBLENBQUMsaUNBQWlDO0lBRW5FSCxnREFBU0EsQ0FBQztRQUNSLE1BQU1tQixPQUFPO1lBQ1gsSUFBSTtnQkFDRixNQUFNakIsbURBQVFBLENBQUNrQixTQUFTO2dCQUN4QkwsWUFBWWIsbURBQVFBLENBQUNZLFFBQVE7Z0JBRTdCLElBQUlaLG1EQUFRQSxDQUFDbUIsU0FBUyxFQUFFO29CQUN0QixNQUFNQztnQkFDUjtZQUNGLEVBQUUsT0FBT0MsT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLGtCQUFrQkE7Z0JBQ2hDbEIsdURBQUtBLENBQUNrQixLQUFLLENBQUM7WUFDZCxTQUFVO2dCQUNSTixhQUFhO1lBQ2Y7UUFDRjtRQUVBRTtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1HLGNBQWM7UUFDbEIsSUFBSSxDQUFDcEIsbURBQVFBLENBQUNZLFFBQVEsRUFBRTtRQUV4QixJQUFJO1lBQ0YsTUFBTVcsV0FBVyxNQUFNdkIsbURBQVFBLENBQUN3QixXQUFXO1lBQzNDLE1BQU1DLFdBQVcsTUFBTXpCLG1EQUFRQSxDQUFDWSxRQUFRLENBQUNjLE9BQU8sQ0FBQztnQkFDL0NDLFFBQVE7WUFDVjtZQUVBLElBQUlGLFNBQVNHLE1BQU0sR0FBRyxHQUFHO2dCQUN2QixNQUFNQyxZQUFZSixRQUFRLENBQUMsRUFBRTtnQkFDN0IsTUFBTUssVUFBVSxNQUFNQztnQkFFdEJwQixRQUFRO29CQUNOcUIsTUFBTVQsU0FBU1MsSUFBSTtvQkFDbkJDLE9BQU9WLFNBQVNVLEtBQUs7b0JBQ3JCQyxjQUFjWCxTQUFTVyxZQUFZO29CQUNuQ0w7b0JBQ0FDO2dCQUNGO1lBQ0Y7UUFDRixFQUFFLE9BQU9ULE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGFBQWFBO1lBQzNCbEIsdURBQUtBLENBQUNrQixLQUFLLENBQUM7UUFDZDtJQUNGO0lBRUEsTUFBTWMsUUFBUTtRQUNaLElBQUksQ0FBQ25DLG1EQUFRQSxFQUFFO1lBQ2JHLHVEQUFLQSxDQUFDa0IsS0FBSyxDQUFDO1lBQ1o7UUFDRjtRQUVBLElBQUk7WUFDRk4sYUFBYTtZQUNiLE1BQU1xQixtQkFBbUIsTUFBTXBDLG1EQUFRQSxDQUFDcUMsT0FBTztZQUMvQ3hCLFlBQVl1QjtZQUNaLE1BQU1oQjtZQUNOakIsdURBQUtBLENBQUNtQyxPQUFPLENBQUM7UUFDaEIsRUFBRSxPQUFPakIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsU0FBU0E7WUFDdkJsQix1REFBS0EsQ0FBQ2tCLEtBQUssQ0FBQztRQUNkLFNBQVU7WUFDUk4sYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNd0IsU0FBUztRQUNiLElBQUksQ0FBQ3ZDLG1EQUFRQSxFQUFFO1lBQ2JHLHVEQUFLQSxDQUFDa0IsS0FBSyxDQUFDO1lBQ1o7UUFDRjtRQUVBLElBQUk7WUFDRk4sYUFBYTtZQUNiLE1BQU1mLG1EQUFRQSxDQUFDdUMsTUFBTTtZQUNyQjFCLFlBQVk7WUFDWkYsUUFBUTtZQUNSUix1REFBS0EsQ0FBQ21DLE9BQU8sQ0FBQztRQUNoQixFQUFFLE9BQU9qQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxXQUFXQTtZQUN6QmxCLHVEQUFLQSxDQUFDa0IsS0FBSyxDQUFDO1FBQ2QsU0FBVTtZQUNSTixhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU1nQixhQUFhO1FBQ2pCLElBQUksQ0FBQ25CLFlBQVksQ0FBQ0YsTUFBTW1CLFdBQVcsT0FBTztRQUUxQyxJQUFJO1lBQ0YsTUFBTUEsWUFBWSxJQUFJM0Isc0RBQVNBLENBQUNRLEtBQUttQixTQUFTO1lBQzlDLE1BQU1DLFVBQVUsTUFBTWQsV0FBV2UsVUFBVSxDQUFDRjtZQUM1QyxPQUFPQyxVQUFVLElBQUksU0FBUzs7UUFDaEMsRUFBRSxPQUFPVCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxXQUFXQTtZQUN6QixPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU1tQixRQUE2QjtRQUNqQzlCO1FBQ0FFO1FBQ0FFO1FBQ0FxQjtRQUNBSTtRQUNBUjtJQUNGO0lBRUEscUJBQ0UsOERBQUMzQixnQkFBZ0JxQyxRQUFRO1FBQUNELE9BQU9BO2tCQUM5Qi9COzs7Ozs7QUFHUCxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vc3JjL3Byb3ZpZGVycy9XZWIzQXV0aFByb3ZpZGVyLnRzeD81NDA3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlLCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHdlYjNhdXRoIH0gZnJvbSAnQC9saWIvd2ViM2F1dGgnXG5pbXBvcnQgeyBJUHJvdmlkZXIgfSBmcm9tICdAd2ViM2F1dGgvYmFzZSdcbmltcG9ydCB7IENvbm5lY3Rpb24sIFB1YmxpY0tleSB9IGZyb20gJ0Bzb2xhbmEvd2ViMy5qcydcbmltcG9ydCB0b2FzdCBmcm9tICdyZWFjdC1ob3QtdG9hc3QnXG5cbmludGVyZmFjZSBVc2VyIHtcbiAgbmFtZT86IHN0cmluZ1xuICBlbWFpbD86IHN0cmluZ1xuICBwcm9maWxlSW1hZ2U/OiBzdHJpbmdcbiAgcHVibGljS2V5Pzogc3RyaW5nXG4gIGJhbGFuY2U/OiBudW1iZXJcbn1cblxuaW50ZXJmYWNlIFdlYjNBdXRoQ29udGV4dFR5cGUge1xuICB1c2VyOiBVc2VyIHwgbnVsbFxuICBwcm92aWRlcjogSVByb3ZpZGVyIHwgbnVsbFxuICBpc0xvYWRpbmc6IGJvb2xlYW5cbiAgbG9naW46ICgpID0+IFByb21pc2U8dm9pZD5cbiAgbG9nb3V0OiAoKSA9PiBQcm9taXNlPHZvaWQ+XG4gIGdldEJhbGFuY2U6ICgpID0+IFByb21pc2U8bnVtYmVyPlxufVxuXG5jb25zdCBXZWIzQXV0aENvbnRleHQgPSBjcmVhdGVDb250ZXh0PFdlYjNBdXRoQ29udGV4dFR5cGUgfCBudWxsPihudWxsKVxuXG5leHBvcnQgY29uc3QgdXNlV2ViM0F1dGhDb250ZXh0ID0gKCkgPT4ge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChXZWIzQXV0aENvbnRleHQpXG4gIGlmICghY29udGV4dCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlV2ViM0F1dGhDb250ZXh0IG11c3QgYmUgdXNlZCB3aXRoaW4gV2ViM0F1dGhQcm92aWRlcicpXG4gIH1cbiAgcmV0dXJuIGNvbnRleHRcbn1cblxuaW50ZXJmYWNlIFdlYjNBdXRoUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGNvbnN0IFdlYjNBdXRoUHJvdmlkZXIgPSAoeyBjaGlsZHJlbiB9OiBXZWIzQXV0aFByb3ZpZGVyUHJvcHMpID0+IHtcbiAgY29uc3QgW3VzZXIsIHNldFVzZXJdID0gdXNlU3RhdGU8VXNlciB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtwcm92aWRlciwgc2V0UHJvdmlkZXJdID0gdXNlU3RhdGU8SVByb3ZpZGVyIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG5cbiAgY29uc3QgY29ubmVjdGlvbiA9IG5ldyBDb25uZWN0aW9uKCdodHRwczovL2FwaS5kZXZuZXQuc29sYW5hLmNvbScsICdjb25maXJtZWQnKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaW5pdCA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHdlYjNhdXRoLmluaXRNb2RhbCgpXG4gICAgICAgIHNldFByb3ZpZGVyKHdlYjNhdXRoLnByb3ZpZGVyKVxuXG4gICAgICAgIGlmICh3ZWIzYXV0aC5jb25uZWN0ZWQpIHtcbiAgICAgICAgICBhd2FpdCBzZXRVc2VySW5mbygpXG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1dlYjNBdXRo5Yid5aeL5YyW5aSx6LSlOicsIGVycm9yKVxuICAgICAgICB0b2FzdC5lcnJvcignV2ViM0F1dGjliJ3lp4vljJblpLHotKUnKVxuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxuICAgICAgfVxuICAgIH1cblxuICAgIGluaXQoKVxuICB9LCBbXSlcblxuICBjb25zdCBzZXRVc2VySW5mbyA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXdlYjNhdXRoLnByb3ZpZGVyKSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB1c2VySW5mbyA9IGF3YWl0IHdlYjNhdXRoLmdldFVzZXJJbmZvKClcbiAgICAgIGNvbnN0IGFjY291bnRzID0gYXdhaXQgd2ViM2F1dGgucHJvdmlkZXIucmVxdWVzdCh7XG4gICAgICAgIG1ldGhvZDogXCJnZXRBY2NvdW50c1wiLFxuICAgICAgfSkgYXMgc3RyaW5nW11cblxuICAgICAgaWYgKGFjY291bnRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgY29uc3QgcHVibGljS2V5ID0gYWNjb3VudHNbMF1cbiAgICAgICAgY29uc3QgYmFsYW5jZSA9IGF3YWl0IGdldEJhbGFuY2UoKVxuXG4gICAgICAgIHNldFVzZXIoe1xuICAgICAgICAgIG5hbWU6IHVzZXJJbmZvLm5hbWUsXG4gICAgICAgICAgZW1haWw6IHVzZXJJbmZvLmVtYWlsLFxuICAgICAgICAgIHByb2ZpbGVJbWFnZTogdXNlckluZm8ucHJvZmlsZUltYWdlLFxuICAgICAgICAgIHB1YmxpY0tleSxcbiAgICAgICAgICBiYWxhbmNlLFxuICAgICAgICB9KVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnlKjmiLfkv6Hmga/lpLHotKU6JywgZXJyb3IpXG4gICAgICB0b2FzdC5lcnJvcign6I635Y+W55So5oi35L+h5oGv5aSx6LSlJylcbiAgICB9XG4gIH1cblxuICBjb25zdCBsb2dpbiA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXdlYjNhdXRoKSB7XG4gICAgICB0b2FzdC5lcnJvcignV2ViM0F1dGjmnKrliJ3lp4vljJYnKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHNldElzTG9hZGluZyh0cnVlKVxuICAgICAgY29uc3Qgd2ViM2F1dGhQcm92aWRlciA9IGF3YWl0IHdlYjNhdXRoLmNvbm5lY3QoKVxuICAgICAgc2V0UHJvdmlkZXIod2ViM2F1dGhQcm92aWRlcilcbiAgICAgIGF3YWl0IHNldFVzZXJJbmZvKClcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoJ+eZu+W9leaIkOWKn++8gScpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+eZu+W9leWksei0pTonLCBlcnJvcilcbiAgICAgIHRvYXN0LmVycm9yKCfnmbvlvZXlpLHotKXvvIzor7fph43or5UnKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgbG9nb3V0ID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghd2ViM2F1dGgpIHtcbiAgICAgIHRvYXN0LmVycm9yKCdXZWIzQXV0aOacquWIneWni+WMlicpXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpXG4gICAgICBhd2FpdCB3ZWIzYXV0aC5sb2dvdXQoKVxuICAgICAgc2V0UHJvdmlkZXIobnVsbClcbiAgICAgIHNldFVzZXIobnVsbClcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoJ+W3sumAgOWHuueZu+W9lScpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+mAgOWHuueZu+W9leWksei0pTonLCBlcnJvcilcbiAgICAgIHRvYXN0LmVycm9yKCfpgIDlh7rnmbvlvZXlpLHotKUnKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0QmFsYW5jZSA9IGFzeW5jICgpOiBQcm9taXNlPG51bWJlcj4gPT4ge1xuICAgIGlmICghcHJvdmlkZXIgfHwgIXVzZXI/LnB1YmxpY0tleSkgcmV0dXJuIDBcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCBwdWJsaWNLZXkgPSBuZXcgUHVibGljS2V5KHVzZXIucHVibGljS2V5KVxuICAgICAgY29uc3QgYmFsYW5jZSA9IGF3YWl0IGNvbm5lY3Rpb24uZ2V0QmFsYW5jZShwdWJsaWNLZXkpXG4gICAgICByZXR1cm4gYmFsYW5jZSAvIDFlOSAvLyDovazmjaLkuLpTT0xcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5L2Z6aKd5aSx6LSlOicsIGVycm9yKVxuICAgICAgcmV0dXJuIDBcbiAgICB9XG4gIH1cblxuICBjb25zdCB2YWx1ZTogV2ViM0F1dGhDb250ZXh0VHlwZSA9IHtcbiAgICB1c2VyLFxuICAgIHByb3ZpZGVyLFxuICAgIGlzTG9hZGluZyxcbiAgICBsb2dpbixcbiAgICBsb2dvdXQsXG4gICAgZ2V0QmFsYW5jZSxcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPFdlYjNBdXRoQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvV2ViM0F1dGhDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIndlYjNhdXRoIiwiQ29ubmVjdGlvbiIsIlB1YmxpY0tleSIsInRvYXN0IiwiV2ViM0F1dGhDb250ZXh0IiwidXNlV2ViM0F1dGhDb250ZXh0IiwiY29udGV4dCIsIkVycm9yIiwiV2ViM0F1dGhQcm92aWRlciIsImNoaWxkcmVuIiwidXNlciIsInNldFVzZXIiLCJwcm92aWRlciIsInNldFByb3ZpZGVyIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiY29ubmVjdGlvbiIsImluaXQiLCJpbml0TW9kYWwiLCJjb25uZWN0ZWQiLCJzZXRVc2VySW5mbyIsImVycm9yIiwiY29uc29sZSIsInVzZXJJbmZvIiwiZ2V0VXNlckluZm8iLCJhY2NvdW50cyIsInJlcXVlc3QiLCJtZXRob2QiLCJsZW5ndGgiLCJwdWJsaWNLZXkiLCJiYWxhbmNlIiwiZ2V0QmFsYW5jZSIsIm5hbWUiLCJlbWFpbCIsInByb2ZpbGVJbWFnZSIsImxvZ2luIiwid2ViM2F1dGhQcm92aWRlciIsImNvbm5lY3QiLCJzdWNjZXNzIiwibG9nb3V0IiwidmFsdWUiLCJQcm92aWRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/Web3AuthProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"81af57afb731\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz83MjMyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiODFhZjU3YWZiNzMxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers_Web3AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/Web3AuthProvider */ \"(rsc)/./src/providers/Web3AuthProvider.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"AI + Web3 创作平台\",\n    description: \"基于AI和Web3技术的创作者平台，支持文字、图片、音乐创作并铸造为NFT\",\n    keywords: [\n        \"AI\",\n        \"Web3\",\n        \"NFT\",\n        \"Solana\",\n        \"创作平台\"\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_Web3AuthProvider__WEBPACK_IMPORTED_MODULE_2__.Web3AuthProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\dapp开发路线\\\\AI + Web3 创作平台\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU1NQTtBQUpnQjtBQUN5QztBQUN0QjtBQUlsQyxNQUFNRyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7UUFBQztRQUFNO1FBQVE7UUFBTztRQUFVO0tBQU87QUFDbkQsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXWiwrSkFBZTtzQkFDOUIsNEVBQUNDLHlFQUFnQkE7O29CQUNkTztrQ0FDRCw4REFBQ04sb0RBQU9BO3dCQUFDVyxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBXZWIzQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9wcm92aWRlcnMvV2ViM0F1dGhQcm92aWRlcidcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdyZWFjdC1ob3QtdG9hc3QnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdBSSArIFdlYjMg5Yib5L2c5bmz5Y+wJyxcbiAgZGVzY3JpcHRpb246ICfln7rkuo5BSeWSjFdlYjPmioDmnK/nmoTliJvkvZzogIXlubPlj7DvvIzmlK/mjIHmloflrZfjgIHlm77niYfjgIHpn7PkuZDliJvkvZzlubbpk7jpgKDkuLpORlQnLFxuICBrZXl3b3JkczogWydBSScsICdXZWIzJywgJ05GVCcsICdTb2xhbmEnLCAn5Yib5L2c5bmz5Y+wJ10sXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8V2ViM0F1dGhQcm92aWRlcj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgPFRvYXN0ZXIgcG9zaXRpb249XCJ0b3AtcmlnaHRcIiAvPlxuICAgICAgICA8L1dlYjNBdXRoUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJXZWIzQXV0aFByb3ZpZGVyIiwiVG9hc3RlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwicG9zaXRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\dapp开发路线\AI + Web3 创作平台\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/providers/Web3AuthProvider.tsx":
/*!********************************************!*\
  !*** ./src/providers/Web3AuthProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Web3AuthProvider: () => (/* binding */ e1),
/* harmony export */   useWeb3AuthContext: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\dapp开发路线\AI + Web3 创作平台\src\providers\Web3AuthProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\dapp开发路线\AI + Web3 创作平台\src\providers\Web3AuthProvider.tsx#useWeb3AuthContext`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\dapp开发路线\AI + Web3 创作平台\src\providers\Web3AuthProvider.tsx#Web3AuthProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@web3auth","vendor-chunks/@toruslabs","vendor-chunks/@metamask","vendor-chunks/ethereum-cryptography","vendor-chunks/@noble","vendor-chunks/pony-cause","vendor-chunks/next","vendor-chunks/@babel","vendor-chunks/framer-motion","vendor-chunks/lodash","vendor-chunks/engine.io-client","vendor-chunks/readable-stream","vendor-chunks/jayson","vendor-chunks/elliptic","vendor-chunks/react-i18next","vendor-chunks/ws","vendor-chunks/hash.js","vendor-chunks/socket.io-client","vendor-chunks/rpc-websockets","vendor-chunks/math-intrinsics","vendor-chunks/bowser","vendor-chunks/socket.io-parser","vendor-chunks/es-errors","vendor-chunks/@solana","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/call-bind-apply-helpers","vendor-chunks/whatwg-url","vendor-chunks/engine.io-parser","vendor-chunks/object-keys","vendor-chunks/jsonify","vendor-chunks/get-proto","vendor-chunks/borsh","vendor-chunks/base64url","vendor-chunks/react-hot-toast","vendor-chunks/@radix-ui","vendor-chunks/utf-8-validate","vendor-chunks/tr46","vendor-chunks/node-gyp-build","vendor-chunks/inherits","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/bufferutil","vendor-chunks/ts-custom-error","vendor-chunks/tailwind-merge","vendor-chunks/superstruct","vendor-chunks/node-fetch","vendor-chunks/jwt-decode","vendor-chunks/i18next","vendor-chunks/goober","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/bignumber.js","vendor-chunks/@socket.io","vendor-chunks/classnames","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/wrappy","vendor-chunks/webidl-conversions","vendor-chunks/void-elements","vendor-chunks/toggle-selection","vendor-chunks/text-encoding-utf-8","vendor-chunks/supports-color","vendor-chunks/string_decoder","vendor-chunks/set-function-length","vendor-chunks/safe-buffer","vendor-chunks/react-qrcode-logo","vendor-chunks/qrcode-generator","vendor-chunks/pump","vendor-chunks/process","vendor-chunks/once","vendor-chunks/oblivious-set","vendor-chunks/ms","vendor-chunks/minimalistic-crypto-utils","vendor-chunks/minimalistic-assert","vendor-chunks/loglevel","vendor-chunks/lodash.merge","vendor-chunks/lodash.isequal","vendor-chunks/lodash.clonedeep","vendor-chunks/json-stable-stringify","vendor-chunks/json-rpc-random-id","vendor-chunks/isarray","vendor-chunks/html-parse-stringify","vendor-chunks/hmac-drbg","vendor-chunks/hasown","vendor-chunks/has-property-descriptors","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/fast-safe-stringify","vendor-chunks/event-target-shim","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/end-of-stream","vendor-chunks/dunder-proto","vendor-chunks/define-data-property","vendor-chunks/copy-to-clipboard","vendor-chunks/call-bound","vendor-chunks/call-bind","vendor-chunks/bs58","vendor-chunks/brorand","vendor-chunks/bn.js","vendor-chunks/base-x","vendor-chunks/abort-controller"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cdapp%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%5CAI%20%2B%20Web3%20%E5%88%9B%E4%BD%9C%E5%B9%B3%E5%8F%B0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();