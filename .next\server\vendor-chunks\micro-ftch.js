"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micro-ftch";
exports.ids = ["vendor-chunks/micro-ftch"];
exports.modules = {

/***/ "(ssr)/./node_modules/micro-ftch/index.js":
/*!******************************************!*\
  !*** ./node_modules/micro-ftch/index.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.InvalidStatusCodeError = exports.InvalidCertError = void 0;\nconst DEFAULT_OPT = Object.freeze({\n    redirect: true,\n    expectStatusCode: 200,\n    headers: {},\n    full: false,\n    keepAlive: true,\n    cors: false,\n    referrer: false,\n    sslAllowSelfSigned: false,\n    _redirectCount: 0\n});\nclass InvalidCertError extends Error {\n    constructor(msg, fingerprint256){\n        super(msg);\n        this.fingerprint256 = fingerprint256;\n    }\n}\nexports.InvalidCertError = InvalidCertError;\nclass InvalidStatusCodeError extends Error {\n    constructor(statusCode){\n        super(`Request Failed. Status Code: ${statusCode}`);\n        this.statusCode = statusCode;\n    }\n}\nexports.InvalidStatusCodeError = InvalidStatusCodeError;\nfunction detectType(b, type) {\n    if (!type || type === \"text\" || type === \"json\") {\n        try {\n            let text = new TextDecoder(\"utf8\", {\n                fatal: true\n            }).decode(b);\n            if (type === \"text\") return text;\n            try {\n                return JSON.parse(text);\n            } catch (err) {\n                if (type === \"json\") throw err;\n                return text;\n            }\n        } catch (err) {\n            if (type === \"text\" || type === \"json\") throw err;\n        }\n    }\n    return b;\n}\nlet agents = {};\nfunction fetchNode(url, _options) {\n    let options = {\n        ...DEFAULT_OPT,\n        ..._options\n    };\n    const http = __webpack_require__(/*! http */ \"http\");\n    const https = __webpack_require__(/*! https */ \"https\");\n    const zlib = __webpack_require__(/*! zlib */ \"zlib\");\n    const { promisify } = __webpack_require__(/*! util */ \"util\");\n    const { resolve: urlResolve } = __webpack_require__(/*! url */ \"url\");\n    const isSecure = !!/^https/.test(url);\n    let opts = {\n        method: options.method || \"GET\",\n        headers: {\n            \"Accept-Encoding\": \"gzip, deflate, br\"\n        }\n    };\n    const compactFP = (s)=>s.replace(/:| /g, \"\").toLowerCase();\n    if (options.keepAlive) {\n        const agentOpt = {\n            keepAlive: true,\n            keepAliveMsecs: 30 * 1000,\n            maxFreeSockets: 1024,\n            maxCachedSessions: 1024\n        };\n        const agentKey = [\n            isSecure,\n            isSecure && options.sslPinnedCertificates?.map((i)=>compactFP(i)).sort()\n        ].join();\n        opts.agent = agents[agentKey] || (agents[agentKey] = new (isSecure ? https : http).Agent(agentOpt));\n    }\n    if (options.type === \"json\") opts.headers[\"Content-Type\"] = \"application/json\";\n    if (options.data) {\n        if (!options.method) opts.method = \"POST\";\n        opts.body = options.type === \"json\" ? JSON.stringify(options.data) : options.data;\n    }\n    opts.headers = {\n        ...opts.headers,\n        ...options.headers\n    };\n    if (options.sslAllowSelfSigned) opts.rejectUnauthorized = false;\n    const handleRes = async (res)=>{\n        const status = res.statusCode;\n        if (options.redirect && 300 <= status && status < 400 && res.headers[\"location\"]) {\n            if (options._redirectCount == 10) throw new Error(\"Request failed. Too much redirects.\");\n            options._redirectCount += 1;\n            return await fetchNode(urlResolve(url, res.headers[\"location\"]), options);\n        }\n        if (options.expectStatusCode && status !== options.expectStatusCode) {\n            res.resume();\n            throw new InvalidStatusCodeError(status);\n        }\n        let buf = [];\n        for await (const chunk of res)buf.push(chunk);\n        let bytes = Buffer.concat(buf);\n        const encoding = res.headers[\"content-encoding\"];\n        if (encoding === \"br\") bytes = await promisify(zlib.brotliDecompress)(bytes);\n        if (encoding === \"gzip\" || encoding === \"deflate\") bytes = await promisify(zlib.unzip)(bytes);\n        const body = detectType(bytes, options.type);\n        if (options.full) return {\n            headers: res.headers,\n            status,\n            body\n        };\n        return body;\n    };\n    return new Promise((resolve, reject)=>{\n        const handleError = async (err)=>{\n            if (err && err.code === \"DEPTH_ZERO_SELF_SIGNED_CERT\") {\n                try {\n                    await fetchNode(url, {\n                        ...options,\n                        sslAllowSelfSigned: true,\n                        sslPinnedCertificates: []\n                    });\n                } catch (e) {\n                    if (e && e.fingerprint256) {\n                        err = new InvalidCertError(`Self-signed SSL certificate: ${e.fingerprint256}`, e.fingerprint256);\n                    }\n                }\n            }\n            reject(err);\n        };\n        const req = (isSecure ? https : http).request(url, opts, (res)=>{\n            res.on(\"error\", handleError);\n            (async ()=>{\n                try {\n                    resolve(await handleRes(res));\n                } catch (error) {\n                    reject(error);\n                }\n            })();\n        });\n        req.on(\"error\", handleError);\n        const pinned = options.sslPinnedCertificates?.map((i)=>compactFP(i));\n        const mfetchSecureConnect = (socket)=>{\n            const fp256 = compactFP(socket.getPeerCertificate()?.fingerprint256 || \"\");\n            if (!fp256 && socket.isSessionReused()) return;\n            if (pinned.includes(fp256)) return;\n            req.emit(\"error\", new InvalidCertError(`Invalid SSL certificate: ${fp256} Expected: ${pinned}`, fp256));\n            return req.abort();\n        };\n        if (options.sslPinnedCertificates) {\n            req.on(\"socket\", (socket)=>{\n                const hasListeners = socket.listeners(\"secureConnect\").map((i)=>(i.name || \"\").replace(\"bound \", \"\")).includes(\"mfetchSecureConnect\");\n                if (hasListeners) return;\n                socket.on(\"secureConnect\", mfetchSecureConnect.bind(null, socket));\n            });\n        }\n        if (options.keepAlive) req.setNoDelay(true);\n        if (opts.body) req.write(opts.body);\n        req.end();\n    });\n}\nconst SAFE_HEADERS = new Set([\n    \"Accept\",\n    \"Accept-Language\",\n    \"Content-Language\",\n    \"Content-Type\"\n].map((i)=>i.toLowerCase()));\nconst FORBIDDEN_HEADERS = new Set([\n    \"Accept-Charset\",\n    \"Accept-Encoding\",\n    \"Access-Control-Request-Headers\",\n    \"Access-Control-Request-Method\",\n    \"Connection\",\n    \"Content-Length\",\n    \"Cookie\",\n    \"Cookie2\",\n    \"Date\",\n    \"DNT\",\n    \"Expect\",\n    \"Host\",\n    \"Keep-Alive\",\n    \"Origin\",\n    \"Referer\",\n    \"TE\",\n    \"Trailer\",\n    \"Transfer-Encoding\",\n    \"Upgrade\",\n    \"Via\"\n].map((i)=>i.toLowerCase()));\nasync function fetchBrowser(url, _options) {\n    let options = {\n        ...DEFAULT_OPT,\n        ..._options\n    };\n    const headers = new Headers();\n    if (options.type === \"json\") headers.set(\"Content-Type\", \"application/json\");\n    let parsed = new URL(url);\n    if (parsed.username) {\n        const auth = btoa(`${parsed.username}:${parsed.password}`);\n        headers.set(\"Authorization\", `Basic ${auth}`);\n        parsed.username = \"\";\n        parsed.password = \"\";\n    }\n    url = \"\" + parsed;\n    for(let k in options.headers){\n        const name = k.toLowerCase();\n        if (SAFE_HEADERS.has(name) || options.cors && !FORBIDDEN_HEADERS.has(name)) headers.set(k, options.headers[k]);\n    }\n    let opts = {\n        headers,\n        redirect: options.redirect ? \"follow\" : \"manual\"\n    };\n    if (!options.referrer) opts.referrerPolicy = \"no-referrer\";\n    if (options.cors) opts.mode = \"cors\";\n    if (options.data) {\n        if (!options.method) opts.method = \"POST\";\n        opts.body = options.type === \"json\" ? JSON.stringify(options.data) : options.data;\n    }\n    const res = await fetch(url, opts);\n    if (options.expectStatusCode && res.status !== options.expectStatusCode) throw new InvalidStatusCodeError(res.status);\n    const body = detectType(new Uint8Array(await res.arrayBuffer()), options.type);\n    if (options.full) return {\n        headers: Object.fromEntries(res.headers.entries()),\n        status: res.status,\n        body\n    };\n    return body;\n}\nconst IS_NODE = !!(typeof process == \"object\" && process.versions && process.versions.node && process.versions.v8);\nfunction fetchUrl(url, options) {\n    const fn = IS_NODE ? fetchNode : fetchBrowser;\n    return fn(url, options);\n}\nexports[\"default\"] = fetchUrl;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micro-ftch/index.js\n");

/***/ })

};
;