/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/borsh";
exports.ids = ["vendor-chunks/borsh"];
exports.modules = {

/***/ "(ssr)/./node_modules/borsh/lib/index.js":
/*!*****************************************!*\
  !*** ./node_modules/borsh/lib/index.js ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, {\n        enumerable: true,\n        get: function() {\n            return m[k];\n        }\n    });\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __decorate = this && this.__decorate || function(decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __importStar = this && this.__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = this && this.__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.deserializeUnchecked = exports.deserialize = exports.serialize = exports.BinaryReader = exports.BinaryWriter = exports.BorshError = exports.baseDecode = exports.baseEncode = void 0;\nconst bn_js_1 = __importDefault(__webpack_require__(/*! bn.js */ \"(ssr)/./node_modules/bn.js/lib/bn.js\"));\nconst bs58_1 = __importDefault(__webpack_require__(/*! bs58 */ \"(ssr)/./node_modules/borsh/node_modules/bs58/index.js\"));\n// TODO: Make sure this polyfill not included when not required\nconst encoding = __importStar(__webpack_require__(/*! text-encoding-utf-8 */ \"(ssr)/./node_modules/text-encoding-utf-8/lib/encoding.lib.js\"));\nconst ResolvedTextDecoder = typeof TextDecoder !== \"function\" ? encoding.TextDecoder : TextDecoder;\nconst textDecoder = new ResolvedTextDecoder(\"utf-8\", {\n    fatal: true\n});\nfunction baseEncode(value) {\n    if (typeof value === \"string\") {\n        value = Buffer.from(value, \"utf8\");\n    }\n    return bs58_1.default.encode(Buffer.from(value));\n}\nexports.baseEncode = baseEncode;\nfunction baseDecode(value) {\n    return Buffer.from(bs58_1.default.decode(value));\n}\nexports.baseDecode = baseDecode;\nconst INITIAL_LENGTH = 1024;\nclass BorshError extends Error {\n    constructor(message){\n        super(message);\n        this.fieldPath = [];\n        this.originalMessage = message;\n    }\n    addToFieldPath(fieldName) {\n        this.fieldPath.splice(0, 0, fieldName);\n        // NOTE: Modifying message directly as jest doesn't use .toString()\n        this.message = this.originalMessage + \": \" + this.fieldPath.join(\".\");\n    }\n}\nexports.BorshError = BorshError;\n/// Binary encoder.\nclass BinaryWriter {\n    constructor(){\n        this.buf = Buffer.alloc(INITIAL_LENGTH);\n        this.length = 0;\n    }\n    maybeResize() {\n        if (this.buf.length < 16 + this.length) {\n            this.buf = Buffer.concat([\n                this.buf,\n                Buffer.alloc(INITIAL_LENGTH)\n            ]);\n        }\n    }\n    writeU8(value) {\n        this.maybeResize();\n        this.buf.writeUInt8(value, this.length);\n        this.length += 1;\n    }\n    writeU16(value) {\n        this.maybeResize();\n        this.buf.writeUInt16LE(value, this.length);\n        this.length += 2;\n    }\n    writeU32(value) {\n        this.maybeResize();\n        this.buf.writeUInt32LE(value, this.length);\n        this.length += 4;\n    }\n    writeU64(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 8)));\n    }\n    writeU128(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 16)));\n    }\n    writeU256(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 32)));\n    }\n    writeU512(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 64)));\n    }\n    writeBuffer(buffer) {\n        // Buffer.from is needed as this.buf.subarray can return plain Uint8Array in browser\n        this.buf = Buffer.concat([\n            Buffer.from(this.buf.subarray(0, this.length)),\n            buffer,\n            Buffer.alloc(INITIAL_LENGTH)\n        ]);\n        this.length += buffer.length;\n    }\n    writeString(str) {\n        this.maybeResize();\n        const b = Buffer.from(str, \"utf8\");\n        this.writeU32(b.length);\n        this.writeBuffer(b);\n    }\n    writeFixedArray(array) {\n        this.writeBuffer(Buffer.from(array));\n    }\n    writeArray(array, fn) {\n        this.maybeResize();\n        this.writeU32(array.length);\n        for (const elem of array){\n            this.maybeResize();\n            fn(elem);\n        }\n    }\n    toArray() {\n        return this.buf.subarray(0, this.length);\n    }\n}\nexports.BinaryWriter = BinaryWriter;\nfunction handlingRangeError(target, propertyKey, propertyDescriptor) {\n    const originalMethod = propertyDescriptor.value;\n    propertyDescriptor.value = function(...args) {\n        try {\n            return originalMethod.apply(this, args);\n        } catch (e) {\n            if (e instanceof RangeError) {\n                const code = e.code;\n                if ([\n                    \"ERR_BUFFER_OUT_OF_BOUNDS\",\n                    \"ERR_OUT_OF_RANGE\"\n                ].indexOf(code) >= 0) {\n                    throw new BorshError(\"Reached the end of buffer when deserializing\");\n                }\n            }\n            throw e;\n        }\n    };\n}\nclass BinaryReader {\n    constructor(buf){\n        this.buf = buf;\n        this.offset = 0;\n    }\n    readU8() {\n        const value = this.buf.readUInt8(this.offset);\n        this.offset += 1;\n        return value;\n    }\n    readU16() {\n        const value = this.buf.readUInt16LE(this.offset);\n        this.offset += 2;\n        return value;\n    }\n    readU32() {\n        const value = this.buf.readUInt32LE(this.offset);\n        this.offset += 4;\n        return value;\n    }\n    readU64() {\n        const buf = this.readBuffer(8);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readU128() {\n        const buf = this.readBuffer(16);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readU256() {\n        const buf = this.readBuffer(32);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readU512() {\n        const buf = this.readBuffer(64);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readBuffer(len) {\n        if (this.offset + len > this.buf.length) {\n            throw new BorshError(`Expected buffer length ${len} isn't within bounds`);\n        }\n        const result = this.buf.slice(this.offset, this.offset + len);\n        this.offset += len;\n        return result;\n    }\n    readString() {\n        const len = this.readU32();\n        const buf = this.readBuffer(len);\n        try {\n            // NOTE: Using TextDecoder to fail on invalid UTF-8\n            return textDecoder.decode(buf);\n        } catch (e) {\n            throw new BorshError(`Error decoding UTF-8 string: ${e}`);\n        }\n    }\n    readFixedArray(len) {\n        return new Uint8Array(this.readBuffer(len));\n    }\n    readArray(fn) {\n        const len = this.readU32();\n        const result = Array();\n        for(let i = 0; i < len; ++i){\n            result.push(fn());\n        }\n        return result;\n    }\n}\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU8\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU16\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU32\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU64\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU128\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU256\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU512\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readString\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readFixedArray\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readArray\", null);\nexports.BinaryReader = BinaryReader;\nfunction capitalizeFirstLetter(string) {\n    return string.charAt(0).toUpperCase() + string.slice(1);\n}\nfunction serializeField(schema, fieldName, value, fieldType, writer) {\n    try {\n        // TODO: Handle missing values properly (make sure they never result in just skipped write)\n        if (typeof fieldType === \"string\") {\n            writer[`write${capitalizeFirstLetter(fieldType)}`](value);\n        } else if (fieldType instanceof Array) {\n            if (typeof fieldType[0] === \"number\") {\n                if (value.length !== fieldType[0]) {\n                    throw new BorshError(`Expecting byte array of length ${fieldType[0]}, but got ${value.length} bytes`);\n                }\n                writer.writeFixedArray(value);\n            } else if (fieldType.length === 2 && typeof fieldType[1] === \"number\") {\n                if (value.length !== fieldType[1]) {\n                    throw new BorshError(`Expecting byte array of length ${fieldType[1]}, but got ${value.length} bytes`);\n                }\n                for(let i = 0; i < fieldType[1]; i++){\n                    serializeField(schema, null, value[i], fieldType[0], writer);\n                }\n            } else {\n                writer.writeArray(value, (item)=>{\n                    serializeField(schema, fieldName, item, fieldType[0], writer);\n                });\n            }\n        } else if (fieldType.kind !== undefined) {\n            switch(fieldType.kind){\n                case \"option\":\n                    {\n                        if (value === null || value === undefined) {\n                            writer.writeU8(0);\n                        } else {\n                            writer.writeU8(1);\n                            serializeField(schema, fieldName, value, fieldType.type, writer);\n                        }\n                        break;\n                    }\n                case \"map\":\n                    {\n                        writer.writeU32(value.size);\n                        value.forEach((val, key)=>{\n                            serializeField(schema, fieldName, key, fieldType.key, writer);\n                            serializeField(schema, fieldName, val, fieldType.value, writer);\n                        });\n                        break;\n                    }\n                default:\n                    throw new BorshError(`FieldType ${fieldType} unrecognized`);\n            }\n        } else {\n            serializeStruct(schema, value, writer);\n        }\n    } catch (error) {\n        if (error instanceof BorshError) {\n            error.addToFieldPath(fieldName);\n        }\n        throw error;\n    }\n}\nfunction serializeStruct(schema, obj, writer) {\n    if (typeof obj.borshSerialize === \"function\") {\n        obj.borshSerialize(writer);\n        return;\n    }\n    const structSchema = schema.get(obj.constructor);\n    if (!structSchema) {\n        throw new BorshError(`Class ${obj.constructor.name} is missing in schema`);\n    }\n    if (structSchema.kind === \"struct\") {\n        structSchema.fields.map(([fieldName, fieldType])=>{\n            serializeField(schema, fieldName, obj[fieldName], fieldType, writer);\n        });\n    } else if (structSchema.kind === \"enum\") {\n        const name = obj[structSchema.field];\n        for(let idx = 0; idx < structSchema.values.length; ++idx){\n            const [fieldName, fieldType] = structSchema.values[idx];\n            if (fieldName === name) {\n                writer.writeU8(idx);\n                serializeField(schema, fieldName, obj[fieldName], fieldType, writer);\n                break;\n            }\n        }\n    } else {\n        throw new BorshError(`Unexpected schema kind: ${structSchema.kind} for ${obj.constructor.name}`);\n    }\n}\n/// Serialize given object using schema of the form:\n/// { class_name -> [ [field_name, field_type], .. ], .. }\nfunction serialize(schema, obj, Writer = BinaryWriter) {\n    const writer = new Writer();\n    serializeStruct(schema, obj, writer);\n    return writer.toArray();\n}\nexports.serialize = serialize;\nfunction deserializeField(schema, fieldName, fieldType, reader) {\n    try {\n        if (typeof fieldType === \"string\") {\n            return reader[`read${capitalizeFirstLetter(fieldType)}`]();\n        }\n        if (fieldType instanceof Array) {\n            if (typeof fieldType[0] === \"number\") {\n                return reader.readFixedArray(fieldType[0]);\n            } else if (typeof fieldType[1] === \"number\") {\n                const arr = [];\n                for(let i = 0; i < fieldType[1]; i++){\n                    arr.push(deserializeField(schema, null, fieldType[0], reader));\n                }\n                return arr;\n            } else {\n                return reader.readArray(()=>deserializeField(schema, fieldName, fieldType[0], reader));\n            }\n        }\n        if (fieldType.kind === \"option\") {\n            const option = reader.readU8();\n            if (option) {\n                return deserializeField(schema, fieldName, fieldType.type, reader);\n            }\n            return undefined;\n        }\n        if (fieldType.kind === \"map\") {\n            let map = new Map();\n            const length = reader.readU32();\n            for(let i = 0; i < length; i++){\n                const key = deserializeField(schema, fieldName, fieldType.key, reader);\n                const val = deserializeField(schema, fieldName, fieldType.value, reader);\n                map.set(key, val);\n            }\n            return map;\n        }\n        return deserializeStruct(schema, fieldType, reader);\n    } catch (error) {\n        if (error instanceof BorshError) {\n            error.addToFieldPath(fieldName);\n        }\n        throw error;\n    }\n}\nfunction deserializeStruct(schema, classType, reader) {\n    if (typeof classType.borshDeserialize === \"function\") {\n        return classType.borshDeserialize(reader);\n    }\n    const structSchema = schema.get(classType);\n    if (!structSchema) {\n        throw new BorshError(`Class ${classType.name} is missing in schema`);\n    }\n    if (structSchema.kind === \"struct\") {\n        const result = {};\n        for (const [fieldName, fieldType] of schema.get(classType).fields){\n            result[fieldName] = deserializeField(schema, fieldName, fieldType, reader);\n        }\n        return new classType(result);\n    }\n    if (structSchema.kind === \"enum\") {\n        const idx = reader.readU8();\n        if (idx >= structSchema.values.length) {\n            throw new BorshError(`Enum index: ${idx} is out of range`);\n        }\n        const [fieldName, fieldType] = structSchema.values[idx];\n        const fieldValue = deserializeField(schema, fieldName, fieldType, reader);\n        return new classType({\n            [fieldName]: fieldValue\n        });\n    }\n    throw new BorshError(`Unexpected schema kind: ${structSchema.kind} for ${classType.constructor.name}`);\n}\n/// Deserializes object from bytes using schema.\nfunction deserialize(schema, classType, buffer, Reader = BinaryReader) {\n    const reader = new Reader(buffer);\n    const result = deserializeStruct(schema, classType, reader);\n    if (reader.offset < buffer.length) {\n        throw new BorshError(`Unexpected ${buffer.length - reader.offset} bytes after deserialized data`);\n    }\n    return result;\n}\nexports.deserialize = deserialize;\n/// Deserializes object from bytes using schema, without checking the length read\nfunction deserializeUnchecked(schema, classType, buffer, Reader = BinaryReader) {\n    const reader = new Reader(buffer);\n    return deserializeStruct(schema, classType, reader);\n}\nexports.deserializeUnchecked = deserializeUnchecked;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/borsh/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/borsh/node_modules/base-x/src/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/borsh/node_modules/base-x/src/index.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n// base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\n// @ts-ignore\nvar _Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer);\nfunction base(ALPHABET) {\n    if (ALPHABET.length >= 255) {\n        throw new TypeError(\"Alphabet too long\");\n    }\n    var BASE_MAP = new Uint8Array(256);\n    for(var j = 0; j < BASE_MAP.length; j++){\n        BASE_MAP[j] = 255;\n    }\n    for(var i = 0; i < ALPHABET.length; i++){\n        var x = ALPHABET.charAt(i);\n        var xc = x.charCodeAt(0);\n        if (BASE_MAP[xc] !== 255) {\n            throw new TypeError(x + \" is ambiguous\");\n        }\n        BASE_MAP[xc] = i;\n    }\n    var BASE = ALPHABET.length;\n    var LEADER = ALPHABET.charAt(0);\n    var FACTOR = Math.log(BASE) / Math.log(256) // log(BASE) / log(256), rounded up\n    ;\n    var iFACTOR = Math.log(256) / Math.log(BASE) // log(256) / log(BASE), rounded up\n    ;\n    function encode(source) {\n        if (Array.isArray(source) || source instanceof Uint8Array) {\n            source = _Buffer.from(source);\n        }\n        if (!_Buffer.isBuffer(source)) {\n            throw new TypeError(\"Expected Buffer\");\n        }\n        if (source.length === 0) {\n            return \"\";\n        }\n        // Skip & count leading zeroes.\n        var zeroes = 0;\n        var length = 0;\n        var pbegin = 0;\n        var pend = source.length;\n        while(pbegin !== pend && source[pbegin] === 0){\n            pbegin++;\n            zeroes++;\n        }\n        // Allocate enough space in big-endian base58 representation.\n        var size = (pend - pbegin) * iFACTOR + 1 >>> 0;\n        var b58 = new Uint8Array(size);\n        // Process the bytes.\n        while(pbegin !== pend){\n            var carry = source[pbegin];\n            // Apply \"b58 = b58 * 256 + ch\".\n            var i = 0;\n            for(var it1 = size - 1; (carry !== 0 || i < length) && it1 !== -1; it1--, i++){\n                carry += 256 * b58[it1] >>> 0;\n                b58[it1] = carry % BASE >>> 0;\n                carry = carry / BASE >>> 0;\n            }\n            if (carry !== 0) {\n                throw new Error(\"Non-zero carry\");\n            }\n            length = i;\n            pbegin++;\n        }\n        // Skip leading zeroes in base58 result.\n        var it2 = size - length;\n        while(it2 !== size && b58[it2] === 0){\n            it2++;\n        }\n        // Translate the result into a string.\n        var str = LEADER.repeat(zeroes);\n        for(; it2 < size; ++it2){\n            str += ALPHABET.charAt(b58[it2]);\n        }\n        return str;\n    }\n    function decodeUnsafe(source) {\n        if (typeof source !== \"string\") {\n            throw new TypeError(\"Expected String\");\n        }\n        if (source.length === 0) {\n            return _Buffer.alloc(0);\n        }\n        var psz = 0;\n        // Skip and count leading '1's.\n        var zeroes = 0;\n        var length = 0;\n        while(source[psz] === LEADER){\n            zeroes++;\n            psz++;\n        }\n        // Allocate enough space in big-endian base256 representation.\n        var size = (source.length - psz) * FACTOR + 1 >>> 0 // log(58) / log(256), rounded up.\n        ;\n        var b256 = new Uint8Array(size);\n        // Process the characters.\n        while(psz < source.length){\n            // Find code of next character\n            var charCode = source.charCodeAt(psz);\n            // Base map can not be indexed using char code\n            if (charCode > 255) {\n                return;\n            }\n            // Decode character\n            var carry = BASE_MAP[charCode];\n            // Invalid character\n            if (carry === 255) {\n                return;\n            }\n            var i = 0;\n            for(var it3 = size - 1; (carry !== 0 || i < length) && it3 !== -1; it3--, i++){\n                carry += BASE * b256[it3] >>> 0;\n                b256[it3] = carry % 256 >>> 0;\n                carry = carry / 256 >>> 0;\n            }\n            if (carry !== 0) {\n                throw new Error(\"Non-zero carry\");\n            }\n            length = i;\n            psz++;\n        }\n        // Skip leading zeroes in b256.\n        var it4 = size - length;\n        while(it4 !== size && b256[it4] === 0){\n            it4++;\n        }\n        var vch = _Buffer.allocUnsafe(zeroes + (size - it4));\n        vch.fill(0x00, 0, zeroes);\n        var j = zeroes;\n        while(it4 !== size){\n            vch[j++] = b256[it4++];\n        }\n        return vch;\n    }\n    function decode(string) {\n        var buffer = decodeUnsafe(string);\n        if (buffer) {\n            return buffer;\n        }\n        throw new Error(\"Non-base\" + BASE + \" character\");\n    }\n    return {\n        encode: encode,\n        decodeUnsafe: decodeUnsafe,\n        decode: decode\n    };\n}\nmodule.exports = base;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/borsh/node_modules/base-x/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/borsh/node_modules/bs58/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/borsh/node_modules/bs58/index.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var basex = __webpack_require__(/*! base-x */ \"(ssr)/./node_modules/borsh/node_modules/base-x/src/index.js\");\nvar ALPHABET = \"123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz\";\nmodule.exports = basex(ALPHABET);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYm9yc2gvbm9kZV9tb2R1bGVzL2JzNTgvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsSUFBSUEsUUFBUUMsbUJBQU9BLENBQUM7QUFDcEIsSUFBSUMsV0FBVztBQUVmQyxPQUFPQyxPQUFPLEdBQUdKLE1BQU1FIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL2JvcnNoL25vZGVfbW9kdWxlcy9iczU4L2luZGV4LmpzP2MyOGIiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGJhc2V4ID0gcmVxdWlyZSgnYmFzZS14JylcbnZhciBBTFBIQUJFVCA9ICcxMjM0NTY3ODlBQkNERUZHSEpLTE1OUFFSU1RVVldYWVphYmNkZWZnaGlqa21ub3BxcnN0dXZ3eHl6J1xuXG5tb2R1bGUuZXhwb3J0cyA9IGJhc2V4KEFMUEhBQkVUKVxuIl0sIm5hbWVzIjpbImJhc2V4IiwicmVxdWlyZSIsIkFMUEhBQkVUIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/borsh/node_modules/bs58/index.js\n");

/***/ })

};
;