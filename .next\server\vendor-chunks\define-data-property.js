"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/define-data-property";
exports.ids = ["vendor-chunks/define-data-property"];
exports.modules = {

/***/ "(ssr)/./node_modules/define-data-property/index.js":
/*!****************************************************!*\
  !*** ./node_modules/define-data-property/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar $defineProperty = __webpack_require__(/*! es-define-property */ \"(ssr)/./node_modules/es-define-property/index.js\");\nvar $SyntaxError = __webpack_require__(/*! es-errors/syntax */ \"(ssr)/./node_modules/es-errors/syntax.js\");\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(ssr)/./node_modules/es-errors/type.js\");\nvar gopd = __webpack_require__(/*! gopd */ \"(ssr)/./node_modules/gopd/index.js\");\n/** @type {import('.')} */ module.exports = function defineDataProperty(obj, property, value) {\n    if (!obj || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        throw new $TypeError(\"`obj` must be an object or a function`\");\n    }\n    if (typeof property !== \"string\" && typeof property !== \"symbol\") {\n        throw new $TypeError(\"`property` must be a string or a symbol`\");\n    }\n    if (arguments.length > 3 && typeof arguments[3] !== \"boolean\" && arguments[3] !== null) {\n        throw new $TypeError(\"`nonEnumerable`, if provided, must be a boolean or null\");\n    }\n    if (arguments.length > 4 && typeof arguments[4] !== \"boolean\" && arguments[4] !== null) {\n        throw new $TypeError(\"`nonWritable`, if provided, must be a boolean or null\");\n    }\n    if (arguments.length > 5 && typeof arguments[5] !== \"boolean\" && arguments[5] !== null) {\n        throw new $TypeError(\"`nonConfigurable`, if provided, must be a boolean or null\");\n    }\n    if (arguments.length > 6 && typeof arguments[6] !== \"boolean\") {\n        throw new $TypeError(\"`loose`, if provided, must be a boolean\");\n    }\n    var nonEnumerable = arguments.length > 3 ? arguments[3] : null;\n    var nonWritable = arguments.length > 4 ? arguments[4] : null;\n    var nonConfigurable = arguments.length > 5 ? arguments[5] : null;\n    var loose = arguments.length > 6 ? arguments[6] : false;\n    /* @type {false | TypedPropertyDescriptor<unknown>} */ var desc = !!gopd && gopd(obj, property);\n    if ($defineProperty) {\n        $defineProperty(obj, property, {\n            configurable: nonConfigurable === null && desc ? desc.configurable : !nonConfigurable,\n            enumerable: nonEnumerable === null && desc ? desc.enumerable : !nonEnumerable,\n            value: value,\n            writable: nonWritable === null && desc ? desc.writable : !nonWritable\n        });\n    } else if (loose || !nonEnumerable && !nonWritable && !nonConfigurable) {\n        // must fall back to [[Set]], and was not explicitly asked to make non-enumerable, non-writable, or non-configurable\n        obj[property] = value; // eslint-disable-line no-param-reassign\n    } else {\n        throw new $SyntaxError(\"This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.\");\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/define-data-property/index.js\n");

/***/ })

};
;