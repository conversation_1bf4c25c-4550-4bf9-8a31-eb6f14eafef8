{"name": "ai-web3-creator-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@web3auth/modal": "^7.3.1", "@web3auth/base": "^7.3.1", "@web3auth/solana-provider": "^7.3.1", "@solana/web3.js": "^1.87.6", "@metaplex-foundation/js": "^0.20.1", "@metaplex-foundation/mpl-token-metadata": "^3.2.1", "openai": "^4.20.1", "axios": "^1.6.2", "zustand": "^4.4.7", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-progress": "^1.0.3", "react-hot-toast": "^2.4.1", "bs58": "^5.0.0", "@radix-ui/react-slot": "^1.0.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0"}}