import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Web3AuthProvider } from '@/providers/Web3AuthProvider'
import { Toaster } from 'react-hot-toast'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'AI + Web3 创作平台',
  description: '基于AI和Web3技术的创作者平台，支持文字、图片、音乐创作并铸造为NFT',
  keywords: ['AI', 'Web3', 'NFT', 'Solana', '创作平台'],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <Web3AuthProvider>
          {children}
          <Toaster position="top-right" />
        </Web3AuthProvider>
      </body>
    </html>
  )
}
