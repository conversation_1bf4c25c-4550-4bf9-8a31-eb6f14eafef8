"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jsonschema";
exports.ids = ["vendor-chunks/jsonschema"];
exports.modules = {

/***/ "(ssr)/./node_modules/jsonschema/lib/attribute.js":
/*!**************************************************!*\
  !*** ./node_modules/jsonschema/lib/attribute.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar helpers = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/jsonschema/lib/helpers.js\");\n/** @type ValidatorResult */ var ValidatorResult = helpers.ValidatorResult;\n/** @type SchemaError */ var SchemaError = helpers.SchemaError;\nvar attribute = {};\nattribute.ignoreProperties = {\n    // informative properties\n    \"id\": true,\n    \"default\": true,\n    \"description\": true,\n    \"title\": true,\n    // arguments to other properties\n    \"additionalItems\": true,\n    \"then\": true,\n    \"else\": true,\n    // special-handled properties\n    \"$schema\": true,\n    \"$ref\": true,\n    \"extends\": true\n};\n/**\n * @name validators\n */ var validators = attribute.validators = {};\n/**\n * Validates whether the instance if of a certain type\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @return {ValidatorResult|null}\n */ validators.type = function validateType(instance, schema, options, ctx) {\n    // Ignore undefined instances\n    if (instance === undefined) {\n        return null;\n    }\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var types = Array.isArray(schema.type) ? schema.type : [\n        schema.type\n    ];\n    if (!types.some(this.testType.bind(this, instance, schema, options, ctx))) {\n        var list = types.map(function(v) {\n            if (!v) return;\n            var id = v.$id || v.id;\n            return id ? \"<\" + id + \">\" : v + \"\";\n        });\n        result.addError({\n            name: \"type\",\n            argument: list,\n            message: \"is not of a type(s) \" + list\n        });\n    }\n    return result;\n};\nfunction testSchemaNoThrow(instance, options, ctx, callback, schema) {\n    var throwError = options.throwError;\n    var throwAll = options.throwAll;\n    options.throwError = false;\n    options.throwAll = false;\n    var res = this.validateSchema(instance, schema, options, ctx);\n    options.throwError = throwError;\n    options.throwAll = throwAll;\n    if (!res.valid && callback instanceof Function) {\n        callback(res);\n    }\n    return res.valid;\n}\n/**\n * Validates whether the instance matches some of the given schemas\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @return {ValidatorResult|null}\n */ validators.anyOf = function validateAnyOf(instance, schema, options, ctx) {\n    // Ignore undefined instances\n    if (instance === undefined) {\n        return null;\n    }\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var inner = new ValidatorResult(instance, schema, options, ctx);\n    if (!Array.isArray(schema.anyOf)) {\n        throw new SchemaError(\"anyOf must be an array\");\n    }\n    if (!schema.anyOf.some(testSchemaNoThrow.bind(this, instance, options, ctx, function(res) {\n        inner.importErrors(res);\n    }))) {\n        var list = schema.anyOf.map(function(v, i) {\n            var id = v.$id || v.id;\n            if (id) return \"<\" + id + \">\";\n            return v.title && JSON.stringify(v.title) || v[\"$ref\"] && \"<\" + v[\"$ref\"] + \">\" || \"[subschema \" + i + \"]\";\n        });\n        if (options.nestedErrors) {\n            result.importErrors(inner);\n        }\n        result.addError({\n            name: \"anyOf\",\n            argument: list,\n            message: \"is not any of \" + list.join(\",\")\n        });\n    }\n    return result;\n};\n/**\n * Validates whether the instance matches every given schema\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @return {String|null}\n */ validators.allOf = function validateAllOf(instance, schema, options, ctx) {\n    // Ignore undefined instances\n    if (instance === undefined) {\n        return null;\n    }\n    if (!Array.isArray(schema.allOf)) {\n        throw new SchemaError(\"allOf must be an array\");\n    }\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var self = this;\n    schema.allOf.forEach(function(v, i) {\n        var valid = self.validateSchema(instance, v, options, ctx);\n        if (!valid.valid) {\n            var id = v.$id || v.id;\n            var msg = id || v.title && JSON.stringify(v.title) || v[\"$ref\"] && \"<\" + v[\"$ref\"] + \">\" || \"[subschema \" + i + \"]\";\n            result.addError({\n                name: \"allOf\",\n                argument: {\n                    id: msg,\n                    length: valid.errors.length,\n                    valid: valid\n                },\n                message: \"does not match allOf schema \" + msg + \" with \" + valid.errors.length + \" error[s]:\"\n            });\n            result.importErrors(valid);\n        }\n    });\n    return result;\n};\n/**\n * Validates whether the instance matches exactly one of the given schemas\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @return {String|null}\n */ validators.oneOf = function validateOneOf(instance, schema, options, ctx) {\n    // Ignore undefined instances\n    if (instance === undefined) {\n        return null;\n    }\n    if (!Array.isArray(schema.oneOf)) {\n        throw new SchemaError(\"oneOf must be an array\");\n    }\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var inner = new ValidatorResult(instance, schema, options, ctx);\n    var count = schema.oneOf.filter(testSchemaNoThrow.bind(this, instance, options, ctx, function(res) {\n        inner.importErrors(res);\n    })).length;\n    var list = schema.oneOf.map(function(v, i) {\n        var id = v.$id || v.id;\n        return id || v.title && JSON.stringify(v.title) || v[\"$ref\"] && \"<\" + v[\"$ref\"] + \">\" || \"[subschema \" + i + \"]\";\n    });\n    if (count !== 1) {\n        if (options.nestedErrors) {\n            result.importErrors(inner);\n        }\n        result.addError({\n            name: \"oneOf\",\n            argument: list,\n            message: \"is not exactly one from \" + list.join(\",\")\n        });\n    }\n    return result;\n};\n/**\n * Validates \"then\" or \"else\" depending on the result of validating \"if\"\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @return {String|null}\n */ validators.if = function validateIf(instance, schema, options, ctx) {\n    // Ignore undefined instances\n    if (instance === undefined) return null;\n    if (!helpers.isSchema(schema.if)) throw new Error('Expected \"if\" keyword to be a schema');\n    var ifValid = testSchemaNoThrow.call(this, instance, options, ctx, null, schema.if);\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var res;\n    if (ifValid) {\n        if (schema.then === undefined) return;\n        if (!helpers.isSchema(schema.then)) throw new Error('Expected \"then\" keyword to be a schema');\n        res = this.validateSchema(instance, schema.then, options, ctx.makeChild(schema.then));\n        result.importErrors(res);\n    } else {\n        if (schema.else === undefined) return;\n        if (!helpers.isSchema(schema.else)) throw new Error('Expected \"else\" keyword to be a schema');\n        res = this.validateSchema(instance, schema.else, options, ctx.makeChild(schema.else));\n        result.importErrors(res);\n    }\n    return result;\n};\nfunction getEnumerableProperty(object, key) {\n    // Determine if `key` shows up in `for(var key in object)`\n    // First test Object.hasOwnProperty.call as an optimization: that guarantees it does\n    if (Object.hasOwnProperty.call(object, key)) return object[key];\n    // Test `key in object` as an optimization; false means it won't\n    if (!(key in object)) return;\n    while(object = Object.getPrototypeOf(object)){\n        if (Object.propertyIsEnumerable.call(object, key)) return object[key];\n    }\n}\n/**\n * Validates propertyNames\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @return {String|null|ValidatorResult}\n */ validators.propertyNames = function validatePropertyNames(instance, schema, options, ctx) {\n    if (!this.types.object(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var subschema = schema.propertyNames !== undefined ? schema.propertyNames : {};\n    if (!helpers.isSchema(subschema)) throw new SchemaError('Expected \"propertyNames\" to be a schema (object or boolean)');\n    for(var property in instance){\n        if (getEnumerableProperty(instance, property) !== undefined) {\n            var res = this.validateSchema(property, subschema, options, ctx.makeChild(subschema));\n            result.importErrors(res);\n        }\n    }\n    return result;\n};\n/**\n * Validates properties\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @return {String|null|ValidatorResult}\n */ validators.properties = function validateProperties(instance, schema, options, ctx) {\n    if (!this.types.object(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var properties = schema.properties || {};\n    for(var property in properties){\n        var subschema = properties[property];\n        if (subschema === undefined) {\n            continue;\n        } else if (subschema === null) {\n            throw new SchemaError('Unexpected null, expected schema in \"properties\"');\n        }\n        if (typeof options.preValidateProperty == \"function\") {\n            options.preValidateProperty(instance, property, subschema, options, ctx);\n        }\n        var prop = getEnumerableProperty(instance, property);\n        var res = this.validateSchema(prop, subschema, options, ctx.makeChild(subschema, property));\n        if (res.instance !== result.instance[property]) result.instance[property] = res.instance;\n        result.importErrors(res);\n    }\n    return result;\n};\n/**\n * Test a specific property within in instance against the additionalProperties schema attribute\n * This ignores properties with definitions in the properties schema attribute, but no other attributes.\n * If too many more types of property-existence tests pop up they may need their own class of tests (like `type` has)\n * @private\n * @return {boolean}\n */ function testAdditionalProperty(instance, schema, options, ctx, property, result) {\n    if (!this.types.object(instance)) return;\n    if (schema.properties && schema.properties[property] !== undefined) {\n        return;\n    }\n    if (schema.additionalProperties === false) {\n        result.addError({\n            name: \"additionalProperties\",\n            argument: property,\n            message: \"is not allowed to have the additional property \" + JSON.stringify(property)\n        });\n    } else {\n        var additionalProperties = schema.additionalProperties || {};\n        if (typeof options.preValidateProperty == \"function\") {\n            options.preValidateProperty(instance, property, additionalProperties, options, ctx);\n        }\n        var res = this.validateSchema(instance[property], additionalProperties, options, ctx.makeChild(additionalProperties, property));\n        if (res.instance !== result.instance[property]) result.instance[property] = res.instance;\n        result.importErrors(res);\n    }\n}\n/**\n * Validates patternProperties\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @return {String|null|ValidatorResult}\n */ validators.patternProperties = function validatePatternProperties(instance, schema, options, ctx) {\n    if (!this.types.object(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var patternProperties = schema.patternProperties || {};\n    for(var property in instance){\n        var test = true;\n        for(var pattern in patternProperties){\n            var subschema = patternProperties[pattern];\n            if (subschema === undefined) {\n                continue;\n            } else if (subschema === null) {\n                throw new SchemaError('Unexpected null, expected schema in \"patternProperties\"');\n            }\n            try {\n                var regexp = new RegExp(pattern, \"u\");\n            } catch (_e) {\n                // In the event the stricter handling causes an error, fall back on the forgiving handling\n                // DEPRECATED\n                regexp = new RegExp(pattern);\n            }\n            if (!regexp.test(property)) {\n                continue;\n            }\n            test = false;\n            if (typeof options.preValidateProperty == \"function\") {\n                options.preValidateProperty(instance, property, subschema, options, ctx);\n            }\n            var res = this.validateSchema(instance[property], subschema, options, ctx.makeChild(subschema, property));\n            if (res.instance !== result.instance[property]) result.instance[property] = res.instance;\n            result.importErrors(res);\n        }\n        if (test) {\n            testAdditionalProperty.call(this, instance, schema, options, ctx, property, result);\n        }\n    }\n    return result;\n};\n/**\n * Validates additionalProperties\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @return {String|null|ValidatorResult}\n */ validators.additionalProperties = function validateAdditionalProperties(instance, schema, options, ctx) {\n    if (!this.types.object(instance)) return;\n    // if patternProperties is defined then we'll test when that one is called instead\n    if (schema.patternProperties) {\n        return null;\n    }\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    for(var property in instance){\n        testAdditionalProperty.call(this, instance, schema, options, ctx, property, result);\n    }\n    return result;\n};\n/**\n * Validates whether the instance value is at least of a certain length, when the instance value is a string.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.minProperties = function validateMinProperties(instance, schema, options, ctx) {\n    if (!this.types.object(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var keys = Object.keys(instance);\n    if (!(keys.length >= schema.minProperties)) {\n        result.addError({\n            name: \"minProperties\",\n            argument: schema.minProperties,\n            message: \"does not meet minimum property length of \" + schema.minProperties\n        });\n    }\n    return result;\n};\n/**\n * Validates whether the instance value is at most of a certain length, when the instance value is a string.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.maxProperties = function validateMaxProperties(instance, schema, options, ctx) {\n    if (!this.types.object(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var keys = Object.keys(instance);\n    if (!(keys.length <= schema.maxProperties)) {\n        result.addError({\n            name: \"maxProperties\",\n            argument: schema.maxProperties,\n            message: \"does not meet maximum property length of \" + schema.maxProperties\n        });\n    }\n    return result;\n};\n/**\n * Validates items when instance is an array\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @return {String|null|ValidatorResult}\n */ validators.items = function validateItems(instance, schema, options, ctx) {\n    var self = this;\n    if (!this.types.array(instance)) return;\n    if (schema.items === undefined) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    instance.every(function(value, i) {\n        if (Array.isArray(schema.items)) {\n            var items = schema.items[i] === undefined ? schema.additionalItems : schema.items[i];\n        } else {\n            var items = schema.items;\n        }\n        if (items === undefined) {\n            return true;\n        }\n        if (items === false) {\n            result.addError({\n                name: \"items\",\n                message: \"additionalItems not permitted\"\n            });\n            return false;\n        }\n        var res = self.validateSchema(value, items, options, ctx.makeChild(items, i));\n        if (res.instance !== result.instance[i]) result.instance[i] = res.instance;\n        result.importErrors(res);\n        return true;\n    });\n    return result;\n};\n/**\n * Validates the \"contains\" keyword\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @return {String|null|ValidatorResult}\n */ validators.contains = function validateContains(instance, schema, options, ctx) {\n    var self = this;\n    if (!this.types.array(instance)) return;\n    if (schema.contains === undefined) return;\n    if (!helpers.isSchema(schema.contains)) throw new Error('Expected \"contains\" keyword to be a schema');\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var count = instance.some(function(value, i) {\n        var res = self.validateSchema(value, schema.contains, options, ctx.makeChild(schema.contains, i));\n        return res.errors.length === 0;\n    });\n    if (count === false) {\n        result.addError({\n            name: \"contains\",\n            argument: schema.contains,\n            message: \"must contain an item matching given schema\"\n        });\n    }\n    return result;\n};\n/**\n * Validates minimum and exclusiveMinimum when the type of the instance value is a number.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.minimum = function validateMinimum(instance, schema, options, ctx) {\n    if (!this.types.number(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    if (schema.exclusiveMinimum && schema.exclusiveMinimum === true) {\n        if (!(instance > schema.minimum)) {\n            result.addError({\n                name: \"minimum\",\n                argument: schema.minimum,\n                message: \"must be greater than \" + schema.minimum\n            });\n        }\n    } else {\n        if (!(instance >= schema.minimum)) {\n            result.addError({\n                name: \"minimum\",\n                argument: schema.minimum,\n                message: \"must be greater than or equal to \" + schema.minimum\n            });\n        }\n    }\n    return result;\n};\n/**\n * Validates maximum and exclusiveMaximum when the type of the instance value is a number.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.maximum = function validateMaximum(instance, schema, options, ctx) {\n    if (!this.types.number(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    if (schema.exclusiveMaximum && schema.exclusiveMaximum === true) {\n        if (!(instance < schema.maximum)) {\n            result.addError({\n                name: \"maximum\",\n                argument: schema.maximum,\n                message: \"must be less than \" + schema.maximum\n            });\n        }\n    } else {\n        if (!(instance <= schema.maximum)) {\n            result.addError({\n                name: \"maximum\",\n                argument: schema.maximum,\n                message: \"must be less than or equal to \" + schema.maximum\n            });\n        }\n    }\n    return result;\n};\n/**\n * Validates the number form of exclusiveMinimum when the type of the instance value is a number.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.exclusiveMinimum = function validateExclusiveMinimum(instance, schema, options, ctx) {\n    // Support the boolean form of exclusiveMinimum, which is handled by the \"minimum\" keyword.\n    if (typeof schema.exclusiveMinimum === \"boolean\") return;\n    if (!this.types.number(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var valid = instance > schema.exclusiveMinimum;\n    if (!valid) {\n        result.addError({\n            name: \"exclusiveMinimum\",\n            argument: schema.exclusiveMinimum,\n            message: \"must be strictly greater than \" + schema.exclusiveMinimum\n        });\n    }\n    return result;\n};\n/**\n * Validates the number form of exclusiveMaximum when the type of the instance value is a number.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.exclusiveMaximum = function validateExclusiveMaximum(instance, schema, options, ctx) {\n    // Support the boolean form of exclusiveMaximum, which is handled by the \"maximum\" keyword.\n    if (typeof schema.exclusiveMaximum === \"boolean\") return;\n    if (!this.types.number(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var valid = instance < schema.exclusiveMaximum;\n    if (!valid) {\n        result.addError({\n            name: \"exclusiveMaximum\",\n            argument: schema.exclusiveMaximum,\n            message: \"must be strictly less than \" + schema.exclusiveMaximum\n        });\n    }\n    return result;\n};\n/**\n * Perform validation for multipleOf and divisibleBy, which are essentially the same.\n * @param instance\n * @param schema\n * @param validationType\n * @param errorMessage\n * @returns {String|null}\n */ var validateMultipleOfOrDivisbleBy = function validateMultipleOfOrDivisbleBy(instance, schema, options, ctx, validationType, errorMessage) {\n    if (!this.types.number(instance)) return;\n    var validationArgument = schema[validationType];\n    if (validationArgument == 0) {\n        throw new SchemaError(validationType + \" cannot be zero\");\n    }\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var instanceDecimals = helpers.getDecimalPlaces(instance);\n    var divisorDecimals = helpers.getDecimalPlaces(validationArgument);\n    var maxDecimals = Math.max(instanceDecimals, divisorDecimals);\n    var multiplier = Math.pow(10, maxDecimals);\n    if (Math.round(instance * multiplier) % Math.round(validationArgument * multiplier) !== 0) {\n        result.addError({\n            name: validationType,\n            argument: validationArgument,\n            message: errorMessage + JSON.stringify(validationArgument)\n        });\n    }\n    return result;\n};\n/**\n * Validates divisibleBy when the type of the instance value is a number.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.multipleOf = function validateMultipleOf(instance, schema, options, ctx) {\n    return validateMultipleOfOrDivisbleBy.call(this, instance, schema, options, ctx, \"multipleOf\", \"is not a multiple of (divisible by) \");\n};\n/**\n * Validates multipleOf when the type of the instance value is a number.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.divisibleBy = function validateDivisibleBy(instance, schema, options, ctx) {\n    return validateMultipleOfOrDivisbleBy.call(this, instance, schema, options, ctx, \"divisibleBy\", \"is not divisible by (multiple of) \");\n};\n/**\n * Validates whether the instance value is present.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.required = function validateRequired(instance, schema, options, ctx) {\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    if (instance === undefined && schema.required === true) {\n        // A boolean form is implemented for reverse-compatibility with schemas written against older drafts\n        result.addError({\n            name: \"required\",\n            message: \"is required\"\n        });\n    } else if (this.types.object(instance) && Array.isArray(schema.required)) {\n        schema.required.forEach(function(n) {\n            if (getEnumerableProperty(instance, n) === undefined) {\n                result.addError({\n                    name: \"required\",\n                    argument: n,\n                    message: \"requires property \" + JSON.stringify(n)\n                });\n            }\n        });\n    }\n    return result;\n};\n/**\n * Validates whether the instance value matches the regular expression, when the instance value is a string.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.pattern = function validatePattern(instance, schema, options, ctx) {\n    if (!this.types.string(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var pattern = schema.pattern;\n    try {\n        var regexp = new RegExp(pattern, \"u\");\n    } catch (_e) {\n        // In the event the stricter handling causes an error, fall back on the forgiving handling\n        // DEPRECATED\n        regexp = new RegExp(pattern);\n    }\n    if (!instance.match(regexp)) {\n        result.addError({\n            name: \"pattern\",\n            argument: schema.pattern,\n            message: \"does not match pattern \" + JSON.stringify(schema.pattern.toString())\n        });\n    }\n    return result;\n};\n/**\n * Validates whether the instance value is of a certain defined format or a custom\n * format.\n * The following formats are supported for string types:\n *   - date-time\n *   - date\n *   - time\n *   - ip-address\n *   - ipv6\n *   - uri\n *   - color\n *   - host-name\n *   - alpha\n *   - alpha-numeric\n *   - utc-millisec\n * @param instance\n * @param schema\n * @param [options]\n * @param [ctx]\n * @return {String|null}\n */ validators.format = function validateFormat(instance, schema, options, ctx) {\n    if (instance === undefined) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    if (!result.disableFormat && !helpers.isFormat(instance, schema.format, this)) {\n        result.addError({\n            name: \"format\",\n            argument: schema.format,\n            message: \"does not conform to the \" + JSON.stringify(schema.format) + \" format\"\n        });\n    }\n    return result;\n};\n/**\n * Validates whether the instance value is at least of a certain length, when the instance value is a string.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.minLength = function validateMinLength(instance, schema, options, ctx) {\n    if (!this.types.string(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var hsp = instance.match(/[\\uDC00-\\uDFFF]/g);\n    var length = instance.length - (hsp ? hsp.length : 0);\n    if (!(length >= schema.minLength)) {\n        result.addError({\n            name: \"minLength\",\n            argument: schema.minLength,\n            message: \"does not meet minimum length of \" + schema.minLength\n        });\n    }\n    return result;\n};\n/**\n * Validates whether the instance value is at most of a certain length, when the instance value is a string.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.maxLength = function validateMaxLength(instance, schema, options, ctx) {\n    if (!this.types.string(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    // TODO if this was already computed in \"minLength\", use that value instead of re-computing\n    var hsp = instance.match(/[\\uDC00-\\uDFFF]/g);\n    var length = instance.length - (hsp ? hsp.length : 0);\n    if (!(length <= schema.maxLength)) {\n        result.addError({\n            name: \"maxLength\",\n            argument: schema.maxLength,\n            message: \"does not meet maximum length of \" + schema.maxLength\n        });\n    }\n    return result;\n};\n/**\n * Validates whether instance contains at least a minimum number of items, when the instance is an Array.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.minItems = function validateMinItems(instance, schema, options, ctx) {\n    if (!this.types.array(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    if (!(instance.length >= schema.minItems)) {\n        result.addError({\n            name: \"minItems\",\n            argument: schema.minItems,\n            message: \"does not meet minimum length of \" + schema.minItems\n        });\n    }\n    return result;\n};\n/**\n * Validates whether instance contains no more than a maximum number of items, when the instance is an Array.\n * @param instance\n * @param schema\n * @return {String|null}\n */ validators.maxItems = function validateMaxItems(instance, schema, options, ctx) {\n    if (!this.types.array(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    if (!(instance.length <= schema.maxItems)) {\n        result.addError({\n            name: \"maxItems\",\n            argument: schema.maxItems,\n            message: \"does not meet maximum length of \" + schema.maxItems\n        });\n    }\n    return result;\n};\n/**\n * Deep compares arrays for duplicates\n * @param v\n * @param i\n * @param a\n * @private\n * @return {boolean}\n */ function testArrays(v, i, a) {\n    var j, len = a.length;\n    for(j = i + 1, len; j < len; j++){\n        if (helpers.deepCompareStrict(v, a[j])) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Validates whether there are no duplicates, when the instance is an Array.\n * @param instance\n * @return {String|null}\n */ validators.uniqueItems = function validateUniqueItems(instance, schema, options, ctx) {\n    if (schema.uniqueItems !== true) return;\n    if (!this.types.array(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    if (!instance.every(testArrays)) {\n        result.addError({\n            name: \"uniqueItems\",\n            message: \"contains duplicate item\"\n        });\n    }\n    return result;\n};\n/**\n * Validate for the presence of dependency properties, if the instance is an object.\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @return {null|ValidatorResult}\n */ validators.dependencies = function validateDependencies(instance, schema, options, ctx) {\n    if (!this.types.object(instance)) return;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    for(var property in schema.dependencies){\n        if (instance[property] === undefined) {\n            continue;\n        }\n        var dep = schema.dependencies[property];\n        var childContext = ctx.makeChild(dep, property);\n        if (typeof dep == \"string\") {\n            dep = [\n                dep\n            ];\n        }\n        if (Array.isArray(dep)) {\n            dep.forEach(function(prop) {\n                if (instance[prop] === undefined) {\n                    result.addError({\n                        // FIXME there's two different \"dependencies\" errors here with slightly different outputs\n                        // Can we make these the same? Or should we create different error types?\n                        name: \"dependencies\",\n                        argument: childContext.propertyPath,\n                        message: \"property \" + prop + \" not found, required by \" + childContext.propertyPath\n                    });\n                }\n            });\n        } else {\n            var res = this.validateSchema(instance, dep, options, childContext);\n            if (result.instance !== res.instance) result.instance = res.instance;\n            if (res && res.errors.length) {\n                result.addError({\n                    name: \"dependencies\",\n                    argument: childContext.propertyPath,\n                    message: \"does not meet dependency required by \" + childContext.propertyPath\n                });\n                result.importErrors(res);\n            }\n        }\n    }\n    return result;\n};\n/**\n * Validates whether the instance value is one of the enumerated values.\n *\n * @param instance\n * @param schema\n * @return {ValidatorResult|null}\n */ validators[\"enum\"] = function validateEnum(instance, schema, options, ctx) {\n    if (instance === undefined) {\n        return null;\n    }\n    if (!Array.isArray(schema[\"enum\"])) {\n        throw new SchemaError(\"enum expects an array\", schema);\n    }\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    if (!schema[\"enum\"].some(helpers.deepCompareStrict.bind(null, instance))) {\n        result.addError({\n            name: \"enum\",\n            argument: schema[\"enum\"],\n            message: \"is not one of enum values: \" + schema[\"enum\"].map(String).join(\",\")\n        });\n    }\n    return result;\n};\n/**\n * Validates whether the instance exactly matches a given value\n *\n * @param instance\n * @param schema\n * @return {ValidatorResult|null}\n */ validators[\"const\"] = function validateEnum(instance, schema, options, ctx) {\n    if (instance === undefined) {\n        return null;\n    }\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    if (!helpers.deepCompareStrict(schema[\"const\"], instance)) {\n        result.addError({\n            name: \"const\",\n            argument: schema[\"const\"],\n            message: \"does not exactly match expected constant: \" + schema[\"const\"]\n        });\n    }\n    return result;\n};\n/**\n * Validates whether the instance if of a prohibited type.\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @return {null|ValidatorResult}\n */ validators.not = validators.disallow = function validateNot(instance, schema, options, ctx) {\n    var self = this;\n    if (instance === undefined) return null;\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    var notTypes = schema.not || schema.disallow;\n    if (!notTypes) return null;\n    if (!Array.isArray(notTypes)) notTypes = [\n        notTypes\n    ];\n    notTypes.forEach(function(type) {\n        if (self.testType(instance, schema, options, ctx, type)) {\n            var id = type && (type.$id || type.id);\n            var schemaId = id || type;\n            result.addError({\n                name: \"not\",\n                argument: schemaId,\n                message: \"is of prohibited type \" + schemaId\n            });\n        }\n    });\n    return result;\n};\nmodule.exports = attribute;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonschema/lib/attribute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonschema/lib/helpers.js":
/*!************************************************!*\
  !*** ./node_modules/jsonschema/lib/helpers.js ***!
  \************************************************/
/***/ ((module, exports) => {

eval("\nvar ValidationError = exports.ValidationError = function ValidationError(message, instance, schema, path, name, argument) {\n    if (Array.isArray(path)) {\n        this.path = path;\n        this.property = path.reduce(function(sum, item) {\n            return sum + makeSuffix(item);\n        }, \"instance\");\n    } else if (path !== undefined) {\n        this.property = path;\n    }\n    if (message) {\n        this.message = message;\n    }\n    if (schema) {\n        var id = schema.$id || schema.id;\n        this.schema = id || schema;\n    }\n    if (instance !== undefined) {\n        this.instance = instance;\n    }\n    this.name = name;\n    this.argument = argument;\n    this.stack = this.toString();\n};\nValidationError.prototype.toString = function toString() {\n    return this.property + \" \" + this.message;\n};\nvar ValidatorResult = exports.ValidatorResult = function ValidatorResult(instance, schema, options, ctx) {\n    this.instance = instance;\n    this.schema = schema;\n    this.options = options;\n    this.path = ctx.path;\n    this.propertyPath = ctx.propertyPath;\n    this.errors = [];\n    this.throwError = options && options.throwError;\n    this.throwFirst = options && options.throwFirst;\n    this.throwAll = options && options.throwAll;\n    this.disableFormat = options && options.disableFormat === true;\n};\nValidatorResult.prototype.addError = function addError(detail) {\n    var err;\n    if (typeof detail == \"string\") {\n        err = new ValidationError(detail, this.instance, this.schema, this.path);\n    } else {\n        if (!detail) throw new Error(\"Missing error detail\");\n        if (!detail.message) throw new Error(\"Missing error message\");\n        if (!detail.name) throw new Error(\"Missing validator type\");\n        err = new ValidationError(detail.message, this.instance, this.schema, this.path, detail.name, detail.argument);\n    }\n    this.errors.push(err);\n    if (this.throwFirst) {\n        throw new ValidatorResultError(this);\n    } else if (this.throwError) {\n        throw err;\n    }\n    return err;\n};\nValidatorResult.prototype.importErrors = function importErrors(res) {\n    if (typeof res == \"string\" || res && res.validatorType) {\n        this.addError(res);\n    } else if (res && res.errors) {\n        this.errors = this.errors.concat(res.errors);\n    }\n};\nfunction stringizer(v, i) {\n    return i + \": \" + v.toString() + \"\\n\";\n}\nValidatorResult.prototype.toString = function toString(res) {\n    return this.errors.map(stringizer).join(\"\");\n};\nObject.defineProperty(ValidatorResult.prototype, \"valid\", {\n    get: function() {\n        return !this.errors.length;\n    }\n});\nmodule.exports.ValidatorResultError = ValidatorResultError;\nfunction ValidatorResultError(result) {\n    if (typeof Error.captureStackTrace === \"function\") {\n        Error.captureStackTrace(this, ValidatorResultError);\n    }\n    this.instance = result.instance;\n    this.schema = result.schema;\n    this.options = result.options;\n    this.errors = result.errors;\n}\nValidatorResultError.prototype = new Error();\nValidatorResultError.prototype.constructor = ValidatorResultError;\nValidatorResultError.prototype.name = \"Validation Error\";\n/**\n * Describes a problem with a Schema which prevents validation of an instance\n * @name SchemaError\n * @constructor\n */ var SchemaError = exports.SchemaError = function SchemaError(msg, schema) {\n    this.message = msg;\n    this.schema = schema;\n    Error.call(this, msg);\n    if (typeof Error.captureStackTrace === \"function\") {\n        Error.captureStackTrace(this, SchemaError);\n    }\n};\nSchemaError.prototype = Object.create(Error.prototype, {\n    constructor: {\n        value: SchemaError,\n        enumerable: false\n    },\n    name: {\n        value: \"SchemaError\",\n        enumerable: false\n    }\n});\nvar SchemaContext = exports.SchemaContext = function SchemaContext(schema, options, path, base, schemas) {\n    this.schema = schema;\n    this.options = options;\n    if (Array.isArray(path)) {\n        this.path = path;\n        this.propertyPath = path.reduce(function(sum, item) {\n            return sum + makeSuffix(item);\n        }, \"instance\");\n    } else {\n        this.propertyPath = path;\n    }\n    this.base = base;\n    this.schemas = schemas;\n};\nSchemaContext.prototype.resolve = function resolve(target) {\n    return (()=>resolveUrl(this.base, target))();\n};\nSchemaContext.prototype.makeChild = function makeChild(schema, propertyName) {\n    var path = propertyName === undefined ? this.path : this.path.concat([\n        propertyName\n    ]);\n    var id = schema.$id || schema.id;\n    let base = (()=>resolveUrl(this.base, id || \"\"))();\n    var ctx = new SchemaContext(schema, this.options, path, base, Object.create(this.schemas));\n    if (id && !ctx.schemas[base]) {\n        ctx.schemas[base] = schema;\n    }\n    return ctx;\n};\nvar FORMAT_REGEXPS = exports.FORMAT_REGEXPS = {\n    // 7.3.1. Dates, Times, and Duration\n    \"date-time\": /^\\d{4}-(?:0[0-9]{1}|1[0-2]{1})-(3[01]|0[1-9]|[12][0-9])[tT ](2[0-4]|[01][0-9]):([0-5][0-9]):(60|[0-5][0-9])(\\.\\d+)?([zZ]|[+-]([0-5][0-9]):(60|[0-5][0-9]))$/,\n    \"date\": /^\\d{4}-(?:0[0-9]{1}|1[0-2]{1})-(3[01]|0[1-9]|[12][0-9])$/,\n    \"time\": /^(2[0-4]|[01][0-9]):([0-5][0-9]):(60|[0-5][0-9])$/,\n    \"duration\": /P(T\\d+(H(\\d+M(\\d+S)?)?|M(\\d+S)?|S)|\\d+(D|M(\\d+D)?|Y(\\d+M(\\d+D)?)?)(T\\d+(H(\\d+M(\\d+S)?)?|M(\\d+S)?|S))?|\\d+W)/i,\n    // 7.3.2. Email Addresses\n    // TODO: fix the email production\n    \"email\": /^(?:[\\w\\!\\#\\$\\%\\&\\'\\*\\+\\-\\/\\=\\?\\^\\`\\{\\|\\}\\~]+\\.)*[\\w\\!\\#\\$\\%\\&\\'\\*\\+\\-\\/\\=\\?\\^\\`\\{\\|\\}\\~]+@(?:(?:(?:[a-zA-Z0-9](?:[a-zA-Z0-9\\-](?!\\.)){0,61}[a-zA-Z0-9]?\\.)+[a-zA-Z0-9](?:[a-zA-Z0-9\\-](?!$)){0,61}[a-zA-Z0-9]?)|(?:\\[(?:(?:[01]?\\d{1,2}|2[0-4]\\d|25[0-5])\\.){3}(?:[01]?\\d{1,2}|2[0-4]\\d|25[0-5])\\]))$/,\n    \"idn-email\": /^(\"(?:[!#-\\[\\]-\\u{10FFFF}]|\\\\[\\t -\\u{10FFFF}])*\"|[!#-'*+\\-/-9=?A-Z\\^-\\u{10FFFF}](?:\\.?[!#-'*+\\-/-9=?A-Z\\^-\\u{10FFFF}])*)@([!#-'*+\\-/-9=?A-Z\\^-\\u{10FFFF}](?:\\.?[!#-'*+\\-/-9=?A-Z\\^-\\u{10FFFF}])*|\\[[!-Z\\^-\\u{10FFFF}]*\\])$/u,\n    // 7.3.3. Hostnames\n    // 7.3.4. IP Addresses\n    \"ip-address\": /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,\n    // FIXME whitespace is invalid\n    \"ipv6\": /^\\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))(%.+)?\\s*$/,\n    // 7.3.5. Resource Identifiers\n    // TODO: A more accurate regular expression for \"uri\" goes:\n    // [A-Za-z][+\\-.0-9A-Za-z]*:((/(/((%[0-9A-Fa-f]{2}|[!$&-.0-9;=A-Z_a-z~])+|(\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~]+)?|[.0-:A-Fa-f]+)\\])?)(:\\d*)?)?)?#(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~])*|(/(/((%[0-9A-Fa-f]{2}|[!$&-.0-9;=A-Z_a-z~])+|(\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~]+)?|[.0-:A-Fa-f]+)\\])?)(:\\d*)?[/?]|[!$&-.0-;=?-Z_a-z~])|/?%[0-9A-Fa-f]{2}|[!$&-.0-;=?-Z_a-z~])(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~])*(#(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~])*)?|/(/((%[0-9A-Fa-f]{2}|[!$&-.0-9;=A-Z_a-z~])+(:\\d*)?|(\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~]+)?|[.0-:A-Fa-f]+)\\])?:\\d*|\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~]+)?|[.0-:A-Fa-f]+)\\])?)?)?\n    \"uri\": /^[a-zA-Z][a-zA-Z0-9+.-]*:[^\\s]*$/,\n    \"uri-reference\": /^(((([A-Za-z][+\\-.0-9A-Za-z]*(:%[0-9A-Fa-f]{2}|:[!$&-.0-;=?-Z_a-z~]|[/?])|\\?)(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~])*|([A-Za-z][+\\-.0-9A-Za-z]*:?)?)|([A-Za-z][+\\-.0-9A-Za-z]*:)?\\/((%[0-9A-Fa-f]{2}|\\/((%[0-9A-Fa-f]{2}|[!$&-.0-9;=A-Z_a-z~])+|(\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~]+)?|[.0-:A-Fa-f]+)\\])?)(:\\d*)?[/?]|[!$&-.0-;=?-Z_a-z~])(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~])*|(\\/((%[0-9A-Fa-f]{2}|[!$&-.0-9;=A-Z_a-z~])+|(\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~]+)?|[.0-:A-Fa-f]+)\\])?)(:\\d*)?)?))#(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~])*|(([A-Za-z][+\\-.0-9A-Za-z]*)?%[0-9A-Fa-f]{2}|[!$&-.0-9;=@_~]|[A-Za-z][+\\-.0-9A-Za-z]*[!$&-*,;=@_~])(%[0-9A-Fa-f]{2}|[!$&-.0-9;=@-Z_a-z~])*((([/?](%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~])*)?#|[/?])(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~])*)?|([A-Za-z][+\\-.0-9A-Za-z]*(:%[0-9A-Fa-f]{2}|:[!$&-.0-;=?-Z_a-z~]|[/?])|\\?)(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~])*|([A-Za-z][+\\-.0-9A-Za-z]*:)?\\/((%[0-9A-Fa-f]{2}|\\/((%[0-9A-Fa-f]{2}|[!$&-.0-9;=A-Z_a-z~])+|(\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~]+)?|[.0-:A-Fa-f]+)\\])?)(:\\d*)?[/?]|[!$&-.0-;=?-Z_a-z~])(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~])*|\\/((%[0-9A-Fa-f]{2}|[!$&-.0-9;=A-Z_a-z~])+(:\\d*)?|(\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~]+)?|[.0-:A-Fa-f]+)\\])?:\\d*|\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~]+)?|[.0-:A-Fa-f]+)\\])?)?|[A-Za-z][+\\-.0-9A-Za-z]*:?)?$/,\n    \"iri\": /^[a-zA-Z][a-zA-Z0-9+.-]*:[^\\s]*$/,\n    \"iri-reference\": /^(((([A-Za-z][+\\-.0-9A-Za-z]*(:%[0-9A-Fa-f]{2}|:[!$&-.0-;=?-Z_a-z~-\\u{10FFFF}]|[/?])|\\?)(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~-\\u{10FFFF}])*|([A-Za-z][+\\-.0-9A-Za-z]*:?)?)|([A-Za-z][+\\-.0-9A-Za-z]*:)?\\/((%[0-9A-Fa-f]{2}|\\/((%[0-9A-Fa-f]{2}|[!$&-.0-9;=A-Z_a-z~-\\u{10FFFF}])+|(\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~-\\u{10FFFF}]+)?|[.0-:A-Fa-f]+)\\])?)(:\\d*)?[/?]|[!$&-.0-;=?-Z_a-z~-\\u{10FFFF}])(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~-\\u{10FFFF}])*|(\\/((%[0-9A-Fa-f]{2}|[!$&-.0-9;=A-Z_a-z~-\\u{10FFFF}])+|(\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~-\\u{10FFFF}]+)?|[.0-:A-Fa-f]+)\\])?)(:\\d*)?)?))#(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~-\\u{10FFFF}])*|(([A-Za-z][+\\-.0-9A-Za-z]*)?%[0-9A-Fa-f]{2}|[!$&-.0-9;=@_~-\\u{10FFFF}]|[A-Za-z][+\\-.0-9A-Za-z]*[!$&-*,;=@_~-\\u{10FFFF}])(%[0-9A-Fa-f]{2}|[!$&-.0-9;=@-Z_a-z~-\\u{10FFFF}])*((([/?](%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~-\\u{10FFFF}])*)?#|[/?])(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~-\\u{10FFFF}])*)?|([A-Za-z][+\\-.0-9A-Za-z]*(:%[0-9A-Fa-f]{2}|:[!$&-.0-;=?-Z_a-z~-\\u{10FFFF}]|[/?])|\\?)(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~-\\u{10FFFF}])*|([A-Za-z][+\\-.0-9A-Za-z]*:)?\\/((%[0-9A-Fa-f]{2}|\\/((%[0-9A-Fa-f]{2}|[!$&-.0-9;=A-Z_a-z~-\\u{10FFFF}])+|(\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~-\\u{10FFFF}]+)?|[.0-:A-Fa-f]+)\\])?)(:\\d*)?[/?]|[!$&-.0-;=?-Z_a-z~-\\u{10FFFF}])(%[0-9A-Fa-f]{2}|[!$&-;=?-Z_a-z~-\\u{10FFFF}])*|\\/((%[0-9A-Fa-f]{2}|[!$&-.0-9;=A-Z_a-z~-\\u{10FFFF}])+(:\\d*)?|(\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~-\\u{10FFFF}]+)?|[.0-:A-Fa-f]+)\\])?:\\d*|\\[(([Vv][0-9A-Fa-f]+\\.[!$&-.0-;=A-Z_a-z~-\\u{10FFFF}]+)?|[.0-:A-Fa-f]+)\\])?)?|[A-Za-z][+\\-.0-9A-Za-z]*:?)?$/u,\n    \"uuid\": /^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i,\n    // 7.3.6. uri-template\n    \"uri-template\": /(%[0-9a-f]{2}|[!#$&(-;=?@\\[\\]_a-z~]|\\{[!#&+,./;=?@|]?(%[0-9a-f]{2}|[0-9_a-z])(\\.?(%[0-9a-f]{2}|[0-9_a-z]))*(:[1-9]\\d{0,3}|\\*)?(,(%[0-9a-f]{2}|[0-9_a-z])(\\.?(%[0-9a-f]{2}|[0-9_a-z]))*(:[1-9]\\d{0,3}|\\*)?)*\\})*/iu,\n    // 7.3.7. JSON Pointers\n    \"json-pointer\": /^(\\/([\\x00-\\x2e0-@\\[-}\\x7f]|~[01])*)*$/iu,\n    \"relative-json-pointer\": /^\\d+(#|(\\/([\\x00-\\x2e0-@\\[-}\\x7f]|~[01])*)*)$/iu,\n    // hostname regex from: http://stackoverflow.com/a/1420225/5628\n    \"hostname\": /^(?=.{1,255}$)[0-9A-Za-z](?:(?:[0-9A-Za-z]|-){0,61}[0-9A-Za-z])?(?:\\.[0-9A-Za-z](?:(?:[0-9A-Za-z]|-){0,61}[0-9A-Za-z])?)*\\.?$/,\n    \"host-name\": /^(?=.{1,255}$)[0-9A-Za-z](?:(?:[0-9A-Za-z]|-){0,61}[0-9A-Za-z])?(?:\\.[0-9A-Za-z](?:(?:[0-9A-Za-z]|-){0,61}[0-9A-Za-z])?)*\\.?$/,\n    \"utc-millisec\": function(input) {\n        return typeof input === \"string\" && parseFloat(input) === parseInt(input, 10) && !isNaN(input);\n    },\n    // 7.3.8. regex\n    \"regex\": function(input) {\n        var result = true;\n        try {\n            new RegExp(input);\n        } catch (e) {\n            result = false;\n        }\n        return result;\n    },\n    // Other definitions\n    // \"style\" was removed from JSON Schema in draft-4 and is deprecated\n    \"style\": /[\\r\\n\\t ]*[^\\r\\n\\t ][^:]*:[\\r\\n\\t ]*[^\\r\\n\\t ;]*[\\r\\n\\t ]*;?/,\n    // \"color\" was removed from JSON Schema in draft-4 and is deprecated\n    \"color\": /^(#?([0-9A-Fa-f]{3}){1,2}\\b|aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow|(rgb\\(\\s*\\b([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\b\\s*,\\s*\\b([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\b\\s*,\\s*\\b([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\b\\s*\\))|(rgb\\(\\s*(\\d?\\d%|100%)+\\s*,\\s*(\\d?\\d%|100%)+\\s*,\\s*(\\d?\\d%|100%)+\\s*\\)))$/,\n    \"phone\": /^\\+(?:[0-9] ?){6,14}[0-9]$/,\n    \"alpha\": /^[a-zA-Z]+$/,\n    \"alphanumeric\": /^[a-zA-Z0-9]+$/\n};\nFORMAT_REGEXPS.regexp = FORMAT_REGEXPS.regex;\nFORMAT_REGEXPS.pattern = FORMAT_REGEXPS.regex;\nFORMAT_REGEXPS.ipv4 = FORMAT_REGEXPS[\"ip-address\"];\nexports.isFormat = function isFormat(input, format, validator) {\n    if (typeof input === \"string\" && FORMAT_REGEXPS[format] !== undefined) {\n        if (FORMAT_REGEXPS[format] instanceof RegExp) {\n            return FORMAT_REGEXPS[format].test(input);\n        }\n        if (typeof FORMAT_REGEXPS[format] === \"function\") {\n            return FORMAT_REGEXPS[format](input);\n        }\n    } else if (validator && validator.customFormats && typeof validator.customFormats[format] === \"function\") {\n        return validator.customFormats[format](input);\n    }\n    return true;\n};\nvar makeSuffix = exports.makeSuffix = function makeSuffix(key) {\n    key = key.toString();\n    // This function could be capable of outputting valid a ECMAScript string, but the\n    // resulting code for testing which form to use would be tens of thousands of characters long\n    // That means this will use the name form for some illegal forms\n    if (!key.match(/[.\\s\\[\\]]/) && !key.match(/^[\\d]/)) {\n        return \".\" + key;\n    }\n    if (key.match(/^\\d+$/)) {\n        return \"[\" + key + \"]\";\n    }\n    return \"[\" + JSON.stringify(key) + \"]\";\n};\nexports.deepCompareStrict = function deepCompareStrict(a, b) {\n    if (typeof a !== typeof b) {\n        return false;\n    }\n    if (Array.isArray(a)) {\n        if (!Array.isArray(b)) {\n            return false;\n        }\n        if (a.length !== b.length) {\n            return false;\n        }\n        return a.every(function(v, i) {\n            return deepCompareStrict(a[i], b[i]);\n        });\n    }\n    if (typeof a === \"object\") {\n        if (!a || !b) {\n            return a === b;\n        }\n        var aKeys = Object.keys(a);\n        var bKeys = Object.keys(b);\n        if (aKeys.length !== bKeys.length) {\n            return false;\n        }\n        return aKeys.every(function(v) {\n            return deepCompareStrict(a[v], b[v]);\n        });\n    }\n    return a === b;\n};\nfunction deepMerger(target, dst, e, i) {\n    if (typeof e === \"object\") {\n        dst[i] = deepMerge(target[i], e);\n    } else {\n        if (target.indexOf(e) === -1) {\n            dst.push(e);\n        }\n    }\n}\nfunction copyist(src, dst, key) {\n    dst[key] = src[key];\n}\nfunction copyistWithDeepMerge(target, src, dst, key) {\n    if (typeof src[key] !== \"object\" || !src[key]) {\n        dst[key] = src[key];\n    } else {\n        if (!target[key]) {\n            dst[key] = src[key];\n        } else {\n            dst[key] = deepMerge(target[key], src[key]);\n        }\n    }\n}\nfunction deepMerge(target, src) {\n    var array = Array.isArray(src);\n    var dst = array && [] || {};\n    if (array) {\n        target = target || [];\n        dst = dst.concat(target);\n        src.forEach(deepMerger.bind(null, target, dst));\n    } else {\n        if (target && typeof target === \"object\") {\n            Object.keys(target).forEach(copyist.bind(null, target, dst));\n        }\n        Object.keys(src).forEach(copyistWithDeepMerge.bind(null, target, src, dst));\n    }\n    return dst;\n}\nmodule.exports.deepMerge = deepMerge;\n/**\n * Validates instance against the provided schema\n * Implements URI+JSON Pointer encoding, e.g. \"%7e\"=\"~0\"=>\"~\", \"~1\"=\"%2f\"=>\"/\"\n * @param o\n * @param s The path to walk o along\n * @return any\n */ exports.objectGetPath = function objectGetPath(o, s) {\n    var parts = s.split(\"/\").slice(1);\n    var k;\n    while(typeof (k = parts.shift()) == \"string\"){\n        var n = decodeURIComponent(k.replace(/~0/, \"~\").replace(/~1/g, \"/\"));\n        if (!(n in o)) return;\n        o = o[n];\n    }\n    return o;\n};\nfunction pathEncoder(v) {\n    return \"/\" + encodeURIComponent(v).replace(/~/g, \"%7E\");\n}\n/**\n * Accept an Array of property names and return a JSON Pointer URI fragment\n * @param Array a\n * @return {String}\n */ exports.encodePath = function encodePointer(a) {\n    // ~ must be encoded explicitly because hacks\n    // the slash is encoded by encodeURIComponent\n    return a.map(pathEncoder).join(\"\");\n};\n/**\n * Calculate the number of decimal places a number uses\n * We need this to get correct results out of multipleOf and divisibleBy\n * when either figure is has decimal places, due to IEEE-754 float issues.\n * @param number\n * @returns {number}\n */ exports.getDecimalPlaces = function getDecimalPlaces(number) {\n    var decimalPlaces = 0;\n    if (isNaN(number)) return decimalPlaces;\n    if (typeof number !== \"number\") {\n        number = Number(number);\n    }\n    var parts = number.toString().split(\"e\");\n    if (parts.length === 2) {\n        if (parts[1][0] !== \"-\") {\n            return decimalPlaces;\n        } else {\n            decimalPlaces = Number(parts[1].slice(1));\n        }\n    }\n    var decimalParts = parts[0].split(\".\");\n    if (decimalParts.length === 2) {\n        decimalPlaces += decimalParts[1].length;\n    }\n    return decimalPlaces;\n};\nexports.isSchema = function isSchema(val) {\n    return typeof val === \"object\" && val || typeof val === \"boolean\";\n};\n/**\n * Resolve target URL from a base and relative URL.\n * Similar to Node's URL Lib's legacy resolve function.\n * Code from example in deprecation note in said library.\n * @param string\n * @param string\n * @returns {string}\n */ var resolveUrl = exports.resolveUrl = function resolveUrl(from, to) {\n    const resolvedUrl = new URL(to, new URL(from, \"resolve://\"));\n    if (resolvedUrl.protocol === \"resolve:\") {\n        const { pathname, search, hash } = resolvedUrl;\n        return pathname + search + hash;\n    }\n    return resolvedUrl.toString();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonschema/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonschema/lib/index.js":
/*!**********************************************!*\
  !*** ./node_modules/jsonschema/lib/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar Validator = module.exports.Validator = __webpack_require__(/*! ./validator */ \"(ssr)/./node_modules/jsonschema/lib/validator.js\");\nmodule.exports.ValidatorResult = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/jsonschema/lib/helpers.js\").ValidatorResult;\nmodule.exports.ValidatorResultError = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/jsonschema/lib/helpers.js\").ValidatorResultError;\nmodule.exports.ValidationError = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/jsonschema/lib/helpers.js\").ValidationError;\nmodule.exports.SchemaError = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/jsonschema/lib/helpers.js\").SchemaError;\nmodule.exports.SchemaScanResult = __webpack_require__(/*! ./scan */ \"(ssr)/./node_modules/jsonschema/lib/scan.js\").SchemaScanResult;\nmodule.exports.scan = __webpack_require__(/*! ./scan */ \"(ssr)/./node_modules/jsonschema/lib/scan.js\").scan;\nmodule.exports.validate = function(instance, schema, options) {\n    var v = new Validator();\n    return v.validate(instance, schema, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbnNjaGVtYS9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQSxJQUFJQSxZQUFZQyxxSEFBbUM7QUFFbkRBLHVJQUFxRTtBQUNyRUEsaUpBQStFO0FBQy9FQSx1SUFBcUU7QUFDckVBLCtIQUE2RDtBQUM3REEsbUlBQW9FO0FBQ3BFQSwyR0FBNEM7QUFFNUNBLHVCQUF1QixHQUFHLFNBQVVVLFFBQVEsRUFBRUMsTUFBTSxFQUFFQyxPQUFPO0lBQzNELElBQUlDLElBQUksSUFBSWQ7SUFDWixPQUFPYyxFQUFFSixRQUFRLENBQUNDLFVBQVVDLFFBQVFDO0FBQ3RDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL2pzb25zY2hlbWEvbGliL2luZGV4LmpzPzJhYjUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgVmFsaWRhdG9yID0gbW9kdWxlLmV4cG9ydHMuVmFsaWRhdG9yID0gcmVxdWlyZSgnLi92YWxpZGF0b3InKTtcblxubW9kdWxlLmV4cG9ydHMuVmFsaWRhdG9yUmVzdWx0ID0gcmVxdWlyZSgnLi9oZWxwZXJzJykuVmFsaWRhdG9yUmVzdWx0O1xubW9kdWxlLmV4cG9ydHMuVmFsaWRhdG9yUmVzdWx0RXJyb3IgPSByZXF1aXJlKCcuL2hlbHBlcnMnKS5WYWxpZGF0b3JSZXN1bHRFcnJvcjtcbm1vZHVsZS5leHBvcnRzLlZhbGlkYXRpb25FcnJvciA9IHJlcXVpcmUoJy4vaGVscGVycycpLlZhbGlkYXRpb25FcnJvcjtcbm1vZHVsZS5leHBvcnRzLlNjaGVtYUVycm9yID0gcmVxdWlyZSgnLi9oZWxwZXJzJykuU2NoZW1hRXJyb3I7XG5tb2R1bGUuZXhwb3J0cy5TY2hlbWFTY2FuUmVzdWx0ID0gcmVxdWlyZSgnLi9zY2FuJykuU2NoZW1hU2NhblJlc3VsdDtcbm1vZHVsZS5leHBvcnRzLnNjYW4gPSByZXF1aXJlKCcuL3NjYW4nKS5zY2FuO1xuXG5tb2R1bGUuZXhwb3J0cy52YWxpZGF0ZSA9IGZ1bmN0aW9uIChpbnN0YW5jZSwgc2NoZW1hLCBvcHRpb25zKSB7XG4gIHZhciB2ID0gbmV3IFZhbGlkYXRvcigpO1xuICByZXR1cm4gdi52YWxpZGF0ZShpbnN0YW5jZSwgc2NoZW1hLCBvcHRpb25zKTtcbn07XG4iXSwibmFtZXMiOlsiVmFsaWRhdG9yIiwibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiLCJWYWxpZGF0b3JSZXN1bHQiLCJWYWxpZGF0b3JSZXN1bHRFcnJvciIsIlZhbGlkYXRpb25FcnJvciIsIlNjaGVtYUVycm9yIiwiU2NoZW1hU2NhblJlc3VsdCIsInNjYW4iLCJ2YWxpZGF0ZSIsImluc3RhbmNlIiwic2NoZW1hIiwib3B0aW9ucyIsInYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonschema/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonschema/lib/scan.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonschema/lib/scan.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar helpers = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/jsonschema/lib/helpers.js\");\nmodule.exports.SchemaScanResult = SchemaScanResult;\nfunction SchemaScanResult(found, ref) {\n    this.id = found;\n    this.ref = ref;\n}\n/**\n * Adds a schema with a certain urn to the Validator instance.\n * @param string uri\n * @param object schema\n * @return {Object}\n */ module.exports.scan = function scan(base, schema) {\n    function scanSchema(baseuri, schema) {\n        if (!schema || typeof schema != \"object\") return;\n        // Mark all referenced schemas so we can tell later which schemas are referred to, but never defined\n        if (schema.$ref) {\n            let resolvedUri = helpers.resolveUrl(baseuri, schema.$ref);\n            ref[resolvedUri] = ref[resolvedUri] ? ref[resolvedUri] + 1 : 0;\n            return;\n        }\n        var id = schema.$id || schema.id;\n        let resolvedBase = helpers.resolveUrl(baseuri, id);\n        var ourBase = id ? resolvedBase : baseuri;\n        if (ourBase) {\n            // If there's no fragment, append an empty one\n            if (ourBase.indexOf(\"#\") < 0) ourBase += \"#\";\n            if (found[ourBase]) {\n                if (!helpers.deepCompareStrict(found[ourBase], schema)) {\n                    throw new Error(\"Schema <\" + ourBase + \"> already exists with different definition\");\n                }\n                return found[ourBase];\n            }\n            found[ourBase] = schema;\n            // strip trailing fragment\n            if (ourBase[ourBase.length - 1] == \"#\") {\n                found[ourBase.substring(0, ourBase.length - 1)] = schema;\n            }\n        }\n        scanArray(ourBase + \"/items\", Array.isArray(schema.items) ? schema.items : [\n            schema.items\n        ]);\n        scanArray(ourBase + \"/extends\", Array.isArray(schema.extends) ? schema.extends : [\n            schema.extends\n        ]);\n        scanSchema(ourBase + \"/additionalItems\", schema.additionalItems);\n        scanObject(ourBase + \"/properties\", schema.properties);\n        scanSchema(ourBase + \"/additionalProperties\", schema.additionalProperties);\n        scanObject(ourBase + \"/definitions\", schema.definitions);\n        scanObject(ourBase + \"/patternProperties\", schema.patternProperties);\n        scanObject(ourBase + \"/dependencies\", schema.dependencies);\n        scanArray(ourBase + \"/disallow\", schema.disallow);\n        scanArray(ourBase + \"/allOf\", schema.allOf);\n        scanArray(ourBase + \"/anyOf\", schema.anyOf);\n        scanArray(ourBase + \"/oneOf\", schema.oneOf);\n        scanSchema(ourBase + \"/not\", schema.not);\n    }\n    function scanArray(baseuri, schemas) {\n        if (!Array.isArray(schemas)) return;\n        for(var i = 0; i < schemas.length; i++){\n            scanSchema(baseuri + \"/\" + i, schemas[i]);\n        }\n    }\n    function scanObject(baseuri, schemas) {\n        if (!schemas || typeof schemas != \"object\") return;\n        for(var p in schemas){\n            scanSchema(baseuri + \"/\" + p, schemas[p]);\n        }\n    }\n    var found = {};\n    var ref = {};\n    scanSchema(base, schema);\n    return new SchemaScanResult(found, ref);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonschema/lib/scan.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonschema/lib/validator.js":
/*!**************************************************!*\
  !*** ./node_modules/jsonschema/lib/validator.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar attribute = __webpack_require__(/*! ./attribute */ \"(ssr)/./node_modules/jsonschema/lib/attribute.js\");\nvar helpers = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/jsonschema/lib/helpers.js\");\nvar scanSchema = (__webpack_require__(/*! ./scan */ \"(ssr)/./node_modules/jsonschema/lib/scan.js\").scan);\nvar ValidatorResult = helpers.ValidatorResult;\nvar ValidatorResultError = helpers.ValidatorResultError;\nvar SchemaError = helpers.SchemaError;\nvar SchemaContext = helpers.SchemaContext;\n//var anonymousBase = 'vnd.jsonschema:///';\nvar anonymousBase = \"/\";\n/**\n * Creates a new Validator object\n * @name Validator\n * @constructor\n */ var Validator = function Validator() {\n    // Allow a validator instance to override global custom formats or to have their\n    // own custom formats.\n    this.customFormats = Object.create(Validator.prototype.customFormats);\n    this.schemas = {};\n    this.unresolvedRefs = [];\n    // Use Object.create to make this extensible without Validator instances stepping on each other's toes.\n    this.types = Object.create(types);\n    this.attributes = Object.create(attribute.validators);\n};\n// Allow formats to be registered globally.\nValidator.prototype.customFormats = {};\n// Hint at the presence of a property\nValidator.prototype.schemas = null;\nValidator.prototype.types = null;\nValidator.prototype.attributes = null;\nValidator.prototype.unresolvedRefs = null;\n/**\n * Adds a schema with a certain urn to the Validator instance.\n * @param schema\n * @param urn\n * @return {Object}\n */ Validator.prototype.addSchema = function addSchema(schema, base) {\n    var self = this;\n    if (!schema) {\n        return null;\n    }\n    var scan = scanSchema(base || anonymousBase, schema);\n    var ourUri = base || schema.$id || schema.id;\n    for(var uri in scan.id){\n        this.schemas[uri] = scan.id[uri];\n    }\n    for(var uri in scan.ref){\n        // If this schema is already defined, it will be filtered out by the next step\n        this.unresolvedRefs.push(uri);\n    }\n    // Remove newly defined schemas from unresolvedRefs\n    this.unresolvedRefs = this.unresolvedRefs.filter(function(uri) {\n        return typeof self.schemas[uri] === \"undefined\";\n    });\n    return this.schemas[ourUri];\n};\nValidator.prototype.addSubSchemaArray = function addSubSchemaArray(baseuri, schemas) {\n    if (!Array.isArray(schemas)) return;\n    for(var i = 0; i < schemas.length; i++){\n        this.addSubSchema(baseuri, schemas[i]);\n    }\n};\nValidator.prototype.addSubSchemaObject = function addSubSchemaArray(baseuri, schemas) {\n    if (!schemas || typeof schemas != \"object\") return;\n    for(var p in schemas){\n        this.addSubSchema(baseuri, schemas[p]);\n    }\n};\n/**\n * Sets all the schemas of the Validator instance.\n * @param schemas\n */ Validator.prototype.setSchemas = function setSchemas(schemas) {\n    this.schemas = schemas;\n};\n/**\n * Returns the schema of a certain urn\n * @param urn\n */ Validator.prototype.getSchema = function getSchema(urn) {\n    return this.schemas[urn];\n};\n/**\n * Validates instance against the provided schema\n * @param instance\n * @param schema\n * @param [options]\n * @param [ctx]\n * @return {Array}\n */ Validator.prototype.validate = function validate(instance, schema, options, ctx) {\n    if (typeof schema !== \"boolean\" && typeof schema !== \"object\" || schema === null) {\n        throw new SchemaError(\"Expected `schema` to be an object or boolean\");\n    }\n    if (!options) {\n        options = {};\n    }\n    // This section indexes subschemas in the provided schema, so they don't need to be added with Validator#addSchema\n    // This will work so long as the function at uri.resolve() will resolve a relative URI to a relative URI\n    var id = schema.$id || schema.id;\n    let base = helpers.resolveUrl(options.base, id || \"\");\n    if (!ctx) {\n        ctx = new SchemaContext(schema, options, [], base, Object.create(this.schemas));\n        if (!ctx.schemas[base]) {\n            ctx.schemas[base] = schema;\n        }\n        var found = scanSchema(base, schema);\n        for(var n in found.id){\n            var sch = found.id[n];\n            ctx.schemas[n] = sch;\n        }\n    }\n    if (options.required && instance === undefined) {\n        var result = new ValidatorResult(instance, schema, options, ctx);\n        result.addError(\"is required, but is undefined\");\n        return result;\n    }\n    var result = this.validateSchema(instance, schema, options, ctx);\n    if (!result) {\n        throw new Error(\"Result undefined\");\n    } else if (options.throwAll && result.errors.length) {\n        throw new ValidatorResultError(result);\n    }\n    return result;\n};\n/**\n* @param Object schema\n* @return mixed schema uri or false\n*/ function shouldResolve(schema) {\n    var ref = typeof schema === \"string\" ? schema : schema.$ref;\n    if (typeof ref == \"string\") return ref;\n    return false;\n}\n/**\n * Validates an instance against the schema (the actual work horse)\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @private\n * @return {ValidatorResult}\n */ Validator.prototype.validateSchema = function validateSchema(instance, schema, options, ctx) {\n    var result = new ValidatorResult(instance, schema, options, ctx);\n    // Support for the true/false schemas\n    if (typeof schema === \"boolean\") {\n        if (schema === true) {\n            // `true` is always valid\n            schema = {};\n        } else if (schema === false) {\n            // `false` is always invalid\n            schema = {\n                type: []\n            };\n        }\n    } else if (!schema) {\n        // This might be a string\n        throw new Error(\"schema is undefined\");\n    }\n    if (schema[\"extends\"]) {\n        if (Array.isArray(schema[\"extends\"])) {\n            var schemaobj = {\n                schema: schema,\n                ctx: ctx\n            };\n            schema[\"extends\"].forEach(this.schemaTraverser.bind(this, schemaobj));\n            schema = schemaobj.schema;\n            schemaobj.schema = null;\n            schemaobj.ctx = null;\n            schemaobj = null;\n        } else {\n            schema = helpers.deepMerge(schema, this.superResolve(schema[\"extends\"], ctx));\n        }\n    }\n    // If passed a string argument, load that schema URI\n    var switchSchema = shouldResolve(schema);\n    if (switchSchema) {\n        var resolved = this.resolve(schema, switchSchema, ctx);\n        var subctx = new SchemaContext(resolved.subschema, options, ctx.path, resolved.switchSchema, ctx.schemas);\n        return this.validateSchema(instance, resolved.subschema, options, subctx);\n    }\n    var skipAttributes = options && options.skipAttributes || [];\n    // Validate each schema attribute against the instance\n    for(var key in schema){\n        if (!attribute.ignoreProperties[key] && skipAttributes.indexOf(key) < 0) {\n            var validatorErr = null;\n            var validator = this.attributes[key];\n            if (validator) {\n                validatorErr = validator.call(this, instance, schema, options, ctx);\n            } else if (options.allowUnknownAttributes === false) {\n                // This represents an error with the schema itself, not an invalid instance\n                throw new SchemaError(\"Unsupported attribute: \" + key, schema);\n            }\n            if (validatorErr) {\n                result.importErrors(validatorErr);\n            }\n        }\n    }\n    if (typeof options.rewrite == \"function\") {\n        var value = options.rewrite.call(this, instance, schema, options, ctx);\n        result.instance = value;\n    }\n    return result;\n};\n/**\n* @private\n* @param Object schema\n* @param SchemaContext ctx\n* @returns Object schema or resolved schema\n*/ Validator.prototype.schemaTraverser = function schemaTraverser(schemaobj, s) {\n    schemaobj.schema = helpers.deepMerge(schemaobj.schema, this.superResolve(s, schemaobj.ctx));\n};\n/**\n* @private\n* @param Object schema\n* @param SchemaContext ctx\n* @returns Object schema or resolved schema\n*/ Validator.prototype.superResolve = function superResolve(schema, ctx) {\n    var ref = shouldResolve(schema);\n    if (ref) {\n        return this.resolve(schema, ref, ctx).subschema;\n    }\n    return schema;\n};\n/**\n* @private\n* @param Object schema\n* @param Object switchSchema\n* @param SchemaContext ctx\n* @return Object resolved schemas {subschema:String, switchSchema: String}\n* @throws SchemaError\n*/ Validator.prototype.resolve = function resolve(schema, switchSchema, ctx) {\n    switchSchema = ctx.resolve(switchSchema);\n    // First see if the schema exists under the provided URI\n    if (ctx.schemas[switchSchema]) {\n        return {\n            subschema: ctx.schemas[switchSchema],\n            switchSchema: switchSchema\n        };\n    }\n    // Else try walking the property pointer\n    let parsed = new URL(switchSchema, \"thismessage::/\");\n    let fragment = parsed.hash;\n    var document = fragment && fragment.length && switchSchema.substr(0, switchSchema.length - fragment.length);\n    if (!document || !ctx.schemas[document]) {\n        throw new SchemaError(\"no such schema <\" + switchSchema + \">\", schema);\n    }\n    var subschema = helpers.objectGetPath(ctx.schemas[document], fragment.substr(1));\n    if (subschema === undefined) {\n        throw new SchemaError(\"no such schema \" + fragment + \" located in <\" + document + \">\", schema);\n    }\n    return {\n        subschema: subschema,\n        switchSchema: switchSchema\n    };\n};\n/**\n * Tests whether the instance if of a certain type.\n * @private\n * @param instance\n * @param schema\n * @param options\n * @param ctx\n * @param type\n * @return {boolean}\n */ Validator.prototype.testType = function validateType(instance, schema, options, ctx, type) {\n    if (type === undefined) {\n        return;\n    } else if (type === null) {\n        throw new SchemaError('Unexpected null in \"type\" keyword');\n    }\n    if (typeof this.types[type] == \"function\") {\n        return this.types[type].call(this, instance);\n    }\n    if (type && typeof type == \"object\") {\n        var res = this.validateSchema(instance, type, options, ctx);\n        return res === undefined || !(res && res.errors.length);\n    }\n    // Undefined or properties not on the list are acceptable, same as not being defined\n    return true;\n};\nvar types = Validator.prototype.types = {};\ntypes.string = function testString(instance) {\n    return typeof instance == \"string\";\n};\ntypes.number = function testNumber(instance) {\n    // isFinite returns false for NaN, Infinity, and -Infinity\n    return typeof instance == \"number\" && isFinite(instance);\n};\ntypes.integer = function testInteger(instance) {\n    return typeof instance == \"number\" && instance % 1 === 0;\n};\ntypes.boolean = function testBoolean(instance) {\n    return typeof instance == \"boolean\";\n};\ntypes.array = function testArray(instance) {\n    return Array.isArray(instance);\n};\ntypes[\"null\"] = function testNull(instance) {\n    return instance === null;\n};\ntypes.date = function testDate(instance) {\n    return instance instanceof Date;\n};\ntypes.any = function testAny(instance) {\n    return true;\n};\ntypes.object = function testObject(instance) {\n    // TODO: fix this - see #15\n    return instance && typeof instance === \"object\" && !Array.isArray(instance) && !(instance instanceof Date);\n};\nmodule.exports = Validator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonschema/lib/validator.js\n");

/***/ })

};
;