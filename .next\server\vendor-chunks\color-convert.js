/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/color-convert";
exports.ids = ["vendor-chunks/color-convert"];
exports.modules = {

/***/ "(ssr)/./node_modules/color-convert/conversions.js":
/*!***************************************************!*\
  !*** ./node_modules/color-convert/conversions.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* MIT license */ /* eslint-disable no-mixed-operators */ const cssKeywords = __webpack_require__(/*! color-name */ \"(ssr)/./node_modules/color-name/index.js\");\n// NOTE: conversions should only return primitive values (i.e. arrays, or\n//       values that give correct `typeof` results).\n//       do not use box values types (i.e. Number(), String(), etc.)\nconst reverseKeywords = {};\nfor (const key of Object.keys(cssKeywords)){\n    reverseKeywords[cssKeywords[key]] = key;\n}\nconst convert = {\n    rgb: {\n        channels: 3,\n        labels: \"rgb\"\n    },\n    hsl: {\n        channels: 3,\n        labels: \"hsl\"\n    },\n    hsv: {\n        channels: 3,\n        labels: \"hsv\"\n    },\n    hwb: {\n        channels: 3,\n        labels: \"hwb\"\n    },\n    cmyk: {\n        channels: 4,\n        labels: \"cmyk\"\n    },\n    xyz: {\n        channels: 3,\n        labels: \"xyz\"\n    },\n    lab: {\n        channels: 3,\n        labels: \"lab\"\n    },\n    lch: {\n        channels: 3,\n        labels: \"lch\"\n    },\n    hex: {\n        channels: 1,\n        labels: [\n            \"hex\"\n        ]\n    },\n    keyword: {\n        channels: 1,\n        labels: [\n            \"keyword\"\n        ]\n    },\n    ansi16: {\n        channels: 1,\n        labels: [\n            \"ansi16\"\n        ]\n    },\n    ansi256: {\n        channels: 1,\n        labels: [\n            \"ansi256\"\n        ]\n    },\n    hcg: {\n        channels: 3,\n        labels: [\n            \"h\",\n            \"c\",\n            \"g\"\n        ]\n    },\n    apple: {\n        channels: 3,\n        labels: [\n            \"r16\",\n            \"g16\",\n            \"b16\"\n        ]\n    },\n    gray: {\n        channels: 1,\n        labels: [\n            \"gray\"\n        ]\n    }\n};\nmodule.exports = convert;\n// Hide .channels and .labels properties\nfor (const model of Object.keys(convert)){\n    if (!(\"channels\" in convert[model])) {\n        throw new Error(\"missing channels property: \" + model);\n    }\n    if (!(\"labels\" in convert[model])) {\n        throw new Error(\"missing channel labels property: \" + model);\n    }\n    if (convert[model].labels.length !== convert[model].channels) {\n        throw new Error(\"channel and label counts mismatch: \" + model);\n    }\n    const { channels, labels } = convert[model];\n    delete convert[model].channels;\n    delete convert[model].labels;\n    Object.defineProperty(convert[model], \"channels\", {\n        value: channels\n    });\n    Object.defineProperty(convert[model], \"labels\", {\n        value: labels\n    });\n}\nconvert.rgb.hsl = function(rgb) {\n    const r = rgb[0] / 255;\n    const g = rgb[1] / 255;\n    const b = rgb[2] / 255;\n    const min = Math.min(r, g, b);\n    const max = Math.max(r, g, b);\n    const delta = max - min;\n    let h;\n    let s;\n    if (max === min) {\n        h = 0;\n    } else if (r === max) {\n        h = (g - b) / delta;\n    } else if (g === max) {\n        h = 2 + (b - r) / delta;\n    } else if (b === max) {\n        h = 4 + (r - g) / delta;\n    }\n    h = Math.min(h * 60, 360);\n    if (h < 0) {\n        h += 360;\n    }\n    const l = (min + max) / 2;\n    if (max === min) {\n        s = 0;\n    } else if (l <= 0.5) {\n        s = delta / (max + min);\n    } else {\n        s = delta / (2 - max - min);\n    }\n    return [\n        h,\n        s * 100,\n        l * 100\n    ];\n};\nconvert.rgb.hsv = function(rgb) {\n    let rdif;\n    let gdif;\n    let bdif;\n    let h;\n    let s;\n    const r = rgb[0] / 255;\n    const g = rgb[1] / 255;\n    const b = rgb[2] / 255;\n    const v = Math.max(r, g, b);\n    const diff = v - Math.min(r, g, b);\n    const diffc = function(c) {\n        return (v - c) / 6 / diff + 1 / 2;\n    };\n    if (diff === 0) {\n        h = 0;\n        s = 0;\n    } else {\n        s = diff / v;\n        rdif = diffc(r);\n        gdif = diffc(g);\n        bdif = diffc(b);\n        if (r === v) {\n            h = bdif - gdif;\n        } else if (g === v) {\n            h = 1 / 3 + rdif - bdif;\n        } else if (b === v) {\n            h = 2 / 3 + gdif - rdif;\n        }\n        if (h < 0) {\n            h += 1;\n        } else if (h > 1) {\n            h -= 1;\n        }\n    }\n    return [\n        h * 360,\n        s * 100,\n        v * 100\n    ];\n};\nconvert.rgb.hwb = function(rgb) {\n    const r = rgb[0];\n    const g = rgb[1];\n    let b = rgb[2];\n    const h = convert.rgb.hsl(rgb)[0];\n    const w = 1 / 255 * Math.min(r, Math.min(g, b));\n    b = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n    return [\n        h,\n        w * 100,\n        b * 100\n    ];\n};\nconvert.rgb.cmyk = function(rgb) {\n    const r = rgb[0] / 255;\n    const g = rgb[1] / 255;\n    const b = rgb[2] / 255;\n    const k = Math.min(1 - r, 1 - g, 1 - b);\n    const c = (1 - r - k) / (1 - k) || 0;\n    const m = (1 - g - k) / (1 - k) || 0;\n    const y = (1 - b - k) / (1 - k) || 0;\n    return [\n        c * 100,\n        m * 100,\n        y * 100,\n        k * 100\n    ];\n};\nfunction comparativeDistance(x, y) {\n    /*\n\t\tSee https://en.m.wikipedia.org/wiki/Euclidean_distance#Squared_Euclidean_distance\n\t*/ return (x[0] - y[0]) ** 2 + (x[1] - y[1]) ** 2 + (x[2] - y[2]) ** 2;\n}\nconvert.rgb.keyword = function(rgb) {\n    const reversed = reverseKeywords[rgb];\n    if (reversed) {\n        return reversed;\n    }\n    let currentClosestDistance = Infinity;\n    let currentClosestKeyword;\n    for (const keyword of Object.keys(cssKeywords)){\n        const value = cssKeywords[keyword];\n        // Compute comparative distance\n        const distance = comparativeDistance(rgb, value);\n        // Check if its less, if so set as closest\n        if (distance < currentClosestDistance) {\n            currentClosestDistance = distance;\n            currentClosestKeyword = keyword;\n        }\n    }\n    return currentClosestKeyword;\n};\nconvert.keyword.rgb = function(keyword) {\n    return cssKeywords[keyword];\n};\nconvert.rgb.xyz = function(rgb) {\n    let r = rgb[0] / 255;\n    let g = rgb[1] / 255;\n    let b = rgb[2] / 255;\n    // Assume sRGB\n    r = r > 0.04045 ? ((r + 0.055) / 1.055) ** 2.4 : r / 12.92;\n    g = g > 0.04045 ? ((g + 0.055) / 1.055) ** 2.4 : g / 12.92;\n    b = b > 0.04045 ? ((b + 0.055) / 1.055) ** 2.4 : b / 12.92;\n    const x = r * 0.4124 + g * 0.3576 + b * 0.1805;\n    const y = r * 0.2126 + g * 0.7152 + b * 0.0722;\n    const z = r * 0.0193 + g * 0.1192 + b * 0.9505;\n    return [\n        x * 100,\n        y * 100,\n        z * 100\n    ];\n};\nconvert.rgb.lab = function(rgb) {\n    const xyz = convert.rgb.xyz(rgb);\n    let x = xyz[0];\n    let y = xyz[1];\n    let z = xyz[2];\n    x /= 95.047;\n    y /= 100;\n    z /= 108.883;\n    x = x > 0.008856 ? x ** (1 / 3) : 7.787 * x + 16 / 116;\n    y = y > 0.008856 ? y ** (1 / 3) : 7.787 * y + 16 / 116;\n    z = z > 0.008856 ? z ** (1 / 3) : 7.787 * z + 16 / 116;\n    const l = 116 * y - 16;\n    const a = 500 * (x - y);\n    const b = 200 * (y - z);\n    return [\n        l,\n        a,\n        b\n    ];\n};\nconvert.hsl.rgb = function(hsl) {\n    const h = hsl[0] / 360;\n    const s = hsl[1] / 100;\n    const l = hsl[2] / 100;\n    let t2;\n    let t3;\n    let val;\n    if (s === 0) {\n        val = l * 255;\n        return [\n            val,\n            val,\n            val\n        ];\n    }\n    if (l < 0.5) {\n        t2 = l * (1 + s);\n    } else {\n        t2 = l + s - l * s;\n    }\n    const t1 = 2 * l - t2;\n    const rgb = [\n        0,\n        0,\n        0\n    ];\n    for(let i = 0; i < 3; i++){\n        t3 = h + 1 / 3 * -(i - 1);\n        if (t3 < 0) {\n            t3++;\n        }\n        if (t3 > 1) {\n            t3--;\n        }\n        if (6 * t3 < 1) {\n            val = t1 + (t2 - t1) * 6 * t3;\n        } else if (2 * t3 < 1) {\n            val = t2;\n        } else if (3 * t3 < 2) {\n            val = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n        } else {\n            val = t1;\n        }\n        rgb[i] = val * 255;\n    }\n    return rgb;\n};\nconvert.hsl.hsv = function(hsl) {\n    const h = hsl[0];\n    let s = hsl[1] / 100;\n    let l = hsl[2] / 100;\n    let smin = s;\n    const lmin = Math.max(l, 0.01);\n    l *= 2;\n    s *= l <= 1 ? l : 2 - l;\n    smin *= lmin <= 1 ? lmin : 2 - lmin;\n    const v = (l + s) / 2;\n    const sv = l === 0 ? 2 * smin / (lmin + smin) : 2 * s / (l + s);\n    return [\n        h,\n        sv * 100,\n        v * 100\n    ];\n};\nconvert.hsv.rgb = function(hsv) {\n    const h = hsv[0] / 60;\n    const s = hsv[1] / 100;\n    let v = hsv[2] / 100;\n    const hi = Math.floor(h) % 6;\n    const f = h - Math.floor(h);\n    const p = 255 * v * (1 - s);\n    const q = 255 * v * (1 - s * f);\n    const t = 255 * v * (1 - s * (1 - f));\n    v *= 255;\n    switch(hi){\n        case 0:\n            return [\n                v,\n                t,\n                p\n            ];\n        case 1:\n            return [\n                q,\n                v,\n                p\n            ];\n        case 2:\n            return [\n                p,\n                v,\n                t\n            ];\n        case 3:\n            return [\n                p,\n                q,\n                v\n            ];\n        case 4:\n            return [\n                t,\n                p,\n                v\n            ];\n        case 5:\n            return [\n                v,\n                p,\n                q\n            ];\n    }\n};\nconvert.hsv.hsl = function(hsv) {\n    const h = hsv[0];\n    const s = hsv[1] / 100;\n    const v = hsv[2] / 100;\n    const vmin = Math.max(v, 0.01);\n    let sl;\n    let l;\n    l = (2 - s) * v;\n    const lmin = (2 - s) * vmin;\n    sl = s * vmin;\n    sl /= lmin <= 1 ? lmin : 2 - lmin;\n    sl = sl || 0;\n    l /= 2;\n    return [\n        h,\n        sl * 100,\n        l * 100\n    ];\n};\n// http://dev.w3.org/csswg/css-color/#hwb-to-rgb\nconvert.hwb.rgb = function(hwb) {\n    const h = hwb[0] / 360;\n    let wh = hwb[1] / 100;\n    let bl = hwb[2] / 100;\n    const ratio = wh + bl;\n    let f;\n    // Wh + bl cant be > 1\n    if (ratio > 1) {\n        wh /= ratio;\n        bl /= ratio;\n    }\n    const i = Math.floor(6 * h);\n    const v = 1 - bl;\n    f = 6 * h - i;\n    if ((i & 0x01) !== 0) {\n        f = 1 - f;\n    }\n    const n = wh + f * (v - wh); // Linear interpolation\n    let r;\n    let g;\n    let b;\n    /* eslint-disable max-statements-per-line,no-multi-spaces */ switch(i){\n        default:\n        case 6:\n        case 0:\n            r = v;\n            g = n;\n            b = wh;\n            break;\n        case 1:\n            r = n;\n            g = v;\n            b = wh;\n            break;\n        case 2:\n            r = wh;\n            g = v;\n            b = n;\n            break;\n        case 3:\n            r = wh;\n            g = n;\n            b = v;\n            break;\n        case 4:\n            r = n;\n            g = wh;\n            b = v;\n            break;\n        case 5:\n            r = v;\n            g = wh;\n            b = n;\n            break;\n    }\n    /* eslint-enable max-statements-per-line,no-multi-spaces */ return [\n        r * 255,\n        g * 255,\n        b * 255\n    ];\n};\nconvert.cmyk.rgb = function(cmyk) {\n    const c = cmyk[0] / 100;\n    const m = cmyk[1] / 100;\n    const y = cmyk[2] / 100;\n    const k = cmyk[3] / 100;\n    const r = 1 - Math.min(1, c * (1 - k) + k);\n    const g = 1 - Math.min(1, m * (1 - k) + k);\n    const b = 1 - Math.min(1, y * (1 - k) + k);\n    return [\n        r * 255,\n        g * 255,\n        b * 255\n    ];\n};\nconvert.xyz.rgb = function(xyz) {\n    const x = xyz[0] / 100;\n    const y = xyz[1] / 100;\n    const z = xyz[2] / 100;\n    let r;\n    let g;\n    let b;\n    r = x * 3.2406 + y * -1.5372 + z * -0.4986;\n    g = x * -0.9689 + y * 1.8758 + z * 0.0415;\n    b = x * 0.0557 + y * -0.2040 + z * 1.0570;\n    // Assume sRGB\n    r = r > 0.0031308 ? 1.055 * r ** (1.0 / 2.4) - 0.055 : r * 12.92;\n    g = g > 0.0031308 ? 1.055 * g ** (1.0 / 2.4) - 0.055 : g * 12.92;\n    b = b > 0.0031308 ? 1.055 * b ** (1.0 / 2.4) - 0.055 : b * 12.92;\n    r = Math.min(Math.max(0, r), 1);\n    g = Math.min(Math.max(0, g), 1);\n    b = Math.min(Math.max(0, b), 1);\n    return [\n        r * 255,\n        g * 255,\n        b * 255\n    ];\n};\nconvert.xyz.lab = function(xyz) {\n    let x = xyz[0];\n    let y = xyz[1];\n    let z = xyz[2];\n    x /= 95.047;\n    y /= 100;\n    z /= 108.883;\n    x = x > 0.008856 ? x ** (1 / 3) : 7.787 * x + 16 / 116;\n    y = y > 0.008856 ? y ** (1 / 3) : 7.787 * y + 16 / 116;\n    z = z > 0.008856 ? z ** (1 / 3) : 7.787 * z + 16 / 116;\n    const l = 116 * y - 16;\n    const a = 500 * (x - y);\n    const b = 200 * (y - z);\n    return [\n        l,\n        a,\n        b\n    ];\n};\nconvert.lab.xyz = function(lab) {\n    const l = lab[0];\n    const a = lab[1];\n    const b = lab[2];\n    let x;\n    let y;\n    let z;\n    y = (l + 16) / 116;\n    x = a / 500 + y;\n    z = y - b / 200;\n    const y2 = y ** 3;\n    const x2 = x ** 3;\n    const z2 = z ** 3;\n    y = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n    x = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n    z = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n    x *= 95.047;\n    y *= 100;\n    z *= 108.883;\n    return [\n        x,\n        y,\n        z\n    ];\n};\nconvert.lab.lch = function(lab) {\n    const l = lab[0];\n    const a = lab[1];\n    const b = lab[2];\n    let h;\n    const hr = Math.atan2(b, a);\n    h = hr * 360 / 2 / Math.PI;\n    if (h < 0) {\n        h += 360;\n    }\n    const c = Math.sqrt(a * a + b * b);\n    return [\n        l,\n        c,\n        h\n    ];\n};\nconvert.lch.lab = function(lch) {\n    const l = lch[0];\n    const c = lch[1];\n    const h = lch[2];\n    const hr = h / 360 * 2 * Math.PI;\n    const a = c * Math.cos(hr);\n    const b = c * Math.sin(hr);\n    return [\n        l,\n        a,\n        b\n    ];\n};\nconvert.rgb.ansi16 = function(args, saturation = null) {\n    const [r, g, b] = args;\n    let value = saturation === null ? convert.rgb.hsv(args)[2] : saturation; // Hsv -> ansi16 optimization\n    value = Math.round(value / 50);\n    if (value === 0) {\n        return 30;\n    }\n    let ansi = 30 + (Math.round(b / 255) << 2 | Math.round(g / 255) << 1 | Math.round(r / 255));\n    if (value === 2) {\n        ansi += 60;\n    }\n    return ansi;\n};\nconvert.hsv.ansi16 = function(args) {\n    // Optimization here; we already know the value and don't need to get\n    // it converted for us.\n    return convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n};\nconvert.rgb.ansi256 = function(args) {\n    const r = args[0];\n    const g = args[1];\n    const b = args[2];\n    // We use the extended greyscale palette here, with the exception of\n    // black and white. normal palette only has 4 greyscale shades.\n    if (r === g && g === b) {\n        if (r < 8) {\n            return 16;\n        }\n        if (r > 248) {\n            return 231;\n        }\n        return Math.round((r - 8) / 247 * 24) + 232;\n    }\n    const ansi = 16 + 36 * Math.round(r / 255 * 5) + 6 * Math.round(g / 255 * 5) + Math.round(b / 255 * 5);\n    return ansi;\n};\nconvert.ansi16.rgb = function(args) {\n    let color = args % 10;\n    // Handle greyscale\n    if (color === 0 || color === 7) {\n        if (args > 50) {\n            color += 3.5;\n        }\n        color = color / 10.5 * 255;\n        return [\n            color,\n            color,\n            color\n        ];\n    }\n    const mult = (~~(args > 50) + 1) * 0.5;\n    const r = (color & 1) * mult * 255;\n    const g = (color >> 1 & 1) * mult * 255;\n    const b = (color >> 2 & 1) * mult * 255;\n    return [\n        r,\n        g,\n        b\n    ];\n};\nconvert.ansi256.rgb = function(args) {\n    // Handle greyscale\n    if (args >= 232) {\n        const c = (args - 232) * 10 + 8;\n        return [\n            c,\n            c,\n            c\n        ];\n    }\n    args -= 16;\n    let rem;\n    const r = Math.floor(args / 36) / 5 * 255;\n    const g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n    const b = rem % 6 / 5 * 255;\n    return [\n        r,\n        g,\n        b\n    ];\n};\nconvert.rgb.hex = function(args) {\n    const integer = ((Math.round(args[0]) & 0xFF) << 16) + ((Math.round(args[1]) & 0xFF) << 8) + (Math.round(args[2]) & 0xFF);\n    const string = integer.toString(16).toUpperCase();\n    return \"000000\".substring(string.length) + string;\n};\nconvert.hex.rgb = function(args) {\n    const match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n    if (!match) {\n        return [\n            0,\n            0,\n            0\n        ];\n    }\n    let colorString = match[0];\n    if (match[0].length === 3) {\n        colorString = colorString.split(\"\").map((char)=>{\n            return char + char;\n        }).join(\"\");\n    }\n    const integer = parseInt(colorString, 16);\n    const r = integer >> 16 & 0xFF;\n    const g = integer >> 8 & 0xFF;\n    const b = integer & 0xFF;\n    return [\n        r,\n        g,\n        b\n    ];\n};\nconvert.rgb.hcg = function(rgb) {\n    const r = rgb[0] / 255;\n    const g = rgb[1] / 255;\n    const b = rgb[2] / 255;\n    const max = Math.max(Math.max(r, g), b);\n    const min = Math.min(Math.min(r, g), b);\n    const chroma = max - min;\n    let grayscale;\n    let hue;\n    if (chroma < 1) {\n        grayscale = min / (1 - chroma);\n    } else {\n        grayscale = 0;\n    }\n    if (chroma <= 0) {\n        hue = 0;\n    } else if (max === r) {\n        hue = (g - b) / chroma % 6;\n    } else if (max === g) {\n        hue = 2 + (b - r) / chroma;\n    } else {\n        hue = 4 + (r - g) / chroma;\n    }\n    hue /= 6;\n    hue %= 1;\n    return [\n        hue * 360,\n        chroma * 100,\n        grayscale * 100\n    ];\n};\nconvert.hsl.hcg = function(hsl) {\n    const s = hsl[1] / 100;\n    const l = hsl[2] / 100;\n    const c = l < 0.5 ? 2.0 * s * l : 2.0 * s * (1.0 - l);\n    let f = 0;\n    if (c < 1.0) {\n        f = (l - 0.5 * c) / (1.0 - c);\n    }\n    return [\n        hsl[0],\n        c * 100,\n        f * 100\n    ];\n};\nconvert.hsv.hcg = function(hsv) {\n    const s = hsv[1] / 100;\n    const v = hsv[2] / 100;\n    const c = s * v;\n    let f = 0;\n    if (c < 1.0) {\n        f = (v - c) / (1 - c);\n    }\n    return [\n        hsv[0],\n        c * 100,\n        f * 100\n    ];\n};\nconvert.hcg.rgb = function(hcg) {\n    const h = hcg[0] / 360;\n    const c = hcg[1] / 100;\n    const g = hcg[2] / 100;\n    if (c === 0.0) {\n        return [\n            g * 255,\n            g * 255,\n            g * 255\n        ];\n    }\n    const pure = [\n        0,\n        0,\n        0\n    ];\n    const hi = h % 1 * 6;\n    const v = hi % 1;\n    const w = 1 - v;\n    let mg = 0;\n    /* eslint-disable max-statements-per-line */ switch(Math.floor(hi)){\n        case 0:\n            pure[0] = 1;\n            pure[1] = v;\n            pure[2] = 0;\n            break;\n        case 1:\n            pure[0] = w;\n            pure[1] = 1;\n            pure[2] = 0;\n            break;\n        case 2:\n            pure[0] = 0;\n            pure[1] = 1;\n            pure[2] = v;\n            break;\n        case 3:\n            pure[0] = 0;\n            pure[1] = w;\n            pure[2] = 1;\n            break;\n        case 4:\n            pure[0] = v;\n            pure[1] = 0;\n            pure[2] = 1;\n            break;\n        default:\n            pure[0] = 1;\n            pure[1] = 0;\n            pure[2] = w;\n    }\n    /* eslint-enable max-statements-per-line */ mg = (1.0 - c) * g;\n    return [\n        (c * pure[0] + mg) * 255,\n        (c * pure[1] + mg) * 255,\n        (c * pure[2] + mg) * 255\n    ];\n};\nconvert.hcg.hsv = function(hcg) {\n    const c = hcg[1] / 100;\n    const g = hcg[2] / 100;\n    const v = c + g * (1.0 - c);\n    let f = 0;\n    if (v > 0.0) {\n        f = c / v;\n    }\n    return [\n        hcg[0],\n        f * 100,\n        v * 100\n    ];\n};\nconvert.hcg.hsl = function(hcg) {\n    const c = hcg[1] / 100;\n    const g = hcg[2] / 100;\n    const l = g * (1.0 - c) + 0.5 * c;\n    let s = 0;\n    if (l > 0.0 && l < 0.5) {\n        s = c / (2 * l);\n    } else if (l >= 0.5 && l < 1.0) {\n        s = c / (2 * (1 - l));\n    }\n    return [\n        hcg[0],\n        s * 100,\n        l * 100\n    ];\n};\nconvert.hcg.hwb = function(hcg) {\n    const c = hcg[1] / 100;\n    const g = hcg[2] / 100;\n    const v = c + g * (1.0 - c);\n    return [\n        hcg[0],\n        (v - c) * 100,\n        (1 - v) * 100\n    ];\n};\nconvert.hwb.hcg = function(hwb) {\n    const w = hwb[1] / 100;\n    const b = hwb[2] / 100;\n    const v = 1 - b;\n    const c = v - w;\n    let g = 0;\n    if (c < 1) {\n        g = (v - c) / (1 - c);\n    }\n    return [\n        hwb[0],\n        c * 100,\n        g * 100\n    ];\n};\nconvert.apple.rgb = function(apple) {\n    return [\n        apple[0] / 65535 * 255,\n        apple[1] / 65535 * 255,\n        apple[2] / 65535 * 255\n    ];\n};\nconvert.rgb.apple = function(rgb) {\n    return [\n        rgb[0] / 255 * 65535,\n        rgb[1] / 255 * 65535,\n        rgb[2] / 255 * 65535\n    ];\n};\nconvert.gray.rgb = function(args) {\n    return [\n        args[0] / 100 * 255,\n        args[0] / 100 * 255,\n        args[0] / 100 * 255\n    ];\n};\nconvert.gray.hsl = function(args) {\n    return [\n        0,\n        0,\n        args[0]\n    ];\n};\nconvert.gray.hsv = convert.gray.hsl;\nconvert.gray.hwb = function(gray) {\n    return [\n        0,\n        100,\n        gray[0]\n    ];\n};\nconvert.gray.cmyk = function(gray) {\n    return [\n        0,\n        0,\n        0,\n        gray[0]\n    ];\n};\nconvert.gray.lab = function(gray) {\n    return [\n        gray[0],\n        0,\n        0\n    ];\n};\nconvert.gray.hex = function(gray) {\n    const val = Math.round(gray[0] / 100 * 255) & 0xFF;\n    const integer = (val << 16) + (val << 8) + val;\n    const string = integer.toString(16).toUpperCase();\n    return \"000000\".substring(string.length) + string;\n};\nconvert.rgb.gray = function(rgb) {\n    const val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n    return [\n        val / 255 * 100\n    ];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/color-convert/conversions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/color-convert/index.js":
/*!*********************************************!*\
  !*** ./node_modules/color-convert/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const conversions = __webpack_require__(/*! ./conversions */ \"(ssr)/./node_modules/color-convert/conversions.js\");\nconst route = __webpack_require__(/*! ./route */ \"(ssr)/./node_modules/color-convert/route.js\");\nconst convert = {};\nconst models = Object.keys(conversions);\nfunction wrapRaw(fn) {\n    const wrappedFn = function(...args) {\n        const arg0 = args[0];\n        if (arg0 === undefined || arg0 === null) {\n            return arg0;\n        }\n        if (arg0.length > 1) {\n            args = arg0;\n        }\n        return fn(args);\n    };\n    // Preserve .conversion property if there is one\n    if (\"conversion\" in fn) {\n        wrappedFn.conversion = fn.conversion;\n    }\n    return wrappedFn;\n}\nfunction wrapRounded(fn) {\n    const wrappedFn = function(...args) {\n        const arg0 = args[0];\n        if (arg0 === undefined || arg0 === null) {\n            return arg0;\n        }\n        if (arg0.length > 1) {\n            args = arg0;\n        }\n        const result = fn(args);\n        // We're assuming the result is an array here.\n        // see notice in conversions.js; don't use box types\n        // in conversion functions.\n        if (typeof result === \"object\") {\n            for(let len = result.length, i = 0; i < len; i++){\n                result[i] = Math.round(result[i]);\n            }\n        }\n        return result;\n    };\n    // Preserve .conversion property if there is one\n    if (\"conversion\" in fn) {\n        wrappedFn.conversion = fn.conversion;\n    }\n    return wrappedFn;\n}\nmodels.forEach((fromModel)=>{\n    convert[fromModel] = {};\n    Object.defineProperty(convert[fromModel], \"channels\", {\n        value: conversions[fromModel].channels\n    });\n    Object.defineProperty(convert[fromModel], \"labels\", {\n        value: conversions[fromModel].labels\n    });\n    const routes = route(fromModel);\n    const routeModels = Object.keys(routes);\n    routeModels.forEach((toModel)=>{\n        const fn = routes[toModel];\n        convert[fromModel][toModel] = wrapRounded(fn);\n        convert[fromModel][toModel].raw = wrapRaw(fn);\n    });\n});\nmodule.exports = convert;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/color-convert/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/color-convert/route.js":
/*!*********************************************!*\
  !*** ./node_modules/color-convert/route.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const conversions = __webpack_require__(/*! ./conversions */ \"(ssr)/./node_modules/color-convert/conversions.js\");\n/*\n\tThis function routes a model to all other models.\n\n\tall functions that are routed have a property `.conversion` attached\n\tto the returned synthetic function. This property is an array\n\tof strings, each with the steps in between the 'from' and 'to'\n\tcolor models (inclusive).\n\n\tconversions that are not possible simply are not included.\n*/ function buildGraph() {\n    const graph = {};\n    // https://jsperf.com/object-keys-vs-for-in-with-closure/3\n    const models = Object.keys(conversions);\n    for(let len = models.length, i = 0; i < len; i++){\n        graph[models[i]] = {\n            // http://jsperf.com/1-vs-infinity\n            // micro-opt, but this is simple.\n            distance: -1,\n            parent: null\n        };\n    }\n    return graph;\n}\n// https://en.wikipedia.org/wiki/Breadth-first_search\nfunction deriveBFS(fromModel) {\n    const graph = buildGraph();\n    const queue = [\n        fromModel\n    ]; // Unshift -> queue -> pop\n    graph[fromModel].distance = 0;\n    while(queue.length){\n        const current = queue.pop();\n        const adjacents = Object.keys(conversions[current]);\n        for(let len = adjacents.length, i = 0; i < len; i++){\n            const adjacent = adjacents[i];\n            const node = graph[adjacent];\n            if (node.distance === -1) {\n                node.distance = graph[current].distance + 1;\n                node.parent = current;\n                queue.unshift(adjacent);\n            }\n        }\n    }\n    return graph;\n}\nfunction link(from, to) {\n    return function(args) {\n        return to(from(args));\n    };\n}\nfunction wrapConversion(toModel, graph) {\n    const path = [\n        graph[toModel].parent,\n        toModel\n    ];\n    let fn = conversions[graph[toModel].parent][toModel];\n    let cur = graph[toModel].parent;\n    while(graph[cur].parent){\n        path.unshift(graph[cur].parent);\n        fn = link(conversions[graph[cur].parent][cur], fn);\n        cur = graph[cur].parent;\n    }\n    fn.conversion = path;\n    return fn;\n}\nmodule.exports = function(fromModel) {\n    const graph = deriveBFS(fromModel);\n    const conversion = {};\n    const models = Object.keys(graph);\n    for(let len = models.length, i = 0; i < len; i++){\n        const toModel = models[i];\n        const node = graph[toModel];\n        if (node.parent === null) {\n            continue;\n        }\n        conversion[toModel] = wrapConversion(toModel, graph);\n    }\n    return conversion;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/color-convert/route.js\n");

/***/ })

};
;