/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lodash.isequal";
exports.ids = ["vendor-chunks/lodash.isequal"];
exports.modules = {

/***/ "(ssr)/./node_modules/lodash.isequal/index.js":
/*!**********************************************!*\
  !*** ./node_modules/lodash.isequal/index.js ***!
  \**********************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* module decorator */ module = __webpack_require__.nmd(module);\n/**\n * Lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright JS Foundation and other contributors <https://js.foundation/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors\n */ /** Used as the size to enable large array optimizations. */ var LARGE_ARRAY_SIZE = 200;\n/** Used to stand-in for `undefined` hash values. */ var HASH_UNDEFINED = \"__lodash_hash_undefined__\";\n/** Used to compose bitmasks for value comparisons. */ var COMPARE_PARTIAL_FLAG = 1, COMPARE_UNORDERED_FLAG = 2;\n/** Used as references for various `Number` constants. */ var MAX_SAFE_INTEGER = 9007199254740991;\n/** `Object#toString` result references. */ var argsTag = \"[object Arguments]\", arrayTag = \"[object Array]\", asyncTag = \"[object AsyncFunction]\", boolTag = \"[object Boolean]\", dateTag = \"[object Date]\", errorTag = \"[object Error]\", funcTag = \"[object Function]\", genTag = \"[object GeneratorFunction]\", mapTag = \"[object Map]\", numberTag = \"[object Number]\", nullTag = \"[object Null]\", objectTag = \"[object Object]\", promiseTag = \"[object Promise]\", proxyTag = \"[object Proxy]\", regexpTag = \"[object RegExp]\", setTag = \"[object Set]\", stringTag = \"[object String]\", symbolTag = \"[object Symbol]\", undefinedTag = \"[object Undefined]\", weakMapTag = \"[object WeakMap]\";\nvar arrayBufferTag = \"[object ArrayBuffer]\", dataViewTag = \"[object DataView]\", float32Tag = \"[object Float32Array]\", float64Tag = \"[object Float64Array]\", int8Tag = \"[object Int8Array]\", int16Tag = \"[object Int16Array]\", int32Tag = \"[object Int32Array]\", uint8Tag = \"[object Uint8Array]\", uint8ClampedTag = \"[object Uint8ClampedArray]\", uint16Tag = \"[object Uint16Array]\", uint32Tag = \"[object Uint32Array]\";\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */ var reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n/** Used to detect host constructors (Safari). */ var reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n/** Used to detect unsigned integer values. */ var reIsUint = /^(?:0|[1-9]\\d*)$/;\n/** Used to identify `toStringTag` values of typed arrays. */ var typedArrayTags = {};\ntypedArrayTags[float32Tag] = typedArrayTags[float64Tag] = typedArrayTags[int8Tag] = typedArrayTags[int16Tag] = typedArrayTags[int32Tag] = typedArrayTags[uint8Tag] = typedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] = typedArrayTags[uint32Tag] = true;\ntypedArrayTags[argsTag] = typedArrayTags[arrayTag] = typedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] = typedArrayTags[dataViewTag] = typedArrayTags[dateTag] = typedArrayTags[errorTag] = typedArrayTags[funcTag] = typedArrayTags[mapTag] = typedArrayTags[numberTag] = typedArrayTags[objectTag] = typedArrayTags[regexpTag] = typedArrayTags[setTag] = typedArrayTags[stringTag] = typedArrayTags[weakMapTag] = false;\n/** Detect free variable `global` from Node.js. */ var freeGlobal = typeof global == \"object\" && global && global.Object === Object && global;\n/** Detect free variable `self`. */ var freeSelf = typeof self == \"object\" && self && self.Object === Object && self;\n/** Used as a reference to the global object. */ var root = freeGlobal || freeSelf || Function(\"return this\")();\n/** Detect free variable `exports`. */ var freeExports =  true && exports && !exports.nodeType && exports;\n/** Detect free variable `module`. */ var freeModule = freeExports && \"object\" == \"object\" && module && !module.nodeType && module;\n/** Detect the popular CommonJS extension `module.exports`. */ var moduleExports = freeModule && freeModule.exports === freeExports;\n/** Detect free variable `process` from Node.js. */ var freeProcess = moduleExports && freeGlobal.process;\n/** Used to access faster Node.js helpers. */ var nodeUtil = function() {\n    try {\n        return freeProcess && freeProcess.binding && freeProcess.binding(\"util\");\n    } catch (e) {}\n}();\n/* Node.js helper references. */ var nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;\n/**\n * A specialized version of `_.filter` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */ function arrayFilter(array, predicate) {\n    var index = -1, length = array == null ? 0 : array.length, resIndex = 0, result = [];\n    while(++index < length){\n        var value = array[index];\n        if (predicate(value, index, array)) {\n            result[resIndex++] = value;\n        }\n    }\n    return result;\n}\n/**\n * Appends the elements of `values` to `array`.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to append.\n * @returns {Array} Returns `array`.\n */ function arrayPush(array, values) {\n    var index = -1, length = values.length, offset = array.length;\n    while(++index < length){\n        array[offset + index] = values[index];\n    }\n    return array;\n}\n/**\n * A specialized version of `_.some` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */ function arraySome(array, predicate) {\n    var index = -1, length = array == null ? 0 : array.length;\n    while(++index < length){\n        if (predicate(array[index], index, array)) {\n            return true;\n        }\n    }\n    return false;\n}\n/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */ function baseTimes(n, iteratee) {\n    var index = -1, result = Array(n);\n    while(++index < n){\n        result[index] = iteratee(index);\n    }\n    return result;\n}\n/**\n * The base implementation of `_.unary` without support for storing metadata.\n *\n * @private\n * @param {Function} func The function to cap arguments for.\n * @returns {Function} Returns the new capped function.\n */ function baseUnary(func) {\n    return function(value) {\n        return func(value);\n    };\n}\n/**\n * Checks if a `cache` value for `key` exists.\n *\n * @private\n * @param {Object} cache The cache to query.\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function cacheHas(cache, key) {\n    return cache.has(key);\n}\n/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */ function getValue(object, key) {\n    return object == null ? undefined : object[key];\n}\n/**\n * Converts `map` to its key-value pairs.\n *\n * @private\n * @param {Object} map The map to convert.\n * @returns {Array} Returns the key-value pairs.\n */ function mapToArray(map) {\n    var index = -1, result = Array(map.size);\n    map.forEach(function(value, key) {\n        result[++index] = [\n            key,\n            value\n        ];\n    });\n    return result;\n}\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */ function overArg(func, transform) {\n    return function(arg) {\n        return func(transform(arg));\n    };\n}\n/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */ function setToArray(set) {\n    var index = -1, result = Array(set.size);\n    set.forEach(function(value) {\n        result[++index] = value;\n    });\n    return result;\n}\n/** Used for built-in method references. */ var arrayProto = Array.prototype, funcProto = Function.prototype, objectProto = Object.prototype;\n/** Used to detect overreaching core-js shims. */ var coreJsData = root[\"__core-js_shared__\"];\n/** Used to resolve the decompiled source of functions. */ var funcToString = funcProto.toString;\n/** Used to check objects for own properties. */ var hasOwnProperty = objectProto.hasOwnProperty;\n/** Used to detect methods masquerading as native. */ var maskSrcKey = function() {\n    var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || \"\");\n    return uid ? \"Symbol(src)_1.\" + uid : \"\";\n}();\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */ var nativeObjectToString = objectProto.toString;\n/** Used to detect if a method is native. */ var reIsNative = RegExp(\"^\" + funcToString.call(hasOwnProperty).replace(reRegExpChar, \"\\\\$&\").replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, \"$1.*?\") + \"$\");\n/** Built-in value references. */ var Buffer = moduleExports ? root.Buffer : undefined, Symbol = root.Symbol, Uint8Array = root.Uint8Array, propertyIsEnumerable = objectProto.propertyIsEnumerable, splice = arrayProto.splice, symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n/* Built-in method references for those with the same name as other `lodash` methods. */ var nativeGetSymbols = Object.getOwnPropertySymbols, nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined, nativeKeys = overArg(Object.keys, Object);\n/* Built-in method references that are verified to be native. */ var DataView = getNative(root, \"DataView\"), Map = getNative(root, \"Map\"), Promise = getNative(root, \"Promise\"), Set = getNative(root, \"Set\"), WeakMap = getNative(root, \"WeakMap\"), nativeCreate = getNative(Object, \"create\");\n/** Used to detect maps, sets, and weakmaps. */ var dataViewCtorString = toSource(DataView), mapCtorString = toSource(Map), promiseCtorString = toSource(Promise), setCtorString = toSource(Set), weakMapCtorString = toSource(WeakMap);\n/** Used to convert symbols to primitives and strings. */ var symbolProto = Symbol ? Symbol.prototype : undefined, symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function Hash(entries) {\n    var index = -1, length = entries == null ? 0 : entries.length;\n    this.clear();\n    while(++index < length){\n        var entry = entries[index];\n        this.set(entry[0], entry[1]);\n    }\n}\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */ function hashClear() {\n    this.__data__ = nativeCreate ? nativeCreate(null) : {};\n    this.size = 0;\n}\n/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function hashDelete(key) {\n    var result = this.has(key) && delete this.__data__[key];\n    this.size -= result ? 1 : 0;\n    return result;\n}\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function hashGet(key) {\n    var data = this.__data__;\n    if (nativeCreate) {\n        var result = data[key];\n        return result === HASH_UNDEFINED ? undefined : result;\n    }\n    return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function hashHas(key) {\n    var data = this.__data__;\n    return nativeCreate ? data[key] !== undefined : hasOwnProperty.call(data, key);\n}\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */ function hashSet(key, value) {\n    var data = this.__data__;\n    this.size += this.has(key) ? 0 : 1;\n    data[key] = nativeCreate && value === undefined ? HASH_UNDEFINED : value;\n    return this;\n}\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype[\"delete\"] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function ListCache(entries) {\n    var index = -1, length = entries == null ? 0 : entries.length;\n    this.clear();\n    while(++index < length){\n        var entry = entries[index];\n        this.set(entry[0], entry[1]);\n    }\n}\n/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */ function listCacheClear() {\n    this.__data__ = [];\n    this.size = 0;\n}\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function listCacheDelete(key) {\n    var data = this.__data__, index = assocIndexOf(data, key);\n    if (index < 0) {\n        return false;\n    }\n    var lastIndex = data.length - 1;\n    if (index == lastIndex) {\n        data.pop();\n    } else {\n        splice.call(data, index, 1);\n    }\n    --this.size;\n    return true;\n}\n/**\n * Gets the list cache value for `key`.\n *\n * @private\n * @name get\n * @memberOf ListCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function listCacheGet(key) {\n    var data = this.__data__, index = assocIndexOf(data, key);\n    return index < 0 ? undefined : data[index][1];\n}\n/**\n * Checks if a list cache value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf ListCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function listCacheHas(key) {\n    return assocIndexOf(this.__data__, key) > -1;\n}\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */ function listCacheSet(key, value) {\n    var data = this.__data__, index = assocIndexOf(data, key);\n    if (index < 0) {\n        ++this.size;\n        data.push([\n            key,\n            value\n        ]);\n    } else {\n        data[index][1] = value;\n    }\n    return this;\n}\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype[\"delete\"] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function MapCache(entries) {\n    var index = -1, length = entries == null ? 0 : entries.length;\n    this.clear();\n    while(++index < length){\n        var entry = entries[index];\n        this.set(entry[0], entry[1]);\n    }\n}\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */ function mapCacheClear() {\n    this.size = 0;\n    this.__data__ = {\n        \"hash\": new Hash,\n        \"map\": new (Map || ListCache),\n        \"string\": new Hash\n    };\n}\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function mapCacheDelete(key) {\n    var result = getMapData(this, key)[\"delete\"](key);\n    this.size -= result ? 1 : 0;\n    return result;\n}\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function mapCacheGet(key) {\n    return getMapData(this, key).get(key);\n}\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function mapCacheHas(key) {\n    return getMapData(this, key).has(key);\n}\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */ function mapCacheSet(key, value) {\n    var data = getMapData(this, key), size = data.size;\n    data.set(key, value);\n    this.size += data.size == size ? 0 : 1;\n    return this;\n}\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype[\"delete\"] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n/**\n *\n * Creates an array cache object to store unique values.\n *\n * @private\n * @constructor\n * @param {Array} [values] The values to cache.\n */ function SetCache(values) {\n    var index = -1, length = values == null ? 0 : values.length;\n    this.__data__ = new MapCache;\n    while(++index < length){\n        this.add(values[index]);\n    }\n}\n/**\n * Adds `value` to the array cache.\n *\n * @private\n * @name add\n * @memberOf SetCache\n * @alias push\n * @param {*} value The value to cache.\n * @returns {Object} Returns the cache instance.\n */ function setCacheAdd(value) {\n    this.__data__.set(value, HASH_UNDEFINED);\n    return this;\n}\n/**\n * Checks if `value` is in the array cache.\n *\n * @private\n * @name has\n * @memberOf SetCache\n * @param {*} value The value to search for.\n * @returns {number} Returns `true` if `value` is found, else `false`.\n */ function setCacheHas(value) {\n    return this.__data__.has(value);\n}\n// Add methods to `SetCache`.\nSetCache.prototype.add = SetCache.prototype.push = setCacheAdd;\nSetCache.prototype.has = setCacheHas;\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function Stack(entries) {\n    var data = this.__data__ = new ListCache(entries);\n    this.size = data.size;\n}\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */ function stackClear() {\n    this.__data__ = new ListCache;\n    this.size = 0;\n}\n/**\n * Removes `key` and its value from the stack.\n *\n * @private\n * @name delete\n * @memberOf Stack\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function stackDelete(key) {\n    var data = this.__data__, result = data[\"delete\"](key);\n    this.size = data.size;\n    return result;\n}\n/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function stackGet(key) {\n    return this.__data__.get(key);\n}\n/**\n * Checks if a stack value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Stack\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function stackHas(key) {\n    return this.__data__.has(key);\n}\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */ function stackSet(key, value) {\n    var data = this.__data__;\n    if (data instanceof ListCache) {\n        var pairs = data.__data__;\n        if (!Map || pairs.length < LARGE_ARRAY_SIZE - 1) {\n            pairs.push([\n                key,\n                value\n            ]);\n            this.size = ++data.size;\n            return this;\n        }\n        data = this.__data__ = new MapCache(pairs);\n    }\n    data.set(key, value);\n    this.size = data.size;\n    return this;\n}\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype[\"delete\"] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */ function arrayLikeKeys(value, inherited) {\n    var isArr = isArray(value), isArg = !isArr && isArguments(value), isBuff = !isArr && !isArg && isBuffer(value), isType = !isArr && !isArg && !isBuff && isTypedArray(value), skipIndexes = isArr || isArg || isBuff || isType, result = skipIndexes ? baseTimes(value.length, String) : [], length = result.length;\n    for(var key in value){\n        if ((inherited || hasOwnProperty.call(value, key)) && !(skipIndexes && // Safari 9 has enumerable `arguments.length` in strict mode.\n        (key == \"length\" || // Node.js 0.10 has enumerable non-index properties on buffers.\n        isBuff && (key == \"offset\" || key == \"parent\") || // PhantomJS 2 has enumerable non-index properties on typed arrays.\n        isType && (key == \"buffer\" || key == \"byteLength\" || key == \"byteOffset\") || // Skip index properties.\n        isIndex(key, length)))) {\n            result.push(key);\n        }\n    }\n    return result;\n}\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */ function assocIndexOf(array, key) {\n    var length = array.length;\n    while(length--){\n        if (eq(array[length][0], key)) {\n            return length;\n        }\n    }\n    return -1;\n}\n/**\n * The base implementation of `getAllKeys` and `getAllKeysIn` which uses\n * `keysFunc` and `symbolsFunc` to get the enumerable property names and\n * symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @param {Function} symbolsFunc The function to get the symbols of `object`.\n * @returns {Array} Returns the array of property names and symbols.\n */ function baseGetAllKeys(object, keysFunc, symbolsFunc) {\n    var result = keysFunc(object);\n    return isArray(object) ? result : arrayPush(result, symbolsFunc(object));\n}\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */ function baseGetTag(value) {\n    if (value == null) {\n        return value === undefined ? undefinedTag : nullTag;\n    }\n    return symToStringTag && symToStringTag in Object(value) ? getRawTag(value) : objectToString(value);\n}\n/**\n * The base implementation of `_.isArguments`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n */ function baseIsArguments(value) {\n    return isObjectLike(value) && baseGetTag(value) == argsTag;\n}\n/**\n * The base implementation of `_.isEqual` which supports partial comparisons\n * and tracks traversed objects.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @param {boolean} bitmask The bitmask flags.\n *  1 - Unordered comparison\n *  2 - Partial comparison\n * @param {Function} [customizer] The function to customize comparisons.\n * @param {Object} [stack] Tracks traversed `value` and `other` objects.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n */ function baseIsEqual(value, other, bitmask, customizer, stack) {\n    if (value === other) {\n        return true;\n    }\n    if (value == null || other == null || !isObjectLike(value) && !isObjectLike(other)) {\n        return value !== value && other !== other;\n    }\n    return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);\n}\n/**\n * A specialized version of `baseIsEqual` for arrays and objects which performs\n * deep comparisons and tracks traversed objects enabling objects with circular\n * references to be compared.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} [stack] Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */ function baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {\n    var objIsArr = isArray(object), othIsArr = isArray(other), objTag = objIsArr ? arrayTag : getTag(object), othTag = othIsArr ? arrayTag : getTag(other);\n    objTag = objTag == argsTag ? objectTag : objTag;\n    othTag = othTag == argsTag ? objectTag : othTag;\n    var objIsObj = objTag == objectTag, othIsObj = othTag == objectTag, isSameTag = objTag == othTag;\n    if (isSameTag && isBuffer(object)) {\n        if (!isBuffer(other)) {\n            return false;\n        }\n        objIsArr = true;\n        objIsObj = false;\n    }\n    if (isSameTag && !objIsObj) {\n        stack || (stack = new Stack);\n        return objIsArr || isTypedArray(object) ? equalArrays(object, other, bitmask, customizer, equalFunc, stack) : equalByTag(object, other, objTag, bitmask, customizer, equalFunc, stack);\n    }\n    if (!(bitmask & COMPARE_PARTIAL_FLAG)) {\n        var objIsWrapped = objIsObj && hasOwnProperty.call(object, \"__wrapped__\"), othIsWrapped = othIsObj && hasOwnProperty.call(other, \"__wrapped__\");\n        if (objIsWrapped || othIsWrapped) {\n            var objUnwrapped = objIsWrapped ? object.value() : object, othUnwrapped = othIsWrapped ? other.value() : other;\n            stack || (stack = new Stack);\n            return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);\n        }\n    }\n    if (!isSameTag) {\n        return false;\n    }\n    stack || (stack = new Stack);\n    return equalObjects(object, other, bitmask, customizer, equalFunc, stack);\n}\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */ function baseIsNative(value) {\n    if (!isObject(value) || isMasked(value)) {\n        return false;\n    }\n    var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\n    return pattern.test(toSource(value));\n}\n/**\n * The base implementation of `_.isTypedArray` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n */ function baseIsTypedArray(value) {\n    return isObjectLike(value) && isLength(value.length) && !!typedArrayTags[baseGetTag(value)];\n}\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */ function baseKeys(object) {\n    if (!isPrototype(object)) {\n        return nativeKeys(object);\n    }\n    var result = [];\n    for(var key in Object(object)){\n        if (hasOwnProperty.call(object, key) && key != \"constructor\") {\n            result.push(key);\n        }\n    }\n    return result;\n}\n/**\n * A specialized version of `baseIsEqualDeep` for arrays with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Array} array The array to compare.\n * @param {Array} other The other array to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `array` and `other` objects.\n * @returns {boolean} Returns `true` if the arrays are equivalent, else `false`.\n */ function equalArrays(array, other, bitmask, customizer, equalFunc, stack) {\n    var isPartial = bitmask & COMPARE_PARTIAL_FLAG, arrLength = array.length, othLength = other.length;\n    if (arrLength != othLength && !(isPartial && othLength > arrLength)) {\n        return false;\n    }\n    // Assume cyclic values are equal.\n    var stacked = stack.get(array);\n    if (stacked && stack.get(other)) {\n        return stacked == other;\n    }\n    var index = -1, result = true, seen = bitmask & COMPARE_UNORDERED_FLAG ? new SetCache : undefined;\n    stack.set(array, other);\n    stack.set(other, array);\n    // Ignore non-index properties.\n    while(++index < arrLength){\n        var arrValue = array[index], othValue = other[index];\n        if (customizer) {\n            var compared = isPartial ? customizer(othValue, arrValue, index, other, array, stack) : customizer(arrValue, othValue, index, array, other, stack);\n        }\n        if (compared !== undefined) {\n            if (compared) {\n                continue;\n            }\n            result = false;\n            break;\n        }\n        // Recursively compare arrays (susceptible to call stack limits).\n        if (seen) {\n            if (!arraySome(other, function(othValue, othIndex) {\n                if (!cacheHas(seen, othIndex) && (arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n                    return seen.push(othIndex);\n                }\n            })) {\n                result = false;\n                break;\n            }\n        } else if (!(arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n            result = false;\n            break;\n        }\n    }\n    stack[\"delete\"](array);\n    stack[\"delete\"](other);\n    return result;\n}\n/**\n * A specialized version of `baseIsEqualDeep` for comparing objects of\n * the same `toStringTag`.\n *\n * **Note:** This function only supports comparing values with tags of\n * `Boolean`, `Date`, `Error`, `Number`, `RegExp`, or `String`.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {string} tag The `toStringTag` of the objects to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */ function equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {\n    switch(tag){\n        case dataViewTag:\n            if (object.byteLength != other.byteLength || object.byteOffset != other.byteOffset) {\n                return false;\n            }\n            object = object.buffer;\n            other = other.buffer;\n        case arrayBufferTag:\n            if (object.byteLength != other.byteLength || !equalFunc(new Uint8Array(object), new Uint8Array(other))) {\n                return false;\n            }\n            return true;\n        case boolTag:\n        case dateTag:\n        case numberTag:\n            // Coerce booleans to `1` or `0` and dates to milliseconds.\n            // Invalid dates are coerced to `NaN`.\n            return eq(+object, +other);\n        case errorTag:\n            return object.name == other.name && object.message == other.message;\n        case regexpTag:\n        case stringTag:\n            // Coerce regexes to strings and treat strings, primitives and objects,\n            // as equal. See http://www.ecma-international.org/ecma-262/7.0/#sec-regexp.prototype.tostring\n            // for more details.\n            return object == other + \"\";\n        case mapTag:\n            var convert = mapToArray;\n        case setTag:\n            var isPartial = bitmask & COMPARE_PARTIAL_FLAG;\n            convert || (convert = setToArray);\n            if (object.size != other.size && !isPartial) {\n                return false;\n            }\n            // Assume cyclic values are equal.\n            var stacked = stack.get(object);\n            if (stacked) {\n                return stacked == other;\n            }\n            bitmask |= COMPARE_UNORDERED_FLAG;\n            // Recursively compare objects (susceptible to call stack limits).\n            stack.set(object, other);\n            var result = equalArrays(convert(object), convert(other), bitmask, customizer, equalFunc, stack);\n            stack[\"delete\"](object);\n            return result;\n        case symbolTag:\n            if (symbolValueOf) {\n                return symbolValueOf.call(object) == symbolValueOf.call(other);\n            }\n    }\n    return false;\n}\n/**\n * A specialized version of `baseIsEqualDeep` for objects with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */ function equalObjects(object, other, bitmask, customizer, equalFunc, stack) {\n    var isPartial = bitmask & COMPARE_PARTIAL_FLAG, objProps = getAllKeys(object), objLength = objProps.length, othProps = getAllKeys(other), othLength = othProps.length;\n    if (objLength != othLength && !isPartial) {\n        return false;\n    }\n    var index = objLength;\n    while(index--){\n        var key = objProps[index];\n        if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {\n            return false;\n        }\n    }\n    // Assume cyclic values are equal.\n    var stacked = stack.get(object);\n    if (stacked && stack.get(other)) {\n        return stacked == other;\n    }\n    var result = true;\n    stack.set(object, other);\n    stack.set(other, object);\n    var skipCtor = isPartial;\n    while(++index < objLength){\n        key = objProps[index];\n        var objValue = object[key], othValue = other[key];\n        if (customizer) {\n            var compared = isPartial ? customizer(othValue, objValue, key, other, object, stack) : customizer(objValue, othValue, key, object, other, stack);\n        }\n        // Recursively compare objects (susceptible to call stack limits).\n        if (!(compared === undefined ? objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack) : compared)) {\n            result = false;\n            break;\n        }\n        skipCtor || (skipCtor = key == \"constructor\");\n    }\n    if (result && !skipCtor) {\n        var objCtor = object.constructor, othCtor = other.constructor;\n        // Non `Object` object instances with different constructors are not equal.\n        if (objCtor != othCtor && \"constructor\" in object && \"constructor\" in other && !(typeof objCtor == \"function\" && objCtor instanceof objCtor && typeof othCtor == \"function\" && othCtor instanceof othCtor)) {\n            result = false;\n        }\n    }\n    stack[\"delete\"](object);\n    stack[\"delete\"](other);\n    return result;\n}\n/**\n * Creates an array of own enumerable property names and symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names and symbols.\n */ function getAllKeys(object) {\n    return baseGetAllKeys(object, keys, getSymbols);\n}\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */ function getMapData(map, key) {\n    var data = map.__data__;\n    return isKeyable(key) ? data[typeof key == \"string\" ? \"string\" : \"hash\"] : data.map;\n}\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */ function getNative(object, key) {\n    var value = getValue(object, key);\n    return baseIsNative(value) ? value : undefined;\n}\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */ function getRawTag(value) {\n    var isOwn = hasOwnProperty.call(value, symToStringTag), tag = value[symToStringTag];\n    try {\n        value[symToStringTag] = undefined;\n        var unmasked = true;\n    } catch (e) {}\n    var result = nativeObjectToString.call(value);\n    if (unmasked) {\n        if (isOwn) {\n            value[symToStringTag] = tag;\n        } else {\n            delete value[symToStringTag];\n        }\n    }\n    return result;\n}\n/**\n * Creates an array of the own enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */ var getSymbols = !nativeGetSymbols ? stubArray : function(object) {\n    if (object == null) {\n        return [];\n    }\n    object = Object(object);\n    return arrayFilter(nativeGetSymbols(object), function(symbol) {\n        return propertyIsEnumerable.call(object, symbol);\n    });\n};\n/**\n * Gets the `toStringTag` of `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */ var getTag = baseGetTag;\n// Fallback for data views, maps, sets, and weak maps in IE 11 and promises in Node.js < 6.\nif (DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag || Map && getTag(new Map) != mapTag || Promise && getTag(Promise.resolve()) != promiseTag || Set && getTag(new Set) != setTag || WeakMap && getTag(new WeakMap) != weakMapTag) {\n    getTag = function(value) {\n        var result = baseGetTag(value), Ctor = result == objectTag ? value.constructor : undefined, ctorString = Ctor ? toSource(Ctor) : \"\";\n        if (ctorString) {\n            switch(ctorString){\n                case dataViewCtorString:\n                    return dataViewTag;\n                case mapCtorString:\n                    return mapTag;\n                case promiseCtorString:\n                    return promiseTag;\n                case setCtorString:\n                    return setTag;\n                case weakMapCtorString:\n                    return weakMapTag;\n            }\n        }\n        return result;\n    };\n}\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */ function isIndex(value, length) {\n    length = length == null ? MAX_SAFE_INTEGER : length;\n    return !!length && (typeof value == \"number\" || reIsUint.test(value)) && value > -1 && value % 1 == 0 && value < length;\n}\n/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */ function isKeyable(value) {\n    var type = typeof value;\n    return type == \"string\" || type == \"number\" || type == \"symbol\" || type == \"boolean\" ? value !== \"__proto__\" : value === null;\n}\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */ function isMasked(func) {\n    return !!maskSrcKey && maskSrcKey in func;\n}\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */ function isPrototype(value) {\n    var Ctor = value && value.constructor, proto = typeof Ctor == \"function\" && Ctor.prototype || objectProto;\n    return value === proto;\n}\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */ function objectToString(value) {\n    return nativeObjectToString.call(value);\n}\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to convert.\n * @returns {string} Returns the source code.\n */ function toSource(func) {\n    if (func != null) {\n        try {\n            return funcToString.call(func);\n        } catch (e) {}\n        try {\n            return func + \"\";\n        } catch (e) {}\n    }\n    return \"\";\n}\n/**\n * Performs a\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * comparison between two values to determine if they are equivalent.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.eq(object, object);\n * // => true\n *\n * _.eq(object, other);\n * // => false\n *\n * _.eq('a', 'a');\n * // => true\n *\n * _.eq('a', Object('a'));\n * // => false\n *\n * _.eq(NaN, NaN);\n * // => true\n */ function eq(value, other) {\n    return value === other || value !== value && other !== other;\n}\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */ var isArguments = baseIsArguments(function() {\n    return arguments;\n}()) ? baseIsArguments : function(value) {\n    return isObjectLike(value) && hasOwnProperty.call(value, \"callee\") && !propertyIsEnumerable.call(value, \"callee\");\n};\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */ var isArray = Array.isArray;\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */ function isArrayLike(value) {\n    return value != null && isLength(value.length) && !isFunction(value);\n}\n/**\n * Checks if `value` is a buffer.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\n * @example\n *\n * _.isBuffer(new Buffer(2));\n * // => true\n *\n * _.isBuffer(new Uint8Array(2));\n * // => false\n */ var isBuffer = nativeIsBuffer || stubFalse;\n/**\n * Performs a deep comparison between two values to determine if they are\n * equivalent.\n *\n * **Note:** This method supports comparing arrays, array buffers, booleans,\n * date objects, error objects, maps, numbers, `Object` objects, regexes,\n * sets, strings, symbols, and typed arrays. `Object` objects are compared\n * by their own, not inherited, enumerable properties. Functions and DOM\n * nodes are compared by strict equality, i.e. `===`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.isEqual(object, other);\n * // => true\n *\n * object === other;\n * // => false\n */ function isEqual(value, other) {\n    return baseIsEqual(value, other);\n}\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */ function isFunction(value) {\n    if (!isObject(value)) {\n        return false;\n    }\n    // The use of `Object#toString` avoids issues with the `typeof` operator\n    // in Safari 9 which returns 'object' for typed arrays and other constructors.\n    var tag = baseGetTag(value);\n    return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */ function isLength(value) {\n    return typeof value == \"number\" && value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */ function isObject(value) {\n    var type = typeof value;\n    return value != null && (type == \"object\" || type == \"function\");\n}\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */ function isObjectLike(value) {\n    return value != null && typeof value == \"object\";\n}\n/**\n * Checks if `value` is classified as a typed array.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n * @example\n *\n * _.isTypedArray(new Uint8Array);\n * // => true\n *\n * _.isTypedArray([]);\n * // => false\n */ var isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;\n/**\n * Creates an array of the own enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects. See the\n * [ES spec](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * for more details.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keys(new Foo);\n * // => ['a', 'b'] (iteration order is not guaranteed)\n *\n * _.keys('hi');\n * // => ['0', '1']\n */ function keys(object) {\n    return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);\n}\n/**\n * This method returns a new empty array.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {Array} Returns the new empty array.\n * @example\n *\n * var arrays = _.times(2, _.stubArray);\n *\n * console.log(arrays);\n * // => [[], []]\n *\n * console.log(arrays[0] === arrays[1]);\n * // => false\n */ function stubArray() {\n    return [];\n}\n/**\n * This method returns `false`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `false`.\n * @example\n *\n * _.times(2, _.stubFalse);\n * // => [false, false]\n */ function stubFalse() {\n    return false;\n}\nmodule.exports = isEqual;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lodash.isequal/index.js\n");

/***/ })

};
;