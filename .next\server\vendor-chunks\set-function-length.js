"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/set-function-length";
exports.ids = ["vendor-chunks/set-function-length"];
exports.modules = {

/***/ "(ssr)/./node_modules/set-function-length/index.js":
/*!***************************************************!*\
  !*** ./node_modules/set-function-length/index.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar GetIntrinsic = __webpack_require__(/*! get-intrinsic */ \"(ssr)/./node_modules/get-intrinsic/index.js\");\nvar define = __webpack_require__(/*! define-data-property */ \"(ssr)/./node_modules/define-data-property/index.js\");\nvar hasDescriptors = __webpack_require__(/*! has-property-descriptors */ \"(ssr)/./node_modules/has-property-descriptors/index.js\")();\nvar gOPD = __webpack_require__(/*! gopd */ \"(ssr)/./node_modules/gopd/index.js\");\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(ssr)/./node_modules/es-errors/type.js\");\nvar $floor = GetIntrinsic(\"%Math.floor%\");\n/** @type {import('.')} */ module.exports = function setFunctionLength(fn, length) {\n    if (typeof fn !== \"function\") {\n        throw new $TypeError(\"`fn` is not a function\");\n    }\n    if (typeof length !== \"number\" || length < 0 || length > 0xFFFFFFFF || $floor(length) !== length) {\n        throw new $TypeError(\"`length` must be a positive 32-bit integer\");\n    }\n    var loose = arguments.length > 2 && !!arguments[2];\n    var functionLengthIsConfigurable = true;\n    var functionLengthIsWritable = true;\n    if (\"length\" in fn && gOPD) {\n        var desc = gOPD(fn, \"length\");\n        if (desc && !desc.configurable) {\n            functionLengthIsConfigurable = false;\n        }\n        if (desc && !desc.writable) {\n            functionLengthIsWritable = false;\n        }\n    }\n    if (functionLengthIsConfigurable || functionLengthIsWritable || !loose) {\n        if (hasDescriptors) {\n            define(/** @type {Parameters<define>[0]} */ fn, \"length\", length, true, true);\n        } else {\n            define(/** @type {Parameters<define>[0]} */ fn, \"length\", length);\n        }\n    }\n    return fn;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/set-function-length/index.js\n");

/***/ })

};
;