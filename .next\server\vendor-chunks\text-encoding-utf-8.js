"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/text-encoding-utf-8";
exports.ids = ["vendor-chunks/text-encoding-utf-8"];
exports.modules = {

/***/ "(ssr)/./node_modules/text-encoding-utf-8/lib/encoding.lib.js":
/*!**************************************************************!*\
  !*** ./node_modules/text-encoding-utf-8/lib/encoding.lib.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// This is free and unencumbered software released into the public domain.\n// See LICENSE.md for more information.\n//\n// Utilities\n//\n/**\n * @param {number} a The number to test.\n * @param {number} min The minimum value in the range, inclusive.\n * @param {number} max The maximum value in the range, inclusive.\n * @return {boolean} True if a >= min and a <= max.\n */ function inRange(a, min, max) {\n    return min <= a && a <= max;\n}\n/**\n * @param {*} o\n * @return {Object}\n */ function ToDictionary(o) {\n    if (o === undefined) return {};\n    if (o === Object(o)) return o;\n    throw TypeError(\"Could not convert argument to dictionary\");\n}\n/**\n * @param {string} string Input string of UTF-16 code units.\n * @return {!Array.<number>} Code points.\n */ function stringToCodePoints(string) {\n    // https://heycam.github.io/webidl/#dfn-obtain-unicode\n    // 1. Let S be the DOMString value.\n    var s = String(string);\n    // 2. Let n be the length of S.\n    var n = s.length;\n    // 3. Initialize i to 0.\n    var i = 0;\n    // 4. Initialize U to be an empty sequence of Unicode characters.\n    var u = [];\n    // 5. While i < n:\n    while(i < n){\n        // 1. Let c be the code unit in S at index i.\n        var c = s.charCodeAt(i);\n        // 2. Depending on the value of c:\n        // c < 0xD800 or c > 0xDFFF\n        if (c < 0xD800 || c > 0xDFFF) {\n            // Append to U the Unicode character with code point c.\n            u.push(c);\n        } else if (0xDC00 <= c && c <= 0xDFFF) {\n            // Append to U a U+FFFD REPLACEMENT CHARACTER.\n            u.push(0xFFFD);\n        } else if (0xD800 <= c && c <= 0xDBFF) {\n            // 1. If i = n−1, then append to U a U+FFFD REPLACEMENT\n            // CHARACTER.\n            if (i === n - 1) {\n                u.push(0xFFFD);\n            } else {\n                // 1. Let d be the code unit in S at index i+1.\n                var d = string.charCodeAt(i + 1);\n                // 2. If 0xDC00 ≤ d ≤ 0xDFFF, then:\n                if (0xDC00 <= d && d <= 0xDFFF) {\n                    // 1. Let a be c & 0x3FF.\n                    var a = c & 0x3FF;\n                    // 2. Let b be d & 0x3FF.\n                    var b = d & 0x3FF;\n                    // 3. Append to U the Unicode character with code point\n                    // 2^16+2^10*a+b.\n                    u.push(0x10000 + (a << 10) + b);\n                    // 4. Set i to i+1.\n                    i += 1;\n                } else {\n                    u.push(0xFFFD);\n                }\n            }\n        }\n        // 3. Set i to i+1.\n        i += 1;\n    }\n    // 6. Return U.\n    return u;\n}\n/**\n * @param {!Array.<number>} code_points Array of code points.\n * @return {string} string String of UTF-16 code units.\n */ function codePointsToString(code_points) {\n    var s = \"\";\n    for(var i = 0; i < code_points.length; ++i){\n        var cp = code_points[i];\n        if (cp <= 0xFFFF) {\n            s += String.fromCharCode(cp);\n        } else {\n            cp -= 0x10000;\n            s += String.fromCharCode((cp >> 10) + 0xD800, (cp & 0x3FF) + 0xDC00);\n        }\n    }\n    return s;\n}\n//\n// Implementation of Encoding specification\n// https://encoding.spec.whatwg.org/\n//\n//\n// 3. Terminology\n//\n/**\n * End-of-stream is a special token that signifies no more tokens\n * are in the stream.\n * @const\n */ var end_of_stream = -1;\n/**\n * A stream represents an ordered sequence of tokens.\n *\n * @constructor\n * @param {!(Array.<number>|Uint8Array)} tokens Array of tokens that provide the\n * stream.\n */ function Stream(tokens) {\n    /** @type {!Array.<number>} */ this.tokens = [].slice.call(tokens);\n}\nStream.prototype = {\n    /**\n   * @return {boolean} True if end-of-stream has been hit.\n   */ endOfStream: function() {\n        return !this.tokens.length;\n    },\n    /**\n   * When a token is read from a stream, the first token in the\n   * stream must be returned and subsequently removed, and\n   * end-of-stream must be returned otherwise.\n   *\n   * @return {number} Get the next token from the stream, or\n   * end_of_stream.\n   */ read: function() {\n        if (!this.tokens.length) return end_of_stream;\n        return this.tokens.shift();\n    },\n    /**\n   * When one or more tokens are prepended to a stream, those tokens\n   * must be inserted, in given order, before the first token in the\n   * stream.\n   *\n   * @param {(number|!Array.<number>)} token The token(s) to prepend to the stream.\n   */ prepend: function(token) {\n        if (Array.isArray(token)) {\n            var tokens = /**@type {!Array.<number>}*/ token;\n            while(tokens.length)this.tokens.unshift(tokens.pop());\n        } else {\n            this.tokens.unshift(token);\n        }\n    },\n    /**\n   * When one or more tokens are pushed to a stream, those tokens\n   * must be inserted, in given order, after the last token in the\n   * stream.\n   *\n   * @param {(number|!Array.<number>)} token The tokens(s) to prepend to the stream.\n   */ push: function(token) {\n        if (Array.isArray(token)) {\n            var tokens = /**@type {!Array.<number>}*/ token;\n            while(tokens.length)this.tokens.push(tokens.shift());\n        } else {\n            this.tokens.push(token);\n        }\n    }\n};\n//\n// 4. Encodings\n//\n// 4.1 Encoders and decoders\n/** @const */ var finished = -1;\n/**\n * @param {boolean} fatal If true, decoding errors raise an exception.\n * @param {number=} opt_code_point Override the standard fallback code point.\n * @return {number} The code point to insert on a decoding error.\n */ function decoderError(fatal, opt_code_point) {\n    if (fatal) throw TypeError(\"Decoder error\");\n    return opt_code_point || 0xFFFD;\n}\n//\n// 7. API\n//\n/** @const */ var DEFAULT_ENCODING = \"utf-8\";\n// 7.1 Interface TextDecoder\n/**\n * @constructor\n * @param {string=} encoding The label of the encoding;\n *     defaults to 'utf-8'.\n * @param {Object=} options\n */ function TextDecoder(encoding, options) {\n    if (!(this instanceof TextDecoder)) {\n        return new TextDecoder(encoding, options);\n    }\n    encoding = encoding !== undefined ? String(encoding).toLowerCase() : DEFAULT_ENCODING;\n    if (encoding !== DEFAULT_ENCODING) {\n        throw new Error(\"Encoding not supported. Only utf-8 is supported\");\n    }\n    options = ToDictionary(options);\n    /** @private @type {boolean} */ this._streaming = false;\n    /** @private @type {boolean} */ this._BOMseen = false;\n    /** @private @type {?Decoder} */ this._decoder = null;\n    /** @private @type {boolean} */ this._fatal = Boolean(options[\"fatal\"]);\n    /** @private @type {boolean} */ this._ignoreBOM = Boolean(options[\"ignoreBOM\"]);\n    Object.defineProperty(this, \"encoding\", {\n        value: \"utf-8\"\n    });\n    Object.defineProperty(this, \"fatal\", {\n        value: this._fatal\n    });\n    Object.defineProperty(this, \"ignoreBOM\", {\n        value: this._ignoreBOM\n    });\n}\nTextDecoder.prototype = {\n    /**\n   * @param {ArrayBufferView=} input The buffer of bytes to decode.\n   * @param {Object=} options\n   * @return {string} The decoded string.\n   */ decode: function decode(input, options) {\n        var bytes;\n        if (typeof input === \"object\" && input instanceof ArrayBuffer) {\n            bytes = new Uint8Array(input);\n        } else if (typeof input === \"object\" && \"buffer\" in input && input.buffer instanceof ArrayBuffer) {\n            bytes = new Uint8Array(input.buffer, input.byteOffset, input.byteLength);\n        } else {\n            bytes = new Uint8Array(0);\n        }\n        options = ToDictionary(options);\n        if (!this._streaming) {\n            this._decoder = new UTF8Decoder({\n                fatal: this._fatal\n            });\n            this._BOMseen = false;\n        }\n        this._streaming = Boolean(options[\"stream\"]);\n        var input_stream = new Stream(bytes);\n        var code_points = [];\n        /** @type {?(number|!Array.<number>)} */ var result;\n        while(!input_stream.endOfStream()){\n            result = this._decoder.handler(input_stream, input_stream.read());\n            if (result === finished) break;\n            if (result === null) continue;\n            if (Array.isArray(result)) code_points.push.apply(code_points, /**@type {!Array.<number>}*/ result);\n            else code_points.push(result);\n        }\n        if (!this._streaming) {\n            do {\n                result = this._decoder.handler(input_stream, input_stream.read());\n                if (result === finished) break;\n                if (result === null) continue;\n                if (Array.isArray(result)) code_points.push.apply(code_points, /**@type {!Array.<number>}*/ result);\n                else code_points.push(result);\n            }while (!input_stream.endOfStream());\n            this._decoder = null;\n        }\n        if (code_points.length) {\n            // If encoding is one of utf-8, utf-16be, and utf-16le, and\n            // ignore BOM flag and BOM seen flag are unset, run these\n            // subsubsteps:\n            if ([\n                \"utf-8\"\n            ].indexOf(this.encoding) !== -1 && !this._ignoreBOM && !this._BOMseen) {\n                // If token is U+FEFF, set BOM seen flag.\n                if (code_points[0] === 0xFEFF) {\n                    this._BOMseen = true;\n                    code_points.shift();\n                } else {\n                    // Otherwise, if token is not end-of-stream, set BOM seen\n                    // flag and append token to output.\n                    this._BOMseen = true;\n                }\n            }\n        }\n        return codePointsToString(code_points);\n    }\n};\n// 7.2 Interface TextEncoder\n/**\n * @constructor\n * @param {string=} encoding The label of the encoding;\n *     defaults to 'utf-8'.\n * @param {Object=} options\n */ function TextEncoder(encoding, options) {\n    if (!(this instanceof TextEncoder)) return new TextEncoder(encoding, options);\n    encoding = encoding !== undefined ? String(encoding).toLowerCase() : DEFAULT_ENCODING;\n    if (encoding !== DEFAULT_ENCODING) {\n        throw new Error(\"Encoding not supported. Only utf-8 is supported\");\n    }\n    options = ToDictionary(options);\n    /** @private @type {boolean} */ this._streaming = false;\n    /** @private @type {?Encoder} */ this._encoder = null;\n    /** @private @type {{fatal: boolean}} */ this._options = {\n        fatal: Boolean(options[\"fatal\"])\n    };\n    Object.defineProperty(this, \"encoding\", {\n        value: \"utf-8\"\n    });\n}\nTextEncoder.prototype = {\n    /**\n   * @param {string=} opt_string The string to encode.\n   * @param {Object=} options\n   * @return {Uint8Array} Encoded bytes, as a Uint8Array.\n   */ encode: function encode(opt_string, options) {\n        opt_string = opt_string ? String(opt_string) : \"\";\n        options = ToDictionary(options);\n        // NOTE: This option is nonstandard. None of the encodings\n        // permitted for encoding (i.e. UTF-8, UTF-16) are stateful,\n        // so streaming is not necessary.\n        if (!this._streaming) this._encoder = new UTF8Encoder(this._options);\n        this._streaming = Boolean(options[\"stream\"]);\n        var bytes = [];\n        var input_stream = new Stream(stringToCodePoints(opt_string));\n        /** @type {?(number|!Array.<number>)} */ var result;\n        while(!input_stream.endOfStream()){\n            result = this._encoder.handler(input_stream, input_stream.read());\n            if (result === finished) break;\n            if (Array.isArray(result)) bytes.push.apply(bytes, /**@type {!Array.<number>}*/ result);\n            else bytes.push(result);\n        }\n        if (!this._streaming) {\n            while(true){\n                result = this._encoder.handler(input_stream, input_stream.read());\n                if (result === finished) break;\n                if (Array.isArray(result)) bytes.push.apply(bytes, /**@type {!Array.<number>}*/ result);\n                else bytes.push(result);\n            }\n            this._encoder = null;\n        }\n        return new Uint8Array(bytes);\n    }\n};\n//\n// 8. The encoding\n//\n// 8.1 utf-8\n/**\n * @constructor\n * @implements {Decoder}\n * @param {{fatal: boolean}} options\n */ function UTF8Decoder(options) {\n    var fatal = options.fatal;\n    // utf-8's decoder's has an associated utf-8 code point, utf-8\n    // bytes seen, and utf-8 bytes needed (all initially 0), a utf-8\n    // lower boundary (initially 0x80), and a utf-8 upper boundary\n    // (initially 0xBF).\n    var /** @type {number} */ utf8_code_point = 0, /** @type {number} */ utf8_bytes_seen = 0, /** @type {number} */ utf8_bytes_needed = 0, /** @type {number} */ utf8_lower_boundary = 0x80, /** @type {number} */ utf8_upper_boundary = 0xBF;\n    /**\n   * @param {Stream} stream The stream of bytes being decoded.\n   * @param {number} bite The next byte read from the stream.\n   * @return {?(number|!Array.<number>)} The next code point(s)\n   *     decoded, or null if not enough data exists in the input\n   *     stream to decode a complete code point.\n   */ this.handler = function(stream, bite) {\n        // 1. If byte is end-of-stream and utf-8 bytes needed is not 0,\n        // set utf-8 bytes needed to 0 and return error.\n        if (bite === end_of_stream && utf8_bytes_needed !== 0) {\n            utf8_bytes_needed = 0;\n            return decoderError(fatal);\n        }\n        // 2. If byte is end-of-stream, return finished.\n        if (bite === end_of_stream) return finished;\n        // 3. If utf-8 bytes needed is 0, based on byte:\n        if (utf8_bytes_needed === 0) {\n            // 0x00 to 0x7F\n            if (inRange(bite, 0x00, 0x7F)) {\n                // Return a code point whose value is byte.\n                return bite;\n            }\n            // 0xC2 to 0xDF\n            if (inRange(bite, 0xC2, 0xDF)) {\n                // Set utf-8 bytes needed to 1 and utf-8 code point to byte\n                // − 0xC0.\n                utf8_bytes_needed = 1;\n                utf8_code_point = bite - 0xC0;\n            } else if (inRange(bite, 0xE0, 0xEF)) {\n                // 1. If byte is 0xE0, set utf-8 lower boundary to 0xA0.\n                if (bite === 0xE0) utf8_lower_boundary = 0xA0;\n                // 2. If byte is 0xED, set utf-8 upper boundary to 0x9F.\n                if (bite === 0xED) utf8_upper_boundary = 0x9F;\n                // 3. Set utf-8 bytes needed to 2 and utf-8 code point to\n                // byte − 0xE0.\n                utf8_bytes_needed = 2;\n                utf8_code_point = bite - 0xE0;\n            } else if (inRange(bite, 0xF0, 0xF4)) {\n                // 1. If byte is 0xF0, set utf-8 lower boundary to 0x90.\n                if (bite === 0xF0) utf8_lower_boundary = 0x90;\n                // 2. If byte is 0xF4, set utf-8 upper boundary to 0x8F.\n                if (bite === 0xF4) utf8_upper_boundary = 0x8F;\n                // 3. Set utf-8 bytes needed to 3 and utf-8 code point to\n                // byte − 0xF0.\n                utf8_bytes_needed = 3;\n                utf8_code_point = bite - 0xF0;\n            } else {\n                // Return error.\n                return decoderError(fatal);\n            }\n            // Then (byte is in the range 0xC2 to 0xF4) set utf-8 code\n            // point to utf-8 code point << (6 × utf-8 bytes needed) and\n            // return continue.\n            utf8_code_point = utf8_code_point << 6 * utf8_bytes_needed;\n            return null;\n        }\n        // 4. If byte is not in the range utf-8 lower boundary to utf-8\n        // upper boundary, run these substeps:\n        if (!inRange(bite, utf8_lower_boundary, utf8_upper_boundary)) {\n            // 1. Set utf-8 code point, utf-8 bytes needed, and utf-8\n            // bytes seen to 0, set utf-8 lower boundary to 0x80, and set\n            // utf-8 upper boundary to 0xBF.\n            utf8_code_point = utf8_bytes_needed = utf8_bytes_seen = 0;\n            utf8_lower_boundary = 0x80;\n            utf8_upper_boundary = 0xBF;\n            // 2. Prepend byte to stream.\n            stream.prepend(bite);\n            // 3. Return error.\n            return decoderError(fatal);\n        }\n        // 5. Set utf-8 lower boundary to 0x80 and utf-8 upper boundary\n        // to 0xBF.\n        utf8_lower_boundary = 0x80;\n        utf8_upper_boundary = 0xBF;\n        // 6. Increase utf-8 bytes seen by one and set utf-8 code point\n        // to utf-8 code point + (byte − 0x80) << (6 × (utf-8 bytes\n        // needed − utf-8 bytes seen)).\n        utf8_bytes_seen += 1;\n        utf8_code_point += bite - 0x80 << 6 * (utf8_bytes_needed - utf8_bytes_seen);\n        // 7. If utf-8 bytes seen is not equal to utf-8 bytes needed,\n        // continue.\n        if (utf8_bytes_seen !== utf8_bytes_needed) return null;\n        // 8. Let code point be utf-8 code point.\n        var code_point = utf8_code_point;\n        // 9. Set utf-8 code point, utf-8 bytes needed, and utf-8 bytes\n        // seen to 0.\n        utf8_code_point = utf8_bytes_needed = utf8_bytes_seen = 0;\n        // 10. Return a code point whose value is code point.\n        return code_point;\n    };\n}\n/**\n * @constructor\n * @implements {Encoder}\n * @param {{fatal: boolean}} options\n */ function UTF8Encoder(options) {\n    var fatal = options.fatal;\n    /**\n   * @param {Stream} stream Input stream.\n   * @param {number} code_point Next code point read from the stream.\n   * @return {(number|!Array.<number>)} Byte(s) to emit.\n   */ this.handler = function(stream, code_point) {\n        // 1. If code point is end-of-stream, return finished.\n        if (code_point === end_of_stream) return finished;\n        // 2. If code point is in the range U+0000 to U+007F, return a\n        // byte whose value is code point.\n        if (inRange(code_point, 0x0000, 0x007f)) return code_point;\n        // 3. Set count and offset based on the range code point is in:\n        var count, offset;\n        // U+0080 to U+07FF:    1 and 0xC0\n        if (inRange(code_point, 0x0080, 0x07FF)) {\n            count = 1;\n            offset = 0xC0;\n        } else if (inRange(code_point, 0x0800, 0xFFFF)) {\n            count = 2;\n            offset = 0xE0;\n        } else if (inRange(code_point, 0x10000, 0x10FFFF)) {\n            count = 3;\n            offset = 0xF0;\n        }\n        // 4.Let bytes be a byte sequence whose first byte is (code\n        // point >> (6 × count)) + offset.\n        var bytes = [\n            (code_point >> 6 * count) + offset\n        ];\n        // 5. Run these substeps while count is greater than 0:\n        while(count > 0){\n            // 1. Set temp to code point >> (6 × (count − 1)).\n            var temp = code_point >> 6 * (count - 1);\n            // 2. Append to bytes 0x80 | (temp & 0x3F).\n            bytes.push(0x80 | temp & 0x3F);\n            // 3. Decrease count by one.\n            count -= 1;\n        }\n        // 6. Return bytes bytes, in order.\n        return bytes;\n    };\n}\nexports.TextEncoder = TextEncoder;\nexports.TextDecoder = TextDecoder;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdGV4dC1lbmNvZGluZy11dGYtOC9saWIvZW5jb2RpbmcubGliLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsMEVBQTBFO0FBQzFFLHVDQUF1QztBQUV2QyxFQUFFO0FBQ0YsWUFBWTtBQUNaLEVBQUU7QUFFRjs7Ozs7Q0FLQyxHQUNELFNBQVNBLFFBQVFDLENBQUMsRUFBRUMsR0FBRyxFQUFFQyxHQUFHO0lBQzFCLE9BQU9ELE9BQU9ELEtBQUtBLEtBQUtFO0FBQzFCO0FBRUE7OztDQUdDLEdBQ0QsU0FBU0MsYUFBYUMsQ0FBQztJQUNyQixJQUFJQSxNQUFNQyxXQUFXLE9BQU8sQ0FBQztJQUM3QixJQUFJRCxNQUFNRSxPQUFPRixJQUFJLE9BQU9BO0lBQzVCLE1BQU1HLFVBQVU7QUFDbEI7QUFFQTs7O0NBR0MsR0FDRCxTQUFTQyxtQkFBbUJDLE1BQU07SUFDaEMsc0RBQXNEO0lBRXRELG1DQUFtQztJQUNuQyxJQUFJQyxJQUFJQyxPQUFPRjtJQUVmLCtCQUErQjtJQUMvQixJQUFJRyxJQUFJRixFQUFFRyxNQUFNO0lBRWhCLHdCQUF3QjtJQUN4QixJQUFJQyxJQUFJO0lBRVIsaUVBQWlFO0lBQ2pFLElBQUlDLElBQUksRUFBRTtJQUVWLGtCQUFrQjtJQUNsQixNQUFPRCxJQUFJRixFQUFHO1FBRVosNkNBQTZDO1FBQzdDLElBQUlJLElBQUlOLEVBQUVPLFVBQVUsQ0FBQ0g7UUFFckIsa0NBQWtDO1FBRWxDLDJCQUEyQjtRQUMzQixJQUFJRSxJQUFJLFVBQVVBLElBQUksUUFBUTtZQUM1Qix1REFBdUQ7WUFDdkRELEVBQUVHLElBQUksQ0FBQ0Y7UUFDVCxPQUdLLElBQUksVUFBVUEsS0FBS0EsS0FBSyxRQUFRO1lBQ25DLDhDQUE4QztZQUM5Q0QsRUFBRUcsSUFBSSxDQUFDO1FBQ1QsT0FHSyxJQUFJLFVBQVVGLEtBQUtBLEtBQUssUUFBUTtZQUNuQyx1REFBdUQ7WUFDdkQsYUFBYTtZQUNiLElBQUlGLE1BQU1GLElBQUksR0FBRztnQkFDZkcsRUFBRUcsSUFBSSxDQUFDO1lBQ1QsT0FFSztnQkFDSCwrQ0FBK0M7Z0JBQy9DLElBQUlDLElBQUlWLE9BQU9RLFVBQVUsQ0FBQ0gsSUFBSTtnQkFFOUIsbUNBQW1DO2dCQUNuQyxJQUFJLFVBQVVLLEtBQUtBLEtBQUssUUFBUTtvQkFDOUIseUJBQXlCO29CQUN6QixJQUFJbkIsSUFBSWdCLElBQUk7b0JBRVoseUJBQXlCO29CQUN6QixJQUFJSSxJQUFJRCxJQUFJO29CQUVaLHVEQUF1RDtvQkFDdkQsaUJBQWlCO29CQUNqQkosRUFBRUcsSUFBSSxDQUFDLFVBQVdsQixDQUFBQSxLQUFLLEVBQUMsSUFBS29CO29CQUU3QixtQkFBbUI7b0JBQ25CTixLQUFLO2dCQUNQLE9BSU07b0JBQ0pDLEVBQUVHLElBQUksQ0FBQztnQkFDVDtZQUNGO1FBQ0Y7UUFFQSxtQkFBbUI7UUFDbkJKLEtBQUs7SUFDUDtJQUVBLGVBQWU7SUFDZixPQUFPQztBQUNUO0FBRUE7OztDQUdDLEdBQ0QsU0FBU00sbUJBQW1CQyxXQUFXO0lBQ3JDLElBQUlaLElBQUk7SUFDUixJQUFLLElBQUlJLElBQUksR0FBR0EsSUFBSVEsWUFBWVQsTUFBTSxFQUFFLEVBQUVDLEVBQUc7UUFDM0MsSUFBSVMsS0FBS0QsV0FBVyxDQUFDUixFQUFFO1FBQ3ZCLElBQUlTLE1BQU0sUUFBUTtZQUNoQmIsS0FBS0MsT0FBT2EsWUFBWSxDQUFDRDtRQUMzQixPQUFPO1lBQ0xBLE1BQU07WUFDTmIsS0FBS0MsT0FBT2EsWUFBWSxDQUFDLENBQUNELE1BQU0sRUFBQyxJQUFLLFFBQ2IsQ0FBQ0EsS0FBSyxLQUFJLElBQUs7UUFDMUM7SUFDRjtJQUNBLE9BQU9iO0FBQ1Q7QUFHQSxFQUFFO0FBQ0YsMkNBQTJDO0FBQzNDLG9DQUFvQztBQUNwQyxFQUFFO0FBRUYsRUFBRTtBQUNGLGlCQUFpQjtBQUNqQixFQUFFO0FBRUY7Ozs7Q0FJQyxHQUFHLElBQUllLGdCQUFnQixDQUFDO0FBRXpCOzs7Ozs7Q0FNQyxHQUNELFNBQVNDLE9BQU9DLE1BQU07SUFDcEIsNEJBQTRCLEdBQzVCLElBQUksQ0FBQ0EsTUFBTSxHQUFHLEVBQUUsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUNGO0FBQzlCO0FBRUFELE9BQU9JLFNBQVMsR0FBRztJQUNqQjs7R0FFQyxHQUNEQyxhQUFhO1FBQ1gsT0FBTyxDQUFDLElBQUksQ0FBQ0osTUFBTSxDQUFDZCxNQUFNO0lBQzVCO0lBRUE7Ozs7Ozs7R0FPQyxHQUNBbUIsTUFBTTtRQUNMLElBQUksQ0FBQyxJQUFJLENBQUNMLE1BQU0sQ0FBQ2QsTUFBTSxFQUNyQixPQUFPWTtRQUNSLE9BQU8sSUFBSSxDQUFDRSxNQUFNLENBQUNNLEtBQUs7SUFDMUI7SUFFRDs7Ozs7O0dBTUMsR0FDREMsU0FBUyxTQUFTQyxLQUFLO1FBQ3JCLElBQUlDLE1BQU1DLE9BQU8sQ0FBQ0YsUUFBUTtZQUN4QixJQUFJUixTQUFTLDBCQUEwQixHQUFHUTtZQUMxQyxNQUFPUixPQUFPZCxNQUFNLENBQ2xCLElBQUksQ0FBQ2MsTUFBTSxDQUFDVyxPQUFPLENBQUNYLE9BQU9ZLEdBQUc7UUFDbEMsT0FBTztZQUNMLElBQUksQ0FBQ1osTUFBTSxDQUFDVyxPQUFPLENBQUNIO1FBQ3RCO0lBQ0Y7SUFFQTs7Ozs7O0dBTUMsR0FDRGpCLE1BQU0sU0FBU2lCLEtBQUs7UUFDbEIsSUFBSUMsTUFBTUMsT0FBTyxDQUFDRixRQUFRO1lBQ3hCLElBQUlSLFNBQVMsMEJBQTBCLEdBQUdRO1lBQzFDLE1BQU9SLE9BQU9kLE1BQU0sQ0FDbEIsSUFBSSxDQUFDYyxNQUFNLENBQUNULElBQUksQ0FBQ1MsT0FBT00sS0FBSztRQUNqQyxPQUFPO1lBQ0wsSUFBSSxDQUFDTixNQUFNLENBQUNULElBQUksQ0FBQ2lCO1FBQ25CO0lBQ0Y7QUFDRjtBQUVBLEVBQUU7QUFDRixlQUFlO0FBQ2YsRUFBRTtBQUVGLDRCQUE0QjtBQUU1QixXQUFXLEdBQ1gsSUFBSUssV0FBVyxDQUFDO0FBRWhCOzs7O0NBSUMsR0FDRCxTQUFTQyxhQUFhQyxLQUFLLEVBQUVDLGNBQWM7SUFDekMsSUFBSUQsT0FDRixNQUFNbkMsVUFBVTtJQUNsQixPQUFPb0Msa0JBQWtCO0FBQzNCO0FBRUEsRUFBRTtBQUNGLFNBQVM7QUFDVCxFQUFFO0FBRUYsV0FBVyxHQUFHLElBQUlDLG1CQUFtQjtBQUVyQyw0QkFBNEI7QUFFNUI7Ozs7O0NBS0MsR0FDRCxTQUFTQyxZQUFZQyxRQUFRLEVBQUVDLE9BQU87SUFDcEMsSUFBSSxDQUFFLEtBQUksWUFBWUYsV0FBVSxHQUFJO1FBQ2xDLE9BQU8sSUFBSUEsWUFBWUMsVUFBVUM7SUFDbkM7SUFDQUQsV0FBV0EsYUFBYXpDLFlBQVlNLE9BQU9tQyxVQUFVRSxXQUFXLEtBQUtKO0lBQ3JFLElBQUlFLGFBQWFGLGtCQUFrQjtRQUNqQyxNQUFNLElBQUlLLE1BQU07SUFDbEI7SUFDQUYsVUFBVTVDLGFBQWE0QztJQUV2Qiw2QkFBNkIsR0FDN0IsSUFBSSxDQUFDRyxVQUFVLEdBQUc7SUFDbEIsNkJBQTZCLEdBQzdCLElBQUksQ0FBQ0MsUUFBUSxHQUFHO0lBQ2hCLDhCQUE4QixHQUM5QixJQUFJLENBQUNDLFFBQVEsR0FBRztJQUNoQiw2QkFBNkIsR0FDN0IsSUFBSSxDQUFDQyxNQUFNLEdBQUdDLFFBQVFQLE9BQU8sQ0FBQyxRQUFRO0lBQ3RDLDZCQUE2QixHQUM3QixJQUFJLENBQUNRLFVBQVUsR0FBR0QsUUFBUVAsT0FBTyxDQUFDLFlBQVk7SUFFOUN6QyxPQUFPa0QsY0FBYyxDQUFDLElBQUksRUFBRSxZQUFZO1FBQUNDLE9BQU87SUFBTztJQUN2RG5ELE9BQU9rRCxjQUFjLENBQUMsSUFBSSxFQUFFLFNBQVM7UUFBQ0MsT0FBTyxJQUFJLENBQUNKLE1BQU07SUFBQTtJQUN4RC9DLE9BQU9rRCxjQUFjLENBQUMsSUFBSSxFQUFFLGFBQWE7UUFBQ0MsT0FBTyxJQUFJLENBQUNGLFVBQVU7SUFBQTtBQUNsRTtBQUVBVixZQUFZZixTQUFTLEdBQUc7SUFDdEI7Ozs7R0FJQyxHQUNENEIsUUFBUSxTQUFTQSxPQUFPQyxLQUFLLEVBQUVaLE9BQU87UUFDcEMsSUFBSWE7UUFDSixJQUFJLE9BQU9ELFVBQVUsWUFBWUEsaUJBQWlCRSxhQUFhO1lBQzdERCxRQUFRLElBQUlFLFdBQVdIO1FBQ3pCLE9BQU8sSUFBSSxPQUFPQSxVQUFVLFlBQVksWUFBWUEsU0FDekNBLE1BQU1JLE1BQU0sWUFBWUYsYUFBYTtZQUM5Q0QsUUFBUSxJQUFJRSxXQUFXSCxNQUFNSSxNQUFNLEVBQ1pKLE1BQU1LLFVBQVUsRUFDaEJMLE1BQU1NLFVBQVU7UUFDekMsT0FBTztZQUNMTCxRQUFRLElBQUlFLFdBQVc7UUFDekI7UUFFQWYsVUFBVTVDLGFBQWE0QztRQUV2QixJQUFJLENBQUMsSUFBSSxDQUFDRyxVQUFVLEVBQUU7WUFDcEIsSUFBSSxDQUFDRSxRQUFRLEdBQUcsSUFBSWMsWUFBWTtnQkFBQ3hCLE9BQU8sSUFBSSxDQUFDVyxNQUFNO1lBQUE7WUFDbkQsSUFBSSxDQUFDRixRQUFRLEdBQUc7UUFDbEI7UUFDQSxJQUFJLENBQUNELFVBQVUsR0FBR0ksUUFBUVAsT0FBTyxDQUFDLFNBQVM7UUFFM0MsSUFBSW9CLGVBQWUsSUFBSXpDLE9BQU9rQztRQUU5QixJQUFJdEMsY0FBYyxFQUFFO1FBRXBCLHNDQUFzQyxHQUN0QyxJQUFJOEM7UUFFSixNQUFPLENBQUNELGFBQWFwQyxXQUFXLEdBQUk7WUFDbENxQyxTQUFTLElBQUksQ0FBQ2hCLFFBQVEsQ0FBQ2lCLE9BQU8sQ0FBQ0YsY0FBY0EsYUFBYW5DLElBQUk7WUFDOUQsSUFBSW9DLFdBQVc1QixVQUNiO1lBQ0YsSUFBSTRCLFdBQVcsTUFDYjtZQUNGLElBQUloQyxNQUFNQyxPQUFPLENBQUMrQixTQUNoQjlDLFlBQVlKLElBQUksQ0FBQ29ELEtBQUssQ0FBQ2hELGFBQWEsMEJBQTBCLEdBQUc4QztpQkFFakU5QyxZQUFZSixJQUFJLENBQUNrRDtRQUNyQjtRQUNBLElBQUksQ0FBQyxJQUFJLENBQUNsQixVQUFVLEVBQUU7WUFDcEIsR0FBRztnQkFDRGtCLFNBQVMsSUFBSSxDQUFDaEIsUUFBUSxDQUFDaUIsT0FBTyxDQUFDRixjQUFjQSxhQUFhbkMsSUFBSTtnQkFDOUQsSUFBSW9DLFdBQVc1QixVQUNiO2dCQUNGLElBQUk0QixXQUFXLE1BQ2I7Z0JBQ0YsSUFBSWhDLE1BQU1DLE9BQU8sQ0FBQytCLFNBQ2hCOUMsWUFBWUosSUFBSSxDQUFDb0QsS0FBSyxDQUFDaEQsYUFBYSwwQkFBMEIsR0FBRzhDO3FCQUVqRTlDLFlBQVlKLElBQUksQ0FBQ2tEO1lBQ3JCLFFBQVMsQ0FBQ0QsYUFBYXBDLFdBQVcsSUFBSTtZQUN0QyxJQUFJLENBQUNxQixRQUFRLEdBQUc7UUFDbEI7UUFFQSxJQUFJOUIsWUFBWVQsTUFBTSxFQUFFO1lBQ3RCLDJEQUEyRDtZQUMzRCx5REFBeUQ7WUFDekQsZUFBZTtZQUNmLElBQUk7Z0JBQUM7YUFBUSxDQUFDMEQsT0FBTyxDQUFDLElBQUksQ0FBQ3pCLFFBQVEsTUFBTSxDQUFDLEtBQ3RDLENBQUMsSUFBSSxDQUFDUyxVQUFVLElBQUksQ0FBQyxJQUFJLENBQUNKLFFBQVEsRUFBRTtnQkFDdEMseUNBQXlDO2dCQUN6QyxJQUFJN0IsV0FBVyxDQUFDLEVBQUUsS0FBSyxRQUFRO29CQUM3QixJQUFJLENBQUM2QixRQUFRLEdBQUc7b0JBQ2hCN0IsWUFBWVcsS0FBSztnQkFDbkIsT0FBTztvQkFDTCx5REFBeUQ7b0JBQ3pELG1DQUFtQztvQkFDbkMsSUFBSSxDQUFDa0IsUUFBUSxHQUFHO2dCQUNsQjtZQUNGO1FBQ0Y7UUFFQSxPQUFPOUIsbUJBQW1CQztJQUM1QjtBQUNGO0FBRUEsNEJBQTRCO0FBRTVCOzs7OztDQUtDLEdBQ0QsU0FBU2tELFlBQVkxQixRQUFRLEVBQUVDLE9BQU87SUFDcEMsSUFBSSxDQUFFLEtBQUksWUFBWXlCLFdBQVUsR0FDOUIsT0FBTyxJQUFJQSxZQUFZMUIsVUFBVUM7SUFDbkNELFdBQVdBLGFBQWF6QyxZQUFZTSxPQUFPbUMsVUFBVUUsV0FBVyxLQUFLSjtJQUNyRSxJQUFJRSxhQUFhRixrQkFBa0I7UUFDakMsTUFBTSxJQUFJSyxNQUFNO0lBQ2xCO0lBQ0FGLFVBQVU1QyxhQUFhNEM7SUFFdkIsNkJBQTZCLEdBQzdCLElBQUksQ0FBQ0csVUFBVSxHQUFHO0lBQ2xCLDhCQUE4QixHQUM5QixJQUFJLENBQUN1QixRQUFRLEdBQUc7SUFDaEIsc0NBQXNDLEdBQ3RDLElBQUksQ0FBQ0MsUUFBUSxHQUFHO1FBQUNoQyxPQUFPWSxRQUFRUCxPQUFPLENBQUMsUUFBUTtJQUFDO0lBRWpEekMsT0FBT2tELGNBQWMsQ0FBQyxJQUFJLEVBQUUsWUFBWTtRQUFDQyxPQUFPO0lBQU87QUFDekQ7QUFFQWUsWUFBWTFDLFNBQVMsR0FBRztJQUN0Qjs7OztHQUlDLEdBQ0Q2QyxRQUFRLFNBQVNBLE9BQU9DLFVBQVUsRUFBRTdCLE9BQU87UUFDekM2QixhQUFhQSxhQUFhakUsT0FBT2lFLGNBQWM7UUFDL0M3QixVQUFVNUMsYUFBYTRDO1FBRXZCLDBEQUEwRDtRQUMxRCw0REFBNEQ7UUFDNUQsaUNBQWlDO1FBQ2pDLElBQUksQ0FBQyxJQUFJLENBQUNHLFVBQVUsRUFDbEIsSUFBSSxDQUFDdUIsUUFBUSxHQUFHLElBQUlJLFlBQVksSUFBSSxDQUFDSCxRQUFRO1FBQy9DLElBQUksQ0FBQ3hCLFVBQVUsR0FBR0ksUUFBUVAsT0FBTyxDQUFDLFNBQVM7UUFFM0MsSUFBSWEsUUFBUSxFQUFFO1FBQ2QsSUFBSU8sZUFBZSxJQUFJekMsT0FBT2xCLG1CQUFtQm9FO1FBQ2pELHNDQUFzQyxHQUN0QyxJQUFJUjtRQUNKLE1BQU8sQ0FBQ0QsYUFBYXBDLFdBQVcsR0FBSTtZQUNsQ3FDLFNBQVMsSUFBSSxDQUFDSyxRQUFRLENBQUNKLE9BQU8sQ0FBQ0YsY0FBY0EsYUFBYW5DLElBQUk7WUFDOUQsSUFBSW9DLFdBQVc1QixVQUNiO1lBQ0YsSUFBSUosTUFBTUMsT0FBTyxDQUFDK0IsU0FDaEJSLE1BQU0xQyxJQUFJLENBQUNvRCxLQUFLLENBQUNWLE9BQU8sMEJBQTBCLEdBQUdRO2lCQUVyRFIsTUFBTTFDLElBQUksQ0FBQ2tEO1FBQ2Y7UUFDQSxJQUFJLENBQUMsSUFBSSxDQUFDbEIsVUFBVSxFQUFFO1lBQ3BCLE1BQU8sS0FBTTtnQkFDWGtCLFNBQVMsSUFBSSxDQUFDSyxRQUFRLENBQUNKLE9BQU8sQ0FBQ0YsY0FBY0EsYUFBYW5DLElBQUk7Z0JBQzlELElBQUlvQyxXQUFXNUIsVUFDYjtnQkFDRixJQUFJSixNQUFNQyxPQUFPLENBQUMrQixTQUNoQlIsTUFBTTFDLElBQUksQ0FBQ29ELEtBQUssQ0FBQ1YsT0FBTywwQkFBMEIsR0FBR1E7cUJBRXJEUixNQUFNMUMsSUFBSSxDQUFDa0Q7WUFDZjtZQUNBLElBQUksQ0FBQ0ssUUFBUSxHQUFHO1FBQ2xCO1FBQ0EsT0FBTyxJQUFJWCxXQUFXRjtJQUN4QjtBQUNGO0FBRUEsRUFBRTtBQUNGLGtCQUFrQjtBQUNsQixFQUFFO0FBRUYsWUFBWTtBQUVaOzs7O0NBSUMsR0FDRCxTQUFTTSxZQUFZbkIsT0FBTztJQUMxQixJQUFJTCxRQUFRSyxRQUFRTCxLQUFLO0lBRXpCLDhEQUE4RDtJQUM5RCxnRUFBZ0U7SUFDaEUsOERBQThEO0lBQzlELG9CQUFvQjtJQUNwQixJQUFJLG1CQUFtQixHQUFHb0Msa0JBQWtCLEdBQ3hDLG1CQUFtQixHQUFHQyxrQkFBa0IsR0FDeEMsbUJBQW1CLEdBQUdDLG9CQUFvQixHQUMxQyxtQkFBbUIsR0FBR0Msc0JBQXNCLE1BQzVDLG1CQUFtQixHQUFHQyxzQkFBc0I7SUFFaEQ7Ozs7OztHQU1DLEdBQ0QsSUFBSSxDQUFDYixPQUFPLEdBQUcsU0FBU2MsTUFBTSxFQUFFQyxJQUFJO1FBQ2xDLCtEQUErRDtRQUMvRCxnREFBZ0Q7UUFDaEQsSUFBSUEsU0FBUzNELGlCQUFpQnVELHNCQUFzQixHQUFHO1lBQ3JEQSxvQkFBb0I7WUFDcEIsT0FBT3ZDLGFBQWFDO1FBQ3RCO1FBRUEsZ0RBQWdEO1FBQ2hELElBQUkwQyxTQUFTM0QsZUFDWCxPQUFPZTtRQUVULGdEQUFnRDtRQUNoRCxJQUFJd0Msc0JBQXNCLEdBQUc7WUFFM0IsZUFBZTtZQUNmLElBQUlqRixRQUFRcUYsTUFBTSxNQUFNLE9BQU87Z0JBQzdCLDJDQUEyQztnQkFDM0MsT0FBT0E7WUFDVDtZQUVBLGVBQWU7WUFDZixJQUFJckYsUUFBUXFGLE1BQU0sTUFBTSxPQUFPO2dCQUM3QiwyREFBMkQ7Z0JBQzNELFVBQVU7Z0JBQ1ZKLG9CQUFvQjtnQkFDcEJGLGtCQUFrQk0sT0FBTztZQUMzQixPQUdLLElBQUlyRixRQUFRcUYsTUFBTSxNQUFNLE9BQU87Z0JBQ2xDLHdEQUF3RDtnQkFDeEQsSUFBSUEsU0FBUyxNQUNYSCxzQkFBc0I7Z0JBQ3hCLHdEQUF3RDtnQkFDeEQsSUFBSUcsU0FBUyxNQUNYRixzQkFBc0I7Z0JBQ3hCLHlEQUF5RDtnQkFDekQsZUFBZTtnQkFDZkYsb0JBQW9CO2dCQUNwQkYsa0JBQWtCTSxPQUFPO1lBQzNCLE9BR0ssSUFBSXJGLFFBQVFxRixNQUFNLE1BQU0sT0FBTztnQkFDbEMsd0RBQXdEO2dCQUN4RCxJQUFJQSxTQUFTLE1BQ1hILHNCQUFzQjtnQkFDeEIsd0RBQXdEO2dCQUN4RCxJQUFJRyxTQUFTLE1BQ1hGLHNCQUFzQjtnQkFDeEIseURBQXlEO2dCQUN6RCxlQUFlO2dCQUNmRixvQkFBb0I7Z0JBQ3BCRixrQkFBa0JNLE9BQU87WUFDM0IsT0FHSztnQkFDSCxnQkFBZ0I7Z0JBQ2hCLE9BQU8zQyxhQUFhQztZQUN0QjtZQUVBLDBEQUEwRDtZQUMxRCw0REFBNEQ7WUFDNUQsbUJBQW1CO1lBQ25Cb0Msa0JBQWtCQSxtQkFBb0IsSUFBSUU7WUFDMUMsT0FBTztRQUNUO1FBRUEsK0RBQStEO1FBQy9ELHNDQUFzQztRQUN0QyxJQUFJLENBQUNqRixRQUFRcUYsTUFBTUgscUJBQXFCQyxzQkFBc0I7WUFFNUQseURBQXlEO1lBQ3pELDZEQUE2RDtZQUM3RCxnQ0FBZ0M7WUFDaENKLGtCQUFrQkUsb0JBQW9CRCxrQkFBa0I7WUFDeERFLHNCQUFzQjtZQUN0QkMsc0JBQXNCO1lBRXRCLDZCQUE2QjtZQUM3QkMsT0FBT2pELE9BQU8sQ0FBQ2tEO1lBRWYsbUJBQW1CO1lBQ25CLE9BQU8zQyxhQUFhQztRQUN0QjtRQUVBLCtEQUErRDtRQUMvRCxXQUFXO1FBQ1h1QyxzQkFBc0I7UUFDdEJDLHNCQUFzQjtRQUV0QiwrREFBK0Q7UUFDL0QsMkRBQTJEO1FBQzNELCtCQUErQjtRQUMvQkgsbUJBQW1CO1FBQ25CRCxtQkFBbUIsT0FBUSxRQUFVLElBQUtFLENBQUFBLG9CQUFvQkQsZUFBYztRQUU1RSw2REFBNkQ7UUFDN0QsWUFBWTtRQUNaLElBQUlBLG9CQUFvQkMsbUJBQ3RCLE9BQU87UUFFVCx5Q0FBeUM7UUFDekMsSUFBSUssYUFBYVA7UUFFakIsK0RBQStEO1FBQy9ELGFBQWE7UUFDYkEsa0JBQWtCRSxvQkFBb0JELGtCQUFrQjtRQUV4RCxxREFBcUQ7UUFDckQsT0FBT007SUFDVDtBQUNGO0FBRUE7Ozs7Q0FJQyxHQUNELFNBQVNSLFlBQVk5QixPQUFPO0lBQzFCLElBQUlMLFFBQVFLLFFBQVFMLEtBQUs7SUFDekI7Ozs7R0FJQyxHQUNELElBQUksQ0FBQzJCLE9BQU8sR0FBRyxTQUFTYyxNQUFNLEVBQUVFLFVBQVU7UUFDeEMsc0RBQXNEO1FBQ3RELElBQUlBLGVBQWU1RCxlQUNqQixPQUFPZTtRQUVULDhEQUE4RDtRQUM5RCxrQ0FBa0M7UUFDbEMsSUFBSXpDLFFBQVFzRixZQUFZLFFBQVEsU0FDOUIsT0FBT0E7UUFFVCwrREFBK0Q7UUFDL0QsSUFBSUMsT0FBT0M7UUFDWCxrQ0FBa0M7UUFDbEMsSUFBSXhGLFFBQVFzRixZQUFZLFFBQVEsU0FBUztZQUN2Q0MsUUFBUTtZQUNSQyxTQUFTO1FBQ1gsT0FFSyxJQUFJeEYsUUFBUXNGLFlBQVksUUFBUSxTQUFTO1lBQzVDQyxRQUFRO1lBQ1JDLFNBQVM7UUFDWCxPQUVLLElBQUl4RixRQUFRc0YsWUFBWSxTQUFTLFdBQVc7WUFDL0NDLFFBQVE7WUFDUkMsU0FBUztRQUNYO1FBRUEsMkRBQTJEO1FBQzNELGtDQUFrQztRQUNsQyxJQUFJM0IsUUFBUTtZQUFFeUIsQ0FBQUEsY0FBZSxJQUFJQyxLQUFLLElBQUtDO1NBQU87UUFFbEQsdURBQXVEO1FBQ3ZELE1BQU9ELFFBQVEsRUFBRztZQUVoQixrREFBa0Q7WUFDbEQsSUFBSUUsT0FBT0gsY0FBZSxJQUFLQyxDQUFBQSxRQUFRO1lBRXZDLDJDQUEyQztZQUMzQzFCLE1BQU0xQyxJQUFJLENBQUMsT0FBUXNFLE9BQU87WUFFMUIsNEJBQTRCO1lBQzVCRixTQUFTO1FBQ1g7UUFFQSxtQ0FBbUM7UUFDbkMsT0FBTzFCO0lBQ1Q7QUFDRjtBQUVBNkIsbUJBQW1CLEdBQUdqQjtBQUN0QmlCLG1CQUFtQixHQUFHNUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvdGV4dC1lbmNvZGluZy11dGYtOC9saWIvZW5jb2RpbmcubGliLmpzP2RmZGMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG4vLyBUaGlzIGlzIGZyZWUgYW5kIHVuZW5jdW1iZXJlZCBzb2Z0d2FyZSByZWxlYXNlZCBpbnRvIHRoZSBwdWJsaWMgZG9tYWluLlxuLy8gU2VlIExJQ0VOU0UubWQgZm9yIG1vcmUgaW5mb3JtYXRpb24uXG5cbi8vXG4vLyBVdGlsaXRpZXNcbi8vXG5cbi8qKlxuICogQHBhcmFtIHtudW1iZXJ9IGEgVGhlIG51bWJlciB0byB0ZXN0LlxuICogQHBhcmFtIHtudW1iZXJ9IG1pbiBUaGUgbWluaW11bSB2YWx1ZSBpbiB0aGUgcmFuZ2UsIGluY2x1c2l2ZS5cbiAqIEBwYXJhbSB7bnVtYmVyfSBtYXggVGhlIG1heGltdW0gdmFsdWUgaW4gdGhlIHJhbmdlLCBpbmNsdXNpdmUuXG4gKiBAcmV0dXJuIHtib29sZWFufSBUcnVlIGlmIGEgPj0gbWluIGFuZCBhIDw9IG1heC5cbiAqL1xuZnVuY3Rpb24gaW5SYW5nZShhLCBtaW4sIG1heCkge1xuICByZXR1cm4gbWluIDw9IGEgJiYgYSA8PSBtYXg7XG59XG5cbi8qKlxuICogQHBhcmFtIHsqfSBvXG4gKiBAcmV0dXJuIHtPYmplY3R9XG4gKi9cbmZ1bmN0aW9uIFRvRGljdGlvbmFyeShvKSB7XG4gIGlmIChvID09PSB1bmRlZmluZWQpIHJldHVybiB7fTtcbiAgaWYgKG8gPT09IE9iamVjdChvKSkgcmV0dXJuIG87XG4gIHRocm93IFR5cGVFcnJvcignQ291bGQgbm90IGNvbnZlcnQgYXJndW1lbnQgdG8gZGljdGlvbmFyeScpO1xufVxuXG4vKipcbiAqIEBwYXJhbSB7c3RyaW5nfSBzdHJpbmcgSW5wdXQgc3RyaW5nIG9mIFVURi0xNiBjb2RlIHVuaXRzLlxuICogQHJldHVybiB7IUFycmF5LjxudW1iZXI+fSBDb2RlIHBvaW50cy5cbiAqL1xuZnVuY3Rpb24gc3RyaW5nVG9Db2RlUG9pbnRzKHN0cmluZykge1xuICAvLyBodHRwczovL2hleWNhbS5naXRodWIuaW8vd2ViaWRsLyNkZm4tb2J0YWluLXVuaWNvZGVcblxuICAvLyAxLiBMZXQgUyBiZSB0aGUgRE9NU3RyaW5nIHZhbHVlLlxuICB2YXIgcyA9IFN0cmluZyhzdHJpbmcpO1xuXG4gIC8vIDIuIExldCBuIGJlIHRoZSBsZW5ndGggb2YgUy5cbiAgdmFyIG4gPSBzLmxlbmd0aDtcblxuICAvLyAzLiBJbml0aWFsaXplIGkgdG8gMC5cbiAgdmFyIGkgPSAwO1xuXG4gIC8vIDQuIEluaXRpYWxpemUgVSB0byBiZSBhbiBlbXB0eSBzZXF1ZW5jZSBvZiBVbmljb2RlIGNoYXJhY3RlcnMuXG4gIHZhciB1ID0gW107XG5cbiAgLy8gNS4gV2hpbGUgaSA8IG46XG4gIHdoaWxlIChpIDwgbikge1xuXG4gICAgLy8gMS4gTGV0IGMgYmUgdGhlIGNvZGUgdW5pdCBpbiBTIGF0IGluZGV4IGkuXG4gICAgdmFyIGMgPSBzLmNoYXJDb2RlQXQoaSk7XG5cbiAgICAvLyAyLiBEZXBlbmRpbmcgb24gdGhlIHZhbHVlIG9mIGM6XG5cbiAgICAvLyBjIDwgMHhEODAwIG9yIGMgPiAweERGRkZcbiAgICBpZiAoYyA8IDB4RDgwMCB8fCBjID4gMHhERkZGKSB7XG4gICAgICAvLyBBcHBlbmQgdG8gVSB0aGUgVW5pY29kZSBjaGFyYWN0ZXIgd2l0aCBjb2RlIHBvaW50IGMuXG4gICAgICB1LnB1c2goYyk7XG4gICAgfVxuXG4gICAgLy8gMHhEQzAwIOKJpCBjIOKJpCAweERGRkZcbiAgICBlbHNlIGlmICgweERDMDAgPD0gYyAmJiBjIDw9IDB4REZGRikge1xuICAgICAgLy8gQXBwZW5kIHRvIFUgYSBVK0ZGRkQgUkVQTEFDRU1FTlQgQ0hBUkFDVEVSLlxuICAgICAgdS5wdXNoKDB4RkZGRCk7XG4gICAgfVxuXG4gICAgLy8gMHhEODAwIOKJpCBjIOKJpCAweERCRkZcbiAgICBlbHNlIGlmICgweEQ4MDAgPD0gYyAmJiBjIDw9IDB4REJGRikge1xuICAgICAgLy8gMS4gSWYgaSA9IG7iiJIxLCB0aGVuIGFwcGVuZCB0byBVIGEgVStGRkZEIFJFUExBQ0VNRU5UXG4gICAgICAvLyBDSEFSQUNURVIuXG4gICAgICBpZiAoaSA9PT0gbiAtIDEpIHtcbiAgICAgICAgdS5wdXNoKDB4RkZGRCk7XG4gICAgICB9XG4gICAgICAvLyAyLiBPdGhlcndpc2UsIGkgPCBu4oiSMTpcbiAgICAgIGVsc2Uge1xuICAgICAgICAvLyAxLiBMZXQgZCBiZSB0aGUgY29kZSB1bml0IGluIFMgYXQgaW5kZXggaSsxLlxuICAgICAgICB2YXIgZCA9IHN0cmluZy5jaGFyQ29kZUF0KGkgKyAxKTtcblxuICAgICAgICAvLyAyLiBJZiAweERDMDAg4omkIGQg4omkIDB4REZGRiwgdGhlbjpcbiAgICAgICAgaWYgKDB4REMwMCA8PSBkICYmIGQgPD0gMHhERkZGKSB7XG4gICAgICAgICAgLy8gMS4gTGV0IGEgYmUgYyAmIDB4M0ZGLlxuICAgICAgICAgIHZhciBhID0gYyAmIDB4M0ZGO1xuXG4gICAgICAgICAgLy8gMi4gTGV0IGIgYmUgZCAmIDB4M0ZGLlxuICAgICAgICAgIHZhciBiID0gZCAmIDB4M0ZGO1xuXG4gICAgICAgICAgLy8gMy4gQXBwZW5kIHRvIFUgdGhlIFVuaWNvZGUgY2hhcmFjdGVyIHdpdGggY29kZSBwb2ludFxuICAgICAgICAgIC8vIDJeMTYrMl4xMCphK2IuXG4gICAgICAgICAgdS5wdXNoKDB4MTAwMDAgKyAoYSA8PCAxMCkgKyBiKTtcblxuICAgICAgICAgIC8vIDQuIFNldCBpIHRvIGkrMS5cbiAgICAgICAgICBpICs9IDE7XG4gICAgICAgIH1cblxuICAgICAgICAvLyAzLiBPdGhlcndpc2UsIGQgPCAweERDMDAgb3IgZCA+IDB4REZGRi4gQXBwZW5kIHRvIFUgYVxuICAgICAgICAvLyBVK0ZGRkQgUkVQTEFDRU1FTlQgQ0hBUkFDVEVSLlxuICAgICAgICBlbHNlICB7XG4gICAgICAgICAgdS5wdXNoKDB4RkZGRCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyAzLiBTZXQgaSB0byBpKzEuXG4gICAgaSArPSAxO1xuICB9XG5cbiAgLy8gNi4gUmV0dXJuIFUuXG4gIHJldHVybiB1O1xufVxuXG4vKipcbiAqIEBwYXJhbSB7IUFycmF5LjxudW1iZXI+fSBjb2RlX3BvaW50cyBBcnJheSBvZiBjb2RlIHBvaW50cy5cbiAqIEByZXR1cm4ge3N0cmluZ30gc3RyaW5nIFN0cmluZyBvZiBVVEYtMTYgY29kZSB1bml0cy5cbiAqL1xuZnVuY3Rpb24gY29kZVBvaW50c1RvU3RyaW5nKGNvZGVfcG9pbnRzKSB7XG4gIHZhciBzID0gJyc7XG4gIGZvciAodmFyIGkgPSAwOyBpIDwgY29kZV9wb2ludHMubGVuZ3RoOyArK2kpIHtcbiAgICB2YXIgY3AgPSBjb2RlX3BvaW50c1tpXTtcbiAgICBpZiAoY3AgPD0gMHhGRkZGKSB7XG4gICAgICBzICs9IFN0cmluZy5mcm9tQ2hhckNvZGUoY3ApO1xuICAgIH0gZWxzZSB7XG4gICAgICBjcCAtPSAweDEwMDAwO1xuICAgICAgcyArPSBTdHJpbmcuZnJvbUNoYXJDb2RlKChjcCA+PiAxMCkgKyAweEQ4MDAsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGNwICYgMHgzRkYpICsgMHhEQzAwKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHM7XG59XG5cblxuLy9cbi8vIEltcGxlbWVudGF0aW9uIG9mIEVuY29kaW5nIHNwZWNpZmljYXRpb25cbi8vIGh0dHBzOi8vZW5jb2Rpbmcuc3BlYy53aGF0d2cub3JnL1xuLy9cblxuLy9cbi8vIDMuIFRlcm1pbm9sb2d5XG4vL1xuXG4vKipcbiAqIEVuZC1vZi1zdHJlYW0gaXMgYSBzcGVjaWFsIHRva2VuIHRoYXQgc2lnbmlmaWVzIG5vIG1vcmUgdG9rZW5zXG4gKiBhcmUgaW4gdGhlIHN0cmVhbS5cbiAqIEBjb25zdFxuICovIHZhciBlbmRfb2Zfc3RyZWFtID0gLTE7XG5cbi8qKlxuICogQSBzdHJlYW0gcmVwcmVzZW50cyBhbiBvcmRlcmVkIHNlcXVlbmNlIG9mIHRva2Vucy5cbiAqXG4gKiBAY29uc3RydWN0b3JcbiAqIEBwYXJhbSB7IShBcnJheS48bnVtYmVyPnxVaW50OEFycmF5KX0gdG9rZW5zIEFycmF5IG9mIHRva2VucyB0aGF0IHByb3ZpZGUgdGhlXG4gKiBzdHJlYW0uXG4gKi9cbmZ1bmN0aW9uIFN0cmVhbSh0b2tlbnMpIHtcbiAgLyoqIEB0eXBlIHshQXJyYXkuPG51bWJlcj59ICovXG4gIHRoaXMudG9rZW5zID0gW10uc2xpY2UuY2FsbCh0b2tlbnMpO1xufVxuXG5TdHJlYW0ucHJvdG90eXBlID0ge1xuICAvKipcbiAgICogQHJldHVybiB7Ym9vbGVhbn0gVHJ1ZSBpZiBlbmQtb2Ytc3RyZWFtIGhhcyBiZWVuIGhpdC5cbiAgICovXG4gIGVuZE9mU3RyZWFtOiBmdW5jdGlvbigpIHtcbiAgICByZXR1cm4gIXRoaXMudG9rZW5zLmxlbmd0aDtcbiAgfSxcblxuICAvKipcbiAgICogV2hlbiBhIHRva2VuIGlzIHJlYWQgZnJvbSBhIHN0cmVhbSwgdGhlIGZpcnN0IHRva2VuIGluIHRoZVxuICAgKiBzdHJlYW0gbXVzdCBiZSByZXR1cm5lZCBhbmQgc3Vic2VxdWVudGx5IHJlbW92ZWQsIGFuZFxuICAgKiBlbmQtb2Ytc3RyZWFtIG11c3QgYmUgcmV0dXJuZWQgb3RoZXJ3aXNlLlxuICAgKlxuICAgKiBAcmV0dXJuIHtudW1iZXJ9IEdldCB0aGUgbmV4dCB0b2tlbiBmcm9tIHRoZSBzdHJlYW0sIG9yXG4gICAqIGVuZF9vZl9zdHJlYW0uXG4gICAqL1xuICAgcmVhZDogZnVuY3Rpb24oKSB7XG4gICAgaWYgKCF0aGlzLnRva2Vucy5sZW5ndGgpXG4gICAgICByZXR1cm4gZW5kX29mX3N0cmVhbTtcbiAgICAgcmV0dXJuIHRoaXMudG9rZW5zLnNoaWZ0KCk7XG4gICB9LFxuXG4gIC8qKlxuICAgKiBXaGVuIG9uZSBvciBtb3JlIHRva2VucyBhcmUgcHJlcGVuZGVkIHRvIGEgc3RyZWFtLCB0aG9zZSB0b2tlbnNcbiAgICogbXVzdCBiZSBpbnNlcnRlZCwgaW4gZ2l2ZW4gb3JkZXIsIGJlZm9yZSB0aGUgZmlyc3QgdG9rZW4gaW4gdGhlXG4gICAqIHN0cmVhbS5cbiAgICpcbiAgICogQHBhcmFtIHsobnVtYmVyfCFBcnJheS48bnVtYmVyPil9IHRva2VuIFRoZSB0b2tlbihzKSB0byBwcmVwZW5kIHRvIHRoZSBzdHJlYW0uXG4gICAqL1xuICBwcmVwZW5kOiBmdW5jdGlvbih0b2tlbikge1xuICAgIGlmIChBcnJheS5pc0FycmF5KHRva2VuKSkge1xuICAgICAgdmFyIHRva2VucyA9IC8qKkB0eXBlIHshQXJyYXkuPG51bWJlcj59Ki8odG9rZW4pO1xuICAgICAgd2hpbGUgKHRva2Vucy5sZW5ndGgpXG4gICAgICAgIHRoaXMudG9rZW5zLnVuc2hpZnQodG9rZW5zLnBvcCgpKTtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy50b2tlbnMudW5zaGlmdCh0b2tlbik7XG4gICAgfVxuICB9LFxuXG4gIC8qKlxuICAgKiBXaGVuIG9uZSBvciBtb3JlIHRva2VucyBhcmUgcHVzaGVkIHRvIGEgc3RyZWFtLCB0aG9zZSB0b2tlbnNcbiAgICogbXVzdCBiZSBpbnNlcnRlZCwgaW4gZ2l2ZW4gb3JkZXIsIGFmdGVyIHRoZSBsYXN0IHRva2VuIGluIHRoZVxuICAgKiBzdHJlYW0uXG4gICAqXG4gICAqIEBwYXJhbSB7KG51bWJlcnwhQXJyYXkuPG51bWJlcj4pfSB0b2tlbiBUaGUgdG9rZW5zKHMpIHRvIHByZXBlbmQgdG8gdGhlIHN0cmVhbS5cbiAgICovXG4gIHB1c2g6IGZ1bmN0aW9uKHRva2VuKSB7XG4gICAgaWYgKEFycmF5LmlzQXJyYXkodG9rZW4pKSB7XG4gICAgICB2YXIgdG9rZW5zID0gLyoqQHR5cGUgeyFBcnJheS48bnVtYmVyPn0qLyh0b2tlbik7XG4gICAgICB3aGlsZSAodG9rZW5zLmxlbmd0aClcbiAgICAgICAgdGhpcy50b2tlbnMucHVzaCh0b2tlbnMuc2hpZnQoKSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRoaXMudG9rZW5zLnB1c2godG9rZW4pO1xuICAgIH1cbiAgfVxufTtcblxuLy9cbi8vIDQuIEVuY29kaW5nc1xuLy9cblxuLy8gNC4xIEVuY29kZXJzIGFuZCBkZWNvZGVyc1xuXG4vKiogQGNvbnN0ICovXG52YXIgZmluaXNoZWQgPSAtMTtcblxuLyoqXG4gKiBAcGFyYW0ge2Jvb2xlYW59IGZhdGFsIElmIHRydWUsIGRlY29kaW5nIGVycm9ycyByYWlzZSBhbiBleGNlcHRpb24uXG4gKiBAcGFyYW0ge251bWJlcj19IG9wdF9jb2RlX3BvaW50IE92ZXJyaWRlIHRoZSBzdGFuZGFyZCBmYWxsYmFjayBjb2RlIHBvaW50LlxuICogQHJldHVybiB7bnVtYmVyfSBUaGUgY29kZSBwb2ludCB0byBpbnNlcnQgb24gYSBkZWNvZGluZyBlcnJvci5cbiAqL1xuZnVuY3Rpb24gZGVjb2RlckVycm9yKGZhdGFsLCBvcHRfY29kZV9wb2ludCkge1xuICBpZiAoZmF0YWwpXG4gICAgdGhyb3cgVHlwZUVycm9yKCdEZWNvZGVyIGVycm9yJyk7XG4gIHJldHVybiBvcHRfY29kZV9wb2ludCB8fCAweEZGRkQ7XG59XG5cbi8vXG4vLyA3LiBBUElcbi8vXG5cbi8qKiBAY29uc3QgKi8gdmFyIERFRkFVTFRfRU5DT0RJTkcgPSAndXRmLTgnO1xuXG4vLyA3LjEgSW50ZXJmYWNlIFRleHREZWNvZGVyXG5cbi8qKlxuICogQGNvbnN0cnVjdG9yXG4gKiBAcGFyYW0ge3N0cmluZz19IGVuY29kaW5nIFRoZSBsYWJlbCBvZiB0aGUgZW5jb2Rpbmc7XG4gKiAgICAgZGVmYXVsdHMgdG8gJ3V0Zi04Jy5cbiAqIEBwYXJhbSB7T2JqZWN0PX0gb3B0aW9uc1xuICovXG5mdW5jdGlvbiBUZXh0RGVjb2RlcihlbmNvZGluZywgb3B0aW9ucykge1xuICBpZiAoISh0aGlzIGluc3RhbmNlb2YgVGV4dERlY29kZXIpKSB7XG4gICAgcmV0dXJuIG5ldyBUZXh0RGVjb2RlcihlbmNvZGluZywgb3B0aW9ucyk7XG4gIH1cbiAgZW5jb2RpbmcgPSBlbmNvZGluZyAhPT0gdW5kZWZpbmVkID8gU3RyaW5nKGVuY29kaW5nKS50b0xvd2VyQ2FzZSgpIDogREVGQVVMVF9FTkNPRElORztcbiAgaWYgKGVuY29kaW5nICE9PSBERUZBVUxUX0VOQ09ESU5HKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdFbmNvZGluZyBub3Qgc3VwcG9ydGVkLiBPbmx5IHV0Zi04IGlzIHN1cHBvcnRlZCcpO1xuICB9XG4gIG9wdGlvbnMgPSBUb0RpY3Rpb25hcnkob3B0aW9ucyk7XG5cbiAgLyoqIEBwcml2YXRlIEB0eXBlIHtib29sZWFufSAqL1xuICB0aGlzLl9zdHJlYW1pbmcgPSBmYWxzZTtcbiAgLyoqIEBwcml2YXRlIEB0eXBlIHtib29sZWFufSAqL1xuICB0aGlzLl9CT01zZWVuID0gZmFsc2U7XG4gIC8qKiBAcHJpdmF0ZSBAdHlwZSB7P0RlY29kZXJ9ICovXG4gIHRoaXMuX2RlY29kZXIgPSBudWxsO1xuICAvKiogQHByaXZhdGUgQHR5cGUge2Jvb2xlYW59ICovXG4gIHRoaXMuX2ZhdGFsID0gQm9vbGVhbihvcHRpb25zWydmYXRhbCddKTtcbiAgLyoqIEBwcml2YXRlIEB0eXBlIHtib29sZWFufSAqL1xuICB0aGlzLl9pZ25vcmVCT00gPSBCb29sZWFuKG9wdGlvbnNbJ2lnbm9yZUJPTSddKTtcblxuICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgJ2VuY29kaW5nJywge3ZhbHVlOiAndXRmLTgnfSk7XG4gIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCAnZmF0YWwnLCB7dmFsdWU6IHRoaXMuX2ZhdGFsfSk7XG4gIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCAnaWdub3JlQk9NJywge3ZhbHVlOiB0aGlzLl9pZ25vcmVCT019KTtcbn1cblxuVGV4dERlY29kZXIucHJvdG90eXBlID0ge1xuICAvKipcbiAgICogQHBhcmFtIHtBcnJheUJ1ZmZlclZpZXc9fSBpbnB1dCBUaGUgYnVmZmVyIG9mIGJ5dGVzIHRvIGRlY29kZS5cbiAgICogQHBhcmFtIHtPYmplY3Q9fSBvcHRpb25zXG4gICAqIEByZXR1cm4ge3N0cmluZ30gVGhlIGRlY29kZWQgc3RyaW5nLlxuICAgKi9cbiAgZGVjb2RlOiBmdW5jdGlvbiBkZWNvZGUoaW5wdXQsIG9wdGlvbnMpIHtcbiAgICB2YXIgYnl0ZXM7XG4gICAgaWYgKHR5cGVvZiBpbnB1dCA9PT0gJ29iamVjdCcgJiYgaW5wdXQgaW5zdGFuY2VvZiBBcnJheUJ1ZmZlcikge1xuICAgICAgYnl0ZXMgPSBuZXcgVWludDhBcnJheShpbnB1dCk7XG4gICAgfSBlbHNlIGlmICh0eXBlb2YgaW5wdXQgPT09ICdvYmplY3QnICYmICdidWZmZXInIGluIGlucHV0ICYmXG4gICAgICAgICAgICAgICBpbnB1dC5idWZmZXIgaW5zdGFuY2VvZiBBcnJheUJ1ZmZlcikge1xuICAgICAgYnl0ZXMgPSBuZXcgVWludDhBcnJheShpbnB1dC5idWZmZXIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0LmJ5dGVPZmZzZXQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0LmJ5dGVMZW5ndGgpO1xuICAgIH0gZWxzZSB7XG4gICAgICBieXRlcyA9IG5ldyBVaW50OEFycmF5KDApO1xuICAgIH1cblxuICAgIG9wdGlvbnMgPSBUb0RpY3Rpb25hcnkob3B0aW9ucyk7XG5cbiAgICBpZiAoIXRoaXMuX3N0cmVhbWluZykge1xuICAgICAgdGhpcy5fZGVjb2RlciA9IG5ldyBVVEY4RGVjb2Rlcih7ZmF0YWw6IHRoaXMuX2ZhdGFsfSk7XG4gICAgICB0aGlzLl9CT01zZWVuID0gZmFsc2U7XG4gICAgfVxuICAgIHRoaXMuX3N0cmVhbWluZyA9IEJvb2xlYW4ob3B0aW9uc1snc3RyZWFtJ10pO1xuXG4gICAgdmFyIGlucHV0X3N0cmVhbSA9IG5ldyBTdHJlYW0oYnl0ZXMpO1xuXG4gICAgdmFyIGNvZGVfcG9pbnRzID0gW107XG5cbiAgICAvKiogQHR5cGUgez8obnVtYmVyfCFBcnJheS48bnVtYmVyPil9ICovXG4gICAgdmFyIHJlc3VsdDtcblxuICAgIHdoaWxlICghaW5wdXRfc3RyZWFtLmVuZE9mU3RyZWFtKCkpIHtcbiAgICAgIHJlc3VsdCA9IHRoaXMuX2RlY29kZXIuaGFuZGxlcihpbnB1dF9zdHJlYW0sIGlucHV0X3N0cmVhbS5yZWFkKCkpO1xuICAgICAgaWYgKHJlc3VsdCA9PT0gZmluaXNoZWQpXG4gICAgICAgIGJyZWFrO1xuICAgICAgaWYgKHJlc3VsdCA9PT0gbnVsbClcbiAgICAgICAgY29udGludWU7XG4gICAgICBpZiAoQXJyYXkuaXNBcnJheShyZXN1bHQpKVxuICAgICAgICBjb2RlX3BvaW50cy5wdXNoLmFwcGx5KGNvZGVfcG9pbnRzLCAvKipAdHlwZSB7IUFycmF5LjxudW1iZXI+fSovKHJlc3VsdCkpO1xuICAgICAgZWxzZVxuICAgICAgICBjb2RlX3BvaW50cy5wdXNoKHJlc3VsdCk7XG4gICAgfVxuICAgIGlmICghdGhpcy5fc3RyZWFtaW5nKSB7XG4gICAgICBkbyB7XG4gICAgICAgIHJlc3VsdCA9IHRoaXMuX2RlY29kZXIuaGFuZGxlcihpbnB1dF9zdHJlYW0sIGlucHV0X3N0cmVhbS5yZWFkKCkpO1xuICAgICAgICBpZiAocmVzdWx0ID09PSBmaW5pc2hlZClcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgaWYgKHJlc3VsdCA9PT0gbnVsbClcbiAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocmVzdWx0KSlcbiAgICAgICAgICBjb2RlX3BvaW50cy5wdXNoLmFwcGx5KGNvZGVfcG9pbnRzLCAvKipAdHlwZSB7IUFycmF5LjxudW1iZXI+fSovKHJlc3VsdCkpO1xuICAgICAgICBlbHNlXG4gICAgICAgICAgY29kZV9wb2ludHMucHVzaChyZXN1bHQpO1xuICAgICAgfSB3aGlsZSAoIWlucHV0X3N0cmVhbS5lbmRPZlN0cmVhbSgpKTtcbiAgICAgIHRoaXMuX2RlY29kZXIgPSBudWxsO1xuICAgIH1cblxuICAgIGlmIChjb2RlX3BvaW50cy5sZW5ndGgpIHtcbiAgICAgIC8vIElmIGVuY29kaW5nIGlzIG9uZSBvZiB1dGYtOCwgdXRmLTE2YmUsIGFuZCB1dGYtMTZsZSwgYW5kXG4gICAgICAvLyBpZ25vcmUgQk9NIGZsYWcgYW5kIEJPTSBzZWVuIGZsYWcgYXJlIHVuc2V0LCBydW4gdGhlc2VcbiAgICAgIC8vIHN1YnN1YnN0ZXBzOlxuICAgICAgaWYgKFsndXRmLTgnXS5pbmRleE9mKHRoaXMuZW5jb2RpbmcpICE9PSAtMSAmJlxuICAgICAgICAgICF0aGlzLl9pZ25vcmVCT00gJiYgIXRoaXMuX0JPTXNlZW4pIHtcbiAgICAgICAgLy8gSWYgdG9rZW4gaXMgVStGRUZGLCBzZXQgQk9NIHNlZW4gZmxhZy5cbiAgICAgICAgaWYgKGNvZGVfcG9pbnRzWzBdID09PSAweEZFRkYpIHtcbiAgICAgICAgICB0aGlzLl9CT01zZWVuID0gdHJ1ZTtcbiAgICAgICAgICBjb2RlX3BvaW50cy5zaGlmdCgpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIE90aGVyd2lzZSwgaWYgdG9rZW4gaXMgbm90IGVuZC1vZi1zdHJlYW0sIHNldCBCT00gc2VlblxuICAgICAgICAgIC8vIGZsYWcgYW5kIGFwcGVuZCB0b2tlbiB0byBvdXRwdXQuXG4gICAgICAgICAgdGhpcy5fQk9Nc2VlbiA9IHRydWU7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gY29kZVBvaW50c1RvU3RyaW5nKGNvZGVfcG9pbnRzKTtcbiAgfVxufTtcblxuLy8gNy4yIEludGVyZmFjZSBUZXh0RW5jb2RlclxuXG4vKipcbiAqIEBjb25zdHJ1Y3RvclxuICogQHBhcmFtIHtzdHJpbmc9fSBlbmNvZGluZyBUaGUgbGFiZWwgb2YgdGhlIGVuY29kaW5nO1xuICogICAgIGRlZmF1bHRzIHRvICd1dGYtOCcuXG4gKiBAcGFyYW0ge09iamVjdD19IG9wdGlvbnNcbiAqL1xuZnVuY3Rpb24gVGV4dEVuY29kZXIoZW5jb2RpbmcsIG9wdGlvbnMpIHtcbiAgaWYgKCEodGhpcyBpbnN0YW5jZW9mIFRleHRFbmNvZGVyKSlcbiAgICByZXR1cm4gbmV3IFRleHRFbmNvZGVyKGVuY29kaW5nLCBvcHRpb25zKTtcbiAgZW5jb2RpbmcgPSBlbmNvZGluZyAhPT0gdW5kZWZpbmVkID8gU3RyaW5nKGVuY29kaW5nKS50b0xvd2VyQ2FzZSgpIDogREVGQVVMVF9FTkNPRElORztcbiAgaWYgKGVuY29kaW5nICE9PSBERUZBVUxUX0VOQ09ESU5HKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdFbmNvZGluZyBub3Qgc3VwcG9ydGVkLiBPbmx5IHV0Zi04IGlzIHN1cHBvcnRlZCcpO1xuICB9XG4gIG9wdGlvbnMgPSBUb0RpY3Rpb25hcnkob3B0aW9ucyk7XG5cbiAgLyoqIEBwcml2YXRlIEB0eXBlIHtib29sZWFufSAqL1xuICB0aGlzLl9zdHJlYW1pbmcgPSBmYWxzZTtcbiAgLyoqIEBwcml2YXRlIEB0eXBlIHs/RW5jb2Rlcn0gKi9cbiAgdGhpcy5fZW5jb2RlciA9IG51bGw7XG4gIC8qKiBAcHJpdmF0ZSBAdHlwZSB7e2ZhdGFsOiBib29sZWFufX0gKi9cbiAgdGhpcy5fb3B0aW9ucyA9IHtmYXRhbDogQm9vbGVhbihvcHRpb25zWydmYXRhbCddKX07XG5cbiAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsICdlbmNvZGluZycsIHt2YWx1ZTogJ3V0Zi04J30pO1xufVxuXG5UZXh0RW5jb2Rlci5wcm90b3R5cGUgPSB7XG4gIC8qKlxuICAgKiBAcGFyYW0ge3N0cmluZz19IG9wdF9zdHJpbmcgVGhlIHN0cmluZyB0byBlbmNvZGUuXG4gICAqIEBwYXJhbSB7T2JqZWN0PX0gb3B0aW9uc1xuICAgKiBAcmV0dXJuIHtVaW50OEFycmF5fSBFbmNvZGVkIGJ5dGVzLCBhcyBhIFVpbnQ4QXJyYXkuXG4gICAqL1xuICBlbmNvZGU6IGZ1bmN0aW9uIGVuY29kZShvcHRfc3RyaW5nLCBvcHRpb25zKSB7XG4gICAgb3B0X3N0cmluZyA9IG9wdF9zdHJpbmcgPyBTdHJpbmcob3B0X3N0cmluZykgOiAnJztcbiAgICBvcHRpb25zID0gVG9EaWN0aW9uYXJ5KG9wdGlvbnMpO1xuXG4gICAgLy8gTk9URTogVGhpcyBvcHRpb24gaXMgbm9uc3RhbmRhcmQuIE5vbmUgb2YgdGhlIGVuY29kaW5nc1xuICAgIC8vIHBlcm1pdHRlZCBmb3IgZW5jb2RpbmcgKGkuZS4gVVRGLTgsIFVURi0xNikgYXJlIHN0YXRlZnVsLFxuICAgIC8vIHNvIHN0cmVhbWluZyBpcyBub3QgbmVjZXNzYXJ5LlxuICAgIGlmICghdGhpcy5fc3RyZWFtaW5nKVxuICAgICAgdGhpcy5fZW5jb2RlciA9IG5ldyBVVEY4RW5jb2Rlcih0aGlzLl9vcHRpb25zKTtcbiAgICB0aGlzLl9zdHJlYW1pbmcgPSBCb29sZWFuKG9wdGlvbnNbJ3N0cmVhbSddKTtcblxuICAgIHZhciBieXRlcyA9IFtdO1xuICAgIHZhciBpbnB1dF9zdHJlYW0gPSBuZXcgU3RyZWFtKHN0cmluZ1RvQ29kZVBvaW50cyhvcHRfc3RyaW5nKSk7XG4gICAgLyoqIEB0eXBlIHs/KG51bWJlcnwhQXJyYXkuPG51bWJlcj4pfSAqL1xuICAgIHZhciByZXN1bHQ7XG4gICAgd2hpbGUgKCFpbnB1dF9zdHJlYW0uZW5kT2ZTdHJlYW0oKSkge1xuICAgICAgcmVzdWx0ID0gdGhpcy5fZW5jb2Rlci5oYW5kbGVyKGlucHV0X3N0cmVhbSwgaW5wdXRfc3RyZWFtLnJlYWQoKSk7XG4gICAgICBpZiAocmVzdWx0ID09PSBmaW5pc2hlZClcbiAgICAgICAgYnJlYWs7XG4gICAgICBpZiAoQXJyYXkuaXNBcnJheShyZXN1bHQpKVxuICAgICAgICBieXRlcy5wdXNoLmFwcGx5KGJ5dGVzLCAvKipAdHlwZSB7IUFycmF5LjxudW1iZXI+fSovKHJlc3VsdCkpO1xuICAgICAgZWxzZVxuICAgICAgICBieXRlcy5wdXNoKHJlc3VsdCk7XG4gICAgfVxuICAgIGlmICghdGhpcy5fc3RyZWFtaW5nKSB7XG4gICAgICB3aGlsZSAodHJ1ZSkge1xuICAgICAgICByZXN1bHQgPSB0aGlzLl9lbmNvZGVyLmhhbmRsZXIoaW5wdXRfc3RyZWFtLCBpbnB1dF9zdHJlYW0ucmVhZCgpKTtcbiAgICAgICAgaWYgKHJlc3VsdCA9PT0gZmluaXNoZWQpXG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGlmIChBcnJheS5pc0FycmF5KHJlc3VsdCkpXG4gICAgICAgICAgYnl0ZXMucHVzaC5hcHBseShieXRlcywgLyoqQHR5cGUgeyFBcnJheS48bnVtYmVyPn0qLyhyZXN1bHQpKTtcbiAgICAgICAgZWxzZVxuICAgICAgICAgIGJ5dGVzLnB1c2gocmVzdWx0KTtcbiAgICAgIH1cbiAgICAgIHRoaXMuX2VuY29kZXIgPSBudWxsO1xuICAgIH1cbiAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoYnl0ZXMpO1xuICB9XG59O1xuXG4vL1xuLy8gOC4gVGhlIGVuY29kaW5nXG4vL1xuXG4vLyA4LjEgdXRmLThcblxuLyoqXG4gKiBAY29uc3RydWN0b3JcbiAqIEBpbXBsZW1lbnRzIHtEZWNvZGVyfVxuICogQHBhcmFtIHt7ZmF0YWw6IGJvb2xlYW59fSBvcHRpb25zXG4gKi9cbmZ1bmN0aW9uIFVURjhEZWNvZGVyKG9wdGlvbnMpIHtcbiAgdmFyIGZhdGFsID0gb3B0aW9ucy5mYXRhbDtcblxuICAvLyB1dGYtOCdzIGRlY29kZXIncyBoYXMgYW4gYXNzb2NpYXRlZCB1dGYtOCBjb2RlIHBvaW50LCB1dGYtOFxuICAvLyBieXRlcyBzZWVuLCBhbmQgdXRmLTggYnl0ZXMgbmVlZGVkIChhbGwgaW5pdGlhbGx5IDApLCBhIHV0Zi04XG4gIC8vIGxvd2VyIGJvdW5kYXJ5IChpbml0aWFsbHkgMHg4MCksIGFuZCBhIHV0Zi04IHVwcGVyIGJvdW5kYXJ5XG4gIC8vIChpbml0aWFsbHkgMHhCRikuXG4gIHZhciAvKiogQHR5cGUge251bWJlcn0gKi8gdXRmOF9jb2RlX3BvaW50ID0gMCxcbiAgICAgIC8qKiBAdHlwZSB7bnVtYmVyfSAqLyB1dGY4X2J5dGVzX3NlZW4gPSAwLFxuICAgICAgLyoqIEB0eXBlIHtudW1iZXJ9ICovIHV0ZjhfYnl0ZXNfbmVlZGVkID0gMCxcbiAgICAgIC8qKiBAdHlwZSB7bnVtYmVyfSAqLyB1dGY4X2xvd2VyX2JvdW5kYXJ5ID0gMHg4MCxcbiAgICAgIC8qKiBAdHlwZSB7bnVtYmVyfSAqLyB1dGY4X3VwcGVyX2JvdW5kYXJ5ID0gMHhCRjtcblxuICAvKipcbiAgICogQHBhcmFtIHtTdHJlYW19IHN0cmVhbSBUaGUgc3RyZWFtIG9mIGJ5dGVzIGJlaW5nIGRlY29kZWQuXG4gICAqIEBwYXJhbSB7bnVtYmVyfSBiaXRlIFRoZSBuZXh0IGJ5dGUgcmVhZCBmcm9tIHRoZSBzdHJlYW0uXG4gICAqIEByZXR1cm4gez8obnVtYmVyfCFBcnJheS48bnVtYmVyPil9IFRoZSBuZXh0IGNvZGUgcG9pbnQocylcbiAgICogICAgIGRlY29kZWQsIG9yIG51bGwgaWYgbm90IGVub3VnaCBkYXRhIGV4aXN0cyBpbiB0aGUgaW5wdXRcbiAgICogICAgIHN0cmVhbSB0byBkZWNvZGUgYSBjb21wbGV0ZSBjb2RlIHBvaW50LlxuICAgKi9cbiAgdGhpcy5oYW5kbGVyID0gZnVuY3Rpb24oc3RyZWFtLCBiaXRlKSB7XG4gICAgLy8gMS4gSWYgYnl0ZSBpcyBlbmQtb2Ytc3RyZWFtIGFuZCB1dGYtOCBieXRlcyBuZWVkZWQgaXMgbm90IDAsXG4gICAgLy8gc2V0IHV0Zi04IGJ5dGVzIG5lZWRlZCB0byAwIGFuZCByZXR1cm4gZXJyb3IuXG4gICAgaWYgKGJpdGUgPT09IGVuZF9vZl9zdHJlYW0gJiYgdXRmOF9ieXRlc19uZWVkZWQgIT09IDApIHtcbiAgICAgIHV0ZjhfYnl0ZXNfbmVlZGVkID0gMDtcbiAgICAgIHJldHVybiBkZWNvZGVyRXJyb3IoZmF0YWwpO1xuICAgIH1cblxuICAgIC8vIDIuIElmIGJ5dGUgaXMgZW5kLW9mLXN0cmVhbSwgcmV0dXJuIGZpbmlzaGVkLlxuICAgIGlmIChiaXRlID09PSBlbmRfb2Zfc3RyZWFtKVxuICAgICAgcmV0dXJuIGZpbmlzaGVkO1xuXG4gICAgLy8gMy4gSWYgdXRmLTggYnl0ZXMgbmVlZGVkIGlzIDAsIGJhc2VkIG9uIGJ5dGU6XG4gICAgaWYgKHV0ZjhfYnl0ZXNfbmVlZGVkID09PSAwKSB7XG5cbiAgICAgIC8vIDB4MDAgdG8gMHg3RlxuICAgICAgaWYgKGluUmFuZ2UoYml0ZSwgMHgwMCwgMHg3RikpIHtcbiAgICAgICAgLy8gUmV0dXJuIGEgY29kZSBwb2ludCB3aG9zZSB2YWx1ZSBpcyBieXRlLlxuICAgICAgICByZXR1cm4gYml0ZTtcbiAgICAgIH1cblxuICAgICAgLy8gMHhDMiB0byAweERGXG4gICAgICBpZiAoaW5SYW5nZShiaXRlLCAweEMyLCAweERGKSkge1xuICAgICAgICAvLyBTZXQgdXRmLTggYnl0ZXMgbmVlZGVkIHRvIDEgYW5kIHV0Zi04IGNvZGUgcG9pbnQgdG8gYnl0ZVxuICAgICAgICAvLyDiiJIgMHhDMC5cbiAgICAgICAgdXRmOF9ieXRlc19uZWVkZWQgPSAxO1xuICAgICAgICB1dGY4X2NvZGVfcG9pbnQgPSBiaXRlIC0gMHhDMDtcbiAgICAgIH1cblxuICAgICAgLy8gMHhFMCB0byAweEVGXG4gICAgICBlbHNlIGlmIChpblJhbmdlKGJpdGUsIDB4RTAsIDB4RUYpKSB7XG4gICAgICAgIC8vIDEuIElmIGJ5dGUgaXMgMHhFMCwgc2V0IHV0Zi04IGxvd2VyIGJvdW5kYXJ5IHRvIDB4QTAuXG4gICAgICAgIGlmIChiaXRlID09PSAweEUwKVxuICAgICAgICAgIHV0ZjhfbG93ZXJfYm91bmRhcnkgPSAweEEwO1xuICAgICAgICAvLyAyLiBJZiBieXRlIGlzIDB4RUQsIHNldCB1dGYtOCB1cHBlciBib3VuZGFyeSB0byAweDlGLlxuICAgICAgICBpZiAoYml0ZSA9PT0gMHhFRClcbiAgICAgICAgICB1dGY4X3VwcGVyX2JvdW5kYXJ5ID0gMHg5RjtcbiAgICAgICAgLy8gMy4gU2V0IHV0Zi04IGJ5dGVzIG5lZWRlZCB0byAyIGFuZCB1dGYtOCBjb2RlIHBvaW50IHRvXG4gICAgICAgIC8vIGJ5dGUg4oiSIDB4RTAuXG4gICAgICAgIHV0ZjhfYnl0ZXNfbmVlZGVkID0gMjtcbiAgICAgICAgdXRmOF9jb2RlX3BvaW50ID0gYml0ZSAtIDB4RTA7XG4gICAgICB9XG5cbiAgICAgIC8vIDB4RjAgdG8gMHhGNFxuICAgICAgZWxzZSBpZiAoaW5SYW5nZShiaXRlLCAweEYwLCAweEY0KSkge1xuICAgICAgICAvLyAxLiBJZiBieXRlIGlzIDB4RjAsIHNldCB1dGYtOCBsb3dlciBib3VuZGFyeSB0byAweDkwLlxuICAgICAgICBpZiAoYml0ZSA9PT0gMHhGMClcbiAgICAgICAgICB1dGY4X2xvd2VyX2JvdW5kYXJ5ID0gMHg5MDtcbiAgICAgICAgLy8gMi4gSWYgYnl0ZSBpcyAweEY0LCBzZXQgdXRmLTggdXBwZXIgYm91bmRhcnkgdG8gMHg4Ri5cbiAgICAgICAgaWYgKGJpdGUgPT09IDB4RjQpXG4gICAgICAgICAgdXRmOF91cHBlcl9ib3VuZGFyeSA9IDB4OEY7XG4gICAgICAgIC8vIDMuIFNldCB1dGYtOCBieXRlcyBuZWVkZWQgdG8gMyBhbmQgdXRmLTggY29kZSBwb2ludCB0b1xuICAgICAgICAvLyBieXRlIOKIkiAweEYwLlxuICAgICAgICB1dGY4X2J5dGVzX25lZWRlZCA9IDM7XG4gICAgICAgIHV0ZjhfY29kZV9wb2ludCA9IGJpdGUgLSAweEYwO1xuICAgICAgfVxuXG4gICAgICAvLyBPdGhlcndpc2VcbiAgICAgIGVsc2Uge1xuICAgICAgICAvLyBSZXR1cm4gZXJyb3IuXG4gICAgICAgIHJldHVybiBkZWNvZGVyRXJyb3IoZmF0YWwpO1xuICAgICAgfVxuXG4gICAgICAvLyBUaGVuIChieXRlIGlzIGluIHRoZSByYW5nZSAweEMyIHRvIDB4RjQpIHNldCB1dGYtOCBjb2RlXG4gICAgICAvLyBwb2ludCB0byB1dGYtOCBjb2RlIHBvaW50IDw8ICg2IMOXIHV0Zi04IGJ5dGVzIG5lZWRlZCkgYW5kXG4gICAgICAvLyByZXR1cm4gY29udGludWUuXG4gICAgICB1dGY4X2NvZGVfcG9pbnQgPSB1dGY4X2NvZGVfcG9pbnQgPDwgKDYgKiB1dGY4X2J5dGVzX25lZWRlZCk7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICAvLyA0LiBJZiBieXRlIGlzIG5vdCBpbiB0aGUgcmFuZ2UgdXRmLTggbG93ZXIgYm91bmRhcnkgdG8gdXRmLThcbiAgICAvLyB1cHBlciBib3VuZGFyeSwgcnVuIHRoZXNlIHN1YnN0ZXBzOlxuICAgIGlmICghaW5SYW5nZShiaXRlLCB1dGY4X2xvd2VyX2JvdW5kYXJ5LCB1dGY4X3VwcGVyX2JvdW5kYXJ5KSkge1xuXG4gICAgICAvLyAxLiBTZXQgdXRmLTggY29kZSBwb2ludCwgdXRmLTggYnl0ZXMgbmVlZGVkLCBhbmQgdXRmLThcbiAgICAgIC8vIGJ5dGVzIHNlZW4gdG8gMCwgc2V0IHV0Zi04IGxvd2VyIGJvdW5kYXJ5IHRvIDB4ODAsIGFuZCBzZXRcbiAgICAgIC8vIHV0Zi04IHVwcGVyIGJvdW5kYXJ5IHRvIDB4QkYuXG4gICAgICB1dGY4X2NvZGVfcG9pbnQgPSB1dGY4X2J5dGVzX25lZWRlZCA9IHV0ZjhfYnl0ZXNfc2VlbiA9IDA7XG4gICAgICB1dGY4X2xvd2VyX2JvdW5kYXJ5ID0gMHg4MDtcbiAgICAgIHV0ZjhfdXBwZXJfYm91bmRhcnkgPSAweEJGO1xuXG4gICAgICAvLyAyLiBQcmVwZW5kIGJ5dGUgdG8gc3RyZWFtLlxuICAgICAgc3RyZWFtLnByZXBlbmQoYml0ZSk7XG5cbiAgICAgIC8vIDMuIFJldHVybiBlcnJvci5cbiAgICAgIHJldHVybiBkZWNvZGVyRXJyb3IoZmF0YWwpO1xuICAgIH1cblxuICAgIC8vIDUuIFNldCB1dGYtOCBsb3dlciBib3VuZGFyeSB0byAweDgwIGFuZCB1dGYtOCB1cHBlciBib3VuZGFyeVxuICAgIC8vIHRvIDB4QkYuXG4gICAgdXRmOF9sb3dlcl9ib3VuZGFyeSA9IDB4ODA7XG4gICAgdXRmOF91cHBlcl9ib3VuZGFyeSA9IDB4QkY7XG5cbiAgICAvLyA2LiBJbmNyZWFzZSB1dGYtOCBieXRlcyBzZWVuIGJ5IG9uZSBhbmQgc2V0IHV0Zi04IGNvZGUgcG9pbnRcbiAgICAvLyB0byB1dGYtOCBjb2RlIHBvaW50ICsgKGJ5dGUg4oiSIDB4ODApIDw8ICg2IMOXICh1dGYtOCBieXRlc1xuICAgIC8vIG5lZWRlZCDiiJIgdXRmLTggYnl0ZXMgc2VlbikpLlxuICAgIHV0ZjhfYnl0ZXNfc2VlbiArPSAxO1xuICAgIHV0ZjhfY29kZV9wb2ludCArPSAoYml0ZSAtIDB4ODApIDw8ICg2ICogKHV0ZjhfYnl0ZXNfbmVlZGVkIC0gdXRmOF9ieXRlc19zZWVuKSk7XG5cbiAgICAvLyA3LiBJZiB1dGYtOCBieXRlcyBzZWVuIGlzIG5vdCBlcXVhbCB0byB1dGYtOCBieXRlcyBuZWVkZWQsXG4gICAgLy8gY29udGludWUuXG4gICAgaWYgKHV0ZjhfYnl0ZXNfc2VlbiAhPT0gdXRmOF9ieXRlc19uZWVkZWQpXG4gICAgICByZXR1cm4gbnVsbDtcblxuICAgIC8vIDguIExldCBjb2RlIHBvaW50IGJlIHV0Zi04IGNvZGUgcG9pbnQuXG4gICAgdmFyIGNvZGVfcG9pbnQgPSB1dGY4X2NvZGVfcG9pbnQ7XG5cbiAgICAvLyA5LiBTZXQgdXRmLTggY29kZSBwb2ludCwgdXRmLTggYnl0ZXMgbmVlZGVkLCBhbmQgdXRmLTggYnl0ZXNcbiAgICAvLyBzZWVuIHRvIDAuXG4gICAgdXRmOF9jb2RlX3BvaW50ID0gdXRmOF9ieXRlc19uZWVkZWQgPSB1dGY4X2J5dGVzX3NlZW4gPSAwO1xuXG4gICAgLy8gMTAuIFJldHVybiBhIGNvZGUgcG9pbnQgd2hvc2UgdmFsdWUgaXMgY29kZSBwb2ludC5cbiAgICByZXR1cm4gY29kZV9wb2ludDtcbiAgfTtcbn1cblxuLyoqXG4gKiBAY29uc3RydWN0b3JcbiAqIEBpbXBsZW1lbnRzIHtFbmNvZGVyfVxuICogQHBhcmFtIHt7ZmF0YWw6IGJvb2xlYW59fSBvcHRpb25zXG4gKi9cbmZ1bmN0aW9uIFVURjhFbmNvZGVyKG9wdGlvbnMpIHtcbiAgdmFyIGZhdGFsID0gb3B0aW9ucy5mYXRhbDtcbiAgLyoqXG4gICAqIEBwYXJhbSB7U3RyZWFtfSBzdHJlYW0gSW5wdXQgc3RyZWFtLlxuICAgKiBAcGFyYW0ge251bWJlcn0gY29kZV9wb2ludCBOZXh0IGNvZGUgcG9pbnQgcmVhZCBmcm9tIHRoZSBzdHJlYW0uXG4gICAqIEByZXR1cm4geyhudW1iZXJ8IUFycmF5LjxudW1iZXI+KX0gQnl0ZShzKSB0byBlbWl0LlxuICAgKi9cbiAgdGhpcy5oYW5kbGVyID0gZnVuY3Rpb24oc3RyZWFtLCBjb2RlX3BvaW50KSB7XG4gICAgLy8gMS4gSWYgY29kZSBwb2ludCBpcyBlbmQtb2Ytc3RyZWFtLCByZXR1cm4gZmluaXNoZWQuXG4gICAgaWYgKGNvZGVfcG9pbnQgPT09IGVuZF9vZl9zdHJlYW0pXG4gICAgICByZXR1cm4gZmluaXNoZWQ7XG5cbiAgICAvLyAyLiBJZiBjb2RlIHBvaW50IGlzIGluIHRoZSByYW5nZSBVKzAwMDAgdG8gVSswMDdGLCByZXR1cm4gYVxuICAgIC8vIGJ5dGUgd2hvc2UgdmFsdWUgaXMgY29kZSBwb2ludC5cbiAgICBpZiAoaW5SYW5nZShjb2RlX3BvaW50LCAweDAwMDAsIDB4MDA3ZikpXG4gICAgICByZXR1cm4gY29kZV9wb2ludDtcblxuICAgIC8vIDMuIFNldCBjb3VudCBhbmQgb2Zmc2V0IGJhc2VkIG9uIHRoZSByYW5nZSBjb2RlIHBvaW50IGlzIGluOlxuICAgIHZhciBjb3VudCwgb2Zmc2V0O1xuICAgIC8vIFUrMDA4MCB0byBVKzA3RkY6ICAgIDEgYW5kIDB4QzBcbiAgICBpZiAoaW5SYW5nZShjb2RlX3BvaW50LCAweDAwODAsIDB4MDdGRikpIHtcbiAgICAgIGNvdW50ID0gMTtcbiAgICAgIG9mZnNldCA9IDB4QzA7XG4gICAgfVxuICAgIC8vIFUrMDgwMCB0byBVK0ZGRkY6ICAgIDIgYW5kIDB4RTBcbiAgICBlbHNlIGlmIChpblJhbmdlKGNvZGVfcG9pbnQsIDB4MDgwMCwgMHhGRkZGKSkge1xuICAgICAgY291bnQgPSAyO1xuICAgICAgb2Zmc2V0ID0gMHhFMDtcbiAgICB9XG4gICAgLy8gVSsxMDAwMCB0byBVKzEwRkZGRjogMyBhbmQgMHhGMFxuICAgIGVsc2UgaWYgKGluUmFuZ2UoY29kZV9wb2ludCwgMHgxMDAwMCwgMHgxMEZGRkYpKSB7XG4gICAgICBjb3VudCA9IDM7XG4gICAgICBvZmZzZXQgPSAweEYwO1xuICAgIH1cblxuICAgIC8vIDQuTGV0IGJ5dGVzIGJlIGEgYnl0ZSBzZXF1ZW5jZSB3aG9zZSBmaXJzdCBieXRlIGlzIChjb2RlXG4gICAgLy8gcG9pbnQgPj4gKDYgw5cgY291bnQpKSArIG9mZnNldC5cbiAgICB2YXIgYnl0ZXMgPSBbKGNvZGVfcG9pbnQgPj4gKDYgKiBjb3VudCkpICsgb2Zmc2V0XTtcblxuICAgIC8vIDUuIFJ1biB0aGVzZSBzdWJzdGVwcyB3aGlsZSBjb3VudCBpcyBncmVhdGVyIHRoYW4gMDpcbiAgICB3aGlsZSAoY291bnQgPiAwKSB7XG5cbiAgICAgIC8vIDEuIFNldCB0ZW1wIHRvIGNvZGUgcG9pbnQgPj4gKDYgw5cgKGNvdW50IOKIkiAxKSkuXG4gICAgICB2YXIgdGVtcCA9IGNvZGVfcG9pbnQgPj4gKDYgKiAoY291bnQgLSAxKSk7XG5cbiAgICAgIC8vIDIuIEFwcGVuZCB0byBieXRlcyAweDgwIHwgKHRlbXAgJiAweDNGKS5cbiAgICAgIGJ5dGVzLnB1c2goMHg4MCB8ICh0ZW1wICYgMHgzRikpO1xuXG4gICAgICAvLyAzLiBEZWNyZWFzZSBjb3VudCBieSBvbmUuXG4gICAgICBjb3VudCAtPSAxO1xuICAgIH1cblxuICAgIC8vIDYuIFJldHVybiBieXRlcyBieXRlcywgaW4gb3JkZXIuXG4gICAgcmV0dXJuIGJ5dGVzO1xuICB9O1xufVxuXG5leHBvcnRzLlRleHRFbmNvZGVyID0gVGV4dEVuY29kZXI7XG5leHBvcnRzLlRleHREZWNvZGVyID0gVGV4dERlY29kZXI7Il0sIm5hbWVzIjpbImluUmFuZ2UiLCJhIiwibWluIiwibWF4IiwiVG9EaWN0aW9uYXJ5IiwibyIsInVuZGVmaW5lZCIsIk9iamVjdCIsIlR5cGVFcnJvciIsInN0cmluZ1RvQ29kZVBvaW50cyIsInN0cmluZyIsInMiLCJTdHJpbmciLCJuIiwibGVuZ3RoIiwiaSIsInUiLCJjIiwiY2hhckNvZGVBdCIsInB1c2giLCJkIiwiYiIsImNvZGVQb2ludHNUb1N0cmluZyIsImNvZGVfcG9pbnRzIiwiY3AiLCJmcm9tQ2hhckNvZGUiLCJlbmRfb2Zfc3RyZWFtIiwiU3RyZWFtIiwidG9rZW5zIiwic2xpY2UiLCJjYWxsIiwicHJvdG90eXBlIiwiZW5kT2ZTdHJlYW0iLCJyZWFkIiwic2hpZnQiLCJwcmVwZW5kIiwidG9rZW4iLCJBcnJheSIsImlzQXJyYXkiLCJ1bnNoaWZ0IiwicG9wIiwiZmluaXNoZWQiLCJkZWNvZGVyRXJyb3IiLCJmYXRhbCIsIm9wdF9jb2RlX3BvaW50IiwiREVGQVVMVF9FTkNPRElORyIsIlRleHREZWNvZGVyIiwiZW5jb2RpbmciLCJvcHRpb25zIiwidG9Mb3dlckNhc2UiLCJFcnJvciIsIl9zdHJlYW1pbmciLCJfQk9Nc2VlbiIsIl9kZWNvZGVyIiwiX2ZhdGFsIiwiQm9vbGVhbiIsIl9pZ25vcmVCT00iLCJkZWZpbmVQcm9wZXJ0eSIsInZhbHVlIiwiZGVjb2RlIiwiaW5wdXQiLCJieXRlcyIsIkFycmF5QnVmZmVyIiwiVWludDhBcnJheSIsImJ1ZmZlciIsImJ5dGVPZmZzZXQiLCJieXRlTGVuZ3RoIiwiVVRGOERlY29kZXIiLCJpbnB1dF9zdHJlYW0iLCJyZXN1bHQiLCJoYW5kbGVyIiwiYXBwbHkiLCJpbmRleE9mIiwiVGV4dEVuY29kZXIiLCJfZW5jb2RlciIsIl9vcHRpb25zIiwiZW5jb2RlIiwib3B0X3N0cmluZyIsIlVURjhFbmNvZGVyIiwidXRmOF9jb2RlX3BvaW50IiwidXRmOF9ieXRlc19zZWVuIiwidXRmOF9ieXRlc19uZWVkZWQiLCJ1dGY4X2xvd2VyX2JvdW5kYXJ5IiwidXRmOF91cHBlcl9ib3VuZGFyeSIsInN0cmVhbSIsImJpdGUiLCJjb2RlX3BvaW50IiwiY291bnQiLCJvZmZzZXQiLCJ0ZW1wIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/text-encoding-utf-8/lib/encoding.lib.js\n");

/***/ })

};
;