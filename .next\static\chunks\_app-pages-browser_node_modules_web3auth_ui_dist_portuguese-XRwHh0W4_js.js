"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_web3auth_ui_dist_portuguese-XRwHh0W4_js"],{

/***/ "(app-pages-browser)/./node_modules/@web3auth/ui/dist/portuguese-XRwHh0W4.js":
/*!***************************************************************!*\
  !*** ./node_modules/@web3auth/ui/dist/portuguese-XRwHh0W4.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ portuguese; },\n/* harmony export */   modal: function() { return /* binding */ modal; }\n/* harmony export */ });\nvar modal = {\n  \"adapter-loader.message\": \"Verifique a sua conta {{adapter}} para continuar\",\n  \"adapter-loader.message1\": \"Verifique o(a) seu/sua {{adapter}}\",\n  \"adapter-loader.message2\": \"conta para continuar\",\n  \"errors-invalid-number-email\": \"Email ou número de telefone inválido\",\n  \"errors-required\": \"Obrigatório\",\n  \"external.back\": \"Voltar\",\n  \"external.connect\": \"Conectar com carteira\",\n  \"external.title\": \"Carteira Externa\",\n  \"external.walletconnect-connect\": \"Conectar\",\n  \"external.walletconnect-copy\": \"Clique no código QR para copiar para a área de transferência\",\n  \"external.walletconnect-subtitle\": \"Digitalize o código QR com uma carteira compatível com WalletConnect\",\n  \"footer.message\": \"Login de autocustódia por\",\n  \"footer.message-new\": \"Login de autocustódia por Web3Auth\",\n  \"footer.policy\": \"Política de Privacidade\",\n  \"footer.terms\": \"Termos de Uso\",\n  \"footer.terms-service\": \"Termos de Serviço\",\n  \"footer.version\": \"Versão\",\n  \"header-subtitle\": \"Selecione uma das seguintes opções para continuar\",\n  \"header-subtitle-name\": \"Sua carteira {{appName}} com um clique\",\n  \"header-subtitle-new\": \"Sua carteira de blockchain com um clique\",\n  \"header-title\": \"Entrar\",\n  \"header-tooltip-desc\": \"A carteira serve como uma conta para armazenar e gerenciar seus ativos digitais na blockchain.\",\n  \"header-tooltip-title\": \"Carteira\",\n  \"network.add-request\": \"Este site está solicitando adicionar uma rede\",\n  \"network.cancel\": \"Cancelar\",\n  \"network.from\": \"De\",\n  \"network.proceed\": \"Prosseguir\",\n  \"network.switch-request\": \"Este site está solicitando trocar de rede\",\n  \"network.to\": \"Para\",\n  \"popup.phone-body\": \"O código do seu país será detectado automaticamente, mas se estiver usando um número de telefone de um país diferente, você precisará inserir manualmente o código correto do país.\",\n  \"popup.phone-header\": \"Número de telefone e código do país\",\n  \"social.continue\": \"Continuar com\",\n  \"social.continueCustom\": \"Continue com o {{adapter}}\",\n  \"social.email\": \"Email\",\n  \"social.email-continue\": \"Continuar com email\",\n  \"social.email-new\": \"<EMAIL>\",\n  \"social.passwordless-cta\": \"Continuar\",\n  \"social.passwordless-title\": \"Email ou telefone\",\n  \"social.phone\": \"Telefone\",\n  \"social.policy\": \"Não armazenamos nenhum dado relacionado ao seu login por rede social.\",\n  \"social.sms\": \"Móvel\",\n  \"social.sms-continue\": \"Continuar com o celular\",\n  \"social.sms-invalid-number\": \"Número de telefone inválido\",\n  \"social.sms-placeholder-text\": \"Por exemplo:\",\n  \"social.view-less\": \"Ver menos opções\",\n  \"social.view-less-new\": \"Ver menos\",\n  \"social.view-more\": \"Ver mais opções\",\n  \"social.view-more-new\": \"Ver mais\",\n  \"post-loading.connected\": \"Você está conectado com sua conta\",\n  \"post-loading.something-wrong\": \"Algo deu errado!\"\n};\nvar portuguese = {\n  modal: modal\n};\n\n\n//# sourceMappingURL=portuguese-XRwHh0W4.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/ui/dist/portuguese-XRwHh0W4.js\n"));

/***/ })

}]);