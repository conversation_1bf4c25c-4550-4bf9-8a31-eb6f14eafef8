"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_web3auth_ui_dist_korean-4betESnt_js"],{

/***/ "(app-pages-browser)/./node_modules/@web3auth/ui/dist/korean-4betESnt.js":
/*!***********************************************************!*\
  !*** ./node_modules/@web3auth/ui/dist/korean-4betESnt.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ korean; },\n/* harmony export */   modal: function() { return /* binding */ modal; }\n/* harmony export */ });\nvar modal = {\n  \"adapter-loader.message\": \"{{adapter}} 계정을 확인하여 계속 진행하세요\",\n  \"adapter-loader.message1\": \"{{adapter}} 확인\",\n  \"adapter-loader.message2\": \"계속할 계정\",\n  \"errors-invalid-number-email\": \"잘못된 이메일 또는 전화번호\",\n  \"errors-required\": \"필수\",\n  \"external.back\": \"뒤로\",\n  \"external.connect\": \"지갑에 연결\",\n  \"external.title\": \"외부 지갑\",\n  \"external.walletconnect-connect\": \"연결\",\n  \"external.walletconnect-copy\": \"클립보드에 복사하려면 QR 코드를 클릭하세요\",\n  \"external.walletconnect-subtitle\": \"WalletConnect 호환 지갑으로 QR 코드를 스캔하세요\",\n  \"footer.message\": \"자기 보관 로그인\",\n  \"footer.message-new\": \"Web3Auth를 통한 자기 보관 로그인\",\n  \"footer.policy\": \"개인정보 처리방침\",\n  \"footer.terms\": \"이용 약관\",\n  \"footer.terms-service\": \"서비스 약관\",\n  \"footer.version\": \"버전\",\n  \"header-subtitle\": \"계속하려면 다음 중 하나를 선택하세요\",\n  \"header-subtitle-name\": \"한 번의 클릭으로 {{appName}} 지갑\",\n  \"header-subtitle-new\": \"한 번의 클릭으로 블록체인 지갑\",\n  \"header-title\": \"로그인\",\n  \"header-tooltip-desc\": \"지갑은 블록체인에서 디지털 자산을 저장하고 관리하는 데 사용되는 계정입니다.\",\n  \"header-tooltip-title\": \"지갑\",\n  \"network.add-request\": \"이 사이트는 네트워크 추가를 요청하고 있습니다\",\n  \"network.cancel\": \"취소\",\n  \"network.from\": \"보낸 사람\",\n  \"network.proceed\": \"진행\",\n  \"network.switch-request\": \"이 사이트는 네트워크 전환을 요청하고 있습니다\",\n  \"network.to\": \"에게\",\n  \"popup.phone-body\": \"국가 코드는 자동으로 감지됩니다. 그러나 다른 국가의 전화 번호를 사용하는 경우 올바른 국가 코드를 수동으로 입력해야 합니다.\",\n  \"popup.phone-header\": \"전화 번호 및 국가 코드\",\n  \"social.continue\": \"계속하기\",\n  \"social.continueCustom\": \"{{adapter}}을 계속하십시오\",\n  \"social.email\": \"이메일\",\n  \"social.email-continue\": \"이메일로 계속하기\",\n  \"social.email-new\": \"<EMAIL>\",\n  \"social.passwordless-cta\": \"계속\",\n  \"social.passwordless-title\": \"이메일 또는 전화\",\n  \"social.phone\": \"핸드폰\",\n  \"social.policy\": \"우리는 귀하의 소셜 로그인과 관련된 데이터를 저장하지 않습니다.\",\n  \"social.sms\": \"모바일\",\n  \"social.sms-continue\": \"모바일로 계속하기\",\n  \"social.sms-invalid-number\": \"유효하지 않은 전화 번호\",\n  \"social.sms-placeholder-text\": \"예시:\",\n  \"social.view-less\": \"더 적은 옵션 보기\",\n  \"social.view-less-new\": \"더 적게 보기\",\n  \"social.view-more\": \"더 많은 옵션 보기\",\n  \"social.view-more-new\": \"더보기\",\n  \"post-loading.connected\": \"귀하는 귀하의 계정과 연결되어 있습니다\",\n  \"post-loading.something-wrong\": \"뭔가 잘못되었습니다!\"\n};\nvar korean = {\n  modal: modal\n};\n\n\n//# sourceMappingURL=korean-4betESnt.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/ui/dist/korean-4betESnt.js\n"));

/***/ })

}]);