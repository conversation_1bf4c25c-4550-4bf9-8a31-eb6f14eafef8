"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_web3auth_ui_dist_mandarin-JJjRWyWU_js"],{

/***/ "(app-pages-browser)/./node_modules/@web3auth/ui/dist/mandarin-JJjRWyWU.js":
/*!*************************************************************!*\
  !*** ./node_modules/@web3auth/ui/dist/mandarin-JJjRWyWU.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ mandarin; },\n/* harmony export */   modal: function() { return /* binding */ modal; }\n/* harmony export */ });\nvar modal = {\n  \"adapter-loader.message\": \"验证您的{{adapter}}帐户以继续\",\n  \"adapter-loader.message1\": \"在您的{{adapter}}上验证\",\n  \"adapter-loader.message2\": \"帐号继续\",\n  \"errors-invalid-number-email\": \"无效的电子邮件或电话号码\",\n  \"errors-required\": \"必填\",\n  \"external.back\": \"返回\",\n  \"external.connect\": \"使用钱包连接\",\n  \"external.title\": \"外部钱包\",\n  \"external.walletconnect-connect\": \"连接\",\n  \"external.walletconnect-copy\": \"点击二维码复制到剪贴板\",\n  \"external.walletconnect-subtitle\": \"使用兼容 WalletConnect 的钱包扫描 QR 码\",\n  \"footer.message\": \"自托管登录由\",\n  \"footer.message-new\": \"Web3Auth 的自托管登录\",\n  \"footer.policy\": \"隐私政策\",\n  \"footer.terms\": \"使用条款\",\n  \"footer.terms-service\": \"服务条款\",\n  \"footer.version\": \"版本\",\n  \"header-subtitle\": \"选择以下选项以继续\",\n  \"header-subtitle-name\": \"一键点击您的 {{appName}} 钱包\",\n  \"header-subtitle-new\": \"一键点击您的区块链钱包\",\n  \"header-title\": \"登录\",\n  \"header-tooltip-desc\": \"钱包作为一个账户用于在区块链上存储和管理您的数字资产。\",\n  \"header-tooltip-title\": \"钱包\",\n  \"network.add-request\": \"此站点正在请求添加网络\",\n  \"network.cancel\": \"取消\",\n  \"network.from\": \"从\",\n  \"network.proceed\": \"继续\",\n  \"network.switch-request\": \"此站点正在请求切换网络\",\n  \"network.to\": \"至\",\n  \"popup.phone-body\": \"您的国家代码将自动检测到，但如果您使用其他国家/地区的电话号码，您需要手动输入正确的国家代码。\",\n  \"popup.phone-header\": \"电话号码和国家代码\",\n  \"social.continue\": \"继续\",\n  \"social.continueCustom\": \"继续使用{{adapter}}\",\n  \"social.email\": \"电子邮件\",\n  \"social.email-continue\": \"继续使用电子邮件\",\n  \"social.email-new\": \"<EMAIL>\",\n  \"social.passwordless-cta\": \"继续\",\n  \"social.passwordless-title\": \"邮件或电话\",\n  \"social.phone\": \"电话\",\n  \"social.policy\": \"我们不存储与您的社交登录相关的任何数据。\",\n  \"social.sms\": \"移动\",\n  \"social.sms-continue\": \"继续使用移动设备\",\n  \"social.sms-invalid-number\": \"无效的电话号码\",\n  \"social.sms-placeholder-text\": \"例如：\",\n  \"social.view-less\": \"查看较少选项\",\n  \"social.view-less-new\": \"查看较少\",\n  \"social.view-more\": \"查看更多选项\",\n  \"social.view-more-new\": \"查看更多\",\n  \"post-loading.connected\": \"您与您的帐户有联系\",\n  \"post-loading.something-wrong\": \"出了些问题！\"\n};\nvar mandarin = {\n  modal: modal\n};\n\n\n//# sourceMappingURL=mandarin-JJjRWyWU.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/ui/dist/mandarin-JJjRWyWU.js\n"));

/***/ })

}]);