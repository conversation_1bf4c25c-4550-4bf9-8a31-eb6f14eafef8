"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_web3auth_ui_dist_french-pSBrSTq9_js"],{

/***/ "(app-pages-browser)/./node_modules/@web3auth/ui/dist/french-pSBrSTq9.js":
/*!***********************************************************!*\
  !*** ./node_modules/@web3auth/ui/dist/french-pSBrSTq9.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ french; },\n/* harmony export */   modal: function() { return /* binding */ modal; }\n/* harmony export */ });\nvar modal = {\n  \"adapter-loader.message\": \"Vérifiez votre compte {{adapter}} pour continuer\",\n  \"adapter-loader.message1\": \"Vérifiez votre {{adapter}}\",\n  \"adapter-loader.message2\": \"compte pour continuer\",\n  \"errors-invalid-number-email\": \"Adresse e-mail ou numéro de téléphone invalide\",\n  \"errors-required\": \"Champ obligatoire\",\n  \"external.back\": \"Retour\",\n  \"external.connect\": \"Se connecter avec un portefeuille\",\n  \"external.title\": \"Portefeuille externe\",\n  \"external.walletconnect-connect\": \"Se connecter\",\n  \"external.walletconnect-copy\": \"Cliquez sur le code QR pour le copier dans le presse-papiers\",\n  \"external.walletconnect-subtitle\": \"Scannez le code QR avec un portefeuille compatible WalletConnect\",\n  \"footer.message\": \"Connexion en autonomie par\",\n  \"footer.message-new\": \"Connexion en autonomie par Web3Auth\",\n  \"footer.policy\": \"Politique de confidentialité\",\n  \"footer.terms\": \"Conditions d'utilisation\",\n  \"footer.terms-service\": \"Conditions d'utilisation\",\n  \"footer.version\": \"Version\",\n  \"header-subtitle\": \"Sélectionnez l'une des options suivantes pour continuer\",\n  \"header-subtitle-name\": \"Votre portefeuille {{appName}} en un clic\",\n  \"header-subtitle-new\": \"Votre portefeuille blockchain en un clic\",\n  \"header-title\": \"Se connecter\",\n  \"header-tooltip-desc\": \"Le portefeuille sert de compte pour stocker et gérer vos actifs numériques sur la blockchain.\",\n  \"header-tooltip-title\": \"Portefeuille\",\n  \"network.add-request\": \"Ce site demande d'ajouter un réseau\",\n  \"network.cancel\": \"Annuler\",\n  \"network.from\": \"De\",\n  \"network.proceed\": \"Continuer\",\n  \"network.switch-request\": \"Ce site demande de changer de réseau\",\n  \"network.to\": \"À\",\n  \"popup.phone-body\": \"Votre code pays sera détecté automatiquement, mais si vous utilisez un numéro de téléphone d'un autre pays, vous devrez saisir manuellement le bon code pays.\",\n  \"popup.phone-header\": \"Numéro de téléphone et code pays\",\n  \"social.continue\": \"Continuer avec\",\n  \"social.continueCustom\": \"Continuer avec {{adapter}}\",\n  \"social.email\": \"Email\",\n  \"social.email-continue\": \"Continuer avec l'email\",\n  \"social.email-new\": \"<EMAIL>\",\n  \"social.passwordless-cta\": \"Continuer\",\n  \"social.passwordless-title\": \"Email ou téléphone\",\n  \"social.phone\": \"Téléphone\",\n  \"social.policy\": \"Nous ne stockons aucune donnée liée à vos connexions sociales.\",\n  \"social.sms\": \"Mobile\",\n  \"social.sms-continue\": \"Continuer avec le mobile\",\n  \"social.sms-invalid-number\": \"Numéro de téléphone invalide\",\n  \"social.sms-placeholder-text\": \"Par exemple :\",\n  \"social.view-less\": \"Voir moins d'options\",\n  \"social.view-less-new\": \"Voir moins\",\n  \"social.view-more\": \"Voir plus d'options\",\n  \"social.view-more-new\": \"Voir plus\",\n  \"post-loading.connected\": \"Vous êtes connecté avec votre compte\",\n  \"post-loading.something-wrong\": \"Quelque chose s'est mal passé!\"\n};\nvar french = {\n  modal: modal\n};\n\n\n//# sourceMappingURL=french-pSBrSTq9.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2ViM2F1dGgvdWkvZGlzdC9mcmVuY2gtcFNCclNUcTkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLHFEQUFxRCxVQUFVO0FBQy9ELCtDQUErQyxTQUFTO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdELFVBQVU7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsU0FBUztBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW9DO0FBQ3BDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9Ad2ViM2F1dGgvdWkvZGlzdC9mcmVuY2gtcFNCclNUcTkuanM/ODU4NiJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgbW9kYWwgPSB7XG4gIFwiYWRhcHRlci1sb2FkZXIubWVzc2FnZVwiOiBcIlbDqXJpZmlleiB2b3RyZSBjb21wdGUge3thZGFwdGVyfX0gcG91ciBjb250aW51ZXJcIixcbiAgXCJhZGFwdGVyLWxvYWRlci5tZXNzYWdlMVwiOiBcIlbDqXJpZmlleiB2b3RyZSB7e2FkYXB0ZXJ9fVwiLFxuICBcImFkYXB0ZXItbG9hZGVyLm1lc3NhZ2UyXCI6IFwiY29tcHRlIHBvdXIgY29udGludWVyXCIsXG4gIFwiZXJyb3JzLWludmFsaWQtbnVtYmVyLWVtYWlsXCI6IFwiQWRyZXNzZSBlLW1haWwgb3UgbnVtw6lybyBkZSB0w6lsw6lwaG9uZSBpbnZhbGlkZVwiLFxuICBcImVycm9ycy1yZXF1aXJlZFwiOiBcIkNoYW1wIG9ibGlnYXRvaXJlXCIsXG4gIFwiZXh0ZXJuYWwuYmFja1wiOiBcIlJldG91clwiLFxuICBcImV4dGVybmFsLmNvbm5lY3RcIjogXCJTZSBjb25uZWN0ZXIgYXZlYyB1biBwb3J0ZWZldWlsbGVcIixcbiAgXCJleHRlcm5hbC50aXRsZVwiOiBcIlBvcnRlZmV1aWxsZSBleHRlcm5lXCIsXG4gIFwiZXh0ZXJuYWwud2FsbGV0Y29ubmVjdC1jb25uZWN0XCI6IFwiU2UgY29ubmVjdGVyXCIsXG4gIFwiZXh0ZXJuYWwud2FsbGV0Y29ubmVjdC1jb3B5XCI6IFwiQ2xpcXVleiBzdXIgbGUgY29kZSBRUiBwb3VyIGxlIGNvcGllciBkYW5zIGxlIHByZXNzZS1wYXBpZXJzXCIsXG4gIFwiZXh0ZXJuYWwud2FsbGV0Y29ubmVjdC1zdWJ0aXRsZVwiOiBcIlNjYW5uZXogbGUgY29kZSBRUiBhdmVjIHVuIHBvcnRlZmV1aWxsZSBjb21wYXRpYmxlIFdhbGxldENvbm5lY3RcIixcbiAgXCJmb290ZXIubWVzc2FnZVwiOiBcIkNvbm5leGlvbiBlbiBhdXRvbm9taWUgcGFyXCIsXG4gIFwiZm9vdGVyLm1lc3NhZ2UtbmV3XCI6IFwiQ29ubmV4aW9uIGVuIGF1dG9ub21pZSBwYXIgV2ViM0F1dGhcIixcbiAgXCJmb290ZXIucG9saWN5XCI6IFwiUG9saXRpcXVlIGRlIGNvbmZpZGVudGlhbGl0w6lcIixcbiAgXCJmb290ZXIudGVybXNcIjogXCJDb25kaXRpb25zIGQndXRpbGlzYXRpb25cIixcbiAgXCJmb290ZXIudGVybXMtc2VydmljZVwiOiBcIkNvbmRpdGlvbnMgZCd1dGlsaXNhdGlvblwiLFxuICBcImZvb3Rlci52ZXJzaW9uXCI6IFwiVmVyc2lvblwiLFxuICBcImhlYWRlci1zdWJ0aXRsZVwiOiBcIlPDqWxlY3Rpb25uZXogbCd1bmUgZGVzIG9wdGlvbnMgc3VpdmFudGVzIHBvdXIgY29udGludWVyXCIsXG4gIFwiaGVhZGVyLXN1YnRpdGxlLW5hbWVcIjogXCJWb3RyZSBwb3J0ZWZldWlsbGUge3thcHBOYW1lfX0gZW4gdW4gY2xpY1wiLFxuICBcImhlYWRlci1zdWJ0aXRsZS1uZXdcIjogXCJWb3RyZSBwb3J0ZWZldWlsbGUgYmxvY2tjaGFpbiBlbiB1biBjbGljXCIsXG4gIFwiaGVhZGVyLXRpdGxlXCI6IFwiU2UgY29ubmVjdGVyXCIsXG4gIFwiaGVhZGVyLXRvb2x0aXAtZGVzY1wiOiBcIkxlIHBvcnRlZmV1aWxsZSBzZXJ0IGRlIGNvbXB0ZSBwb3VyIHN0b2NrZXIgZXQgZ8OpcmVyIHZvcyBhY3RpZnMgbnVtw6lyaXF1ZXMgc3VyIGxhIGJsb2NrY2hhaW4uXCIsXG4gIFwiaGVhZGVyLXRvb2x0aXAtdGl0bGVcIjogXCJQb3J0ZWZldWlsbGVcIixcbiAgXCJuZXR3b3JrLmFkZC1yZXF1ZXN0XCI6IFwiQ2Ugc2l0ZSBkZW1hbmRlIGQnYWpvdXRlciB1biByw6lzZWF1XCIsXG4gIFwibmV0d29yay5jYW5jZWxcIjogXCJBbm51bGVyXCIsXG4gIFwibmV0d29yay5mcm9tXCI6IFwiRGVcIixcbiAgXCJuZXR3b3JrLnByb2NlZWRcIjogXCJDb250aW51ZXJcIixcbiAgXCJuZXR3b3JrLnN3aXRjaC1yZXF1ZXN0XCI6IFwiQ2Ugc2l0ZSBkZW1hbmRlIGRlIGNoYW5nZXIgZGUgcsOpc2VhdVwiLFxuICBcIm5ldHdvcmsudG9cIjogXCLDgFwiLFxuICBcInBvcHVwLnBob25lLWJvZHlcIjogXCJWb3RyZSBjb2RlIHBheXMgc2VyYSBkw6l0ZWN0w6kgYXV0b21hdGlxdWVtZW50LCBtYWlzIHNpIHZvdXMgdXRpbGlzZXogdW4gbnVtw6lybyBkZSB0w6lsw6lwaG9uZSBkJ3VuIGF1dHJlIHBheXMsIHZvdXMgZGV2cmV6IHNhaXNpciBtYW51ZWxsZW1lbnQgbGUgYm9uIGNvZGUgcGF5cy5cIixcbiAgXCJwb3B1cC5waG9uZS1oZWFkZXJcIjogXCJOdW3DqXJvIGRlIHTDqWzDqXBob25lIGV0IGNvZGUgcGF5c1wiLFxuICBcInNvY2lhbC5jb250aW51ZVwiOiBcIkNvbnRpbnVlciBhdmVjXCIsXG4gIFwic29jaWFsLmNvbnRpbnVlQ3VzdG9tXCI6IFwiQ29udGludWVyIGF2ZWMge3thZGFwdGVyfX1cIixcbiAgXCJzb2NpYWwuZW1haWxcIjogXCJFbWFpbFwiLFxuICBcInNvY2lhbC5lbWFpbC1jb250aW51ZVwiOiBcIkNvbnRpbnVlciBhdmVjIGwnZW1haWxcIixcbiAgXCJzb2NpYWwuZW1haWwtbmV3XCI6IFwibm9tQGV4ZW1wbGUuY29tXCIsXG4gIFwic29jaWFsLnBhc3N3b3JkbGVzcy1jdGFcIjogXCJDb250aW51ZXJcIixcbiAgXCJzb2NpYWwucGFzc3dvcmRsZXNzLXRpdGxlXCI6IFwiRW1haWwgb3UgdMOpbMOpcGhvbmVcIixcbiAgXCJzb2NpYWwucGhvbmVcIjogXCJUw6lsw6lwaG9uZVwiLFxuICBcInNvY2lhbC5wb2xpY3lcIjogXCJOb3VzIG5lIHN0b2Nrb25zIGF1Y3VuZSBkb25uw6llIGxpw6llIMOgIHZvcyBjb25uZXhpb25zIHNvY2lhbGVzLlwiLFxuICBcInNvY2lhbC5zbXNcIjogXCJNb2JpbGVcIixcbiAgXCJzb2NpYWwuc21zLWNvbnRpbnVlXCI6IFwiQ29udGludWVyIGF2ZWMgbGUgbW9iaWxlXCIsXG4gIFwic29jaWFsLnNtcy1pbnZhbGlkLW51bWJlclwiOiBcIk51bcOpcm8gZGUgdMOpbMOpcGhvbmUgaW52YWxpZGVcIixcbiAgXCJzb2NpYWwuc21zLXBsYWNlaG9sZGVyLXRleHRcIjogXCJQYXIgZXhlbXBsZSA6XCIsXG4gIFwic29jaWFsLnZpZXctbGVzc1wiOiBcIlZvaXIgbW9pbnMgZCdvcHRpb25zXCIsXG4gIFwic29jaWFsLnZpZXctbGVzcy1uZXdcIjogXCJWb2lyIG1vaW5zXCIsXG4gIFwic29jaWFsLnZpZXctbW9yZVwiOiBcIlZvaXIgcGx1cyBkJ29wdGlvbnNcIixcbiAgXCJzb2NpYWwudmlldy1tb3JlLW5ld1wiOiBcIlZvaXIgcGx1c1wiLFxuICBcInBvc3QtbG9hZGluZy5jb25uZWN0ZWRcIjogXCJWb3VzIMOqdGVzIGNvbm5lY3TDqSBhdmVjIHZvdHJlIGNvbXB0ZVwiLFxuICBcInBvc3QtbG9hZGluZy5zb21ldGhpbmctd3JvbmdcIjogXCJRdWVscXVlIGNob3NlIHMnZXN0IG1hbCBwYXNzw6khXCJcbn07XG52YXIgZnJlbmNoID0ge1xuICBtb2RhbDogbW9kYWxcbn07XG5cbmV4cG9ydCB7IGZyZW5jaCBhcyBkZWZhdWx0LCBtb2RhbCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZnJlbmNoLXBTQnJTVHE5LmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/ui/dist/french-pSBrSTq9.js\n"));

/***/ })

}]);