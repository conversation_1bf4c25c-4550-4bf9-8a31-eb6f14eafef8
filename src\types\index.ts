// 用户类型
export interface User {
  id: string
  name?: string
  email?: string
  profileImage?: string
  publicKey: string
  balance: number
  createdAt: Date
}

// AI创作类型
export interface AICreation {
  id: string
  userId: string
  type: 'text' | 'image' | 'music'
  title: string
  description?: string
  content: string // 文字内容或文件URL
  prompt: string
  parameters: Record<string, any>
  createdAt: Date
  isNFT: boolean
  nftMintAddress?: string
}

// NFT类型
export interface NFT {
  id: string
  mintAddress: string
  name: string
  description: string
  image: string
  attributes: Array<{
    trait_type: string
    value: string | number
  }>
  creator: string
  owner: string
  royalty: number
  price?: number
  isListed: boolean
  createdAt: Date
}

// 创作参数类型
export interface CreationParams {
  text?: {
    maxLength: number
    style: 'story' | 'poem' | 'article' | 'script'
    tone: 'formal' | 'casual' | 'creative' | 'professional'
  }
  image?: {
    width: number
    height: number
    style: 'realistic' | 'artistic' | 'cartoon' | 'abstract'
    quality: 'standard' | 'hd'
  }
  music?: {
    duration: number
    genre: 'ambient' | 'electronic' | 'classical' | 'pop'
    mood: 'happy' | 'sad' | 'energetic' | 'calm'
  }
}

// 收益类型
export interface Earnings {
  id: string
  userId: string
  nftMintAddress: string
  amount: number
  type: 'sale' | 'royalty'
  transactionSignature: string
  createdAt: Date
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Solana交易类型
export interface SolanaTransaction {
  signature: string
  slot: number
  blockTime: number
  confirmationStatus: 'processed' | 'confirmed' | 'finalized'
}
