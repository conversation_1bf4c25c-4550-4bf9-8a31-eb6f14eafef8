"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_web3auth_ui_dist_japanese-Zd4lzpvO_js"],{

/***/ "(app-pages-browser)/./node_modules/@web3auth/ui/dist/japanese-Zd4lzpvO.js":
/*!*************************************************************!*\
  !*** ./node_modules/@web3auth/ui/dist/japanese-Zd4lzpvO.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ japanese; },\n/* harmony export */   modal: function() { return /* binding */ modal; }\n/* harmony export */ });\nvar modal = {\n  \"adapter-loader.message\": \"{{adapter}} アカウントを確認して続行する\",\n  \"adapter-loader.message1\": \"{{adapter}} を確認する\",\n  \"adapter-loader.message2\": \"続行するアカウント\",\n  \"errors-invalid-number-email\": \"無効なメールアドレスまたは電話番号\",\n  \"errors-required\": \"必須\",\n  \"external.back\": \"戻る\",\n  \"external.connect\": \"ウォレットと接続\",\n  \"external.title\": \"外部ウォレット\",\n  \"external.walletconnect-connect\": \"接続する\",\n  \"external.walletconnect-copy\": \"QRコードをクリックしてクリップボードにコピー\",\n  \"external.walletconnect-subtitle\": \"WalletConnect対応ウォレットでQRコードをスキャンしてください\",\n  \"footer.message\": \"自己保管ログイン by\",\n  \"footer.message-new\": \"Web3Authによる自己保管ログイン\",\n  \"footer.policy\": \"プライバシーポリシー\",\n  \"footer.terms\": \"利用規約\",\n  \"footer.terms-service\": \"利用規約\",\n  \"footer.version\": \"バージョン\",\n  \"header-subtitle\": \"続行するために以下のオプションのいずれかを選択してください\",\n  \"header-subtitle-name\": \"ワンクリックで {{appName}} ウォレット\",\n  \"header-subtitle-new\": \"ワンクリックであなたのブロックチェーンウォレット\",\n  \"header-title\": \"ログイン\",\n  \"header-tooltip-desc\": \"ウォレットは、ブロックチェーン上でデジタル資産を保存および管理するためのアカウントとして機能します。\",\n  \"header-tooltip-title\": \"ウォレット\",\n  \"network.add-request\": \"このサイトはネットワークの追加をリクエストしています\",\n  \"network.cancel\": \"キャンセル\",\n  \"network.from\": \"から\",\n  \"network.proceed\": \"進む\",\n  \"network.switch-request\": \"このサイトはネットワークの切り替えを要求しています\",\n  \"network.to\": \"へ\",\n  \"popup.phone-body\": \"国コードは自動的に検出されますが、異なる国の電話番号を使用する場合は、正しい国コードを手動で入力する必要があります。\",\n  \"popup.phone-header\": \"電話番号と国コード\",\n  \"social.continue\": \"続ける\",\n  \"social.continueCustom\": \"{{adapter}}を続けます\",\n  \"social.email\": \"Eメール\",\n  \"social.email-continue\": \"メールで続行\",\n  \"social.email-new\": \"<EMAIL>\",\n  \"social.passwordless-cta\": \"続ける\",\n  \"social.passwordless-title\": \"メールまたは電話\",\n  \"social.phone\": \"電話\",\n  \"social.policy\": \"ソーシャルログインに関連するデータは保存されません。\",\n  \"social.sms\": \"モバイル\",\n  \"social.sms-continue\": \"モバイルで続行\",\n  \"social.sms-invalid-number\": \"無効な電話番号\",\n  \"social.sms-placeholder-text\": \"例:\",\n  \"social.view-less\": \"オプションを少なく表示\",\n  \"social.view-less-new\": \"表示を減らす\",\n  \"social.view-more\": \"その他のオプションを表示\",\n  \"social.view-more-new\": \"もっと見る\",\n  \"post-loading.connected\": \"アカウントに接続されています\",\n  \"post-loading.something-wrong\": \"何かがうまくいかなかった！\"\n};\nvar japanese = {\n  modal: modal\n};\n\n\n//# sourceMappingURL=japanese-Zd4lzpvO.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2ViM2F1dGgvdWkvZGlzdC9qYXBhbmVzZS1aZDRsenB2Ty5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsK0JBQStCLFVBQVU7QUFDekMsZ0NBQWdDLFVBQVU7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsVUFBVTtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixTQUFTO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0M7QUFDdEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0B3ZWIzYXV0aC91aS9kaXN0L2phcGFuZXNlLVpkNGx6cHZPLmpzPzFhMzAiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIG1vZGFsID0ge1xuICBcImFkYXB0ZXItbG9hZGVyLm1lc3NhZ2VcIjogXCJ7e2FkYXB0ZXJ9fSDjgqLjgqvjgqbjg7Pjg4jjgpLnorroqo3jgZfjgabntprooYzjgZnjgotcIixcbiAgXCJhZGFwdGVyLWxvYWRlci5tZXNzYWdlMVwiOiBcInt7YWRhcHRlcn19IOOCkueiuuiqjeOBmeOCi1wiLFxuICBcImFkYXB0ZXItbG9hZGVyLm1lc3NhZ2UyXCI6IFwi57aa6KGM44GZ44KL44Ki44Kr44Km44Oz44OIXCIsXG4gIFwiZXJyb3JzLWludmFsaWQtbnVtYmVyLWVtYWlsXCI6IFwi54Sh5Yq544Gq44Oh44O844Or44Ki44OJ44Os44K544G+44Gf44Gv6Zu76Kmx55Wq5Y+3XCIsXG4gIFwiZXJyb3JzLXJlcXVpcmVkXCI6IFwi5b+F6aCIXCIsXG4gIFwiZXh0ZXJuYWwuYmFja1wiOiBcIuaIu+OCi1wiLFxuICBcImV4dGVybmFsLmNvbm5lY3RcIjogXCLjgqbjgqnjg6zjg4Pjg4jjgajmjqXntppcIixcbiAgXCJleHRlcm5hbC50aXRsZVwiOiBcIuWklumDqOOCpuOCqeODrOODg+ODiFwiLFxuICBcImV4dGVybmFsLndhbGxldGNvbm5lY3QtY29ubmVjdFwiOiBcIuaOpee2muOBmeOCi1wiLFxuICBcImV4dGVybmFsLndhbGxldGNvbm5lY3QtY29weVwiOiBcIlFS44Kz44O844OJ44KS44Kv44Oq44OD44Kv44GX44Gm44Kv44Oq44OD44OX44Oc44O844OJ44Gr44Kz44OU44O8XCIsXG4gIFwiZXh0ZXJuYWwud2FsbGV0Y29ubmVjdC1zdWJ0aXRsZVwiOiBcIldhbGxldENvbm5lY3Tlr77lv5zjgqbjgqnjg6zjg4Pjg4jjgadRUuOCs+ODvOODieOCkuOCueOCreODo+ODs+OBl+OBpuOBj+OBoOOBleOBhFwiLFxuICBcImZvb3Rlci5tZXNzYWdlXCI6IFwi6Ieq5bex5L+d566h44Ot44Kw44Kk44OzIGJ5XCIsXG4gIFwiZm9vdGVyLm1lc3NhZ2UtbmV3XCI6IFwiV2ViM0F1dGjjgavjgojjgovoh6rlt7Hkv53nrqHjg63jgrDjgqTjg7NcIixcbiAgXCJmb290ZXIucG9saWN5XCI6IFwi44OX44Op44Kk44OQ44K344O844Od44Oq44K344O8XCIsXG4gIFwiZm9vdGVyLnRlcm1zXCI6IFwi5Yip55So6KaP57SEXCIsXG4gIFwiZm9vdGVyLnRlcm1zLXNlcnZpY2VcIjogXCLliKnnlKjopo/ntIRcIixcbiAgXCJmb290ZXIudmVyc2lvblwiOiBcIuODkOODvOOCuOODp+ODs1wiLFxuICBcImhlYWRlci1zdWJ0aXRsZVwiOiBcIue2muihjOOBmeOCi+OBn+OCgeOBq+S7peS4i+OBruOCquODl+OCt+ODp+ODs+OBruOBhOOBmuOCjOOBi+OCkumBuOaKnuOBl+OBpuOBj+OBoOOBleOBhFwiLFxuICBcImhlYWRlci1zdWJ0aXRsZS1uYW1lXCI6IFwi44Ov44Oz44Kv44Oq44OD44Kv44GnIHt7YXBwTmFtZX19IOOCpuOCqeODrOODg+ODiFwiLFxuICBcImhlYWRlci1zdWJ0aXRsZS1uZXdcIjogXCLjg6/jg7Pjgq/jg6rjg4Pjgq/jgafjgYLjgarjgZ/jga7jg5bjg63jg4Pjgq/jg4Hjgqfjg7zjg7Pjgqbjgqnjg6zjg4Pjg4hcIixcbiAgXCJoZWFkZXItdGl0bGVcIjogXCLjg63jgrDjgqTjg7NcIixcbiAgXCJoZWFkZXItdG9vbHRpcC1kZXNjXCI6IFwi44Km44Kp44Os44OD44OI44Gv44CB44OW44Ot44OD44Kv44OB44Kn44O844Oz5LiK44Gn44OH44K444K/44Or6LOH55Sj44KS5L+d5a2Y44GK44KI44Gz566h55CG44GZ44KL44Gf44KB44Gu44Ki44Kr44Km44Oz44OI44Go44GX44Gm5qmf6IO944GX44G+44GZ44CCXCIsXG4gIFwiaGVhZGVyLXRvb2x0aXAtdGl0bGVcIjogXCLjgqbjgqnjg6zjg4Pjg4hcIixcbiAgXCJuZXR3b3JrLmFkZC1yZXF1ZXN0XCI6IFwi44GT44Gu44K144Kk44OI44Gv44ON44OD44OI44Ov44O844Kv44Gu6L+95Yqg44KS44Oq44Kv44Ko44K544OI44GX44Gm44GE44G+44GZXCIsXG4gIFwibmV0d29yay5jYW5jZWxcIjogXCLjgq3jg6Pjg7Pjgrvjg6tcIixcbiAgXCJuZXR3b3JrLmZyb21cIjogXCLjgYvjgolcIixcbiAgXCJuZXR3b3JrLnByb2NlZWRcIjogXCLpgLLjgoBcIixcbiAgXCJuZXR3b3JrLnN3aXRjaC1yZXF1ZXN0XCI6IFwi44GT44Gu44K144Kk44OI44Gv44ON44OD44OI44Ov44O844Kv44Gu5YiH44KK5pu/44GI44KS6KaB5rGC44GX44Gm44GE44G+44GZXCIsXG4gIFwibmV0d29yay50b1wiOiBcIuOBuFwiLFxuICBcInBvcHVwLnBob25lLWJvZHlcIjogXCLlm73jgrPjg7zjg4njga/oh6rli5XnmoTjgavmpJzlh7rjgZXjgozjgb7jgZnjgYzjgIHnlbDjgarjgovlm73jga7pm7voqbHnlarlj7fjgpLkvb/nlKjjgZnjgovloLTlkIjjga/jgIHmraPjgZfjgYTlm73jgrPjg7zjg4njgpLmiYvli5XjgaflhaXlipvjgZnjgovlv4XopoHjgYzjgYLjgorjgb7jgZnjgIJcIixcbiAgXCJwb3B1cC5waG9uZS1oZWFkZXJcIjogXCLpm7voqbHnlarlj7fjgajlm73jgrPjg7zjg4lcIixcbiAgXCJzb2NpYWwuY29udGludWVcIjogXCLntprjgZHjgotcIixcbiAgXCJzb2NpYWwuY29udGludWVDdXN0b21cIjogXCJ7e2FkYXB0ZXJ9feOCkue2muOBkeOBvuOBmVwiLFxuICBcInNvY2lhbC5lbWFpbFwiOiBcIkXjg6Hjg7zjg6tcIixcbiAgXCJzb2NpYWwuZW1haWwtY29udGludWVcIjogXCLjg6Hjg7zjg6vjgafntprooYxcIixcbiAgXCJzb2NpYWwuZW1haWwtbmV3XCI6IFwibmFtZUBleGFtcGxlLmNvbVwiLFxuICBcInNvY2lhbC5wYXNzd29yZGxlc3MtY3RhXCI6IFwi57aa44GR44KLXCIsXG4gIFwic29jaWFsLnBhc3N3b3JkbGVzcy10aXRsZVwiOiBcIuODoeODvOODq+OBvuOBn+OBr+mbu+ipsVwiLFxuICBcInNvY2lhbC5waG9uZVwiOiBcIumbu+ipsVwiLFxuICBcInNvY2lhbC5wb2xpY3lcIjogXCLjgr3jg7zjgrfjg6Pjg6vjg63jgrDjgqTjg7PjgavplqLpgKPjgZnjgovjg4fjg7zjgr/jga/kv53lrZjjgZXjgozjgb7jgZvjgpPjgIJcIixcbiAgXCJzb2NpYWwuc21zXCI6IFwi44Oi44OQ44Kk44OrXCIsXG4gIFwic29jaWFsLnNtcy1jb250aW51ZVwiOiBcIuODouODkOOCpOODq+OBp+e2muihjFwiLFxuICBcInNvY2lhbC5zbXMtaW52YWxpZC1udW1iZXJcIjogXCLnhKHlirnjgarpm7voqbHnlarlj7dcIixcbiAgXCJzb2NpYWwuc21zLXBsYWNlaG9sZGVyLXRleHRcIjogXCLkvos6XCIsXG4gIFwic29jaWFsLnZpZXctbGVzc1wiOiBcIuOCquODl+OCt+ODp+ODs+OCkuWwkeOBquOBj+ihqOekulwiLFxuICBcInNvY2lhbC52aWV3LWxlc3MtbmV3XCI6IFwi6KGo56S644KS5rib44KJ44GZXCIsXG4gIFwic29jaWFsLnZpZXctbW9yZVwiOiBcIuOBneOBruS7luOBruOCquODl+OCt+ODp+ODs+OCkuihqOekulwiLFxuICBcInNvY2lhbC52aWV3LW1vcmUtbmV3XCI6IFwi44KC44Gj44Go6KaL44KLXCIsXG4gIFwicG9zdC1sb2FkaW5nLmNvbm5lY3RlZFwiOiBcIuOCouOCq+OCpuODs+ODiOOBq+aOpee2muOBleOCjOOBpuOBhOOBvuOBmVwiLFxuICBcInBvc3QtbG9hZGluZy5zb21ldGhpbmctd3JvbmdcIjogXCLkvZXjgYvjgYzjgYbjgb7jgY/jgYTjgYvjgarjgYvjgaPjgZ/vvIFcIlxufTtcbnZhciBqYXBhbmVzZSA9IHtcbiAgbW9kYWw6IG1vZGFsXG59O1xuXG5leHBvcnQgeyBqYXBhbmVzZSBhcyBkZWZhdWx0LCBtb2RhbCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9amFwYW5lc2UtWmQ0bHpwdk8uanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/ui/dist/japanese-Zd4lzpvO.js\n"));

/***/ })

}]);