"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_web3auth_ui_dist_dutch-PZr73RiR_js"],{

/***/ "(app-pages-browser)/./node_modules/@web3auth/ui/dist/dutch-PZr73RiR.js":
/*!**********************************************************!*\
  !*** ./node_modules/@web3auth/ui/dist/dutch-PZr73RiR.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ dutch; },\n/* harmony export */   modal: function() { return /* binding */ modal; }\n/* harmony export */ });\nvar modal = {\n  \"adapter-loader.message\": \"Verifieer uw {{adapter}}-account om door te gaan\",\n  \"adapter-loader.message1\": \"Verifieer uw {{adapter}}\",\n  \"adapter-loader.message2\": \"account om door te gaan\",\n  \"errors-invalid-number-email\": \"Ongeldig e-mailadres of telefoonnummer\",\n  \"errors-required\": \"Vereist\",\n  \"external.back\": \"Terug\",\n  \"external.connect\": \"Verbinden met portemonnee\",\n  \"external.title\": \"Externe portemonnee\",\n  \"external.walletconnect-connect\": \"Verbinden\",\n  \"external.walletconnect-copy\": \"Klik op de QR-code om te kopiëren naar het klembord\",\n  \"external.walletconnect-subtitle\": \"Scan de QR-code met een WalletConnect-compatibele portemonnee\",\n  \"footer.message\": \"Zelfbeheerde login door\",\n  \"footer.message-new\": \"Zelf-custodial login via Web3Auth\",\n  \"footer.policy\": \"Privacybeleid\",\n  \"footer.terms\": \"Gebruiksvoorwaarden\",\n  \"footer.terms-service\": \"Gebruiksvoorwaarden\",\n  \"footer.version\": \"Versie\",\n  \"header-subtitle\": \"Selecteer een van de volgende opties om door te gaan\",\n  \"header-subtitle-name\": \"Uw {{appName}}-portemonnee met één klik\",\n  \"header-subtitle-new\": \"Uw blockchain-portemonnee met één klik\",\n  \"header-title\": \"Aanmelden\",\n  \"header-tooltip-desc\": \"De portemonnee dient als een account om uw digitale activa op de blockchain op te slaan en te beheren.\",\n  \"header-tooltip-title\": \"Portemonnee\",\n  \"network.add-request\": \"Deze site vraagt om een netwerk toe te voegen\",\n  \"network.cancel\": \"Annuleren\",\n  \"network.from\": \"Van\",\n  \"network.proceed\": \"Doorgaan\",\n  \"network.switch-request\": \"Deze site vraagt om over te schakelen naar een ander netwerk\",\n  \"network.to\": \"Naar\",\n  \"popup.phone-body\": \"Uw landcode wordt automatisch gedetecteerd, maar als u een telefoonnummer uit een ander land gebruikt, moet u handmatig de juiste landcode invoeren.\",\n  \"popup.phone-header\": \"Telefoonnummer en landcode\",\n  \"social.continue\": \"Doorgaan met\",\n  \"social.continueCustom\": \"Doorgaan met {{adapter}}\",\n  \"social.email\": \"E-mail\",\n  \"social.email-continue\": \"Doorgaan met e-mail\",\n  \"social.email-new\": \"<EMAIL>\",\n  \"social.passwordless-cta\": \"Doorgaan\",\n  \"social.passwordless-title\": \"E-mail of telefoon\",\n  \"social.phone\": \"Telefoon\",\n  \"social.policy\": \"We slaan geen gegevens op die verband houden met uw sociale logins.\",\n  \"social.sms\": \"Mobiel\",\n  \"social.sms-continue\": \"Doorgaan met mobiel\",\n  \"social.sms-invalid-number\": \"Ongeldig telefoonnummer\",\n  \"social.sms-placeholder-text\": \"Bijv.:\",\n  \"social.view-less\": \"Minder opties bekijken\",\n  \"social.view-less-new\": \"Minder bekijken\",\n  \"social.view-more\": \"Meer opties bekijken\",\n  \"social.view-more-new\": \"Meer bekijken\",\n  \"post-loading.connected\": \"U bent verbonden met uw account\",\n  \"post-loading.something-wrong\": \"Er is iets fout gegaan!\"\n};\nvar dutch = {\n  modal: modal\n};\n\n\n//# sourceMappingURL=dutch-PZr73RiR.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/ui/dist/dutch-PZr73RiR.js\n"));

/***/ })

}]);