"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_web3auth_ui_dist_spanish-EYsDIJxY_js"],{

/***/ "(app-pages-browser)/./node_modules/@web3auth/ui/dist/spanish-EYsDIJxY.js":
/*!************************************************************!*\
  !*** ./node_modules/@web3auth/ui/dist/spanish-EYsDIJxY.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ spanish; },\n/* harmony export */   modal: function() { return /* binding */ modal; }\n/* harmony export */ });\nvar modal = {\n  \"adapter-loader.message\": \"Verifica tu cuenta {{adapter}} para continuar\",\n  \"adapter-loader.message1\": \"Verifique su {{adapter}}\",\n  \"adapter-loader.message2\": \"cuenta para continuar\",\n  \"errors-invalid-number-email\": \"Correo electrónico o número de teléfono inválido\",\n  \"errors-required\": \"Campo obligatorio\",\n  \"external.back\": \"Atrás\",\n  \"external.connect\": \"Conectar con billetera\",\n  \"external.title\": \"Billetera Externa\",\n  \"external.walletconnect-connect\": \"Conectar\",\n  \"external.walletconnect-copy\": \"Haz clic en el código QR para copiarlo al portapapeles\",\n  \"external.walletconnect-subtitle\": \"Escanear el código QR con una billetera compatible con WalletConnect\",\n  \"footer.message\": \"Inicio de sesión con autogestión por\",\n  \"footer.message-new\": \"Inicio de sesión con autogestión por Web3Auth\",\n  \"footer.policy\": \"Política de privacidad\",\n  \"footer.terms\": \"Términos de Uso\",\n  \"footer.terms-service\": \"Términos de servicio\",\n  \"footer.version\": \"Versión\",\n  \"header-subtitle\": \"Seleccione una de las siguientes opciones para continuar\",\n  \"header-subtitle-name\": \"Su billetera {{appName}} con un solo clic\",\n  \"header-subtitle-new\": \"Su billetera blockchain con un solo clic\",\n  \"header-title\": \"Iniciar sesión\",\n  \"header-tooltip-desc\": \"La billetera sirve como una cuenta para almacenar y administrar sus activos digitales en la cadena de bloques.\",\n  \"header-tooltip-title\": \"Billetera\",\n  \"network.add-request\": \"Este sitio está solicitando agregar una red\",\n  \"network.cancel\": \"Cancelar\",\n  \"network.from\": \"De\",\n  \"network.proceed\": \"Continuar\",\n  \"network.switch-request\": \"Este sitio está solicitando cambiar de red\",\n  \"network.to\": \"A\",\n  \"popup.phone-body\": \"Su código de país se detectará automáticamente, pero si está utilizando un número de teléfono de otro país, deberá ingresar manualmente el código de país correcto.\",\n  \"popup.phone-header\": \"Número de teléfono y código de país\",\n  \"social.continue\": \"Continuar con\",\n  \"social.continueCustom\": \"Continuar con {{adapter}}\",\n  \"social.email\": \"Correo electrónico\",\n  \"social.email-continue\": \"Continuar con correo electrónico\",\n  \"social.email-new\": \"<EMAIL>\",\n  \"social.passwordless-cta\": \"Continuar\",\n  \"social.passwordless-title\": \"Email o teléfono\",\n  \"social.phone\": \"Teléfono\",\n  \"social.policy\": \"No almacenamos ningún dato relacionado con sus inicios de sesión sociales.\",\n  \"social.sms\": \"Móvil\",\n  \"social.sms-continue\": \"Continuar con móvil\",\n  \"social.sms-invalid-number\": \"Número de teléfono inválido\",\n  \"social.sms-placeholder-text\": \"Por ej.:\",\n  \"social.view-less\": \"Ver menos opciones\",\n  \"social.view-less-new\": \"Ver menos\",\n  \"social.view-more\": \"Ver más opciones\",\n  \"social.view-more-new\": \"Ver más\",\n  \"post-loading.connected\": \"Estás conectado con tu cuenta\",\n  \"post-loading.something-wrong\": \"¡Algo salió mal!\"\n};\nvar spanish = {\n  modal: modal\n};\n\n\n//# sourceMappingURL=spanish-EYsDIJxY.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@web3auth/ui/dist/spanish-EYsDIJxY.js\n"));

/***/ })

}]);