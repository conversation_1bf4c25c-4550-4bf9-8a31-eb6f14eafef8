/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tweetnacl";
exports.ids = ["vendor-chunks/tweetnacl"];
exports.modules = {

/***/ "(ssr)/./node_modules/tweetnacl/nacl-fast.js":
/*!*********************************************!*\
  !*** ./node_modules/tweetnacl/nacl-fast.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("(function(nacl) {\n    \"use strict\";\n    // Ported in 2014 by Dmitry Chestnykh and Devi Mandiri.\n    // Public domain.\n    //\n    // Implementation derived from TweetNaCl version 20140427.\n    // See for details: http://tweetnacl.cr.yp.to/\n    var gf = function(init) {\n        var i, r = new Float64Array(16);\n        if (init) for(i = 0; i < init.length; i++)r[i] = init[i];\n        return r;\n    };\n    //  Pluggable, initialized in high-level API below.\n    var randombytes = function() {\n        throw new Error(\"no PRNG\");\n    };\n    var _0 = new Uint8Array(16);\n    var _9 = new Uint8Array(32);\n    _9[0] = 9;\n    var gf0 = gf(), gf1 = gf([\n        1\n    ]), _121665 = gf([\n        0xdb41,\n        1\n    ]), D = gf([\n        0x78a3,\n        0x1359,\n        0x4dca,\n        0x75eb,\n        0xd8ab,\n        0x4141,\n        0x0a4d,\n        0x0070,\n        0xe898,\n        0x7779,\n        0x4079,\n        0x8cc7,\n        0xfe73,\n        0x2b6f,\n        0x6cee,\n        0x5203\n    ]), D2 = gf([\n        0xf159,\n        0x26b2,\n        0x9b94,\n        0xebd6,\n        0xb156,\n        0x8283,\n        0x149a,\n        0x00e0,\n        0xd130,\n        0xeef3,\n        0x80f2,\n        0x198e,\n        0xfce7,\n        0x56df,\n        0xd9dc,\n        0x2406\n    ]), X = gf([\n        0xd51a,\n        0x8f25,\n        0x2d60,\n        0xc956,\n        0xa7b2,\n        0x9525,\n        0xc760,\n        0x692c,\n        0xdc5c,\n        0xfdd6,\n        0xe231,\n        0xc0a4,\n        0x53fe,\n        0xcd6e,\n        0x36d3,\n        0x2169\n    ]), Y = gf([\n        0x6658,\n        0x6666,\n        0x6666,\n        0x6666,\n        0x6666,\n        0x6666,\n        0x6666,\n        0x6666,\n        0x6666,\n        0x6666,\n        0x6666,\n        0x6666,\n        0x6666,\n        0x6666,\n        0x6666,\n        0x6666\n    ]), I = gf([\n        0xa0b0,\n        0x4a0e,\n        0x1b27,\n        0xc4ee,\n        0xe478,\n        0xad2f,\n        0x1806,\n        0x2f43,\n        0xd7a7,\n        0x3dfb,\n        0x0099,\n        0x2b4d,\n        0xdf0b,\n        0x4fc1,\n        0x2480,\n        0x2b83\n    ]);\n    function ts64(x, i, h, l) {\n        x[i] = h >> 24 & 0xff;\n        x[i + 1] = h >> 16 & 0xff;\n        x[i + 2] = h >> 8 & 0xff;\n        x[i + 3] = h & 0xff;\n        x[i + 4] = l >> 24 & 0xff;\n        x[i + 5] = l >> 16 & 0xff;\n        x[i + 6] = l >> 8 & 0xff;\n        x[i + 7] = l & 0xff;\n    }\n    function vn(x, xi, y, yi, n) {\n        var i, d = 0;\n        for(i = 0; i < n; i++)d |= x[xi + i] ^ y[yi + i];\n        return (1 & d - 1 >>> 8) - 1;\n    }\n    function crypto_verify_16(x, xi, y, yi) {\n        return vn(x, xi, y, yi, 16);\n    }\n    function crypto_verify_32(x, xi, y, yi) {\n        return vn(x, xi, y, yi, 32);\n    }\n    function core_salsa20(o, p, k, c) {\n        var j0 = c[0] & 0xff | (c[1] & 0xff) << 8 | (c[2] & 0xff) << 16 | (c[3] & 0xff) << 24, j1 = k[0] & 0xff | (k[1] & 0xff) << 8 | (k[2] & 0xff) << 16 | (k[3] & 0xff) << 24, j2 = k[4] & 0xff | (k[5] & 0xff) << 8 | (k[6] & 0xff) << 16 | (k[7] & 0xff) << 24, j3 = k[8] & 0xff | (k[9] & 0xff) << 8 | (k[10] & 0xff) << 16 | (k[11] & 0xff) << 24, j4 = k[12] & 0xff | (k[13] & 0xff) << 8 | (k[14] & 0xff) << 16 | (k[15] & 0xff) << 24, j5 = c[4] & 0xff | (c[5] & 0xff) << 8 | (c[6] & 0xff) << 16 | (c[7] & 0xff) << 24, j6 = p[0] & 0xff | (p[1] & 0xff) << 8 | (p[2] & 0xff) << 16 | (p[3] & 0xff) << 24, j7 = p[4] & 0xff | (p[5] & 0xff) << 8 | (p[6] & 0xff) << 16 | (p[7] & 0xff) << 24, j8 = p[8] & 0xff | (p[9] & 0xff) << 8 | (p[10] & 0xff) << 16 | (p[11] & 0xff) << 24, j9 = p[12] & 0xff | (p[13] & 0xff) << 8 | (p[14] & 0xff) << 16 | (p[15] & 0xff) << 24, j10 = c[8] & 0xff | (c[9] & 0xff) << 8 | (c[10] & 0xff) << 16 | (c[11] & 0xff) << 24, j11 = k[16] & 0xff | (k[17] & 0xff) << 8 | (k[18] & 0xff) << 16 | (k[19] & 0xff) << 24, j12 = k[20] & 0xff | (k[21] & 0xff) << 8 | (k[22] & 0xff) << 16 | (k[23] & 0xff) << 24, j13 = k[24] & 0xff | (k[25] & 0xff) << 8 | (k[26] & 0xff) << 16 | (k[27] & 0xff) << 24, j14 = k[28] & 0xff | (k[29] & 0xff) << 8 | (k[30] & 0xff) << 16 | (k[31] & 0xff) << 24, j15 = c[12] & 0xff | (c[13] & 0xff) << 8 | (c[14] & 0xff) << 16 | (c[15] & 0xff) << 24;\n        var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7, x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14, x15 = j15, u;\n        for(var i = 0; i < 20; i += 2){\n            u = x0 + x12 | 0;\n            x4 ^= u << 7 | u >>> 32 - 7;\n            u = x4 + x0 | 0;\n            x8 ^= u << 9 | u >>> 32 - 9;\n            u = x8 + x4 | 0;\n            x12 ^= u << 13 | u >>> 32 - 13;\n            u = x12 + x8 | 0;\n            x0 ^= u << 18 | u >>> 32 - 18;\n            u = x5 + x1 | 0;\n            x9 ^= u << 7 | u >>> 32 - 7;\n            u = x9 + x5 | 0;\n            x13 ^= u << 9 | u >>> 32 - 9;\n            u = x13 + x9 | 0;\n            x1 ^= u << 13 | u >>> 32 - 13;\n            u = x1 + x13 | 0;\n            x5 ^= u << 18 | u >>> 32 - 18;\n            u = x10 + x6 | 0;\n            x14 ^= u << 7 | u >>> 32 - 7;\n            u = x14 + x10 | 0;\n            x2 ^= u << 9 | u >>> 32 - 9;\n            u = x2 + x14 | 0;\n            x6 ^= u << 13 | u >>> 32 - 13;\n            u = x6 + x2 | 0;\n            x10 ^= u << 18 | u >>> 32 - 18;\n            u = x15 + x11 | 0;\n            x3 ^= u << 7 | u >>> 32 - 7;\n            u = x3 + x15 | 0;\n            x7 ^= u << 9 | u >>> 32 - 9;\n            u = x7 + x3 | 0;\n            x11 ^= u << 13 | u >>> 32 - 13;\n            u = x11 + x7 | 0;\n            x15 ^= u << 18 | u >>> 32 - 18;\n            u = x0 + x3 | 0;\n            x1 ^= u << 7 | u >>> 32 - 7;\n            u = x1 + x0 | 0;\n            x2 ^= u << 9 | u >>> 32 - 9;\n            u = x2 + x1 | 0;\n            x3 ^= u << 13 | u >>> 32 - 13;\n            u = x3 + x2 | 0;\n            x0 ^= u << 18 | u >>> 32 - 18;\n            u = x5 + x4 | 0;\n            x6 ^= u << 7 | u >>> 32 - 7;\n            u = x6 + x5 | 0;\n            x7 ^= u << 9 | u >>> 32 - 9;\n            u = x7 + x6 | 0;\n            x4 ^= u << 13 | u >>> 32 - 13;\n            u = x4 + x7 | 0;\n            x5 ^= u << 18 | u >>> 32 - 18;\n            u = x10 + x9 | 0;\n            x11 ^= u << 7 | u >>> 32 - 7;\n            u = x11 + x10 | 0;\n            x8 ^= u << 9 | u >>> 32 - 9;\n            u = x8 + x11 | 0;\n            x9 ^= u << 13 | u >>> 32 - 13;\n            u = x9 + x8 | 0;\n            x10 ^= u << 18 | u >>> 32 - 18;\n            u = x15 + x14 | 0;\n            x12 ^= u << 7 | u >>> 32 - 7;\n            u = x12 + x15 | 0;\n            x13 ^= u << 9 | u >>> 32 - 9;\n            u = x13 + x12 | 0;\n            x14 ^= u << 13 | u >>> 32 - 13;\n            u = x14 + x13 | 0;\n            x15 ^= u << 18 | u >>> 32 - 18;\n        }\n        x0 = x0 + j0 | 0;\n        x1 = x1 + j1 | 0;\n        x2 = x2 + j2 | 0;\n        x3 = x3 + j3 | 0;\n        x4 = x4 + j4 | 0;\n        x5 = x5 + j5 | 0;\n        x6 = x6 + j6 | 0;\n        x7 = x7 + j7 | 0;\n        x8 = x8 + j8 | 0;\n        x9 = x9 + j9 | 0;\n        x10 = x10 + j10 | 0;\n        x11 = x11 + j11 | 0;\n        x12 = x12 + j12 | 0;\n        x13 = x13 + j13 | 0;\n        x14 = x14 + j14 | 0;\n        x15 = x15 + j15 | 0;\n        o[0] = x0 >>> 0 & 0xff;\n        o[1] = x0 >>> 8 & 0xff;\n        o[2] = x0 >>> 16 & 0xff;\n        o[3] = x0 >>> 24 & 0xff;\n        o[4] = x1 >>> 0 & 0xff;\n        o[5] = x1 >>> 8 & 0xff;\n        o[6] = x1 >>> 16 & 0xff;\n        o[7] = x1 >>> 24 & 0xff;\n        o[8] = x2 >>> 0 & 0xff;\n        o[9] = x2 >>> 8 & 0xff;\n        o[10] = x2 >>> 16 & 0xff;\n        o[11] = x2 >>> 24 & 0xff;\n        o[12] = x3 >>> 0 & 0xff;\n        o[13] = x3 >>> 8 & 0xff;\n        o[14] = x3 >>> 16 & 0xff;\n        o[15] = x3 >>> 24 & 0xff;\n        o[16] = x4 >>> 0 & 0xff;\n        o[17] = x4 >>> 8 & 0xff;\n        o[18] = x4 >>> 16 & 0xff;\n        o[19] = x4 >>> 24 & 0xff;\n        o[20] = x5 >>> 0 & 0xff;\n        o[21] = x5 >>> 8 & 0xff;\n        o[22] = x5 >>> 16 & 0xff;\n        o[23] = x5 >>> 24 & 0xff;\n        o[24] = x6 >>> 0 & 0xff;\n        o[25] = x6 >>> 8 & 0xff;\n        o[26] = x6 >>> 16 & 0xff;\n        o[27] = x6 >>> 24 & 0xff;\n        o[28] = x7 >>> 0 & 0xff;\n        o[29] = x7 >>> 8 & 0xff;\n        o[30] = x7 >>> 16 & 0xff;\n        o[31] = x7 >>> 24 & 0xff;\n        o[32] = x8 >>> 0 & 0xff;\n        o[33] = x8 >>> 8 & 0xff;\n        o[34] = x8 >>> 16 & 0xff;\n        o[35] = x8 >>> 24 & 0xff;\n        o[36] = x9 >>> 0 & 0xff;\n        o[37] = x9 >>> 8 & 0xff;\n        o[38] = x9 >>> 16 & 0xff;\n        o[39] = x9 >>> 24 & 0xff;\n        o[40] = x10 >>> 0 & 0xff;\n        o[41] = x10 >>> 8 & 0xff;\n        o[42] = x10 >>> 16 & 0xff;\n        o[43] = x10 >>> 24 & 0xff;\n        o[44] = x11 >>> 0 & 0xff;\n        o[45] = x11 >>> 8 & 0xff;\n        o[46] = x11 >>> 16 & 0xff;\n        o[47] = x11 >>> 24 & 0xff;\n        o[48] = x12 >>> 0 & 0xff;\n        o[49] = x12 >>> 8 & 0xff;\n        o[50] = x12 >>> 16 & 0xff;\n        o[51] = x12 >>> 24 & 0xff;\n        o[52] = x13 >>> 0 & 0xff;\n        o[53] = x13 >>> 8 & 0xff;\n        o[54] = x13 >>> 16 & 0xff;\n        o[55] = x13 >>> 24 & 0xff;\n        o[56] = x14 >>> 0 & 0xff;\n        o[57] = x14 >>> 8 & 0xff;\n        o[58] = x14 >>> 16 & 0xff;\n        o[59] = x14 >>> 24 & 0xff;\n        o[60] = x15 >>> 0 & 0xff;\n        o[61] = x15 >>> 8 & 0xff;\n        o[62] = x15 >>> 16 & 0xff;\n        o[63] = x15 >>> 24 & 0xff;\n    }\n    function core_hsalsa20(o, p, k, c) {\n        var j0 = c[0] & 0xff | (c[1] & 0xff) << 8 | (c[2] & 0xff) << 16 | (c[3] & 0xff) << 24, j1 = k[0] & 0xff | (k[1] & 0xff) << 8 | (k[2] & 0xff) << 16 | (k[3] & 0xff) << 24, j2 = k[4] & 0xff | (k[5] & 0xff) << 8 | (k[6] & 0xff) << 16 | (k[7] & 0xff) << 24, j3 = k[8] & 0xff | (k[9] & 0xff) << 8 | (k[10] & 0xff) << 16 | (k[11] & 0xff) << 24, j4 = k[12] & 0xff | (k[13] & 0xff) << 8 | (k[14] & 0xff) << 16 | (k[15] & 0xff) << 24, j5 = c[4] & 0xff | (c[5] & 0xff) << 8 | (c[6] & 0xff) << 16 | (c[7] & 0xff) << 24, j6 = p[0] & 0xff | (p[1] & 0xff) << 8 | (p[2] & 0xff) << 16 | (p[3] & 0xff) << 24, j7 = p[4] & 0xff | (p[5] & 0xff) << 8 | (p[6] & 0xff) << 16 | (p[7] & 0xff) << 24, j8 = p[8] & 0xff | (p[9] & 0xff) << 8 | (p[10] & 0xff) << 16 | (p[11] & 0xff) << 24, j9 = p[12] & 0xff | (p[13] & 0xff) << 8 | (p[14] & 0xff) << 16 | (p[15] & 0xff) << 24, j10 = c[8] & 0xff | (c[9] & 0xff) << 8 | (c[10] & 0xff) << 16 | (c[11] & 0xff) << 24, j11 = k[16] & 0xff | (k[17] & 0xff) << 8 | (k[18] & 0xff) << 16 | (k[19] & 0xff) << 24, j12 = k[20] & 0xff | (k[21] & 0xff) << 8 | (k[22] & 0xff) << 16 | (k[23] & 0xff) << 24, j13 = k[24] & 0xff | (k[25] & 0xff) << 8 | (k[26] & 0xff) << 16 | (k[27] & 0xff) << 24, j14 = k[28] & 0xff | (k[29] & 0xff) << 8 | (k[30] & 0xff) << 16 | (k[31] & 0xff) << 24, j15 = c[12] & 0xff | (c[13] & 0xff) << 8 | (c[14] & 0xff) << 16 | (c[15] & 0xff) << 24;\n        var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7, x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14, x15 = j15, u;\n        for(var i = 0; i < 20; i += 2){\n            u = x0 + x12 | 0;\n            x4 ^= u << 7 | u >>> 32 - 7;\n            u = x4 + x0 | 0;\n            x8 ^= u << 9 | u >>> 32 - 9;\n            u = x8 + x4 | 0;\n            x12 ^= u << 13 | u >>> 32 - 13;\n            u = x12 + x8 | 0;\n            x0 ^= u << 18 | u >>> 32 - 18;\n            u = x5 + x1 | 0;\n            x9 ^= u << 7 | u >>> 32 - 7;\n            u = x9 + x5 | 0;\n            x13 ^= u << 9 | u >>> 32 - 9;\n            u = x13 + x9 | 0;\n            x1 ^= u << 13 | u >>> 32 - 13;\n            u = x1 + x13 | 0;\n            x5 ^= u << 18 | u >>> 32 - 18;\n            u = x10 + x6 | 0;\n            x14 ^= u << 7 | u >>> 32 - 7;\n            u = x14 + x10 | 0;\n            x2 ^= u << 9 | u >>> 32 - 9;\n            u = x2 + x14 | 0;\n            x6 ^= u << 13 | u >>> 32 - 13;\n            u = x6 + x2 | 0;\n            x10 ^= u << 18 | u >>> 32 - 18;\n            u = x15 + x11 | 0;\n            x3 ^= u << 7 | u >>> 32 - 7;\n            u = x3 + x15 | 0;\n            x7 ^= u << 9 | u >>> 32 - 9;\n            u = x7 + x3 | 0;\n            x11 ^= u << 13 | u >>> 32 - 13;\n            u = x11 + x7 | 0;\n            x15 ^= u << 18 | u >>> 32 - 18;\n            u = x0 + x3 | 0;\n            x1 ^= u << 7 | u >>> 32 - 7;\n            u = x1 + x0 | 0;\n            x2 ^= u << 9 | u >>> 32 - 9;\n            u = x2 + x1 | 0;\n            x3 ^= u << 13 | u >>> 32 - 13;\n            u = x3 + x2 | 0;\n            x0 ^= u << 18 | u >>> 32 - 18;\n            u = x5 + x4 | 0;\n            x6 ^= u << 7 | u >>> 32 - 7;\n            u = x6 + x5 | 0;\n            x7 ^= u << 9 | u >>> 32 - 9;\n            u = x7 + x6 | 0;\n            x4 ^= u << 13 | u >>> 32 - 13;\n            u = x4 + x7 | 0;\n            x5 ^= u << 18 | u >>> 32 - 18;\n            u = x10 + x9 | 0;\n            x11 ^= u << 7 | u >>> 32 - 7;\n            u = x11 + x10 | 0;\n            x8 ^= u << 9 | u >>> 32 - 9;\n            u = x8 + x11 | 0;\n            x9 ^= u << 13 | u >>> 32 - 13;\n            u = x9 + x8 | 0;\n            x10 ^= u << 18 | u >>> 32 - 18;\n            u = x15 + x14 | 0;\n            x12 ^= u << 7 | u >>> 32 - 7;\n            u = x12 + x15 | 0;\n            x13 ^= u << 9 | u >>> 32 - 9;\n            u = x13 + x12 | 0;\n            x14 ^= u << 13 | u >>> 32 - 13;\n            u = x14 + x13 | 0;\n            x15 ^= u << 18 | u >>> 32 - 18;\n        }\n        o[0] = x0 >>> 0 & 0xff;\n        o[1] = x0 >>> 8 & 0xff;\n        o[2] = x0 >>> 16 & 0xff;\n        o[3] = x0 >>> 24 & 0xff;\n        o[4] = x5 >>> 0 & 0xff;\n        o[5] = x5 >>> 8 & 0xff;\n        o[6] = x5 >>> 16 & 0xff;\n        o[7] = x5 >>> 24 & 0xff;\n        o[8] = x10 >>> 0 & 0xff;\n        o[9] = x10 >>> 8 & 0xff;\n        o[10] = x10 >>> 16 & 0xff;\n        o[11] = x10 >>> 24 & 0xff;\n        o[12] = x15 >>> 0 & 0xff;\n        o[13] = x15 >>> 8 & 0xff;\n        o[14] = x15 >>> 16 & 0xff;\n        o[15] = x15 >>> 24 & 0xff;\n        o[16] = x6 >>> 0 & 0xff;\n        o[17] = x6 >>> 8 & 0xff;\n        o[18] = x6 >>> 16 & 0xff;\n        o[19] = x6 >>> 24 & 0xff;\n        o[20] = x7 >>> 0 & 0xff;\n        o[21] = x7 >>> 8 & 0xff;\n        o[22] = x7 >>> 16 & 0xff;\n        o[23] = x7 >>> 24 & 0xff;\n        o[24] = x8 >>> 0 & 0xff;\n        o[25] = x8 >>> 8 & 0xff;\n        o[26] = x8 >>> 16 & 0xff;\n        o[27] = x8 >>> 24 & 0xff;\n        o[28] = x9 >>> 0 & 0xff;\n        o[29] = x9 >>> 8 & 0xff;\n        o[30] = x9 >>> 16 & 0xff;\n        o[31] = x9 >>> 24 & 0xff;\n    }\n    function crypto_core_salsa20(out, inp, k, c) {\n        core_salsa20(out, inp, k, c);\n    }\n    function crypto_core_hsalsa20(out, inp, k, c) {\n        core_hsalsa20(out, inp, k, c);\n    }\n    var sigma = new Uint8Array([\n        101,\n        120,\n        112,\n        97,\n        110,\n        100,\n        32,\n        51,\n        50,\n        45,\n        98,\n        121,\n        116,\n        101,\n        32,\n        107\n    ]);\n    // \"expand 32-byte k\"\n    function crypto_stream_salsa20_xor(c, cpos, m, mpos, b, n, k) {\n        var z = new Uint8Array(16), x = new Uint8Array(64);\n        var u, i;\n        for(i = 0; i < 16; i++)z[i] = 0;\n        for(i = 0; i < 8; i++)z[i] = n[i];\n        while(b >= 64){\n            crypto_core_salsa20(x, z, k, sigma);\n            for(i = 0; i < 64; i++)c[cpos + i] = m[mpos + i] ^ x[i];\n            u = 1;\n            for(i = 8; i < 16; i++){\n                u = u + (z[i] & 0xff) | 0;\n                z[i] = u & 0xff;\n                u >>>= 8;\n            }\n            b -= 64;\n            cpos += 64;\n            mpos += 64;\n        }\n        if (b > 0) {\n            crypto_core_salsa20(x, z, k, sigma);\n            for(i = 0; i < b; i++)c[cpos + i] = m[mpos + i] ^ x[i];\n        }\n        return 0;\n    }\n    function crypto_stream_salsa20(c, cpos, b, n, k) {\n        var z = new Uint8Array(16), x = new Uint8Array(64);\n        var u, i;\n        for(i = 0; i < 16; i++)z[i] = 0;\n        for(i = 0; i < 8; i++)z[i] = n[i];\n        while(b >= 64){\n            crypto_core_salsa20(x, z, k, sigma);\n            for(i = 0; i < 64; i++)c[cpos + i] = x[i];\n            u = 1;\n            for(i = 8; i < 16; i++){\n                u = u + (z[i] & 0xff) | 0;\n                z[i] = u & 0xff;\n                u >>>= 8;\n            }\n            b -= 64;\n            cpos += 64;\n        }\n        if (b > 0) {\n            crypto_core_salsa20(x, z, k, sigma);\n            for(i = 0; i < b; i++)c[cpos + i] = x[i];\n        }\n        return 0;\n    }\n    function crypto_stream(c, cpos, d, n, k) {\n        var s = new Uint8Array(32);\n        crypto_core_hsalsa20(s, n, k, sigma);\n        var sn = new Uint8Array(8);\n        for(var i = 0; i < 8; i++)sn[i] = n[i + 16];\n        return crypto_stream_salsa20(c, cpos, d, sn, s);\n    }\n    function crypto_stream_xor(c, cpos, m, mpos, d, n, k) {\n        var s = new Uint8Array(32);\n        crypto_core_hsalsa20(s, n, k, sigma);\n        var sn = new Uint8Array(8);\n        for(var i = 0; i < 8; i++)sn[i] = n[i + 16];\n        return crypto_stream_salsa20_xor(c, cpos, m, mpos, d, sn, s);\n    }\n    /*\n* Port of Andrew Moon's Poly1305-donna-16. Public domain.\n* https://github.com/floodyberry/poly1305-donna\n*/ var poly1305 = function(key) {\n        this.buffer = new Uint8Array(16);\n        this.r = new Uint16Array(10);\n        this.h = new Uint16Array(10);\n        this.pad = new Uint16Array(8);\n        this.leftover = 0;\n        this.fin = 0;\n        var t0, t1, t2, t3, t4, t5, t6, t7;\n        t0 = key[0] & 0xff | (key[1] & 0xff) << 8;\n        this.r[0] = t0 & 0x1fff;\n        t1 = key[2] & 0xff | (key[3] & 0xff) << 8;\n        this.r[1] = (t0 >>> 13 | t1 << 3) & 0x1fff;\n        t2 = key[4] & 0xff | (key[5] & 0xff) << 8;\n        this.r[2] = (t1 >>> 10 | t2 << 6) & 0x1f03;\n        t3 = key[6] & 0xff | (key[7] & 0xff) << 8;\n        this.r[3] = (t2 >>> 7 | t3 << 9) & 0x1fff;\n        t4 = key[8] & 0xff | (key[9] & 0xff) << 8;\n        this.r[4] = (t3 >>> 4 | t4 << 12) & 0x00ff;\n        this.r[5] = t4 >>> 1 & 0x1ffe;\n        t5 = key[10] & 0xff | (key[11] & 0xff) << 8;\n        this.r[6] = (t4 >>> 14 | t5 << 2) & 0x1fff;\n        t6 = key[12] & 0xff | (key[13] & 0xff) << 8;\n        this.r[7] = (t5 >>> 11 | t6 << 5) & 0x1f81;\n        t7 = key[14] & 0xff | (key[15] & 0xff) << 8;\n        this.r[8] = (t6 >>> 8 | t7 << 8) & 0x1fff;\n        this.r[9] = t7 >>> 5 & 0x007f;\n        this.pad[0] = key[16] & 0xff | (key[17] & 0xff) << 8;\n        this.pad[1] = key[18] & 0xff | (key[19] & 0xff) << 8;\n        this.pad[2] = key[20] & 0xff | (key[21] & 0xff) << 8;\n        this.pad[3] = key[22] & 0xff | (key[23] & 0xff) << 8;\n        this.pad[4] = key[24] & 0xff | (key[25] & 0xff) << 8;\n        this.pad[5] = key[26] & 0xff | (key[27] & 0xff) << 8;\n        this.pad[6] = key[28] & 0xff | (key[29] & 0xff) << 8;\n        this.pad[7] = key[30] & 0xff | (key[31] & 0xff) << 8;\n    };\n    poly1305.prototype.blocks = function(m, mpos, bytes) {\n        var hibit = this.fin ? 0 : 1 << 11;\n        var t0, t1, t2, t3, t4, t5, t6, t7, c;\n        var d0, d1, d2, d3, d4, d5, d6, d7, d8, d9;\n        var h0 = this.h[0], h1 = this.h[1], h2 = this.h[2], h3 = this.h[3], h4 = this.h[4], h5 = this.h[5], h6 = this.h[6], h7 = this.h[7], h8 = this.h[8], h9 = this.h[9];\n        var r0 = this.r[0], r1 = this.r[1], r2 = this.r[2], r3 = this.r[3], r4 = this.r[4], r5 = this.r[5], r6 = this.r[6], r7 = this.r[7], r8 = this.r[8], r9 = this.r[9];\n        while(bytes >= 16){\n            t0 = m[mpos + 0] & 0xff | (m[mpos + 1] & 0xff) << 8;\n            h0 += t0 & 0x1fff;\n            t1 = m[mpos + 2] & 0xff | (m[mpos + 3] & 0xff) << 8;\n            h1 += (t0 >>> 13 | t1 << 3) & 0x1fff;\n            t2 = m[mpos + 4] & 0xff | (m[mpos + 5] & 0xff) << 8;\n            h2 += (t1 >>> 10 | t2 << 6) & 0x1fff;\n            t3 = m[mpos + 6] & 0xff | (m[mpos + 7] & 0xff) << 8;\n            h3 += (t2 >>> 7 | t3 << 9) & 0x1fff;\n            t4 = m[mpos + 8] & 0xff | (m[mpos + 9] & 0xff) << 8;\n            h4 += (t3 >>> 4 | t4 << 12) & 0x1fff;\n            h5 += t4 >>> 1 & 0x1fff;\n            t5 = m[mpos + 10] & 0xff | (m[mpos + 11] & 0xff) << 8;\n            h6 += (t4 >>> 14 | t5 << 2) & 0x1fff;\n            t6 = m[mpos + 12] & 0xff | (m[mpos + 13] & 0xff) << 8;\n            h7 += (t5 >>> 11 | t6 << 5) & 0x1fff;\n            t7 = m[mpos + 14] & 0xff | (m[mpos + 15] & 0xff) << 8;\n            h8 += (t6 >>> 8 | t7 << 8) & 0x1fff;\n            h9 += t7 >>> 5 | hibit;\n            c = 0;\n            d0 = c;\n            d0 += h0 * r0;\n            d0 += h1 * (5 * r9);\n            d0 += h2 * (5 * r8);\n            d0 += h3 * (5 * r7);\n            d0 += h4 * (5 * r6);\n            c = d0 >>> 13;\n            d0 &= 0x1fff;\n            d0 += h5 * (5 * r5);\n            d0 += h6 * (5 * r4);\n            d0 += h7 * (5 * r3);\n            d0 += h8 * (5 * r2);\n            d0 += h9 * (5 * r1);\n            c += d0 >>> 13;\n            d0 &= 0x1fff;\n            d1 = c;\n            d1 += h0 * r1;\n            d1 += h1 * r0;\n            d1 += h2 * (5 * r9);\n            d1 += h3 * (5 * r8);\n            d1 += h4 * (5 * r7);\n            c = d1 >>> 13;\n            d1 &= 0x1fff;\n            d1 += h5 * (5 * r6);\n            d1 += h6 * (5 * r5);\n            d1 += h7 * (5 * r4);\n            d1 += h8 * (5 * r3);\n            d1 += h9 * (5 * r2);\n            c += d1 >>> 13;\n            d1 &= 0x1fff;\n            d2 = c;\n            d2 += h0 * r2;\n            d2 += h1 * r1;\n            d2 += h2 * r0;\n            d2 += h3 * (5 * r9);\n            d2 += h4 * (5 * r8);\n            c = d2 >>> 13;\n            d2 &= 0x1fff;\n            d2 += h5 * (5 * r7);\n            d2 += h6 * (5 * r6);\n            d2 += h7 * (5 * r5);\n            d2 += h8 * (5 * r4);\n            d2 += h9 * (5 * r3);\n            c += d2 >>> 13;\n            d2 &= 0x1fff;\n            d3 = c;\n            d3 += h0 * r3;\n            d3 += h1 * r2;\n            d3 += h2 * r1;\n            d3 += h3 * r0;\n            d3 += h4 * (5 * r9);\n            c = d3 >>> 13;\n            d3 &= 0x1fff;\n            d3 += h5 * (5 * r8);\n            d3 += h6 * (5 * r7);\n            d3 += h7 * (5 * r6);\n            d3 += h8 * (5 * r5);\n            d3 += h9 * (5 * r4);\n            c += d3 >>> 13;\n            d3 &= 0x1fff;\n            d4 = c;\n            d4 += h0 * r4;\n            d4 += h1 * r3;\n            d4 += h2 * r2;\n            d4 += h3 * r1;\n            d4 += h4 * r0;\n            c = d4 >>> 13;\n            d4 &= 0x1fff;\n            d4 += h5 * (5 * r9);\n            d4 += h6 * (5 * r8);\n            d4 += h7 * (5 * r7);\n            d4 += h8 * (5 * r6);\n            d4 += h9 * (5 * r5);\n            c += d4 >>> 13;\n            d4 &= 0x1fff;\n            d5 = c;\n            d5 += h0 * r5;\n            d5 += h1 * r4;\n            d5 += h2 * r3;\n            d5 += h3 * r2;\n            d5 += h4 * r1;\n            c = d5 >>> 13;\n            d5 &= 0x1fff;\n            d5 += h5 * r0;\n            d5 += h6 * (5 * r9);\n            d5 += h7 * (5 * r8);\n            d5 += h8 * (5 * r7);\n            d5 += h9 * (5 * r6);\n            c += d5 >>> 13;\n            d5 &= 0x1fff;\n            d6 = c;\n            d6 += h0 * r6;\n            d6 += h1 * r5;\n            d6 += h2 * r4;\n            d6 += h3 * r3;\n            d6 += h4 * r2;\n            c = d6 >>> 13;\n            d6 &= 0x1fff;\n            d6 += h5 * r1;\n            d6 += h6 * r0;\n            d6 += h7 * (5 * r9);\n            d6 += h8 * (5 * r8);\n            d6 += h9 * (5 * r7);\n            c += d6 >>> 13;\n            d6 &= 0x1fff;\n            d7 = c;\n            d7 += h0 * r7;\n            d7 += h1 * r6;\n            d7 += h2 * r5;\n            d7 += h3 * r4;\n            d7 += h4 * r3;\n            c = d7 >>> 13;\n            d7 &= 0x1fff;\n            d7 += h5 * r2;\n            d7 += h6 * r1;\n            d7 += h7 * r0;\n            d7 += h8 * (5 * r9);\n            d7 += h9 * (5 * r8);\n            c += d7 >>> 13;\n            d7 &= 0x1fff;\n            d8 = c;\n            d8 += h0 * r8;\n            d8 += h1 * r7;\n            d8 += h2 * r6;\n            d8 += h3 * r5;\n            d8 += h4 * r4;\n            c = d8 >>> 13;\n            d8 &= 0x1fff;\n            d8 += h5 * r3;\n            d8 += h6 * r2;\n            d8 += h7 * r1;\n            d8 += h8 * r0;\n            d8 += h9 * (5 * r9);\n            c += d8 >>> 13;\n            d8 &= 0x1fff;\n            d9 = c;\n            d9 += h0 * r9;\n            d9 += h1 * r8;\n            d9 += h2 * r7;\n            d9 += h3 * r6;\n            d9 += h4 * r5;\n            c = d9 >>> 13;\n            d9 &= 0x1fff;\n            d9 += h5 * r4;\n            d9 += h6 * r3;\n            d9 += h7 * r2;\n            d9 += h8 * r1;\n            d9 += h9 * r0;\n            c += d9 >>> 13;\n            d9 &= 0x1fff;\n            c = (c << 2) + c | 0;\n            c = c + d0 | 0;\n            d0 = c & 0x1fff;\n            c = c >>> 13;\n            d1 += c;\n            h0 = d0;\n            h1 = d1;\n            h2 = d2;\n            h3 = d3;\n            h4 = d4;\n            h5 = d5;\n            h6 = d6;\n            h7 = d7;\n            h8 = d8;\n            h9 = d9;\n            mpos += 16;\n            bytes -= 16;\n        }\n        this.h[0] = h0;\n        this.h[1] = h1;\n        this.h[2] = h2;\n        this.h[3] = h3;\n        this.h[4] = h4;\n        this.h[5] = h5;\n        this.h[6] = h6;\n        this.h[7] = h7;\n        this.h[8] = h8;\n        this.h[9] = h9;\n    };\n    poly1305.prototype.finish = function(mac, macpos) {\n        var g = new Uint16Array(10);\n        var c, mask, f, i;\n        if (this.leftover) {\n            i = this.leftover;\n            this.buffer[i++] = 1;\n            for(; i < 16; i++)this.buffer[i] = 0;\n            this.fin = 1;\n            this.blocks(this.buffer, 0, 16);\n        }\n        c = this.h[1] >>> 13;\n        this.h[1] &= 0x1fff;\n        for(i = 2; i < 10; i++){\n            this.h[i] += c;\n            c = this.h[i] >>> 13;\n            this.h[i] &= 0x1fff;\n        }\n        this.h[0] += c * 5;\n        c = this.h[0] >>> 13;\n        this.h[0] &= 0x1fff;\n        this.h[1] += c;\n        c = this.h[1] >>> 13;\n        this.h[1] &= 0x1fff;\n        this.h[2] += c;\n        g[0] = this.h[0] + 5;\n        c = g[0] >>> 13;\n        g[0] &= 0x1fff;\n        for(i = 1; i < 10; i++){\n            g[i] = this.h[i] + c;\n            c = g[i] >>> 13;\n            g[i] &= 0x1fff;\n        }\n        g[9] -= 1 << 13;\n        mask = (c ^ 1) - 1;\n        for(i = 0; i < 10; i++)g[i] &= mask;\n        mask = ~mask;\n        for(i = 0; i < 10; i++)this.h[i] = this.h[i] & mask | g[i];\n        this.h[0] = (this.h[0] | this.h[1] << 13) & 0xffff;\n        this.h[1] = (this.h[1] >>> 3 | this.h[2] << 10) & 0xffff;\n        this.h[2] = (this.h[2] >>> 6 | this.h[3] << 7) & 0xffff;\n        this.h[3] = (this.h[3] >>> 9 | this.h[4] << 4) & 0xffff;\n        this.h[4] = (this.h[4] >>> 12 | this.h[5] << 1 | this.h[6] << 14) & 0xffff;\n        this.h[5] = (this.h[6] >>> 2 | this.h[7] << 11) & 0xffff;\n        this.h[6] = (this.h[7] >>> 5 | this.h[8] << 8) & 0xffff;\n        this.h[7] = (this.h[8] >>> 8 | this.h[9] << 5) & 0xffff;\n        f = this.h[0] + this.pad[0];\n        this.h[0] = f & 0xffff;\n        for(i = 1; i < 8; i++){\n            f = (this.h[i] + this.pad[i] | 0) + (f >>> 16) | 0;\n            this.h[i] = f & 0xffff;\n        }\n        mac[macpos + 0] = this.h[0] >>> 0 & 0xff;\n        mac[macpos + 1] = this.h[0] >>> 8 & 0xff;\n        mac[macpos + 2] = this.h[1] >>> 0 & 0xff;\n        mac[macpos + 3] = this.h[1] >>> 8 & 0xff;\n        mac[macpos + 4] = this.h[2] >>> 0 & 0xff;\n        mac[macpos + 5] = this.h[2] >>> 8 & 0xff;\n        mac[macpos + 6] = this.h[3] >>> 0 & 0xff;\n        mac[macpos + 7] = this.h[3] >>> 8 & 0xff;\n        mac[macpos + 8] = this.h[4] >>> 0 & 0xff;\n        mac[macpos + 9] = this.h[4] >>> 8 & 0xff;\n        mac[macpos + 10] = this.h[5] >>> 0 & 0xff;\n        mac[macpos + 11] = this.h[5] >>> 8 & 0xff;\n        mac[macpos + 12] = this.h[6] >>> 0 & 0xff;\n        mac[macpos + 13] = this.h[6] >>> 8 & 0xff;\n        mac[macpos + 14] = this.h[7] >>> 0 & 0xff;\n        mac[macpos + 15] = this.h[7] >>> 8 & 0xff;\n    };\n    poly1305.prototype.update = function(m, mpos, bytes) {\n        var i, want;\n        if (this.leftover) {\n            want = 16 - this.leftover;\n            if (want > bytes) want = bytes;\n            for(i = 0; i < want; i++)this.buffer[this.leftover + i] = m[mpos + i];\n            bytes -= want;\n            mpos += want;\n            this.leftover += want;\n            if (this.leftover < 16) return;\n            this.blocks(this.buffer, 0, 16);\n            this.leftover = 0;\n        }\n        if (bytes >= 16) {\n            want = bytes - bytes % 16;\n            this.blocks(m, mpos, want);\n            mpos += want;\n            bytes -= want;\n        }\n        if (bytes) {\n            for(i = 0; i < bytes; i++)this.buffer[this.leftover + i] = m[mpos + i];\n            this.leftover += bytes;\n        }\n    };\n    function crypto_onetimeauth(out, outpos, m, mpos, n, k) {\n        var s = new poly1305(k);\n        s.update(m, mpos, n);\n        s.finish(out, outpos);\n        return 0;\n    }\n    function crypto_onetimeauth_verify(h, hpos, m, mpos, n, k) {\n        var x = new Uint8Array(16);\n        crypto_onetimeauth(x, 0, m, mpos, n, k);\n        return crypto_verify_16(h, hpos, x, 0);\n    }\n    function crypto_secretbox(c, m, d, n, k) {\n        var i;\n        if (d < 32) return -1;\n        crypto_stream_xor(c, 0, m, 0, d, n, k);\n        crypto_onetimeauth(c, 16, c, 32, d - 32, c);\n        for(i = 0; i < 16; i++)c[i] = 0;\n        return 0;\n    }\n    function crypto_secretbox_open(m, c, d, n, k) {\n        var i;\n        var x = new Uint8Array(32);\n        if (d < 32) return -1;\n        crypto_stream(x, 0, 32, n, k);\n        if (crypto_onetimeauth_verify(c, 16, c, 32, d - 32, x) !== 0) return -1;\n        crypto_stream_xor(m, 0, c, 0, d, n, k);\n        for(i = 0; i < 32; i++)m[i] = 0;\n        return 0;\n    }\n    function set25519(r, a) {\n        var i;\n        for(i = 0; i < 16; i++)r[i] = a[i] | 0;\n    }\n    function car25519(o) {\n        var i, v, c = 1;\n        for(i = 0; i < 16; i++){\n            v = o[i] + c + 65535;\n            c = Math.floor(v / 65536);\n            o[i] = v - c * 65536;\n        }\n        o[0] += c - 1 + 37 * (c - 1);\n    }\n    function sel25519(p, q, b) {\n        var t, c = ~(b - 1);\n        for(var i = 0; i < 16; i++){\n            t = c & (p[i] ^ q[i]);\n            p[i] ^= t;\n            q[i] ^= t;\n        }\n    }\n    function pack25519(o, n) {\n        var i, j, b;\n        var m = gf(), t = gf();\n        for(i = 0; i < 16; i++)t[i] = n[i];\n        car25519(t);\n        car25519(t);\n        car25519(t);\n        for(j = 0; j < 2; j++){\n            m[0] = t[0] - 0xffed;\n            for(i = 1; i < 15; i++){\n                m[i] = t[i] - 0xffff - (m[i - 1] >> 16 & 1);\n                m[i - 1] &= 0xffff;\n            }\n            m[15] = t[15] - 0x7fff - (m[14] >> 16 & 1);\n            b = m[15] >> 16 & 1;\n            m[14] &= 0xffff;\n            sel25519(t, m, 1 - b);\n        }\n        for(i = 0; i < 16; i++){\n            o[2 * i] = t[i] & 0xff;\n            o[2 * i + 1] = t[i] >> 8;\n        }\n    }\n    function neq25519(a, b) {\n        var c = new Uint8Array(32), d = new Uint8Array(32);\n        pack25519(c, a);\n        pack25519(d, b);\n        return crypto_verify_32(c, 0, d, 0);\n    }\n    function par25519(a) {\n        var d = new Uint8Array(32);\n        pack25519(d, a);\n        return d[0] & 1;\n    }\n    function unpack25519(o, n) {\n        var i;\n        for(i = 0; i < 16; i++)o[i] = n[2 * i] + (n[2 * i + 1] << 8);\n        o[15] &= 0x7fff;\n    }\n    function A(o, a, b) {\n        for(var i = 0; i < 16; i++)o[i] = a[i] + b[i];\n    }\n    function Z(o, a, b) {\n        for(var i = 0; i < 16; i++)o[i] = a[i] - b[i];\n    }\n    function M(o, a, b) {\n        var v, c, t0 = 0, t1 = 0, t2 = 0, t3 = 0, t4 = 0, t5 = 0, t6 = 0, t7 = 0, t8 = 0, t9 = 0, t10 = 0, t11 = 0, t12 = 0, t13 = 0, t14 = 0, t15 = 0, t16 = 0, t17 = 0, t18 = 0, t19 = 0, t20 = 0, t21 = 0, t22 = 0, t23 = 0, t24 = 0, t25 = 0, t26 = 0, t27 = 0, t28 = 0, t29 = 0, t30 = 0, b0 = b[0], b1 = b[1], b2 = b[2], b3 = b[3], b4 = b[4], b5 = b[5], b6 = b[6], b7 = b[7], b8 = b[8], b9 = b[9], b10 = b[10], b11 = b[11], b12 = b[12], b13 = b[13], b14 = b[14], b15 = b[15];\n        v = a[0];\n        t0 += v * b0;\n        t1 += v * b1;\n        t2 += v * b2;\n        t3 += v * b3;\n        t4 += v * b4;\n        t5 += v * b5;\n        t6 += v * b6;\n        t7 += v * b7;\n        t8 += v * b8;\n        t9 += v * b9;\n        t10 += v * b10;\n        t11 += v * b11;\n        t12 += v * b12;\n        t13 += v * b13;\n        t14 += v * b14;\n        t15 += v * b15;\n        v = a[1];\n        t1 += v * b0;\n        t2 += v * b1;\n        t3 += v * b2;\n        t4 += v * b3;\n        t5 += v * b4;\n        t6 += v * b5;\n        t7 += v * b6;\n        t8 += v * b7;\n        t9 += v * b8;\n        t10 += v * b9;\n        t11 += v * b10;\n        t12 += v * b11;\n        t13 += v * b12;\n        t14 += v * b13;\n        t15 += v * b14;\n        t16 += v * b15;\n        v = a[2];\n        t2 += v * b0;\n        t3 += v * b1;\n        t4 += v * b2;\n        t5 += v * b3;\n        t6 += v * b4;\n        t7 += v * b5;\n        t8 += v * b6;\n        t9 += v * b7;\n        t10 += v * b8;\n        t11 += v * b9;\n        t12 += v * b10;\n        t13 += v * b11;\n        t14 += v * b12;\n        t15 += v * b13;\n        t16 += v * b14;\n        t17 += v * b15;\n        v = a[3];\n        t3 += v * b0;\n        t4 += v * b1;\n        t5 += v * b2;\n        t6 += v * b3;\n        t7 += v * b4;\n        t8 += v * b5;\n        t9 += v * b6;\n        t10 += v * b7;\n        t11 += v * b8;\n        t12 += v * b9;\n        t13 += v * b10;\n        t14 += v * b11;\n        t15 += v * b12;\n        t16 += v * b13;\n        t17 += v * b14;\n        t18 += v * b15;\n        v = a[4];\n        t4 += v * b0;\n        t5 += v * b1;\n        t6 += v * b2;\n        t7 += v * b3;\n        t8 += v * b4;\n        t9 += v * b5;\n        t10 += v * b6;\n        t11 += v * b7;\n        t12 += v * b8;\n        t13 += v * b9;\n        t14 += v * b10;\n        t15 += v * b11;\n        t16 += v * b12;\n        t17 += v * b13;\n        t18 += v * b14;\n        t19 += v * b15;\n        v = a[5];\n        t5 += v * b0;\n        t6 += v * b1;\n        t7 += v * b2;\n        t8 += v * b3;\n        t9 += v * b4;\n        t10 += v * b5;\n        t11 += v * b6;\n        t12 += v * b7;\n        t13 += v * b8;\n        t14 += v * b9;\n        t15 += v * b10;\n        t16 += v * b11;\n        t17 += v * b12;\n        t18 += v * b13;\n        t19 += v * b14;\n        t20 += v * b15;\n        v = a[6];\n        t6 += v * b0;\n        t7 += v * b1;\n        t8 += v * b2;\n        t9 += v * b3;\n        t10 += v * b4;\n        t11 += v * b5;\n        t12 += v * b6;\n        t13 += v * b7;\n        t14 += v * b8;\n        t15 += v * b9;\n        t16 += v * b10;\n        t17 += v * b11;\n        t18 += v * b12;\n        t19 += v * b13;\n        t20 += v * b14;\n        t21 += v * b15;\n        v = a[7];\n        t7 += v * b0;\n        t8 += v * b1;\n        t9 += v * b2;\n        t10 += v * b3;\n        t11 += v * b4;\n        t12 += v * b5;\n        t13 += v * b6;\n        t14 += v * b7;\n        t15 += v * b8;\n        t16 += v * b9;\n        t17 += v * b10;\n        t18 += v * b11;\n        t19 += v * b12;\n        t20 += v * b13;\n        t21 += v * b14;\n        t22 += v * b15;\n        v = a[8];\n        t8 += v * b0;\n        t9 += v * b1;\n        t10 += v * b2;\n        t11 += v * b3;\n        t12 += v * b4;\n        t13 += v * b5;\n        t14 += v * b6;\n        t15 += v * b7;\n        t16 += v * b8;\n        t17 += v * b9;\n        t18 += v * b10;\n        t19 += v * b11;\n        t20 += v * b12;\n        t21 += v * b13;\n        t22 += v * b14;\n        t23 += v * b15;\n        v = a[9];\n        t9 += v * b0;\n        t10 += v * b1;\n        t11 += v * b2;\n        t12 += v * b3;\n        t13 += v * b4;\n        t14 += v * b5;\n        t15 += v * b6;\n        t16 += v * b7;\n        t17 += v * b8;\n        t18 += v * b9;\n        t19 += v * b10;\n        t20 += v * b11;\n        t21 += v * b12;\n        t22 += v * b13;\n        t23 += v * b14;\n        t24 += v * b15;\n        v = a[10];\n        t10 += v * b0;\n        t11 += v * b1;\n        t12 += v * b2;\n        t13 += v * b3;\n        t14 += v * b4;\n        t15 += v * b5;\n        t16 += v * b6;\n        t17 += v * b7;\n        t18 += v * b8;\n        t19 += v * b9;\n        t20 += v * b10;\n        t21 += v * b11;\n        t22 += v * b12;\n        t23 += v * b13;\n        t24 += v * b14;\n        t25 += v * b15;\n        v = a[11];\n        t11 += v * b0;\n        t12 += v * b1;\n        t13 += v * b2;\n        t14 += v * b3;\n        t15 += v * b4;\n        t16 += v * b5;\n        t17 += v * b6;\n        t18 += v * b7;\n        t19 += v * b8;\n        t20 += v * b9;\n        t21 += v * b10;\n        t22 += v * b11;\n        t23 += v * b12;\n        t24 += v * b13;\n        t25 += v * b14;\n        t26 += v * b15;\n        v = a[12];\n        t12 += v * b0;\n        t13 += v * b1;\n        t14 += v * b2;\n        t15 += v * b3;\n        t16 += v * b4;\n        t17 += v * b5;\n        t18 += v * b6;\n        t19 += v * b7;\n        t20 += v * b8;\n        t21 += v * b9;\n        t22 += v * b10;\n        t23 += v * b11;\n        t24 += v * b12;\n        t25 += v * b13;\n        t26 += v * b14;\n        t27 += v * b15;\n        v = a[13];\n        t13 += v * b0;\n        t14 += v * b1;\n        t15 += v * b2;\n        t16 += v * b3;\n        t17 += v * b4;\n        t18 += v * b5;\n        t19 += v * b6;\n        t20 += v * b7;\n        t21 += v * b8;\n        t22 += v * b9;\n        t23 += v * b10;\n        t24 += v * b11;\n        t25 += v * b12;\n        t26 += v * b13;\n        t27 += v * b14;\n        t28 += v * b15;\n        v = a[14];\n        t14 += v * b0;\n        t15 += v * b1;\n        t16 += v * b2;\n        t17 += v * b3;\n        t18 += v * b4;\n        t19 += v * b5;\n        t20 += v * b6;\n        t21 += v * b7;\n        t22 += v * b8;\n        t23 += v * b9;\n        t24 += v * b10;\n        t25 += v * b11;\n        t26 += v * b12;\n        t27 += v * b13;\n        t28 += v * b14;\n        t29 += v * b15;\n        v = a[15];\n        t15 += v * b0;\n        t16 += v * b1;\n        t17 += v * b2;\n        t18 += v * b3;\n        t19 += v * b4;\n        t20 += v * b5;\n        t21 += v * b6;\n        t22 += v * b7;\n        t23 += v * b8;\n        t24 += v * b9;\n        t25 += v * b10;\n        t26 += v * b11;\n        t27 += v * b12;\n        t28 += v * b13;\n        t29 += v * b14;\n        t30 += v * b15;\n        t0 += 38 * t16;\n        t1 += 38 * t17;\n        t2 += 38 * t18;\n        t3 += 38 * t19;\n        t4 += 38 * t20;\n        t5 += 38 * t21;\n        t6 += 38 * t22;\n        t7 += 38 * t23;\n        t8 += 38 * t24;\n        t9 += 38 * t25;\n        t10 += 38 * t26;\n        t11 += 38 * t27;\n        t12 += 38 * t28;\n        t13 += 38 * t29;\n        t14 += 38 * t30;\n        // t15 left as is\n        // first car\n        c = 1;\n        v = t0 + c + 65535;\n        c = Math.floor(v / 65536);\n        t0 = v - c * 65536;\n        v = t1 + c + 65535;\n        c = Math.floor(v / 65536);\n        t1 = v - c * 65536;\n        v = t2 + c + 65535;\n        c = Math.floor(v / 65536);\n        t2 = v - c * 65536;\n        v = t3 + c + 65535;\n        c = Math.floor(v / 65536);\n        t3 = v - c * 65536;\n        v = t4 + c + 65535;\n        c = Math.floor(v / 65536);\n        t4 = v - c * 65536;\n        v = t5 + c + 65535;\n        c = Math.floor(v / 65536);\n        t5 = v - c * 65536;\n        v = t6 + c + 65535;\n        c = Math.floor(v / 65536);\n        t6 = v - c * 65536;\n        v = t7 + c + 65535;\n        c = Math.floor(v / 65536);\n        t7 = v - c * 65536;\n        v = t8 + c + 65535;\n        c = Math.floor(v / 65536);\n        t8 = v - c * 65536;\n        v = t9 + c + 65535;\n        c = Math.floor(v / 65536);\n        t9 = v - c * 65536;\n        v = t10 + c + 65535;\n        c = Math.floor(v / 65536);\n        t10 = v - c * 65536;\n        v = t11 + c + 65535;\n        c = Math.floor(v / 65536);\n        t11 = v - c * 65536;\n        v = t12 + c + 65535;\n        c = Math.floor(v / 65536);\n        t12 = v - c * 65536;\n        v = t13 + c + 65535;\n        c = Math.floor(v / 65536);\n        t13 = v - c * 65536;\n        v = t14 + c + 65535;\n        c = Math.floor(v / 65536);\n        t14 = v - c * 65536;\n        v = t15 + c + 65535;\n        c = Math.floor(v / 65536);\n        t15 = v - c * 65536;\n        t0 += c - 1 + 37 * (c - 1);\n        // second car\n        c = 1;\n        v = t0 + c + 65535;\n        c = Math.floor(v / 65536);\n        t0 = v - c * 65536;\n        v = t1 + c + 65535;\n        c = Math.floor(v / 65536);\n        t1 = v - c * 65536;\n        v = t2 + c + 65535;\n        c = Math.floor(v / 65536);\n        t2 = v - c * 65536;\n        v = t3 + c + 65535;\n        c = Math.floor(v / 65536);\n        t3 = v - c * 65536;\n        v = t4 + c + 65535;\n        c = Math.floor(v / 65536);\n        t4 = v - c * 65536;\n        v = t5 + c + 65535;\n        c = Math.floor(v / 65536);\n        t5 = v - c * 65536;\n        v = t6 + c + 65535;\n        c = Math.floor(v / 65536);\n        t6 = v - c * 65536;\n        v = t7 + c + 65535;\n        c = Math.floor(v / 65536);\n        t7 = v - c * 65536;\n        v = t8 + c + 65535;\n        c = Math.floor(v / 65536);\n        t8 = v - c * 65536;\n        v = t9 + c + 65535;\n        c = Math.floor(v / 65536);\n        t9 = v - c * 65536;\n        v = t10 + c + 65535;\n        c = Math.floor(v / 65536);\n        t10 = v - c * 65536;\n        v = t11 + c + 65535;\n        c = Math.floor(v / 65536);\n        t11 = v - c * 65536;\n        v = t12 + c + 65535;\n        c = Math.floor(v / 65536);\n        t12 = v - c * 65536;\n        v = t13 + c + 65535;\n        c = Math.floor(v / 65536);\n        t13 = v - c * 65536;\n        v = t14 + c + 65535;\n        c = Math.floor(v / 65536);\n        t14 = v - c * 65536;\n        v = t15 + c + 65535;\n        c = Math.floor(v / 65536);\n        t15 = v - c * 65536;\n        t0 += c - 1 + 37 * (c - 1);\n        o[0] = t0;\n        o[1] = t1;\n        o[2] = t2;\n        o[3] = t3;\n        o[4] = t4;\n        o[5] = t5;\n        o[6] = t6;\n        o[7] = t7;\n        o[8] = t8;\n        o[9] = t9;\n        o[10] = t10;\n        o[11] = t11;\n        o[12] = t12;\n        o[13] = t13;\n        o[14] = t14;\n        o[15] = t15;\n    }\n    function S(o, a) {\n        M(o, a, a);\n    }\n    function inv25519(o, i) {\n        var c = gf();\n        var a;\n        for(a = 0; a < 16; a++)c[a] = i[a];\n        for(a = 253; a >= 0; a--){\n            S(c, c);\n            if (a !== 2 && a !== 4) M(c, c, i);\n        }\n        for(a = 0; a < 16; a++)o[a] = c[a];\n    }\n    function pow2523(o, i) {\n        var c = gf();\n        var a;\n        for(a = 0; a < 16; a++)c[a] = i[a];\n        for(a = 250; a >= 0; a--){\n            S(c, c);\n            if (a !== 1) M(c, c, i);\n        }\n        for(a = 0; a < 16; a++)o[a] = c[a];\n    }\n    function crypto_scalarmult(q, n, p) {\n        var z = new Uint8Array(32);\n        var x = new Float64Array(80), r, i;\n        var a = gf(), b = gf(), c = gf(), d = gf(), e = gf(), f = gf();\n        for(i = 0; i < 31; i++)z[i] = n[i];\n        z[31] = n[31] & 127 | 64;\n        z[0] &= 248;\n        unpack25519(x, p);\n        for(i = 0; i < 16; i++){\n            b[i] = x[i];\n            d[i] = a[i] = c[i] = 0;\n        }\n        a[0] = d[0] = 1;\n        for(i = 254; i >= 0; --i){\n            r = z[i >>> 3] >>> (i & 7) & 1;\n            sel25519(a, b, r);\n            sel25519(c, d, r);\n            A(e, a, c);\n            Z(a, a, c);\n            A(c, b, d);\n            Z(b, b, d);\n            S(d, e);\n            S(f, a);\n            M(a, c, a);\n            M(c, b, e);\n            A(e, a, c);\n            Z(a, a, c);\n            S(b, a);\n            Z(c, d, f);\n            M(a, c, _121665);\n            A(a, a, d);\n            M(c, c, a);\n            M(a, d, f);\n            M(d, b, x);\n            S(b, e);\n            sel25519(a, b, r);\n            sel25519(c, d, r);\n        }\n        for(i = 0; i < 16; i++){\n            x[i + 16] = a[i];\n            x[i + 32] = c[i];\n            x[i + 48] = b[i];\n            x[i + 64] = d[i];\n        }\n        var x32 = x.subarray(32);\n        var x16 = x.subarray(16);\n        inv25519(x32, x32);\n        M(x16, x16, x32);\n        pack25519(q, x16);\n        return 0;\n    }\n    function crypto_scalarmult_base(q, n) {\n        return crypto_scalarmult(q, n, _9);\n    }\n    function crypto_box_keypair(y, x) {\n        randombytes(x, 32);\n        return crypto_scalarmult_base(y, x);\n    }\n    function crypto_box_beforenm(k, y, x) {\n        var s = new Uint8Array(32);\n        crypto_scalarmult(s, x, y);\n        return crypto_core_hsalsa20(k, _0, s, sigma);\n    }\n    var crypto_box_afternm = crypto_secretbox;\n    var crypto_box_open_afternm = crypto_secretbox_open;\n    function crypto_box(c, m, d, n, y, x) {\n        var k = new Uint8Array(32);\n        crypto_box_beforenm(k, y, x);\n        return crypto_box_afternm(c, m, d, n, k);\n    }\n    function crypto_box_open(m, c, d, n, y, x) {\n        var k = new Uint8Array(32);\n        crypto_box_beforenm(k, y, x);\n        return crypto_box_open_afternm(m, c, d, n, k);\n    }\n    var K = [\n        0x428a2f98,\n        0xd728ae22,\n        0x71374491,\n        0x23ef65cd,\n        0xb5c0fbcf,\n        0xec4d3b2f,\n        0xe9b5dba5,\n        0x8189dbbc,\n        0x3956c25b,\n        0xf348b538,\n        0x59f111f1,\n        0xb605d019,\n        0x923f82a4,\n        0xaf194f9b,\n        0xab1c5ed5,\n        0xda6d8118,\n        0xd807aa98,\n        0xa3030242,\n        0x12835b01,\n        0x45706fbe,\n        0x243185be,\n        0x4ee4b28c,\n        0x550c7dc3,\n        0xd5ffb4e2,\n        0x72be5d74,\n        0xf27b896f,\n        0x80deb1fe,\n        0x3b1696b1,\n        0x9bdc06a7,\n        0x25c71235,\n        0xc19bf174,\n        0xcf692694,\n        0xe49b69c1,\n        0x9ef14ad2,\n        0xefbe4786,\n        0x384f25e3,\n        0x0fc19dc6,\n        0x8b8cd5b5,\n        0x240ca1cc,\n        0x77ac9c65,\n        0x2de92c6f,\n        0x592b0275,\n        0x4a7484aa,\n        0x6ea6e483,\n        0x5cb0a9dc,\n        0xbd41fbd4,\n        0x76f988da,\n        0x831153b5,\n        0x983e5152,\n        0xee66dfab,\n        0xa831c66d,\n        0x2db43210,\n        0xb00327c8,\n        0x98fb213f,\n        0xbf597fc7,\n        0xbeef0ee4,\n        0xc6e00bf3,\n        0x3da88fc2,\n        0xd5a79147,\n        0x930aa725,\n        0x06ca6351,\n        0xe003826f,\n        0x14292967,\n        0x0a0e6e70,\n        0x27b70a85,\n        0x46d22ffc,\n        0x2e1b2138,\n        0x5c26c926,\n        0x4d2c6dfc,\n        0x5ac42aed,\n        0x53380d13,\n        0x9d95b3df,\n        0x650a7354,\n        0x8baf63de,\n        0x766a0abb,\n        0x3c77b2a8,\n        0x81c2c92e,\n        0x47edaee6,\n        0x92722c85,\n        0x1482353b,\n        0xa2bfe8a1,\n        0x4cf10364,\n        0xa81a664b,\n        0xbc423001,\n        0xc24b8b70,\n        0xd0f89791,\n        0xc76c51a3,\n        0x0654be30,\n        0xd192e819,\n        0xd6ef5218,\n        0xd6990624,\n        0x5565a910,\n        0xf40e3585,\n        0x5771202a,\n        0x106aa070,\n        0x32bbd1b8,\n        0x19a4c116,\n        0xb8d2d0c8,\n        0x1e376c08,\n        0x5141ab53,\n        0x2748774c,\n        0xdf8eeb99,\n        0x34b0bcb5,\n        0xe19b48a8,\n        0x391c0cb3,\n        0xc5c95a63,\n        0x4ed8aa4a,\n        0xe3418acb,\n        0x5b9cca4f,\n        0x7763e373,\n        0x682e6ff3,\n        0xd6b2b8a3,\n        0x748f82ee,\n        0x5defb2fc,\n        0x78a5636f,\n        0x43172f60,\n        0x84c87814,\n        0xa1f0ab72,\n        0x8cc70208,\n        0x1a6439ec,\n        0x90befffa,\n        0x23631e28,\n        0xa4506ceb,\n        0xde82bde9,\n        0xbef9a3f7,\n        0xb2c67915,\n        0xc67178f2,\n        0xe372532b,\n        0xca273ece,\n        0xea26619c,\n        0xd186b8c7,\n        0x21c0c207,\n        0xeada7dd6,\n        0xcde0eb1e,\n        0xf57d4f7f,\n        0xee6ed178,\n        0x06f067aa,\n        0x72176fba,\n        0x0a637dc5,\n        0xa2c898a6,\n        0x113f9804,\n        0xbef90dae,\n        0x1b710b35,\n        0x131c471b,\n        0x28db77f5,\n        0x23047d84,\n        0x32caab7b,\n        0x40c72493,\n        0x3c9ebe0a,\n        0x15c9bebc,\n        0x431d67c4,\n        0x9c100d4c,\n        0x4cc5d4be,\n        0xcb3e42b6,\n        0x597f299c,\n        0xfc657e2a,\n        0x5fcb6fab,\n        0x3ad6faec,\n        0x6c44198c,\n        0x4a475817\n    ];\n    function crypto_hashblocks_hl(hh, hl, m, n) {\n        var wh = new Int32Array(16), wl = new Int32Array(16), bh0, bh1, bh2, bh3, bh4, bh5, bh6, bh7, bl0, bl1, bl2, bl3, bl4, bl5, bl6, bl7, th, tl, i, j, h, l, a, b, c, d;\n        var ah0 = hh[0], ah1 = hh[1], ah2 = hh[2], ah3 = hh[3], ah4 = hh[4], ah5 = hh[5], ah6 = hh[6], ah7 = hh[7], al0 = hl[0], al1 = hl[1], al2 = hl[2], al3 = hl[3], al4 = hl[4], al5 = hl[5], al6 = hl[6], al7 = hl[7];\n        var pos = 0;\n        while(n >= 128){\n            for(i = 0; i < 16; i++){\n                j = 8 * i + pos;\n                wh[i] = m[j + 0] << 24 | m[j + 1] << 16 | m[j + 2] << 8 | m[j + 3];\n                wl[i] = m[j + 4] << 24 | m[j + 5] << 16 | m[j + 6] << 8 | m[j + 7];\n            }\n            for(i = 0; i < 80; i++){\n                bh0 = ah0;\n                bh1 = ah1;\n                bh2 = ah2;\n                bh3 = ah3;\n                bh4 = ah4;\n                bh5 = ah5;\n                bh6 = ah6;\n                bh7 = ah7;\n                bl0 = al0;\n                bl1 = al1;\n                bl2 = al2;\n                bl3 = al3;\n                bl4 = al4;\n                bl5 = al5;\n                bl6 = al6;\n                bl7 = al7;\n                // add\n                h = ah7;\n                l = al7;\n                a = l & 0xffff;\n                b = l >>> 16;\n                c = h & 0xffff;\n                d = h >>> 16;\n                // Sigma1\n                h = (ah4 >>> 14 | al4 << 32 - 14) ^ (ah4 >>> 18 | al4 << 32 - 18) ^ (al4 >>> 41 - 32 | ah4 << 32 - (41 - 32));\n                l = (al4 >>> 14 | ah4 << 32 - 14) ^ (al4 >>> 18 | ah4 << 32 - 18) ^ (ah4 >>> 41 - 32 | al4 << 32 - (41 - 32));\n                a += l & 0xffff;\n                b += l >>> 16;\n                c += h & 0xffff;\n                d += h >>> 16;\n                // Ch\n                h = ah4 & ah5 ^ ~ah4 & ah6;\n                l = al4 & al5 ^ ~al4 & al6;\n                a += l & 0xffff;\n                b += l >>> 16;\n                c += h & 0xffff;\n                d += h >>> 16;\n                // K\n                h = K[i * 2];\n                l = K[i * 2 + 1];\n                a += l & 0xffff;\n                b += l >>> 16;\n                c += h & 0xffff;\n                d += h >>> 16;\n                // w\n                h = wh[i % 16];\n                l = wl[i % 16];\n                a += l & 0xffff;\n                b += l >>> 16;\n                c += h & 0xffff;\n                d += h >>> 16;\n                b += a >>> 16;\n                c += b >>> 16;\n                d += c >>> 16;\n                th = c & 0xffff | d << 16;\n                tl = a & 0xffff | b << 16;\n                // add\n                h = th;\n                l = tl;\n                a = l & 0xffff;\n                b = l >>> 16;\n                c = h & 0xffff;\n                d = h >>> 16;\n                // Sigma0\n                h = (ah0 >>> 28 | al0 << 32 - 28) ^ (al0 >>> 34 - 32 | ah0 << 32 - (34 - 32)) ^ (al0 >>> 39 - 32 | ah0 << 32 - (39 - 32));\n                l = (al0 >>> 28 | ah0 << 32 - 28) ^ (ah0 >>> 34 - 32 | al0 << 32 - (34 - 32)) ^ (ah0 >>> 39 - 32 | al0 << 32 - (39 - 32));\n                a += l & 0xffff;\n                b += l >>> 16;\n                c += h & 0xffff;\n                d += h >>> 16;\n                // Maj\n                h = ah0 & ah1 ^ ah0 & ah2 ^ ah1 & ah2;\n                l = al0 & al1 ^ al0 & al2 ^ al1 & al2;\n                a += l & 0xffff;\n                b += l >>> 16;\n                c += h & 0xffff;\n                d += h >>> 16;\n                b += a >>> 16;\n                c += b >>> 16;\n                d += c >>> 16;\n                bh7 = c & 0xffff | d << 16;\n                bl7 = a & 0xffff | b << 16;\n                // add\n                h = bh3;\n                l = bl3;\n                a = l & 0xffff;\n                b = l >>> 16;\n                c = h & 0xffff;\n                d = h >>> 16;\n                h = th;\n                l = tl;\n                a += l & 0xffff;\n                b += l >>> 16;\n                c += h & 0xffff;\n                d += h >>> 16;\n                b += a >>> 16;\n                c += b >>> 16;\n                d += c >>> 16;\n                bh3 = c & 0xffff | d << 16;\n                bl3 = a & 0xffff | b << 16;\n                ah1 = bh0;\n                ah2 = bh1;\n                ah3 = bh2;\n                ah4 = bh3;\n                ah5 = bh4;\n                ah6 = bh5;\n                ah7 = bh6;\n                ah0 = bh7;\n                al1 = bl0;\n                al2 = bl1;\n                al3 = bl2;\n                al4 = bl3;\n                al5 = bl4;\n                al6 = bl5;\n                al7 = bl6;\n                al0 = bl7;\n                if (i % 16 === 15) {\n                    for(j = 0; j < 16; j++){\n                        // add\n                        h = wh[j];\n                        l = wl[j];\n                        a = l & 0xffff;\n                        b = l >>> 16;\n                        c = h & 0xffff;\n                        d = h >>> 16;\n                        h = wh[(j + 9) % 16];\n                        l = wl[(j + 9) % 16];\n                        a += l & 0xffff;\n                        b += l >>> 16;\n                        c += h & 0xffff;\n                        d += h >>> 16;\n                        // sigma0\n                        th = wh[(j + 1) % 16];\n                        tl = wl[(j + 1) % 16];\n                        h = (th >>> 1 | tl << 32 - 1) ^ (th >>> 8 | tl << 32 - 8) ^ th >>> 7;\n                        l = (tl >>> 1 | th << 32 - 1) ^ (tl >>> 8 | th << 32 - 8) ^ (tl >>> 7 | th << 32 - 7);\n                        a += l & 0xffff;\n                        b += l >>> 16;\n                        c += h & 0xffff;\n                        d += h >>> 16;\n                        // sigma1\n                        th = wh[(j + 14) % 16];\n                        tl = wl[(j + 14) % 16];\n                        h = (th >>> 19 | tl << 32 - 19) ^ (tl >>> 61 - 32 | th << 32 - (61 - 32)) ^ th >>> 6;\n                        l = (tl >>> 19 | th << 32 - 19) ^ (th >>> 61 - 32 | tl << 32 - (61 - 32)) ^ (tl >>> 6 | th << 32 - 6);\n                        a += l & 0xffff;\n                        b += l >>> 16;\n                        c += h & 0xffff;\n                        d += h >>> 16;\n                        b += a >>> 16;\n                        c += b >>> 16;\n                        d += c >>> 16;\n                        wh[j] = c & 0xffff | d << 16;\n                        wl[j] = a & 0xffff | b << 16;\n                    }\n                }\n            }\n            // add\n            h = ah0;\n            l = al0;\n            a = l & 0xffff;\n            b = l >>> 16;\n            c = h & 0xffff;\n            d = h >>> 16;\n            h = hh[0];\n            l = hl[0];\n            a += l & 0xffff;\n            b += l >>> 16;\n            c += h & 0xffff;\n            d += h >>> 16;\n            b += a >>> 16;\n            c += b >>> 16;\n            d += c >>> 16;\n            hh[0] = ah0 = c & 0xffff | d << 16;\n            hl[0] = al0 = a & 0xffff | b << 16;\n            h = ah1;\n            l = al1;\n            a = l & 0xffff;\n            b = l >>> 16;\n            c = h & 0xffff;\n            d = h >>> 16;\n            h = hh[1];\n            l = hl[1];\n            a += l & 0xffff;\n            b += l >>> 16;\n            c += h & 0xffff;\n            d += h >>> 16;\n            b += a >>> 16;\n            c += b >>> 16;\n            d += c >>> 16;\n            hh[1] = ah1 = c & 0xffff | d << 16;\n            hl[1] = al1 = a & 0xffff | b << 16;\n            h = ah2;\n            l = al2;\n            a = l & 0xffff;\n            b = l >>> 16;\n            c = h & 0xffff;\n            d = h >>> 16;\n            h = hh[2];\n            l = hl[2];\n            a += l & 0xffff;\n            b += l >>> 16;\n            c += h & 0xffff;\n            d += h >>> 16;\n            b += a >>> 16;\n            c += b >>> 16;\n            d += c >>> 16;\n            hh[2] = ah2 = c & 0xffff | d << 16;\n            hl[2] = al2 = a & 0xffff | b << 16;\n            h = ah3;\n            l = al3;\n            a = l & 0xffff;\n            b = l >>> 16;\n            c = h & 0xffff;\n            d = h >>> 16;\n            h = hh[3];\n            l = hl[3];\n            a += l & 0xffff;\n            b += l >>> 16;\n            c += h & 0xffff;\n            d += h >>> 16;\n            b += a >>> 16;\n            c += b >>> 16;\n            d += c >>> 16;\n            hh[3] = ah3 = c & 0xffff | d << 16;\n            hl[3] = al3 = a & 0xffff | b << 16;\n            h = ah4;\n            l = al4;\n            a = l & 0xffff;\n            b = l >>> 16;\n            c = h & 0xffff;\n            d = h >>> 16;\n            h = hh[4];\n            l = hl[4];\n            a += l & 0xffff;\n            b += l >>> 16;\n            c += h & 0xffff;\n            d += h >>> 16;\n            b += a >>> 16;\n            c += b >>> 16;\n            d += c >>> 16;\n            hh[4] = ah4 = c & 0xffff | d << 16;\n            hl[4] = al4 = a & 0xffff | b << 16;\n            h = ah5;\n            l = al5;\n            a = l & 0xffff;\n            b = l >>> 16;\n            c = h & 0xffff;\n            d = h >>> 16;\n            h = hh[5];\n            l = hl[5];\n            a += l & 0xffff;\n            b += l >>> 16;\n            c += h & 0xffff;\n            d += h >>> 16;\n            b += a >>> 16;\n            c += b >>> 16;\n            d += c >>> 16;\n            hh[5] = ah5 = c & 0xffff | d << 16;\n            hl[5] = al5 = a & 0xffff | b << 16;\n            h = ah6;\n            l = al6;\n            a = l & 0xffff;\n            b = l >>> 16;\n            c = h & 0xffff;\n            d = h >>> 16;\n            h = hh[6];\n            l = hl[6];\n            a += l & 0xffff;\n            b += l >>> 16;\n            c += h & 0xffff;\n            d += h >>> 16;\n            b += a >>> 16;\n            c += b >>> 16;\n            d += c >>> 16;\n            hh[6] = ah6 = c & 0xffff | d << 16;\n            hl[6] = al6 = a & 0xffff | b << 16;\n            h = ah7;\n            l = al7;\n            a = l & 0xffff;\n            b = l >>> 16;\n            c = h & 0xffff;\n            d = h >>> 16;\n            h = hh[7];\n            l = hl[7];\n            a += l & 0xffff;\n            b += l >>> 16;\n            c += h & 0xffff;\n            d += h >>> 16;\n            b += a >>> 16;\n            c += b >>> 16;\n            d += c >>> 16;\n            hh[7] = ah7 = c & 0xffff | d << 16;\n            hl[7] = al7 = a & 0xffff | b << 16;\n            pos += 128;\n            n -= 128;\n        }\n        return n;\n    }\n    function crypto_hash(out, m, n) {\n        var hh = new Int32Array(8), hl = new Int32Array(8), x = new Uint8Array(256), i, b = n;\n        hh[0] = 0x6a09e667;\n        hh[1] = 0xbb67ae85;\n        hh[2] = 0x3c6ef372;\n        hh[3] = 0xa54ff53a;\n        hh[4] = 0x510e527f;\n        hh[5] = 0x9b05688c;\n        hh[6] = 0x1f83d9ab;\n        hh[7] = 0x5be0cd19;\n        hl[0] = 0xf3bcc908;\n        hl[1] = 0x84caa73b;\n        hl[2] = 0xfe94f82b;\n        hl[3] = 0x5f1d36f1;\n        hl[4] = 0xade682d1;\n        hl[5] = 0x2b3e6c1f;\n        hl[6] = 0xfb41bd6b;\n        hl[7] = 0x137e2179;\n        crypto_hashblocks_hl(hh, hl, m, n);\n        n %= 128;\n        for(i = 0; i < n; i++)x[i] = m[b - n + i];\n        x[n] = 128;\n        n = 256 - 128 * (n < 112 ? 1 : 0);\n        x[n - 9] = 0;\n        ts64(x, n - 8, b / 0x20000000 | 0, b << 3);\n        crypto_hashblocks_hl(hh, hl, x, n);\n        for(i = 0; i < 8; i++)ts64(out, 8 * i, hh[i], hl[i]);\n        return 0;\n    }\n    function add(p, q) {\n        var a = gf(), b = gf(), c = gf(), d = gf(), e = gf(), f = gf(), g = gf(), h = gf(), t = gf();\n        Z(a, p[1], p[0]);\n        Z(t, q[1], q[0]);\n        M(a, a, t);\n        A(b, p[0], p[1]);\n        A(t, q[0], q[1]);\n        M(b, b, t);\n        M(c, p[3], q[3]);\n        M(c, c, D2);\n        M(d, p[2], q[2]);\n        A(d, d, d);\n        Z(e, b, a);\n        Z(f, d, c);\n        A(g, d, c);\n        A(h, b, a);\n        M(p[0], e, f);\n        M(p[1], h, g);\n        M(p[2], g, f);\n        M(p[3], e, h);\n    }\n    function cswap(p, q, b) {\n        var i;\n        for(i = 0; i < 4; i++){\n            sel25519(p[i], q[i], b);\n        }\n    }\n    function pack(r, p) {\n        var tx = gf(), ty = gf(), zi = gf();\n        inv25519(zi, p[2]);\n        M(tx, p[0], zi);\n        M(ty, p[1], zi);\n        pack25519(r, ty);\n        r[31] ^= par25519(tx) << 7;\n    }\n    function scalarmult(p, q, s) {\n        var b, i;\n        set25519(p[0], gf0);\n        set25519(p[1], gf1);\n        set25519(p[2], gf1);\n        set25519(p[3], gf0);\n        for(i = 255; i >= 0; --i){\n            b = s[i / 8 | 0] >> (i & 7) & 1;\n            cswap(p, q, b);\n            add(q, p);\n            add(p, p);\n            cswap(p, q, b);\n        }\n    }\n    function scalarbase(p, s) {\n        var q = [\n            gf(),\n            gf(),\n            gf(),\n            gf()\n        ];\n        set25519(q[0], X);\n        set25519(q[1], Y);\n        set25519(q[2], gf1);\n        M(q[3], X, Y);\n        scalarmult(p, q, s);\n    }\n    function crypto_sign_keypair(pk, sk, seeded) {\n        var d = new Uint8Array(64);\n        var p = [\n            gf(),\n            gf(),\n            gf(),\n            gf()\n        ];\n        var i;\n        if (!seeded) randombytes(sk, 32);\n        crypto_hash(d, sk, 32);\n        d[0] &= 248;\n        d[31] &= 127;\n        d[31] |= 64;\n        scalarbase(p, d);\n        pack(pk, p);\n        for(i = 0; i < 32; i++)sk[i + 32] = pk[i];\n        return 0;\n    }\n    var L = new Float64Array([\n        0xed,\n        0xd3,\n        0xf5,\n        0x5c,\n        0x1a,\n        0x63,\n        0x12,\n        0x58,\n        0xd6,\n        0x9c,\n        0xf7,\n        0xa2,\n        0xde,\n        0xf9,\n        0xde,\n        0x14,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0x10\n    ]);\n    function modL(r, x) {\n        var carry, i, j, k;\n        for(i = 63; i >= 32; --i){\n            carry = 0;\n            for(j = i - 32, k = i - 12; j < k; ++j){\n                x[j] += carry - 16 * x[i] * L[j - (i - 32)];\n                carry = Math.floor((x[j] + 128) / 256);\n                x[j] -= carry * 256;\n            }\n            x[j] += carry;\n            x[i] = 0;\n        }\n        carry = 0;\n        for(j = 0; j < 32; j++){\n            x[j] += carry - (x[31] >> 4) * L[j];\n            carry = x[j] >> 8;\n            x[j] &= 255;\n        }\n        for(j = 0; j < 32; j++)x[j] -= carry * L[j];\n        for(i = 0; i < 32; i++){\n            x[i + 1] += x[i] >> 8;\n            r[i] = x[i] & 255;\n        }\n    }\n    function reduce(r) {\n        var x = new Float64Array(64), i;\n        for(i = 0; i < 64; i++)x[i] = r[i];\n        for(i = 0; i < 64; i++)r[i] = 0;\n        modL(r, x);\n    }\n    // Note: difference from C - smlen returned, not passed as argument.\n    function crypto_sign(sm, m, n, sk) {\n        var d = new Uint8Array(64), h = new Uint8Array(64), r = new Uint8Array(64);\n        var i, j, x = new Float64Array(64);\n        var p = [\n            gf(),\n            gf(),\n            gf(),\n            gf()\n        ];\n        crypto_hash(d, sk, 32);\n        d[0] &= 248;\n        d[31] &= 127;\n        d[31] |= 64;\n        var smlen = n + 64;\n        for(i = 0; i < n; i++)sm[64 + i] = m[i];\n        for(i = 0; i < 32; i++)sm[32 + i] = d[32 + i];\n        crypto_hash(r, sm.subarray(32), n + 32);\n        reduce(r);\n        scalarbase(p, r);\n        pack(sm, p);\n        for(i = 32; i < 64; i++)sm[i] = sk[i];\n        crypto_hash(h, sm, n + 64);\n        reduce(h);\n        for(i = 0; i < 64; i++)x[i] = 0;\n        for(i = 0; i < 32; i++)x[i] = r[i];\n        for(i = 0; i < 32; i++){\n            for(j = 0; j < 32; j++){\n                x[i + j] += h[i] * d[j];\n            }\n        }\n        modL(sm.subarray(32), x);\n        return smlen;\n    }\n    function unpackneg(r, p) {\n        var t = gf(), chk = gf(), num = gf(), den = gf(), den2 = gf(), den4 = gf(), den6 = gf();\n        set25519(r[2], gf1);\n        unpack25519(r[1], p);\n        S(num, r[1]);\n        M(den, num, D);\n        Z(num, num, r[2]);\n        A(den, r[2], den);\n        S(den2, den);\n        S(den4, den2);\n        M(den6, den4, den2);\n        M(t, den6, num);\n        M(t, t, den);\n        pow2523(t, t);\n        M(t, t, num);\n        M(t, t, den);\n        M(t, t, den);\n        M(r[0], t, den);\n        S(chk, r[0]);\n        M(chk, chk, den);\n        if (neq25519(chk, num)) M(r[0], r[0], I);\n        S(chk, r[0]);\n        M(chk, chk, den);\n        if (neq25519(chk, num)) return -1;\n        if (par25519(r[0]) === p[31] >> 7) Z(r[0], gf0, r[0]);\n        M(r[3], r[0], r[1]);\n        return 0;\n    }\n    function crypto_sign_open(m, sm, n, pk) {\n        var i;\n        var t = new Uint8Array(32), h = new Uint8Array(64);\n        var p = [\n            gf(),\n            gf(),\n            gf(),\n            gf()\n        ], q = [\n            gf(),\n            gf(),\n            gf(),\n            gf()\n        ];\n        if (n < 64) return -1;\n        if (unpackneg(q, pk)) return -1;\n        for(i = 0; i < n; i++)m[i] = sm[i];\n        for(i = 0; i < 32; i++)m[i + 32] = pk[i];\n        crypto_hash(h, m, n);\n        reduce(h);\n        scalarmult(p, q, h);\n        scalarbase(q, sm.subarray(32));\n        add(p, q);\n        pack(t, p);\n        n -= 64;\n        if (crypto_verify_32(sm, 0, t, 0)) {\n            for(i = 0; i < n; i++)m[i] = 0;\n            return -1;\n        }\n        for(i = 0; i < n; i++)m[i] = sm[i + 64];\n        return n;\n    }\n    var crypto_secretbox_KEYBYTES = 32, crypto_secretbox_NONCEBYTES = 24, crypto_secretbox_ZEROBYTES = 32, crypto_secretbox_BOXZEROBYTES = 16, crypto_scalarmult_BYTES = 32, crypto_scalarmult_SCALARBYTES = 32, crypto_box_PUBLICKEYBYTES = 32, crypto_box_SECRETKEYBYTES = 32, crypto_box_BEFORENMBYTES = 32, crypto_box_NONCEBYTES = crypto_secretbox_NONCEBYTES, crypto_box_ZEROBYTES = crypto_secretbox_ZEROBYTES, crypto_box_BOXZEROBYTES = crypto_secretbox_BOXZEROBYTES, crypto_sign_BYTES = 64, crypto_sign_PUBLICKEYBYTES = 32, crypto_sign_SECRETKEYBYTES = 64, crypto_sign_SEEDBYTES = 32, crypto_hash_BYTES = 64;\n    nacl.lowlevel = {\n        crypto_core_hsalsa20: crypto_core_hsalsa20,\n        crypto_stream_xor: crypto_stream_xor,\n        crypto_stream: crypto_stream,\n        crypto_stream_salsa20_xor: crypto_stream_salsa20_xor,\n        crypto_stream_salsa20: crypto_stream_salsa20,\n        crypto_onetimeauth: crypto_onetimeauth,\n        crypto_onetimeauth_verify: crypto_onetimeauth_verify,\n        crypto_verify_16: crypto_verify_16,\n        crypto_verify_32: crypto_verify_32,\n        crypto_secretbox: crypto_secretbox,\n        crypto_secretbox_open: crypto_secretbox_open,\n        crypto_scalarmult: crypto_scalarmult,\n        crypto_scalarmult_base: crypto_scalarmult_base,\n        crypto_box_beforenm: crypto_box_beforenm,\n        crypto_box_afternm: crypto_box_afternm,\n        crypto_box: crypto_box,\n        crypto_box_open: crypto_box_open,\n        crypto_box_keypair: crypto_box_keypair,\n        crypto_hash: crypto_hash,\n        crypto_sign: crypto_sign,\n        crypto_sign_keypair: crypto_sign_keypair,\n        crypto_sign_open: crypto_sign_open,\n        crypto_secretbox_KEYBYTES: crypto_secretbox_KEYBYTES,\n        crypto_secretbox_NONCEBYTES: crypto_secretbox_NONCEBYTES,\n        crypto_secretbox_ZEROBYTES: crypto_secretbox_ZEROBYTES,\n        crypto_secretbox_BOXZEROBYTES: crypto_secretbox_BOXZEROBYTES,\n        crypto_scalarmult_BYTES: crypto_scalarmult_BYTES,\n        crypto_scalarmult_SCALARBYTES: crypto_scalarmult_SCALARBYTES,\n        crypto_box_PUBLICKEYBYTES: crypto_box_PUBLICKEYBYTES,\n        crypto_box_SECRETKEYBYTES: crypto_box_SECRETKEYBYTES,\n        crypto_box_BEFORENMBYTES: crypto_box_BEFORENMBYTES,\n        crypto_box_NONCEBYTES: crypto_box_NONCEBYTES,\n        crypto_box_ZEROBYTES: crypto_box_ZEROBYTES,\n        crypto_box_BOXZEROBYTES: crypto_box_BOXZEROBYTES,\n        crypto_sign_BYTES: crypto_sign_BYTES,\n        crypto_sign_PUBLICKEYBYTES: crypto_sign_PUBLICKEYBYTES,\n        crypto_sign_SECRETKEYBYTES: crypto_sign_SECRETKEYBYTES,\n        crypto_sign_SEEDBYTES: crypto_sign_SEEDBYTES,\n        crypto_hash_BYTES: crypto_hash_BYTES,\n        gf: gf,\n        D: D,\n        L: L,\n        pack25519: pack25519,\n        unpack25519: unpack25519,\n        M: M,\n        A: A,\n        S: S,\n        Z: Z,\n        pow2523: pow2523,\n        add: add,\n        set25519: set25519,\n        modL: modL,\n        scalarmult: scalarmult,\n        scalarbase: scalarbase\n    };\n    /* High-level API */ function checkLengths(k, n) {\n        if (k.length !== crypto_secretbox_KEYBYTES) throw new Error(\"bad key size\");\n        if (n.length !== crypto_secretbox_NONCEBYTES) throw new Error(\"bad nonce size\");\n    }\n    function checkBoxLengths(pk, sk) {\n        if (pk.length !== crypto_box_PUBLICKEYBYTES) throw new Error(\"bad public key size\");\n        if (sk.length !== crypto_box_SECRETKEYBYTES) throw new Error(\"bad secret key size\");\n    }\n    function checkArrayTypes() {\n        for(var i = 0; i < arguments.length; i++){\n            if (!(arguments[i] instanceof Uint8Array)) throw new TypeError(\"unexpected type, use Uint8Array\");\n        }\n    }\n    function cleanup(arr) {\n        for(var i = 0; i < arr.length; i++)arr[i] = 0;\n    }\n    nacl.randomBytes = function(n) {\n        var b = new Uint8Array(n);\n        randombytes(b, n);\n        return b;\n    };\n    nacl.secretbox = function(msg, nonce, key) {\n        checkArrayTypes(msg, nonce, key);\n        checkLengths(key, nonce);\n        var m = new Uint8Array(crypto_secretbox_ZEROBYTES + msg.length);\n        var c = new Uint8Array(m.length);\n        for(var i = 0; i < msg.length; i++)m[i + crypto_secretbox_ZEROBYTES] = msg[i];\n        crypto_secretbox(c, m, m.length, nonce, key);\n        return c.subarray(crypto_secretbox_BOXZEROBYTES);\n    };\n    nacl.secretbox.open = function(box, nonce, key) {\n        checkArrayTypes(box, nonce, key);\n        checkLengths(key, nonce);\n        var c = new Uint8Array(crypto_secretbox_BOXZEROBYTES + box.length);\n        var m = new Uint8Array(c.length);\n        for(var i = 0; i < box.length; i++)c[i + crypto_secretbox_BOXZEROBYTES] = box[i];\n        if (c.length < 32) return null;\n        if (crypto_secretbox_open(m, c, c.length, nonce, key) !== 0) return null;\n        return m.subarray(crypto_secretbox_ZEROBYTES);\n    };\n    nacl.secretbox.keyLength = crypto_secretbox_KEYBYTES;\n    nacl.secretbox.nonceLength = crypto_secretbox_NONCEBYTES;\n    nacl.secretbox.overheadLength = crypto_secretbox_BOXZEROBYTES;\n    nacl.scalarMult = function(n, p) {\n        checkArrayTypes(n, p);\n        if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error(\"bad n size\");\n        if (p.length !== crypto_scalarmult_BYTES) throw new Error(\"bad p size\");\n        var q = new Uint8Array(crypto_scalarmult_BYTES);\n        crypto_scalarmult(q, n, p);\n        return q;\n    };\n    nacl.scalarMult.base = function(n) {\n        checkArrayTypes(n);\n        if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error(\"bad n size\");\n        var q = new Uint8Array(crypto_scalarmult_BYTES);\n        crypto_scalarmult_base(q, n);\n        return q;\n    };\n    nacl.scalarMult.scalarLength = crypto_scalarmult_SCALARBYTES;\n    nacl.scalarMult.groupElementLength = crypto_scalarmult_BYTES;\n    nacl.box = function(msg, nonce, publicKey, secretKey) {\n        var k = nacl.box.before(publicKey, secretKey);\n        return nacl.secretbox(msg, nonce, k);\n    };\n    nacl.box.before = function(publicKey, secretKey) {\n        checkArrayTypes(publicKey, secretKey);\n        checkBoxLengths(publicKey, secretKey);\n        var k = new Uint8Array(crypto_box_BEFORENMBYTES);\n        crypto_box_beforenm(k, publicKey, secretKey);\n        return k;\n    };\n    nacl.box.after = nacl.secretbox;\n    nacl.box.open = function(msg, nonce, publicKey, secretKey) {\n        var k = nacl.box.before(publicKey, secretKey);\n        return nacl.secretbox.open(msg, nonce, k);\n    };\n    nacl.box.open.after = nacl.secretbox.open;\n    nacl.box.keyPair = function() {\n        var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n        var sk = new Uint8Array(crypto_box_SECRETKEYBYTES);\n        crypto_box_keypair(pk, sk);\n        return {\n            publicKey: pk,\n            secretKey: sk\n        };\n    };\n    nacl.box.keyPair.fromSecretKey = function(secretKey) {\n        checkArrayTypes(secretKey);\n        if (secretKey.length !== crypto_box_SECRETKEYBYTES) throw new Error(\"bad secret key size\");\n        var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n        crypto_scalarmult_base(pk, secretKey);\n        return {\n            publicKey: pk,\n            secretKey: new Uint8Array(secretKey)\n        };\n    };\n    nacl.box.publicKeyLength = crypto_box_PUBLICKEYBYTES;\n    nacl.box.secretKeyLength = crypto_box_SECRETKEYBYTES;\n    nacl.box.sharedKeyLength = crypto_box_BEFORENMBYTES;\n    nacl.box.nonceLength = crypto_box_NONCEBYTES;\n    nacl.box.overheadLength = nacl.secretbox.overheadLength;\n    nacl.sign = function(msg, secretKey) {\n        checkArrayTypes(msg, secretKey);\n        if (secretKey.length !== crypto_sign_SECRETKEYBYTES) throw new Error(\"bad secret key size\");\n        var signedMsg = new Uint8Array(crypto_sign_BYTES + msg.length);\n        crypto_sign(signedMsg, msg, msg.length, secretKey);\n        return signedMsg;\n    };\n    nacl.sign.open = function(signedMsg, publicKey) {\n        checkArrayTypes(signedMsg, publicKey);\n        if (publicKey.length !== crypto_sign_PUBLICKEYBYTES) throw new Error(\"bad public key size\");\n        var tmp = new Uint8Array(signedMsg.length);\n        var mlen = crypto_sign_open(tmp, signedMsg, signedMsg.length, publicKey);\n        if (mlen < 0) return null;\n        var m = new Uint8Array(mlen);\n        for(var i = 0; i < m.length; i++)m[i] = tmp[i];\n        return m;\n    };\n    nacl.sign.detached = function(msg, secretKey) {\n        var signedMsg = nacl.sign(msg, secretKey);\n        var sig = new Uint8Array(crypto_sign_BYTES);\n        for(var i = 0; i < sig.length; i++)sig[i] = signedMsg[i];\n        return sig;\n    };\n    nacl.sign.detached.verify = function(msg, sig, publicKey) {\n        checkArrayTypes(msg, sig, publicKey);\n        if (sig.length !== crypto_sign_BYTES) throw new Error(\"bad signature size\");\n        if (publicKey.length !== crypto_sign_PUBLICKEYBYTES) throw new Error(\"bad public key size\");\n        var sm = new Uint8Array(crypto_sign_BYTES + msg.length);\n        var m = new Uint8Array(crypto_sign_BYTES + msg.length);\n        var i;\n        for(i = 0; i < crypto_sign_BYTES; i++)sm[i] = sig[i];\n        for(i = 0; i < msg.length; i++)sm[i + crypto_sign_BYTES] = msg[i];\n        return crypto_sign_open(m, sm, sm.length, publicKey) >= 0;\n    };\n    nacl.sign.keyPair = function() {\n        var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n        var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n        crypto_sign_keypair(pk, sk);\n        return {\n            publicKey: pk,\n            secretKey: sk\n        };\n    };\n    nacl.sign.keyPair.fromSecretKey = function(secretKey) {\n        checkArrayTypes(secretKey);\n        if (secretKey.length !== crypto_sign_SECRETKEYBYTES) throw new Error(\"bad secret key size\");\n        var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n        for(var i = 0; i < pk.length; i++)pk[i] = secretKey[32 + i];\n        return {\n            publicKey: pk,\n            secretKey: new Uint8Array(secretKey)\n        };\n    };\n    nacl.sign.keyPair.fromSeed = function(seed) {\n        checkArrayTypes(seed);\n        if (seed.length !== crypto_sign_SEEDBYTES) throw new Error(\"bad seed size\");\n        var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n        var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n        for(var i = 0; i < 32; i++)sk[i] = seed[i];\n        crypto_sign_keypair(pk, sk, true);\n        return {\n            publicKey: pk,\n            secretKey: sk\n        };\n    };\n    nacl.sign.publicKeyLength = crypto_sign_PUBLICKEYBYTES;\n    nacl.sign.secretKeyLength = crypto_sign_SECRETKEYBYTES;\n    nacl.sign.seedLength = crypto_sign_SEEDBYTES;\n    nacl.sign.signatureLength = crypto_sign_BYTES;\n    nacl.hash = function(msg) {\n        checkArrayTypes(msg);\n        var h = new Uint8Array(crypto_hash_BYTES);\n        crypto_hash(h, msg, msg.length);\n        return h;\n    };\n    nacl.hash.hashLength = crypto_hash_BYTES;\n    nacl.verify = function(x, y) {\n        checkArrayTypes(x, y);\n        // Zero length arguments are considered not equal.\n        if (x.length === 0 || y.length === 0) return false;\n        if (x.length !== y.length) return false;\n        return vn(x, 0, y, 0, x.length) === 0 ? true : false;\n    };\n    nacl.setPRNG = function(fn) {\n        randombytes = fn;\n    };\n    (function() {\n        // Initialize PRNG if environment provides CSPRNG.\n        // If not, methods calling randombytes will throw.\n        var crypto = typeof self !== \"undefined\" ? self.crypto || self.msCrypto : null;\n        if (crypto && crypto.getRandomValues) {\n            // Browsers.\n            var QUOTA = 65536;\n            nacl.setPRNG(function(x, n) {\n                var i, v = new Uint8Array(n);\n                for(i = 0; i < n; i += QUOTA){\n                    crypto.getRandomValues(v.subarray(i, i + Math.min(n - i, QUOTA)));\n                }\n                for(i = 0; i < n; i++)x[i] = v[i];\n                cleanup(v);\n            });\n        } else if (true) {\n            // Node.js.\n            crypto = __webpack_require__(/*! crypto */ \"crypto\");\n            if (crypto && crypto.randomBytes) {\n                nacl.setPRNG(function(x, n) {\n                    var i, v = crypto.randomBytes(n);\n                    for(i = 0; i < n; i++)x[i] = v[i];\n                    cleanup(v);\n                });\n            }\n        }\n    })();\n})( true && module.exports ? module.exports : self.nacl = self.nacl || {});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdHdlZXRuYWNsL25hY2wtZmFzdC5qcyIsIm1hcHBpbmdzIjoiQUFBQyxVQUFTQSxJQUFJO0lBQ2Q7SUFFQSx1REFBdUQ7SUFDdkQsaUJBQWlCO0lBQ2pCLEVBQUU7SUFDRiwwREFBMEQ7SUFDMUQsOENBQThDO0lBRTlDLElBQUlDLEtBQUssU0FBU0MsSUFBSTtRQUNwQixJQUFJQyxHQUFHQyxJQUFJLElBQUlDLGFBQWE7UUFDNUIsSUFBSUgsTUFBTSxJQUFLQyxJQUFJLEdBQUdBLElBQUlELEtBQUtJLE1BQU0sRUFBRUgsSUFBS0MsQ0FBQyxDQUFDRCxFQUFFLEdBQUdELElBQUksQ0FBQ0MsRUFBRTtRQUMxRCxPQUFPQztJQUNUO0lBRUEsbURBQW1EO0lBQ25ELElBQUlHLGNBQWM7UUFBdUIsTUFBTSxJQUFJQyxNQUFNO0lBQVk7SUFFckUsSUFBSUMsS0FBSyxJQUFJQyxXQUFXO0lBQ3hCLElBQUlDLEtBQUssSUFBSUQsV0FBVztJQUFLQyxFQUFFLENBQUMsRUFBRSxHQUFHO0lBRXJDLElBQUlDLE1BQU1YLE1BQ05ZLE1BQU1aLEdBQUc7UUFBQztLQUFFLEdBQ1phLFVBQVViLEdBQUc7UUFBQztRQUFRO0tBQUUsR0FDeEJjLElBQUlkLEdBQUc7UUFBQztRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtLQUFPLEdBQ3ZJZSxLQUFLZixHQUFHO1FBQUM7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7S0FBTyxHQUN4SWdCLElBQUloQixHQUFHO1FBQUM7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7S0FBTyxHQUN2SWlCLElBQUlqQixHQUFHO1FBQUM7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7S0FBTyxHQUN2SWtCLElBQUlsQixHQUFHO1FBQUM7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQVE7S0FBTztJQUUzSSxTQUFTbUIsS0FBS0MsQ0FBQyxFQUFFbEIsQ0FBQyxFQUFFbUIsQ0FBQyxFQUFFQyxDQUFDO1FBQ3RCRixDQUFDLENBQUNsQixFQUFFLEdBQUssS0FBTSxLQUFNO1FBQ3JCa0IsQ0FBQyxDQUFDbEIsSUFBRSxFQUFFLEdBQUcsS0FBTSxLQUFNO1FBQ3JCa0IsQ0FBQyxDQUFDbEIsSUFBRSxFQUFFLEdBQUcsS0FBTyxJQUFLO1FBQ3JCa0IsQ0FBQyxDQUFDbEIsSUFBRSxFQUFFLEdBQUdtQixJQUFJO1FBQ2JELENBQUMsQ0FBQ2xCLElBQUUsRUFBRSxHQUFHLEtBQU0sS0FBTztRQUN0QmtCLENBQUMsQ0FBQ2xCLElBQUUsRUFBRSxHQUFHLEtBQU0sS0FBTztRQUN0QmtCLENBQUMsQ0FBQ2xCLElBQUUsRUFBRSxHQUFHLEtBQU8sSUFBTTtRQUN0QmtCLENBQUMsQ0FBQ2xCLElBQUUsRUFBRSxHQUFHb0IsSUFBSTtJQUNmO0lBRUEsU0FBU0MsR0FBR0gsQ0FBQyxFQUFFSSxFQUFFLEVBQUVDLENBQUMsRUFBRUMsRUFBRSxFQUFFQyxDQUFDO1FBQ3pCLElBQUl6QixHQUFFMEIsSUFBSTtRQUNWLElBQUsxQixJQUFJLEdBQUdBLElBQUl5QixHQUFHekIsSUFBSzBCLEtBQUtSLENBQUMsQ0FBQ0ksS0FBR3RCLEVBQUUsR0FBQ3VCLENBQUMsQ0FBQ0MsS0FBR3hCLEVBQUU7UUFDNUMsT0FBTyxDQUFDLElBQUssSUFBSyxNQUFPLENBQUMsSUFBSztJQUNqQztJQUVBLFNBQVMyQixpQkFBaUJULENBQUMsRUFBRUksRUFBRSxFQUFFQyxDQUFDLEVBQUVDLEVBQUU7UUFDcEMsT0FBT0gsR0FBR0gsR0FBRUksSUFBR0MsR0FBRUMsSUFBRztJQUN0QjtJQUVBLFNBQVNJLGlCQUFpQlYsQ0FBQyxFQUFFSSxFQUFFLEVBQUVDLENBQUMsRUFBRUMsRUFBRTtRQUNwQyxPQUFPSCxHQUFHSCxHQUFFSSxJQUFHQyxHQUFFQyxJQUFHO0lBQ3RCO0lBRUEsU0FBU0ssYUFBYUMsQ0FBQyxFQUFFQyxDQUFDLEVBQUVDLENBQUMsRUFBRUMsQ0FBQztRQUM5QixJQUFJQyxLQUFNRCxDQUFDLENBQUUsRUFBRSxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUUsRUFBRSxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQzlFRSxLQUFNSCxDQUFDLENBQUUsRUFBRSxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUUsRUFBRSxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQzlFSSxLQUFNSixDQUFDLENBQUUsRUFBRSxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUUsRUFBRSxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQzlFSyxLQUFNTCxDQUFDLENBQUUsRUFBRSxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQzlFTSxLQUFNTixDQUFDLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQzlFTyxLQUFNTixDQUFDLENBQUUsRUFBRSxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUUsRUFBRSxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQzlFTyxLQUFNVCxDQUFDLENBQUUsRUFBRSxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUUsRUFBRSxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQzlFVSxLQUFNVixDQUFDLENBQUUsRUFBRSxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUUsRUFBRSxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQzlFVyxLQUFNWCxDQUFDLENBQUUsRUFBRSxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQzlFWSxLQUFNWixDQUFDLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQzlFYSxNQUFNWCxDQUFDLENBQUUsRUFBRSxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQzlFWSxNQUFNYixDQUFDLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQzlFYyxNQUFNZCxDQUFDLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQzlFZSxNQUFNZixDQUFDLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQzlFZ0IsTUFBTWhCLENBQUMsQ0FBQyxHQUFHLEdBQUcsT0FBTyxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxJQUFJLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLEtBQUssQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksSUFDOUVpQixNQUFNaEIsQ0FBQyxDQUFDLEdBQUcsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSTtRQUVsRixJQUFJaUIsS0FBS2hCLElBQUlpQixLQUFLaEIsSUFBSWlCLEtBQUtoQixJQUFJaUIsS0FBS2hCLElBQUlpQixLQUFLaEIsSUFBSWlCLEtBQUtoQixJQUFJaUIsS0FBS2hCLElBQUlpQixLQUFLaEIsSUFDcEVpQixLQUFLaEIsSUFBSWlCLEtBQUtoQixJQUFJaUIsTUFBTWhCLEtBQUtpQixNQUFNaEIsS0FBS2lCLE1BQU1oQixLQUFLaUIsTUFBTWhCLEtBQUtpQixNQUFNaEIsS0FDcEVpQixNQUFNaEIsS0FBS2lCO1FBRWYsSUFBSyxJQUFJbEUsSUFBSSxHQUFHQSxJQUFJLElBQUlBLEtBQUssRUFBRztZQUM5QmtFLElBQUloQixLQUFLWSxNQUFNO1lBQ2ZSLE1BQU1ZLEtBQUcsSUFBSUEsTUFBSyxLQUFHO1lBQ3JCQSxJQUFJWixLQUFLSixLQUFLO1lBQ2RRLE1BQU1RLEtBQUcsSUFBSUEsTUFBSyxLQUFHO1lBQ3JCQSxJQUFJUixLQUFLSixLQUFLO1lBQ2RRLE9BQU9JLEtBQUcsS0FBS0EsTUFBSyxLQUFHO1lBQ3ZCQSxJQUFJSixNQUFNSixLQUFLO1lBQ2ZSLE1BQU1nQixLQUFHLEtBQUtBLE1BQUssS0FBRztZQUV0QkEsSUFBSVgsS0FBS0osS0FBSztZQUNkUSxNQUFNTyxLQUFHLElBQUlBLE1BQUssS0FBRztZQUNyQkEsSUFBSVAsS0FBS0osS0FBSztZQUNkUSxPQUFPRyxLQUFHLElBQUlBLE1BQUssS0FBRztZQUN0QkEsSUFBSUgsTUFBTUosS0FBSztZQUNmUixNQUFNZSxLQUFHLEtBQUtBLE1BQUssS0FBRztZQUN0QkEsSUFBSWYsS0FBS1ksTUFBTTtZQUNmUixNQUFNVyxLQUFHLEtBQUtBLE1BQUssS0FBRztZQUV0QkEsSUFBSU4sTUFBTUosS0FBSztZQUNmUSxPQUFPRSxLQUFHLElBQUlBLE1BQUssS0FBRztZQUN0QkEsSUFBSUYsTUFBTUosTUFBTTtZQUNoQlIsTUFBTWMsS0FBRyxJQUFJQSxNQUFLLEtBQUc7WUFDckJBLElBQUlkLEtBQUtZLE1BQU07WUFDZlIsTUFBTVUsS0FBRyxLQUFLQSxNQUFLLEtBQUc7WUFDdEJBLElBQUlWLEtBQUtKLEtBQUs7WUFDZFEsT0FBT00sS0FBRyxLQUFLQSxNQUFLLEtBQUc7WUFFdkJBLElBQUlELE1BQU1KLE1BQU07WUFDaEJSLE1BQU1hLEtBQUcsSUFBSUEsTUFBSyxLQUFHO1lBQ3JCQSxJQUFJYixLQUFLWSxNQUFNO1lBQ2ZSLE1BQU1TLEtBQUcsSUFBSUEsTUFBSyxLQUFHO1lBQ3JCQSxJQUFJVCxLQUFLSixLQUFLO1lBQ2RRLE9BQU9LLEtBQUcsS0FBS0EsTUFBSyxLQUFHO1lBQ3ZCQSxJQUFJTCxNQUFNSixLQUFLO1lBQ2ZRLE9BQU9DLEtBQUcsS0FBS0EsTUFBSyxLQUFHO1lBRXZCQSxJQUFJaEIsS0FBS0csS0FBSztZQUNkRixNQUFNZSxLQUFHLElBQUlBLE1BQUssS0FBRztZQUNyQkEsSUFBSWYsS0FBS0QsS0FBSztZQUNkRSxNQUFNYyxLQUFHLElBQUlBLE1BQUssS0FBRztZQUNyQkEsSUFBSWQsS0FBS0QsS0FBSztZQUNkRSxNQUFNYSxLQUFHLEtBQUtBLE1BQUssS0FBRztZQUN0QkEsSUFBSWIsS0FBS0QsS0FBSztZQUNkRixNQUFNZ0IsS0FBRyxLQUFLQSxNQUFLLEtBQUc7WUFFdEJBLElBQUlYLEtBQUtELEtBQUs7WUFDZEUsTUFBTVUsS0FBRyxJQUFJQSxNQUFLLEtBQUc7WUFDckJBLElBQUlWLEtBQUtELEtBQUs7WUFDZEUsTUFBTVMsS0FBRyxJQUFJQSxNQUFLLEtBQUc7WUFDckJBLElBQUlULEtBQUtELEtBQUs7WUFDZEYsTUFBTVksS0FBRyxLQUFLQSxNQUFLLEtBQUc7WUFDdEJBLElBQUlaLEtBQUtHLEtBQUs7WUFDZEYsTUFBTVcsS0FBRyxLQUFLQSxNQUFLLEtBQUc7WUFFdEJBLElBQUlOLE1BQU1ELEtBQUs7WUFDZkUsT0FBT0ssS0FBRyxJQUFJQSxNQUFLLEtBQUc7WUFDdEJBLElBQUlMLE1BQU1ELE1BQU07WUFDaEJGLE1BQU1RLEtBQUcsSUFBSUEsTUFBSyxLQUFHO1lBQ3JCQSxJQUFJUixLQUFLRyxNQUFNO1lBQ2ZGLE1BQU1PLEtBQUcsS0FBS0EsTUFBSyxLQUFHO1lBQ3RCQSxJQUFJUCxLQUFLRCxLQUFLO1lBQ2RFLE9BQU9NLEtBQUcsS0FBS0EsTUFBSyxLQUFHO1lBRXZCQSxJQUFJRCxNQUFNRCxNQUFNO1lBQ2hCRixPQUFPSSxLQUFHLElBQUlBLE1BQUssS0FBRztZQUN0QkEsSUFBSUosTUFBTUcsTUFBTTtZQUNoQkYsT0FBT0csS0FBRyxJQUFJQSxNQUFLLEtBQUc7WUFDdEJBLElBQUlILE1BQU1ELE1BQU07WUFDaEJFLE9BQU9FLEtBQUcsS0FBS0EsTUFBSyxLQUFHO1lBQ3ZCQSxJQUFJRixNQUFNRCxNQUFNO1lBQ2hCRSxPQUFPQyxLQUFHLEtBQUtBLE1BQUssS0FBRztRQUN6QjtRQUNDaEIsS0FBTUEsS0FBTWhCLEtBQUs7UUFDakJpQixLQUFNQSxLQUFNaEIsS0FBSztRQUNqQmlCLEtBQU1BLEtBQU1oQixLQUFLO1FBQ2pCaUIsS0FBTUEsS0FBTWhCLEtBQUs7UUFDakJpQixLQUFNQSxLQUFNaEIsS0FBSztRQUNqQmlCLEtBQU1BLEtBQU1oQixLQUFLO1FBQ2pCaUIsS0FBTUEsS0FBTWhCLEtBQUs7UUFDakJpQixLQUFNQSxLQUFNaEIsS0FBSztRQUNqQmlCLEtBQU1BLEtBQU1oQixLQUFLO1FBQ2pCaUIsS0FBTUEsS0FBTWhCLEtBQUs7UUFDbEJpQixNQUFNQSxNQUFNaEIsTUFBTTtRQUNsQmlCLE1BQU1BLE1BQU1oQixNQUFNO1FBQ2xCaUIsTUFBTUEsTUFBTWhCLE1BQU07UUFDbEJpQixNQUFNQSxNQUFNaEIsTUFBTTtRQUNsQmlCLE1BQU1BLE1BQU1oQixNQUFNO1FBQ2xCaUIsTUFBTUEsTUFBTWhCLE1BQU07UUFFbEJuQixDQUFDLENBQUUsRUFBRSxHQUFHb0IsT0FBUSxJQUFJO1FBQ3BCcEIsQ0FBQyxDQUFFLEVBQUUsR0FBR29CLE9BQVEsSUFBSTtRQUNwQnBCLENBQUMsQ0FBRSxFQUFFLEdBQUdvQixPQUFPLEtBQUs7UUFDcEJwQixDQUFDLENBQUUsRUFBRSxHQUFHb0IsT0FBTyxLQUFLO1FBRXBCcEIsQ0FBQyxDQUFFLEVBQUUsR0FBR3FCLE9BQVEsSUFBSTtRQUNwQnJCLENBQUMsQ0FBRSxFQUFFLEdBQUdxQixPQUFRLElBQUk7UUFDcEJyQixDQUFDLENBQUUsRUFBRSxHQUFHcUIsT0FBTyxLQUFLO1FBQ3BCckIsQ0FBQyxDQUFFLEVBQUUsR0FBR3FCLE9BQU8sS0FBSztRQUVwQnJCLENBQUMsQ0FBRSxFQUFFLEdBQUdzQixPQUFRLElBQUk7UUFDcEJ0QixDQUFDLENBQUUsRUFBRSxHQUFHc0IsT0FBUSxJQUFJO1FBQ3BCdEIsQ0FBQyxDQUFDLEdBQUcsR0FBR3NCLE9BQU8sS0FBSztRQUNwQnRCLENBQUMsQ0FBQyxHQUFHLEdBQUdzQixPQUFPLEtBQUs7UUFFcEJ0QixDQUFDLENBQUMsR0FBRyxHQUFHdUIsT0FBUSxJQUFJO1FBQ3BCdkIsQ0FBQyxDQUFDLEdBQUcsR0FBR3VCLE9BQVEsSUFBSTtRQUNwQnZCLENBQUMsQ0FBQyxHQUFHLEdBQUd1QixPQUFPLEtBQUs7UUFDcEJ2QixDQUFDLENBQUMsR0FBRyxHQUFHdUIsT0FBTyxLQUFLO1FBRXBCdkIsQ0FBQyxDQUFDLEdBQUcsR0FBR3dCLE9BQVEsSUFBSTtRQUNwQnhCLENBQUMsQ0FBQyxHQUFHLEdBQUd3QixPQUFRLElBQUk7UUFDcEJ4QixDQUFDLENBQUMsR0FBRyxHQUFHd0IsT0FBTyxLQUFLO1FBQ3BCeEIsQ0FBQyxDQUFDLEdBQUcsR0FBR3dCLE9BQU8sS0FBSztRQUVwQnhCLENBQUMsQ0FBQyxHQUFHLEdBQUd5QixPQUFRLElBQUk7UUFDcEJ6QixDQUFDLENBQUMsR0FBRyxHQUFHeUIsT0FBUSxJQUFJO1FBQ3BCekIsQ0FBQyxDQUFDLEdBQUcsR0FBR3lCLE9BQU8sS0FBSztRQUNwQnpCLENBQUMsQ0FBQyxHQUFHLEdBQUd5QixPQUFPLEtBQUs7UUFFcEJ6QixDQUFDLENBQUMsR0FBRyxHQUFHMEIsT0FBUSxJQUFJO1FBQ3BCMUIsQ0FBQyxDQUFDLEdBQUcsR0FBRzBCLE9BQVEsSUFBSTtRQUNwQjFCLENBQUMsQ0FBQyxHQUFHLEdBQUcwQixPQUFPLEtBQUs7UUFDcEIxQixDQUFDLENBQUMsR0FBRyxHQUFHMEIsT0FBTyxLQUFLO1FBRXBCMUIsQ0FBQyxDQUFDLEdBQUcsR0FBRzJCLE9BQVEsSUFBSTtRQUNwQjNCLENBQUMsQ0FBQyxHQUFHLEdBQUcyQixPQUFRLElBQUk7UUFDcEIzQixDQUFDLENBQUMsR0FBRyxHQUFHMkIsT0FBTyxLQUFLO1FBQ3BCM0IsQ0FBQyxDQUFDLEdBQUcsR0FBRzJCLE9BQU8sS0FBSztRQUVwQjNCLENBQUMsQ0FBQyxHQUFHLEdBQUc0QixPQUFRLElBQUk7UUFDcEI1QixDQUFDLENBQUMsR0FBRyxHQUFHNEIsT0FBUSxJQUFJO1FBQ3BCNUIsQ0FBQyxDQUFDLEdBQUcsR0FBRzRCLE9BQU8sS0FBSztRQUNwQjVCLENBQUMsQ0FBQyxHQUFHLEdBQUc0QixPQUFPLEtBQUs7UUFFcEI1QixDQUFDLENBQUMsR0FBRyxHQUFHNkIsT0FBUSxJQUFJO1FBQ3BCN0IsQ0FBQyxDQUFDLEdBQUcsR0FBRzZCLE9BQVEsSUFBSTtRQUNwQjdCLENBQUMsQ0FBQyxHQUFHLEdBQUc2QixPQUFPLEtBQUs7UUFDcEI3QixDQUFDLENBQUMsR0FBRyxHQUFHNkIsT0FBTyxLQUFLO1FBRXBCN0IsQ0FBQyxDQUFDLEdBQUcsR0FBRzhCLFFBQVMsSUFBSTtRQUNyQjlCLENBQUMsQ0FBQyxHQUFHLEdBQUc4QixRQUFTLElBQUk7UUFDckI5QixDQUFDLENBQUMsR0FBRyxHQUFHOEIsUUFBUSxLQUFLO1FBQ3JCOUIsQ0FBQyxDQUFDLEdBQUcsR0FBRzhCLFFBQVEsS0FBSztRQUVyQjlCLENBQUMsQ0FBQyxHQUFHLEdBQUcrQixRQUFTLElBQUk7UUFDckIvQixDQUFDLENBQUMsR0FBRyxHQUFHK0IsUUFBUyxJQUFJO1FBQ3JCL0IsQ0FBQyxDQUFDLEdBQUcsR0FBRytCLFFBQVEsS0FBSztRQUNyQi9CLENBQUMsQ0FBQyxHQUFHLEdBQUcrQixRQUFRLEtBQUs7UUFFckIvQixDQUFDLENBQUMsR0FBRyxHQUFHZ0MsUUFBUyxJQUFJO1FBQ3JCaEMsQ0FBQyxDQUFDLEdBQUcsR0FBR2dDLFFBQVMsSUFBSTtRQUNyQmhDLENBQUMsQ0FBQyxHQUFHLEdBQUdnQyxRQUFRLEtBQUs7UUFDckJoQyxDQUFDLENBQUMsR0FBRyxHQUFHZ0MsUUFBUSxLQUFLO1FBRXJCaEMsQ0FBQyxDQUFDLEdBQUcsR0FBR2lDLFFBQVMsSUFBSTtRQUNyQmpDLENBQUMsQ0FBQyxHQUFHLEdBQUdpQyxRQUFTLElBQUk7UUFDckJqQyxDQUFDLENBQUMsR0FBRyxHQUFHaUMsUUFBUSxLQUFLO1FBQ3JCakMsQ0FBQyxDQUFDLEdBQUcsR0FBR2lDLFFBQVEsS0FBSztRQUVyQmpDLENBQUMsQ0FBQyxHQUFHLEdBQUdrQyxRQUFTLElBQUk7UUFDckJsQyxDQUFDLENBQUMsR0FBRyxHQUFHa0MsUUFBUyxJQUFJO1FBQ3JCbEMsQ0FBQyxDQUFDLEdBQUcsR0FBR2tDLFFBQVEsS0FBSztRQUNyQmxDLENBQUMsQ0FBQyxHQUFHLEdBQUdrQyxRQUFRLEtBQUs7UUFFckJsQyxDQUFDLENBQUMsR0FBRyxHQUFHbUMsUUFBUyxJQUFJO1FBQ3JCbkMsQ0FBQyxDQUFDLEdBQUcsR0FBR21DLFFBQVMsSUFBSTtRQUNyQm5DLENBQUMsQ0FBQyxHQUFHLEdBQUdtQyxRQUFRLEtBQUs7UUFDckJuQyxDQUFDLENBQUMsR0FBRyxHQUFHbUMsUUFBUSxLQUFLO0lBQ3ZCO0lBRUEsU0FBU0UsY0FBY3JDLENBQUMsRUFBQ0MsQ0FBQyxFQUFDQyxDQUFDLEVBQUNDLENBQUM7UUFDNUIsSUFBSUMsS0FBTUQsQ0FBQyxDQUFFLEVBQUUsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUUsRUFBRSxHQUFHLElBQUcsS0FBSSxJQUM5RUUsS0FBTUgsQ0FBQyxDQUFFLEVBQUUsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUUsRUFBRSxHQUFHLElBQUcsS0FBSSxJQUM5RUksS0FBTUosQ0FBQyxDQUFFLEVBQUUsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUUsRUFBRSxHQUFHLElBQUcsS0FBSSxJQUM5RUssS0FBTUwsQ0FBQyxDQUFFLEVBQUUsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxJQUM5RU0sS0FBTU4sQ0FBQyxDQUFDLEdBQUcsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxJQUM5RU8sS0FBTU4sQ0FBQyxDQUFFLEVBQUUsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUUsRUFBRSxHQUFHLElBQUcsS0FBSSxJQUM5RU8sS0FBTVQsQ0FBQyxDQUFFLEVBQUUsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUUsRUFBRSxHQUFHLElBQUcsS0FBSSxJQUM5RVUsS0FBTVYsQ0FBQyxDQUFFLEVBQUUsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUUsRUFBRSxHQUFHLElBQUcsS0FBSSxJQUM5RVcsS0FBTVgsQ0FBQyxDQUFFLEVBQUUsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxJQUM5RVksS0FBTVosQ0FBQyxDQUFDLEdBQUcsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxJQUM5RWEsTUFBTVgsQ0FBQyxDQUFFLEVBQUUsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxJQUM5RVksTUFBTWIsQ0FBQyxDQUFDLEdBQUcsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxJQUM5RWMsTUFBTWQsQ0FBQyxDQUFDLEdBQUcsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxJQUM5RWUsTUFBTWYsQ0FBQyxDQUFDLEdBQUcsR0FBRyxPQUFPLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQUksQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksS0FBSyxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxJQUM5RWdCLE1BQU1oQixDQUFDLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUksSUFBSSxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxLQUFLLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLElBQzlFaUIsTUFBTWhCLENBQUMsQ0FBQyxHQUFHLEdBQUcsT0FBTyxDQUFDQSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBSSxJQUFJLENBQUNBLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFJLEtBQUssQ0FBQ0EsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQUk7UUFFbEYsSUFBSWlCLEtBQUtoQixJQUFJaUIsS0FBS2hCLElBQUlpQixLQUFLaEIsSUFBSWlCLEtBQUtoQixJQUFJaUIsS0FBS2hCLElBQUlpQixLQUFLaEIsSUFBSWlCLEtBQUtoQixJQUFJaUIsS0FBS2hCLElBQ3BFaUIsS0FBS2hCLElBQUlpQixLQUFLaEIsSUFBSWlCLE1BQU1oQixLQUFLaUIsTUFBTWhCLEtBQUtpQixNQUFNaEIsS0FBS2lCLE1BQU1oQixLQUFLaUIsTUFBTWhCLEtBQ3BFaUIsTUFBTWhCLEtBQUtpQjtRQUVmLElBQUssSUFBSWxFLElBQUksR0FBR0EsSUFBSSxJQUFJQSxLQUFLLEVBQUc7WUFDOUJrRSxJQUFJaEIsS0FBS1ksTUFBTTtZQUNmUixNQUFNWSxLQUFHLElBQUlBLE1BQUssS0FBRztZQUNyQkEsSUFBSVosS0FBS0osS0FBSztZQUNkUSxNQUFNUSxLQUFHLElBQUlBLE1BQUssS0FBRztZQUNyQkEsSUFBSVIsS0FBS0osS0FBSztZQUNkUSxPQUFPSSxLQUFHLEtBQUtBLE1BQUssS0FBRztZQUN2QkEsSUFBSUosTUFBTUosS0FBSztZQUNmUixNQUFNZ0IsS0FBRyxLQUFLQSxNQUFLLEtBQUc7WUFFdEJBLElBQUlYLEtBQUtKLEtBQUs7WUFDZFEsTUFBTU8sS0FBRyxJQUFJQSxNQUFLLEtBQUc7WUFDckJBLElBQUlQLEtBQUtKLEtBQUs7WUFDZFEsT0FBT0csS0FBRyxJQUFJQSxNQUFLLEtBQUc7WUFDdEJBLElBQUlILE1BQU1KLEtBQUs7WUFDZlIsTUFBTWUsS0FBRyxLQUFLQSxNQUFLLEtBQUc7WUFDdEJBLElBQUlmLEtBQUtZLE1BQU07WUFDZlIsTUFBTVcsS0FBRyxLQUFLQSxNQUFLLEtBQUc7WUFFdEJBLElBQUlOLE1BQU1KLEtBQUs7WUFDZlEsT0FBT0UsS0FBRyxJQUFJQSxNQUFLLEtBQUc7WUFDdEJBLElBQUlGLE1BQU1KLE1BQU07WUFDaEJSLE1BQU1jLEtBQUcsSUFBSUEsTUFBSyxLQUFHO1lBQ3JCQSxJQUFJZCxLQUFLWSxNQUFNO1lBQ2ZSLE1BQU1VLEtBQUcsS0FBS0EsTUFBSyxLQUFHO1lBQ3RCQSxJQUFJVixLQUFLSixLQUFLO1lBQ2RRLE9BQU9NLEtBQUcsS0FBS0EsTUFBSyxLQUFHO1lBRXZCQSxJQUFJRCxNQUFNSixNQUFNO1lBQ2hCUixNQUFNYSxLQUFHLElBQUlBLE1BQUssS0FBRztZQUNyQkEsSUFBSWIsS0FBS1ksTUFBTTtZQUNmUixNQUFNUyxLQUFHLElBQUlBLE1BQUssS0FBRztZQUNyQkEsSUFBSVQsS0FBS0osS0FBSztZQUNkUSxPQUFPSyxLQUFHLEtBQUtBLE1BQUssS0FBRztZQUN2QkEsSUFBSUwsTUFBTUosS0FBSztZQUNmUSxPQUFPQyxLQUFHLEtBQUtBLE1BQUssS0FBRztZQUV2QkEsSUFBSWhCLEtBQUtHLEtBQUs7WUFDZEYsTUFBTWUsS0FBRyxJQUFJQSxNQUFLLEtBQUc7WUFDckJBLElBQUlmLEtBQUtELEtBQUs7WUFDZEUsTUFBTWMsS0FBRyxJQUFJQSxNQUFLLEtBQUc7WUFDckJBLElBQUlkLEtBQUtELEtBQUs7WUFDZEUsTUFBTWEsS0FBRyxLQUFLQSxNQUFLLEtBQUc7WUFDdEJBLElBQUliLEtBQUtELEtBQUs7WUFDZEYsTUFBTWdCLEtBQUcsS0FBS0EsTUFBSyxLQUFHO1lBRXRCQSxJQUFJWCxLQUFLRCxLQUFLO1lBQ2RFLE1BQU1VLEtBQUcsSUFBSUEsTUFBSyxLQUFHO1lBQ3JCQSxJQUFJVixLQUFLRCxLQUFLO1lBQ2RFLE1BQU1TLEtBQUcsSUFBSUEsTUFBSyxLQUFHO1lBQ3JCQSxJQUFJVCxLQUFLRCxLQUFLO1lBQ2RGLE1BQU1ZLEtBQUcsS0FBS0EsTUFBSyxLQUFHO1lBQ3RCQSxJQUFJWixLQUFLRyxLQUFLO1lBQ2RGLE1BQU1XLEtBQUcsS0FBS0EsTUFBSyxLQUFHO1lBRXRCQSxJQUFJTixNQUFNRCxLQUFLO1lBQ2ZFLE9BQU9LLEtBQUcsSUFBSUEsTUFBSyxLQUFHO1lBQ3RCQSxJQUFJTCxNQUFNRCxNQUFNO1lBQ2hCRixNQUFNUSxLQUFHLElBQUlBLE1BQUssS0FBRztZQUNyQkEsSUFBSVIsS0FBS0csTUFBTTtZQUNmRixNQUFNTyxLQUFHLEtBQUtBLE1BQUssS0FBRztZQUN0QkEsSUFBSVAsS0FBS0QsS0FBSztZQUNkRSxPQUFPTSxLQUFHLEtBQUtBLE1BQUssS0FBRztZQUV2QkEsSUFBSUQsTUFBTUQsTUFBTTtZQUNoQkYsT0FBT0ksS0FBRyxJQUFJQSxNQUFLLEtBQUc7WUFDdEJBLElBQUlKLE1BQU1HLE1BQU07WUFDaEJGLE9BQU9HLEtBQUcsSUFBSUEsTUFBSyxLQUFHO1lBQ3RCQSxJQUFJSCxNQUFNRCxNQUFNO1lBQ2hCRSxPQUFPRSxLQUFHLEtBQUtBLE1BQUssS0FBRztZQUN2QkEsSUFBSUYsTUFBTUQsTUFBTTtZQUNoQkUsT0FBT0MsS0FBRyxLQUFLQSxNQUFLLEtBQUc7UUFDekI7UUFFQXBDLENBQUMsQ0FBRSxFQUFFLEdBQUdvQixPQUFRLElBQUk7UUFDcEJwQixDQUFDLENBQUUsRUFBRSxHQUFHb0IsT0FBUSxJQUFJO1FBQ3BCcEIsQ0FBQyxDQUFFLEVBQUUsR0FBR29CLE9BQU8sS0FBSztRQUNwQnBCLENBQUMsQ0FBRSxFQUFFLEdBQUdvQixPQUFPLEtBQUs7UUFFcEJwQixDQUFDLENBQUUsRUFBRSxHQUFHeUIsT0FBUSxJQUFJO1FBQ3BCekIsQ0FBQyxDQUFFLEVBQUUsR0FBR3lCLE9BQVEsSUFBSTtRQUNwQnpCLENBQUMsQ0FBRSxFQUFFLEdBQUd5QixPQUFPLEtBQUs7UUFDcEJ6QixDQUFDLENBQUUsRUFBRSxHQUFHeUIsT0FBTyxLQUFLO1FBRXBCekIsQ0FBQyxDQUFFLEVBQUUsR0FBRzhCLFFBQVMsSUFBSTtRQUNyQjlCLENBQUMsQ0FBRSxFQUFFLEdBQUc4QixRQUFTLElBQUk7UUFDckI5QixDQUFDLENBQUMsR0FBRyxHQUFHOEIsUUFBUSxLQUFLO1FBQ3JCOUIsQ0FBQyxDQUFDLEdBQUcsR0FBRzhCLFFBQVEsS0FBSztRQUVyQjlCLENBQUMsQ0FBQyxHQUFHLEdBQUdtQyxRQUFTLElBQUk7UUFDckJuQyxDQUFDLENBQUMsR0FBRyxHQUFHbUMsUUFBUyxJQUFJO1FBQ3JCbkMsQ0FBQyxDQUFDLEdBQUcsR0FBR21DLFFBQVEsS0FBSztRQUNyQm5DLENBQUMsQ0FBQyxHQUFHLEdBQUdtQyxRQUFRLEtBQUs7UUFFckJuQyxDQUFDLENBQUMsR0FBRyxHQUFHMEIsT0FBUSxJQUFJO1FBQ3BCMUIsQ0FBQyxDQUFDLEdBQUcsR0FBRzBCLE9BQVEsSUFBSTtRQUNwQjFCLENBQUMsQ0FBQyxHQUFHLEdBQUcwQixPQUFPLEtBQUs7UUFDcEIxQixDQUFDLENBQUMsR0FBRyxHQUFHMEIsT0FBTyxLQUFLO1FBRXBCMUIsQ0FBQyxDQUFDLEdBQUcsR0FBRzJCLE9BQVEsSUFBSTtRQUNwQjNCLENBQUMsQ0FBQyxHQUFHLEdBQUcyQixPQUFRLElBQUk7UUFDcEIzQixDQUFDLENBQUMsR0FBRyxHQUFHMkIsT0FBTyxLQUFLO1FBQ3BCM0IsQ0FBQyxDQUFDLEdBQUcsR0FBRzJCLE9BQU8sS0FBSztRQUVwQjNCLENBQUMsQ0FBQyxHQUFHLEdBQUc0QixPQUFRLElBQUk7UUFDcEI1QixDQUFDLENBQUMsR0FBRyxHQUFHNEIsT0FBUSxJQUFJO1FBQ3BCNUIsQ0FBQyxDQUFDLEdBQUcsR0FBRzRCLE9BQU8sS0FBSztRQUNwQjVCLENBQUMsQ0FBQyxHQUFHLEdBQUc0QixPQUFPLEtBQUs7UUFFcEI1QixDQUFDLENBQUMsR0FBRyxHQUFHNkIsT0FBUSxJQUFJO1FBQ3BCN0IsQ0FBQyxDQUFDLEdBQUcsR0FBRzZCLE9BQVEsSUFBSTtRQUNwQjdCLENBQUMsQ0FBQyxHQUFHLEdBQUc2QixPQUFPLEtBQUs7UUFDcEI3QixDQUFDLENBQUMsR0FBRyxHQUFHNkIsT0FBTyxLQUFLO0lBQ3RCO0lBRUEsU0FBU1Msb0JBQW9CQyxHQUFHLEVBQUNDLEdBQUcsRUFBQ3RDLENBQUMsRUFBQ0MsQ0FBQztRQUN0Q0osYUFBYXdDLEtBQUlDLEtBQUl0QyxHQUFFQztJQUN6QjtJQUVBLFNBQVNzQyxxQkFBcUJGLEdBQUcsRUFBQ0MsR0FBRyxFQUFDdEMsQ0FBQyxFQUFDQyxDQUFDO1FBQ3ZDa0MsY0FBY0UsS0FBSUMsS0FBSXRDLEdBQUVDO0lBQzFCO0lBRUEsSUFBSXVDLFFBQVEsSUFBSWpFLFdBQVc7UUFBQztRQUFLO1FBQUs7UUFBSztRQUFJO1FBQUs7UUFBSztRQUFJO1FBQUk7UUFBSTtRQUFJO1FBQUk7UUFBSztRQUFLO1FBQUs7UUFBSTtLQUFJO0lBQ3hGLHFCQUFxQjtJQUVqQyxTQUFTa0UsMEJBQTBCeEMsQ0FBQyxFQUFDeUMsSUFBSSxFQUFDQyxDQUFDLEVBQUNDLElBQUksRUFBQ0MsQ0FBQyxFQUFDcEQsQ0FBQyxFQUFDTyxDQUFDO1FBQ3BELElBQUk4QyxJQUFJLElBQUl2RSxXQUFXLEtBQUtXLElBQUksSUFBSVgsV0FBVztRQUMvQyxJQUFJMkQsR0FBR2xFO1FBQ1AsSUFBS0EsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs4RSxDQUFDLENBQUM5RSxFQUFFLEdBQUc7UUFDaEMsSUFBS0EsSUFBSSxHQUFHQSxJQUFJLEdBQUdBLElBQUs4RSxDQUFDLENBQUM5RSxFQUFFLEdBQUd5QixDQUFDLENBQUN6QixFQUFFO1FBQ25DLE1BQU82RSxLQUFLLEdBQUk7WUFDZFQsb0JBQW9CbEQsR0FBRTRELEdBQUU5QyxHQUFFd0M7WUFDMUIsSUFBS3hFLElBQUksR0FBR0EsSUFBSSxJQUFJQSxJQUFLaUMsQ0FBQyxDQUFDeUMsT0FBSzFFLEVBQUUsR0FBRzJFLENBQUMsQ0FBQ0MsT0FBSzVFLEVBQUUsR0FBR2tCLENBQUMsQ0FBQ2xCLEVBQUU7WUFDckRrRSxJQUFJO1lBQ0osSUFBS2xFLElBQUksR0FBR0EsSUFBSSxJQUFJQSxJQUFLO2dCQUN2QmtFLElBQUlBLElBQUtZLENBQUFBLENBQUMsQ0FBQzlFLEVBQUUsR0FBRyxJQUFHLElBQUs7Z0JBQ3hCOEUsQ0FBQyxDQUFDOUUsRUFBRSxHQUFHa0UsSUFBSTtnQkFDWEEsT0FBTztZQUNUO1lBQ0FXLEtBQUs7WUFDTEgsUUFBUTtZQUNSRSxRQUFRO1FBQ1Y7UUFDQSxJQUFJQyxJQUFJLEdBQUc7WUFDVFQsb0JBQW9CbEQsR0FBRTRELEdBQUU5QyxHQUFFd0M7WUFDMUIsSUFBS3hFLElBQUksR0FBR0EsSUFBSTZFLEdBQUc3RSxJQUFLaUMsQ0FBQyxDQUFDeUMsT0FBSzFFLEVBQUUsR0FBRzJFLENBQUMsQ0FBQ0MsT0FBSzVFLEVBQUUsR0FBR2tCLENBQUMsQ0FBQ2xCLEVBQUU7UUFDdEQ7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxTQUFTK0Usc0JBQXNCOUMsQ0FBQyxFQUFDeUMsSUFBSSxFQUFDRyxDQUFDLEVBQUNwRCxDQUFDLEVBQUNPLENBQUM7UUFDekMsSUFBSThDLElBQUksSUFBSXZFLFdBQVcsS0FBS1csSUFBSSxJQUFJWCxXQUFXO1FBQy9DLElBQUkyRCxHQUFHbEU7UUFDUCxJQUFLQSxJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBSzhFLENBQUMsQ0FBQzlFLEVBQUUsR0FBRztRQUNoQyxJQUFLQSxJQUFJLEdBQUdBLElBQUksR0FBR0EsSUFBSzhFLENBQUMsQ0FBQzlFLEVBQUUsR0FBR3lCLENBQUMsQ0FBQ3pCLEVBQUU7UUFDbkMsTUFBTzZFLEtBQUssR0FBSTtZQUNkVCxvQkFBb0JsRCxHQUFFNEQsR0FBRTlDLEdBQUV3QztZQUMxQixJQUFLeEUsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUtpQyxDQUFDLENBQUN5QyxPQUFLMUUsRUFBRSxHQUFHa0IsQ0FBQyxDQUFDbEIsRUFBRTtZQUN6Q2tFLElBQUk7WUFDSixJQUFLbEUsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs7Z0JBQ3ZCa0UsSUFBSUEsSUFBS1ksQ0FBQUEsQ0FBQyxDQUFDOUUsRUFBRSxHQUFHLElBQUcsSUFBSztnQkFDeEI4RSxDQUFDLENBQUM5RSxFQUFFLEdBQUdrRSxJQUFJO2dCQUNYQSxPQUFPO1lBQ1Q7WUFDQVcsS0FBSztZQUNMSCxRQUFRO1FBQ1Y7UUFDQSxJQUFJRyxJQUFJLEdBQUc7WUFDVFQsb0JBQW9CbEQsR0FBRTRELEdBQUU5QyxHQUFFd0M7WUFDMUIsSUFBS3hFLElBQUksR0FBR0EsSUFBSTZFLEdBQUc3RSxJQUFLaUMsQ0FBQyxDQUFDeUMsT0FBSzFFLEVBQUUsR0FBR2tCLENBQUMsQ0FBQ2xCLEVBQUU7UUFDMUM7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxTQUFTZ0YsY0FBYy9DLENBQUMsRUFBQ3lDLElBQUksRUFBQ2hELENBQUMsRUFBQ0QsQ0FBQyxFQUFDTyxDQUFDO1FBQ2pDLElBQUlpRCxJQUFJLElBQUkxRSxXQUFXO1FBQ3ZCZ0UscUJBQXFCVSxHQUFFeEQsR0FBRU8sR0FBRXdDO1FBQzNCLElBQUlVLEtBQUssSUFBSTNFLFdBQVc7UUFDeEIsSUFBSyxJQUFJUCxJQUFJLEdBQUdBLElBQUksR0FBR0EsSUFBS2tGLEVBQUUsQ0FBQ2xGLEVBQUUsR0FBR3lCLENBQUMsQ0FBQ3pCLElBQUUsR0FBRztRQUMzQyxPQUFPK0Usc0JBQXNCOUMsR0FBRXlDLE1BQUtoRCxHQUFFd0QsSUFBR0Q7SUFDM0M7SUFFQSxTQUFTRSxrQkFBa0JsRCxDQUFDLEVBQUN5QyxJQUFJLEVBQUNDLENBQUMsRUFBQ0MsSUFBSSxFQUFDbEQsQ0FBQyxFQUFDRCxDQUFDLEVBQUNPLENBQUM7UUFDNUMsSUFBSWlELElBQUksSUFBSTFFLFdBQVc7UUFDdkJnRSxxQkFBcUJVLEdBQUV4RCxHQUFFTyxHQUFFd0M7UUFDM0IsSUFBSVUsS0FBSyxJQUFJM0UsV0FBVztRQUN4QixJQUFLLElBQUlQLElBQUksR0FBR0EsSUFBSSxHQUFHQSxJQUFLa0YsRUFBRSxDQUFDbEYsRUFBRSxHQUFHeUIsQ0FBQyxDQUFDekIsSUFBRSxHQUFHO1FBQzNDLE9BQU95RSwwQkFBMEJ4QyxHQUFFeUMsTUFBS0MsR0FBRUMsTUFBS2xELEdBQUV3RCxJQUFHRDtJQUN0RDtJQUVBOzs7QUFHQSxHQUVBLElBQUlHLFdBQVcsU0FBU0MsR0FBRztRQUN6QixJQUFJLENBQUNDLE1BQU0sR0FBRyxJQUFJL0UsV0FBVztRQUM3QixJQUFJLENBQUNOLENBQUMsR0FBRyxJQUFJc0YsWUFBWTtRQUN6QixJQUFJLENBQUNwRSxDQUFDLEdBQUcsSUFBSW9FLFlBQVk7UUFDekIsSUFBSSxDQUFDQyxHQUFHLEdBQUcsSUFBSUQsWUFBWTtRQUMzQixJQUFJLENBQUNFLFFBQVEsR0FBRztRQUNoQixJQUFJLENBQUNDLEdBQUcsR0FBRztRQUVYLElBQUlDLElBQUlDLElBQUlDLElBQUlDLElBQUlDLElBQUlDLElBQUlDLElBQUlDO1FBRWhDUCxLQUFLTixHQUFHLENBQUUsRUFBRSxHQUFHLE9BQU8sQ0FBQ0EsR0FBRyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQU07UUFBRyxJQUFJLENBQUNwRixDQUFDLENBQUMsRUFBRSxHQUFHLEtBQTZCO1FBQ3RGMkYsS0FBS1AsR0FBRyxDQUFFLEVBQUUsR0FBRyxPQUFPLENBQUNBLEdBQUcsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFNO1FBQUcsSUFBSSxDQUFDcEYsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLE9BQVEsS0FBTzJGLE1BQU8sQ0FBQyxJQUFLO1FBQ3RGQyxLQUFLUixHQUFHLENBQUUsRUFBRSxHQUFHLE9BQU8sQ0FBQ0EsR0FBRyxDQUFFLEVBQUUsR0FBRyxJQUFHLEtBQU07UUFBRyxJQUFJLENBQUNwRixDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsT0FBUSxLQUFPNEYsTUFBTyxDQUFDLElBQUs7UUFDdEZDLEtBQUtULEdBQUcsQ0FBRSxFQUFFLEdBQUcsT0FBTyxDQUFDQSxHQUFHLENBQUUsRUFBRSxHQUFHLElBQUcsS0FBTTtRQUFHLElBQUksQ0FBQ3BGLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxPQUFTLElBQU02RixNQUFPLENBQUMsSUFBSztRQUN0RkMsS0FBS1YsR0FBRyxDQUFFLEVBQUUsR0FBRyxPQUFPLENBQUNBLEdBQUcsQ0FBRSxFQUFFLEdBQUcsSUFBRyxLQUFNO1FBQUcsSUFBSSxDQUFDcEYsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLE9BQVMsSUFBTThGLE1BQU0sRUFBRSxJQUFLO1FBQ3RGLElBQUksQ0FBQzlGLENBQUMsQ0FBQyxFQUFFLEdBQUcsT0FBVSxJQUFNO1FBQzVCK0YsS0FBS1gsR0FBRyxDQUFDLEdBQUcsR0FBRyxPQUFPLENBQUNBLEdBQUcsQ0FBQyxHQUFHLEdBQUcsSUFBRyxLQUFNO1FBQUcsSUFBSSxDQUFDcEYsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLE9BQVEsS0FBTytGLE1BQU8sQ0FBQyxJQUFLO1FBQ3RGQyxLQUFLWixHQUFHLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsR0FBRyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQU07UUFBRyxJQUFJLENBQUNwRixDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsT0FBUSxLQUFPZ0csTUFBTyxDQUFDLElBQUs7UUFDdEZDLEtBQUtiLEdBQUcsQ0FBQyxHQUFHLEdBQUcsT0FBTyxDQUFDQSxHQUFHLENBQUMsR0FBRyxHQUFHLElBQUcsS0FBTTtRQUFHLElBQUksQ0FBQ3BGLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxPQUFTLElBQU1pRyxNQUFPLENBQUMsSUFBSztRQUN0RixJQUFJLENBQUNqRyxDQUFDLENBQUMsRUFBRSxHQUFHLE9BQVUsSUFBTTtRQUU1QixJQUFJLENBQUN1RixHQUFHLENBQUMsRUFBRSxHQUFHSCxHQUFHLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsR0FBRyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQU07UUFDbkQsSUFBSSxDQUFDRyxHQUFHLENBQUMsRUFBRSxHQUFHSCxHQUFHLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsR0FBRyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQU07UUFDbkQsSUFBSSxDQUFDRyxHQUFHLENBQUMsRUFBRSxHQUFHSCxHQUFHLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsR0FBRyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQU07UUFDbkQsSUFBSSxDQUFDRyxHQUFHLENBQUMsRUFBRSxHQUFHSCxHQUFHLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsR0FBRyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQU07UUFDbkQsSUFBSSxDQUFDRyxHQUFHLENBQUMsRUFBRSxHQUFHSCxHQUFHLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsR0FBRyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQU07UUFDbkQsSUFBSSxDQUFDRyxHQUFHLENBQUMsRUFBRSxHQUFHSCxHQUFHLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsR0FBRyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQU07UUFDbkQsSUFBSSxDQUFDRyxHQUFHLENBQUMsRUFBRSxHQUFHSCxHQUFHLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsR0FBRyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQU07UUFDbkQsSUFBSSxDQUFDRyxHQUFHLENBQUMsRUFBRSxHQUFHSCxHQUFHLENBQUMsR0FBRyxHQUFHLE9BQU8sQ0FBQ0EsR0FBRyxDQUFDLEdBQUcsR0FBRyxJQUFHLEtBQU07SUFDckQ7SUFFQUQsU0FBU2UsU0FBUyxDQUFDQyxNQUFNLEdBQUcsU0FBU3pCLENBQUMsRUFBRUMsSUFBSSxFQUFFeUIsS0FBSztRQUNqRCxJQUFJQyxRQUFRLElBQUksQ0FBQ1osR0FBRyxHQUFHLElBQUssS0FBSztRQUNqQyxJQUFJQyxJQUFJQyxJQUFJQyxJQUFJQyxJQUFJQyxJQUFJQyxJQUFJQyxJQUFJQyxJQUFJakU7UUFDcEMsSUFBSXNFLElBQUlDLElBQUlDLElBQUlDLElBQUlDLElBQUlDLElBQUlDLElBQUlDLElBQUlDLElBQUlDO1FBRXhDLElBQUlDLEtBQUssSUFBSSxDQUFDOUYsQ0FBQyxDQUFDLEVBQUUsRUFDZCtGLEtBQUssSUFBSSxDQUFDL0YsQ0FBQyxDQUFDLEVBQUUsRUFDZGdHLEtBQUssSUFBSSxDQUFDaEcsQ0FBQyxDQUFDLEVBQUUsRUFDZGlHLEtBQUssSUFBSSxDQUFDakcsQ0FBQyxDQUFDLEVBQUUsRUFDZGtHLEtBQUssSUFBSSxDQUFDbEcsQ0FBQyxDQUFDLEVBQUUsRUFDZG1HLEtBQUssSUFBSSxDQUFDbkcsQ0FBQyxDQUFDLEVBQUUsRUFDZG9HLEtBQUssSUFBSSxDQUFDcEcsQ0FBQyxDQUFDLEVBQUUsRUFDZHFHLEtBQUssSUFBSSxDQUFDckcsQ0FBQyxDQUFDLEVBQUUsRUFDZHNHLEtBQUssSUFBSSxDQUFDdEcsQ0FBQyxDQUFDLEVBQUUsRUFDZHVHLEtBQUssSUFBSSxDQUFDdkcsQ0FBQyxDQUFDLEVBQUU7UUFFbEIsSUFBSXdHLEtBQUssSUFBSSxDQUFDMUgsQ0FBQyxDQUFDLEVBQUUsRUFDZDJILEtBQUssSUFBSSxDQUFDM0gsQ0FBQyxDQUFDLEVBQUUsRUFDZDRILEtBQUssSUFBSSxDQUFDNUgsQ0FBQyxDQUFDLEVBQUUsRUFDZDZILEtBQUssSUFBSSxDQUFDN0gsQ0FBQyxDQUFDLEVBQUUsRUFDZDhILEtBQUssSUFBSSxDQUFDOUgsQ0FBQyxDQUFDLEVBQUUsRUFDZCtILEtBQUssSUFBSSxDQUFDL0gsQ0FBQyxDQUFDLEVBQUUsRUFDZGdJLEtBQUssSUFBSSxDQUFDaEksQ0FBQyxDQUFDLEVBQUUsRUFDZGlJLEtBQUssSUFBSSxDQUFDakksQ0FBQyxDQUFDLEVBQUUsRUFDZGtJLEtBQUssSUFBSSxDQUFDbEksQ0FBQyxDQUFDLEVBQUUsRUFDZG1JLEtBQUssSUFBSSxDQUFDbkksQ0FBQyxDQUFDLEVBQUU7UUFFbEIsTUFBT29HLFNBQVMsR0FBSTtZQUNsQlYsS0FBS2hCLENBQUMsQ0FBQ0MsT0FBTSxFQUFFLEdBQUcsT0FBTyxDQUFDRCxDQUFDLENBQUNDLE9BQU0sRUFBRSxHQUFHLElBQUcsS0FBTTtZQUFHcUMsTUFBTSxLQUE2QjtZQUN0RnJCLEtBQUtqQixDQUFDLENBQUNDLE9BQU0sRUFBRSxHQUFHLE9BQU8sQ0FBQ0QsQ0FBQyxDQUFDQyxPQUFNLEVBQUUsR0FBRyxJQUFHLEtBQU07WUFBR3NDLE1BQU0sQ0FBQyxPQUFRLEtBQU90QixNQUFPLENBQUMsSUFBSztZQUN0RkMsS0FBS2xCLENBQUMsQ0FBQ0MsT0FBTSxFQUFFLEdBQUcsT0FBTyxDQUFDRCxDQUFDLENBQUNDLE9BQU0sRUFBRSxHQUFHLElBQUcsS0FBTTtZQUFHdUMsTUFBTSxDQUFDLE9BQVEsS0FBT3RCLE1BQU8sQ0FBQyxJQUFLO1lBQ3RGQyxLQUFLbkIsQ0FBQyxDQUFDQyxPQUFNLEVBQUUsR0FBRyxPQUFPLENBQUNELENBQUMsQ0FBQ0MsT0FBTSxFQUFFLEdBQUcsSUFBRyxLQUFNO1lBQUd3QyxNQUFNLENBQUMsT0FBUyxJQUFNdEIsTUFBTyxDQUFDLElBQUs7WUFDdEZDLEtBQUtwQixDQUFDLENBQUNDLE9BQU0sRUFBRSxHQUFHLE9BQU8sQ0FBQ0QsQ0FBQyxDQUFDQyxPQUFNLEVBQUUsR0FBRyxJQUFHLEtBQU07WUFBR3lDLE1BQU0sQ0FBQyxPQUFTLElBQU10QixNQUFNLEVBQUUsSUFBSztZQUN0RnVCLE1BQU0sT0FBVSxJQUFNO1lBQ3RCdEIsS0FBS3JCLENBQUMsQ0FBQ0MsT0FBSyxHQUFHLEdBQUcsT0FBTyxDQUFDRCxDQUFDLENBQUNDLE9BQUssR0FBRyxHQUFHLElBQUcsS0FBTTtZQUFHMkMsTUFBTSxDQUFDLE9BQVEsS0FBT3ZCLE1BQU8sQ0FBQyxJQUFLO1lBQ3RGQyxLQUFLdEIsQ0FBQyxDQUFDQyxPQUFLLEdBQUcsR0FBRyxPQUFPLENBQUNELENBQUMsQ0FBQ0MsT0FBSyxHQUFHLEdBQUcsSUFBRyxLQUFNO1lBQUc0QyxNQUFNLENBQUMsT0FBUSxLQUFPdkIsTUFBTyxDQUFDLElBQUs7WUFDdEZDLEtBQUt2QixDQUFDLENBQUNDLE9BQUssR0FBRyxHQUFHLE9BQU8sQ0FBQ0QsQ0FBQyxDQUFDQyxPQUFLLEdBQUcsR0FBRyxJQUFHLEtBQU07WUFBRzZDLE1BQU0sQ0FBQyxPQUFTLElBQU12QixNQUFPLENBQUMsSUFBSztZQUN0RndCLE1BQU0sT0FBUyxJQUFNcEI7WUFFckJyRSxJQUFJO1lBRUpzRSxLQUFLdEU7WUFDTHNFLE1BQU1VLEtBQUtVO1lBQ1hwQixNQUFNVyxLQUFNLEtBQUlrQixFQUFDO1lBQ2pCN0IsTUFBTVksS0FBTSxLQUFJZ0IsRUFBQztZQUNqQjVCLE1BQU1hLEtBQU0sS0FBSWMsRUFBQztZQUNqQjNCLE1BQU1jLEtBQU0sS0FBSVksRUFBQztZQUNqQmhHLElBQUtzRSxPQUFPO1lBQUtBLE1BQU07WUFDdkJBLE1BQU1lLEtBQU0sS0FBSVUsRUFBQztZQUNqQnpCLE1BQU1nQixLQUFNLEtBQUlRLEVBQUM7WUFDakJ4QixNQUFNaUIsS0FBTSxLQUFJTSxFQUFDO1lBQ2pCdkIsTUFBTWtCLEtBQU0sS0FBSUksRUFBQztZQUNqQnRCLE1BQU1tQixLQUFNLEtBQUlFLEVBQUM7WUFDakIzRixLQUFNc0UsT0FBTztZQUFLQSxNQUFNO1lBRXhCQyxLQUFLdkU7WUFDTHVFLE1BQU1TLEtBQUtXO1lBQ1hwQixNQUFNVSxLQUFLUztZQUNYbkIsTUFBTVcsS0FBTSxLQUFJaUIsRUFBQztZQUNqQjVCLE1BQU1ZLEtBQU0sS0FBSWUsRUFBQztZQUNqQjNCLE1BQU1hLEtBQU0sS0FBSWEsRUFBQztZQUNqQmpHLElBQUt1RSxPQUFPO1lBQUtBLE1BQU07WUFDdkJBLE1BQU1jLEtBQU0sS0FBSVcsRUFBQztZQUNqQnpCLE1BQU1lLEtBQU0sS0FBSVMsRUFBQztZQUNqQnhCLE1BQU1nQixLQUFNLEtBQUlPLEVBQUM7WUFDakJ2QixNQUFNaUIsS0FBTSxLQUFJSyxFQUFDO1lBQ2pCdEIsTUFBTWtCLEtBQU0sS0FBSUcsRUFBQztZQUNqQjVGLEtBQU11RSxPQUFPO1lBQUtBLE1BQU07WUFFeEJDLEtBQUt4RTtZQUNMd0UsTUFBTVEsS0FBS1k7WUFDWHBCLE1BQU1TLEtBQUtVO1lBQ1huQixNQUFNVSxLQUFLUTtZQUNYbEIsTUFBTVcsS0FBTSxLQUFJZ0IsRUFBQztZQUNqQjNCLE1BQU1ZLEtBQU0sS0FBSWMsRUFBQztZQUNqQmxHLElBQUt3RSxPQUFPO1lBQUtBLE1BQU07WUFDdkJBLE1BQU1hLEtBQU0sS0FBSVksRUFBQztZQUNqQnpCLE1BQU1jLEtBQU0sS0FBSVUsRUFBQztZQUNqQnhCLE1BQU1lLEtBQU0sS0FBSVEsRUFBQztZQUNqQnZCLE1BQU1nQixLQUFNLEtBQUlNLEVBQUM7WUFDakJ0QixNQUFNaUIsS0FBTSxLQUFJSSxFQUFDO1lBQ2pCN0YsS0FBTXdFLE9BQU87WUFBS0EsTUFBTTtZQUV4QkMsS0FBS3pFO1lBQ0x5RSxNQUFNTyxLQUFLYTtZQUNYcEIsTUFBTVEsS0FBS1c7WUFDWG5CLE1BQU1TLEtBQUtTO1lBQ1hsQixNQUFNVSxLQUFLTztZQUNYakIsTUFBTVcsS0FBTSxLQUFJZSxFQUFDO1lBQ2pCbkcsSUFBS3lFLE9BQU87WUFBS0EsTUFBTTtZQUN2QkEsTUFBTVksS0FBTSxLQUFJYSxFQUFDO1lBQ2pCekIsTUFBTWEsS0FBTSxLQUFJVyxFQUFDO1lBQ2pCeEIsTUFBTWMsS0FBTSxLQUFJUyxFQUFDO1lBQ2pCdkIsTUFBTWUsS0FBTSxLQUFJTyxFQUFDO1lBQ2pCdEIsTUFBTWdCLEtBQU0sS0FBSUssRUFBQztZQUNqQjlGLEtBQU15RSxPQUFPO1lBQUtBLE1BQU07WUFFeEJDLEtBQUsxRTtZQUNMMEUsTUFBTU0sS0FBS2M7WUFDWHBCLE1BQU1PLEtBQUtZO1lBQ1huQixNQUFNUSxLQUFLVTtZQUNYbEIsTUFBTVMsS0FBS1E7WUFDWGpCLE1BQU1VLEtBQUtNO1lBQ1gxRixJQUFLMEUsT0FBTztZQUFLQSxNQUFNO1lBQ3ZCQSxNQUFNVyxLQUFNLEtBQUljLEVBQUM7WUFDakJ6QixNQUFNWSxLQUFNLEtBQUlZLEVBQUM7WUFDakJ4QixNQUFNYSxLQUFNLEtBQUlVLEVBQUM7WUFDakJ2QixNQUFNYyxLQUFNLEtBQUlRLEVBQUM7WUFDakJ0QixNQUFNZSxLQUFNLEtBQUlNLEVBQUM7WUFDakIvRixLQUFNMEUsT0FBTztZQUFLQSxNQUFNO1lBRXhCQyxLQUFLM0U7WUFDTDJFLE1BQU1LLEtBQUtlO1lBQ1hwQixNQUFNTSxLQUFLYTtZQUNYbkIsTUFBTU8sS0FBS1c7WUFDWGxCLE1BQU1RLEtBQUtTO1lBQ1hqQixNQUFNUyxLQUFLTztZQUNYM0YsSUFBSzJFLE9BQU87WUFBS0EsTUFBTTtZQUN2QkEsTUFBTVUsS0FBS0s7WUFDWGYsTUFBTVcsS0FBTSxLQUFJYSxFQUFDO1lBQ2pCeEIsTUFBTVksS0FBTSxLQUFJVyxFQUFDO1lBQ2pCdkIsTUFBTWEsS0FBTSxLQUFJUyxFQUFDO1lBQ2pCdEIsTUFBTWMsS0FBTSxLQUFJTyxFQUFDO1lBQ2pCaEcsS0FBTTJFLE9BQU87WUFBS0EsTUFBTTtZQUV4QkMsS0FBSzVFO1lBQ0w0RSxNQUFNSSxLQUFLZ0I7WUFDWHBCLE1BQU1LLEtBQUtjO1lBQ1huQixNQUFNTSxLQUFLWTtZQUNYbEIsTUFBTU8sS0FBS1U7WUFDWGpCLE1BQU1RLEtBQUtRO1lBQ1g1RixJQUFLNEUsT0FBTztZQUFLQSxNQUFNO1lBQ3ZCQSxNQUFNUyxLQUFLTTtZQUNYZixNQUFNVSxLQUFLSTtZQUNYZCxNQUFNVyxLQUFNLEtBQUlZLEVBQUM7WUFDakJ2QixNQUFNWSxLQUFNLEtBQUlVLEVBQUM7WUFDakJ0QixNQUFNYSxLQUFNLEtBQUlRLEVBQUM7WUFDakJqRyxLQUFNNEUsT0FBTztZQUFLQSxNQUFNO1lBRXhCQyxLQUFLN0U7WUFDTDZFLE1BQU1HLEtBQUtpQjtZQUNYcEIsTUFBTUksS0FBS2U7WUFDWG5CLE1BQU1LLEtBQUthO1lBQ1hsQixNQUFNTSxLQUFLVztZQUNYakIsTUFBTU8sS0FBS1M7WUFDWDdGLElBQUs2RSxPQUFPO1lBQUtBLE1BQU07WUFDdkJBLE1BQU1RLEtBQUtPO1lBQ1hmLE1BQU1TLEtBQUtLO1lBQ1hkLE1BQU1VLEtBQUtHO1lBQ1hiLE1BQU1XLEtBQU0sS0FBSVcsRUFBQztZQUNqQnRCLE1BQU1ZLEtBQU0sS0FBSVMsRUFBQztZQUNqQmxHLEtBQU02RSxPQUFPO1lBQUtBLE1BQU07WUFFeEJDLEtBQUs5RTtZQUNMOEUsTUFBTUUsS0FBS2tCO1lBQ1hwQixNQUFNRyxLQUFLZ0I7WUFDWG5CLE1BQU1JLEtBQUtjO1lBQ1hsQixNQUFNSyxLQUFLWTtZQUNYakIsTUFBTU0sS0FBS1U7WUFDWDlGLElBQUs4RSxPQUFPO1lBQUtBLE1BQU07WUFDdkJBLE1BQU1PLEtBQUtRO1lBQ1hmLE1BQU1RLEtBQUtNO1lBQ1hkLE1BQU1TLEtBQUtJO1lBQ1hiLE1BQU1VLEtBQUtFO1lBQ1haLE1BQU1XLEtBQU0sS0FBSVUsRUFBQztZQUNqQm5HLEtBQU04RSxPQUFPO1lBQUtBLE1BQU07WUFFeEJDLEtBQUsvRTtZQUNMK0UsTUFBTUMsS0FBS21CO1lBQ1hwQixNQUFNRSxLQUFLaUI7WUFDWG5CLE1BQU1HLEtBQUtlO1lBQ1hsQixNQUFNSSxLQUFLYTtZQUNYakIsTUFBTUssS0FBS1c7WUFDWC9GLElBQUsrRSxPQUFPO1lBQUtBLE1BQU07WUFDdkJBLE1BQU1NLEtBQUtTO1lBQ1hmLE1BQU1PLEtBQUtPO1lBQ1hkLE1BQU1RLEtBQUtLO1lBQ1hiLE1BQU1TLEtBQUtHO1lBQ1haLE1BQU1VLEtBQUtDO1lBQ1gxRixLQUFNK0UsT0FBTztZQUFLQSxNQUFNO1lBRXhCL0UsSUFBSSxDQUFHQSxLQUFLLEtBQUtBLElBQU07WUFDdkJBLElBQUksSUFBS3NFLEtBQU07WUFDZkEsS0FBS3RFLElBQUk7WUFDVEEsSUFBS0EsTUFBTTtZQUNYdUUsTUFBTXZFO1lBRU5nRixLQUFLVjtZQUNMVyxLQUFLVjtZQUNMVyxLQUFLVjtZQUNMVyxLQUFLVjtZQUNMVyxLQUFLVjtZQUNMVyxLQUFLVjtZQUNMVyxLQUFLVjtZQUNMVyxLQUFLVjtZQUNMVyxLQUFLVjtZQUNMVyxLQUFLVjtZQUVMcEMsUUFBUTtZQUNSeUIsU0FBUztRQUNYO1FBQ0EsSUFBSSxDQUFDbEYsQ0FBQyxDQUFDLEVBQUUsR0FBRzhGO1FBQ1osSUFBSSxDQUFDOUYsQ0FBQyxDQUFDLEVBQUUsR0FBRytGO1FBQ1osSUFBSSxDQUFDL0YsQ0FBQyxDQUFDLEVBQUUsR0FBR2dHO1FBQ1osSUFBSSxDQUFDaEcsQ0FBQyxDQUFDLEVBQUUsR0FBR2lHO1FBQ1osSUFBSSxDQUFDakcsQ0FBQyxDQUFDLEVBQUUsR0FBR2tHO1FBQ1osSUFBSSxDQUFDbEcsQ0FBQyxDQUFDLEVBQUUsR0FBR21HO1FBQ1osSUFBSSxDQUFDbkcsQ0FBQyxDQUFDLEVBQUUsR0FBR29HO1FBQ1osSUFBSSxDQUFDcEcsQ0FBQyxDQUFDLEVBQUUsR0FBR3FHO1FBQ1osSUFBSSxDQUFDckcsQ0FBQyxDQUFDLEVBQUUsR0FBR3NHO1FBQ1osSUFBSSxDQUFDdEcsQ0FBQyxDQUFDLEVBQUUsR0FBR3VHO0lBQ2Q7SUFFQXRDLFNBQVNlLFNBQVMsQ0FBQ2tDLE1BQU0sR0FBRyxTQUFTQyxHQUFHLEVBQUVDLE1BQU07UUFDOUMsSUFBSUMsSUFBSSxJQUFJakQsWUFBWTtRQUN4QixJQUFJdEQsR0FBR3dHLE1BQU1DLEdBQUcxSTtRQUVoQixJQUFJLElBQUksQ0FBQ3lGLFFBQVEsRUFBRTtZQUNqQnpGLElBQUksSUFBSSxDQUFDeUYsUUFBUTtZQUNqQixJQUFJLENBQUNILE1BQU0sQ0FBQ3RGLElBQUksR0FBRztZQUNuQixNQUFPQSxJQUFJLElBQUlBLElBQUssSUFBSSxDQUFDc0YsTUFBTSxDQUFDdEYsRUFBRSxHQUFHO1lBQ3JDLElBQUksQ0FBQzBGLEdBQUcsR0FBRztZQUNYLElBQUksQ0FBQ1UsTUFBTSxDQUFDLElBQUksQ0FBQ2QsTUFBTSxFQUFFLEdBQUc7UUFDOUI7UUFFQXJELElBQUksSUFBSSxDQUFDZCxDQUFDLENBQUMsRUFBRSxLQUFLO1FBQ2xCLElBQUksQ0FBQ0EsQ0FBQyxDQUFDLEVBQUUsSUFBSTtRQUNiLElBQUtuQixJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBSztZQUN2QixJQUFJLENBQUNtQixDQUFDLENBQUNuQixFQUFFLElBQUlpQztZQUNiQSxJQUFJLElBQUksQ0FBQ2QsQ0FBQyxDQUFDbkIsRUFBRSxLQUFLO1lBQ2xCLElBQUksQ0FBQ21CLENBQUMsQ0FBQ25CLEVBQUUsSUFBSTtRQUNmO1FBQ0EsSUFBSSxDQUFDbUIsQ0FBQyxDQUFDLEVBQUUsSUFBS2MsSUFBSTtRQUNsQkEsSUFBSSxJQUFJLENBQUNkLENBQUMsQ0FBQyxFQUFFLEtBQUs7UUFDbEIsSUFBSSxDQUFDQSxDQUFDLENBQUMsRUFBRSxJQUFJO1FBQ2IsSUFBSSxDQUFDQSxDQUFDLENBQUMsRUFBRSxJQUFJYztRQUNiQSxJQUFJLElBQUksQ0FBQ2QsQ0FBQyxDQUFDLEVBQUUsS0FBSztRQUNsQixJQUFJLENBQUNBLENBQUMsQ0FBQyxFQUFFLElBQUk7UUFDYixJQUFJLENBQUNBLENBQUMsQ0FBQyxFQUFFLElBQUljO1FBRWJ1RyxDQUFDLENBQUMsRUFBRSxHQUFHLElBQUksQ0FBQ3JILENBQUMsQ0FBQyxFQUFFLEdBQUc7UUFDbkJjLElBQUl1RyxDQUFDLENBQUMsRUFBRSxLQUFLO1FBQ2JBLENBQUMsQ0FBQyxFQUFFLElBQUk7UUFDUixJQUFLeEksSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs7WUFDdkJ3SSxDQUFDLENBQUN4SSxFQUFFLEdBQUcsSUFBSSxDQUFDbUIsQ0FBQyxDQUFDbkIsRUFBRSxHQUFHaUM7WUFDbkJBLElBQUl1RyxDQUFDLENBQUN4SSxFQUFFLEtBQUs7WUFDYndJLENBQUMsQ0FBQ3hJLEVBQUUsSUFBSTtRQUNWO1FBQ0F3SSxDQUFDLENBQUMsRUFBRSxJQUFLLEtBQUs7UUFFZEMsT0FBTyxDQUFDeEcsSUFBSSxLQUFLO1FBQ2pCLElBQUtqQyxJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBS3dJLENBQUMsQ0FBQ3hJLEVBQUUsSUFBSXlJO1FBQ2pDQSxPQUFPLENBQUNBO1FBQ1IsSUFBS3pJLElBQUksR0FBR0EsSUFBSSxJQUFJQSxJQUFLLElBQUksQ0FBQ21CLENBQUMsQ0FBQ25CLEVBQUUsR0FBRyxJQUFLLENBQUNtQixDQUFDLENBQUNuQixFQUFFLEdBQUd5SSxPQUFRRCxDQUFDLENBQUN4SSxFQUFFO1FBRTlELElBQUksQ0FBQ21CLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxJQUFLLENBQUNBLENBQUMsQ0FBQyxFQUFFLEdBQVksSUFBSSxDQUFDQSxDQUFDLENBQUMsRUFBRSxJQUFJLEVBQUUsSUFBeUI7UUFDM0UsSUFBSSxDQUFDQSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsSUFBSyxDQUFDQSxDQUFDLENBQUMsRUFBRSxLQUFNLElBQU0sSUFBSSxDQUFDQSxDQUFDLENBQUMsRUFBRSxJQUFJLEVBQUUsSUFBeUI7UUFDM0UsSUFBSSxDQUFDQSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsSUFBSyxDQUFDQSxDQUFDLENBQUMsRUFBRSxLQUFNLElBQU0sSUFBSSxDQUFDQSxDQUFDLENBQUMsRUFBRSxJQUFLLENBQUMsSUFBeUI7UUFDM0UsSUFBSSxDQUFDQSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsSUFBSyxDQUFDQSxDQUFDLENBQUMsRUFBRSxLQUFNLElBQU0sSUFBSSxDQUFDQSxDQUFDLENBQUMsRUFBRSxJQUFLLENBQUMsSUFBeUI7UUFDM0UsSUFBSSxDQUFDQSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsSUFBSyxDQUFDQSxDQUFDLENBQUMsRUFBRSxLQUFLLEtBQU8sSUFBSSxDQUFDQSxDQUFDLENBQUMsRUFBRSxJQUFLLElBQU0sSUFBSSxDQUFDQSxDQUFDLENBQUMsRUFBRSxJQUFJLEVBQUUsSUFBSztRQUMzRSxJQUFJLENBQUNBLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxJQUFLLENBQUNBLENBQUMsQ0FBQyxFQUFFLEtBQU0sSUFBTSxJQUFJLENBQUNBLENBQUMsQ0FBQyxFQUFFLElBQUksRUFBRSxJQUF5QjtRQUMzRSxJQUFJLENBQUNBLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxJQUFLLENBQUNBLENBQUMsQ0FBQyxFQUFFLEtBQU0sSUFBTSxJQUFJLENBQUNBLENBQUMsQ0FBQyxFQUFFLElBQUssQ0FBQyxJQUF5QjtRQUMzRSxJQUFJLENBQUNBLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxJQUFLLENBQUNBLENBQUMsQ0FBQyxFQUFFLEtBQU0sSUFBTSxJQUFJLENBQUNBLENBQUMsQ0FBQyxFQUFFLElBQUssQ0FBQyxJQUF5QjtRQUUzRXVILElBQUksSUFBSSxDQUFDdkgsQ0FBQyxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUNxRSxHQUFHLENBQUMsRUFBRTtRQUMzQixJQUFJLENBQUNyRSxDQUFDLENBQUMsRUFBRSxHQUFHdUgsSUFBSTtRQUNoQixJQUFLMUksSUFBSSxHQUFHQSxJQUFJLEdBQUdBLElBQUs7WUFDdEIwSSxJQUFJLENBQUUsSUFBSyxDQUFDdkgsQ0FBQyxDQUFDbkIsRUFBRSxHQUFHLElBQUksQ0FBQ3dGLEdBQUcsQ0FBQ3hGLEVBQUUsR0FBSSxLQUFNMEksQ0FBQUEsTUFBTSxFQUFDLElBQU07WUFDckQsSUFBSSxDQUFDdkgsQ0FBQyxDQUFDbkIsRUFBRSxHQUFHMEksSUFBSTtRQUNsQjtRQUVBSixHQUFHLENBQUNDLFNBQVEsRUFBRSxHQUFHLElBQUssQ0FBQ3BILENBQUMsQ0FBQyxFQUFFLEtBQUssSUFBSztRQUNyQ21ILEdBQUcsQ0FBQ0MsU0FBUSxFQUFFLEdBQUcsSUFBSyxDQUFDcEgsQ0FBQyxDQUFDLEVBQUUsS0FBSyxJQUFLO1FBQ3JDbUgsR0FBRyxDQUFDQyxTQUFRLEVBQUUsR0FBRyxJQUFLLENBQUNwSCxDQUFDLENBQUMsRUFBRSxLQUFLLElBQUs7UUFDckNtSCxHQUFHLENBQUNDLFNBQVEsRUFBRSxHQUFHLElBQUssQ0FBQ3BILENBQUMsQ0FBQyxFQUFFLEtBQUssSUFBSztRQUNyQ21ILEdBQUcsQ0FBQ0MsU0FBUSxFQUFFLEdBQUcsSUFBSyxDQUFDcEgsQ0FBQyxDQUFDLEVBQUUsS0FBSyxJQUFLO1FBQ3JDbUgsR0FBRyxDQUFDQyxTQUFRLEVBQUUsR0FBRyxJQUFLLENBQUNwSCxDQUFDLENBQUMsRUFBRSxLQUFLLElBQUs7UUFDckNtSCxHQUFHLENBQUNDLFNBQVEsRUFBRSxHQUFHLElBQUssQ0FBQ3BILENBQUMsQ0FBQyxFQUFFLEtBQUssSUFBSztRQUNyQ21ILEdBQUcsQ0FBQ0MsU0FBUSxFQUFFLEdBQUcsSUFBSyxDQUFDcEgsQ0FBQyxDQUFDLEVBQUUsS0FBSyxJQUFLO1FBQ3JDbUgsR0FBRyxDQUFDQyxTQUFRLEVBQUUsR0FBRyxJQUFLLENBQUNwSCxDQUFDLENBQUMsRUFBRSxLQUFLLElBQUs7UUFDckNtSCxHQUFHLENBQUNDLFNBQVEsRUFBRSxHQUFHLElBQUssQ0FBQ3BILENBQUMsQ0FBQyxFQUFFLEtBQUssSUFBSztRQUNyQ21ILEdBQUcsQ0FBQ0MsU0FBTyxHQUFHLEdBQUcsSUFBSyxDQUFDcEgsQ0FBQyxDQUFDLEVBQUUsS0FBSyxJQUFLO1FBQ3JDbUgsR0FBRyxDQUFDQyxTQUFPLEdBQUcsR0FBRyxJQUFLLENBQUNwSCxDQUFDLENBQUMsRUFBRSxLQUFLLElBQUs7UUFDckNtSCxHQUFHLENBQUNDLFNBQU8sR0FBRyxHQUFHLElBQUssQ0FBQ3BILENBQUMsQ0FBQyxFQUFFLEtBQUssSUFBSztRQUNyQ21ILEdBQUcsQ0FBQ0MsU0FBTyxHQUFHLEdBQUcsSUFBSyxDQUFDcEgsQ0FBQyxDQUFDLEVBQUUsS0FBSyxJQUFLO1FBQ3JDbUgsR0FBRyxDQUFDQyxTQUFPLEdBQUcsR0FBRyxJQUFLLENBQUNwSCxDQUFDLENBQUMsRUFBRSxLQUFLLElBQUs7UUFDckNtSCxHQUFHLENBQUNDLFNBQU8sR0FBRyxHQUFHLElBQUssQ0FBQ3BILENBQUMsQ0FBQyxFQUFFLEtBQUssSUFBSztJQUN2QztJQUVBaUUsU0FBU2UsU0FBUyxDQUFDd0MsTUFBTSxHQUFHLFNBQVNoRSxDQUFDLEVBQUVDLElBQUksRUFBRXlCLEtBQUs7UUFDakQsSUFBSXJHLEdBQUc0STtRQUVQLElBQUksSUFBSSxDQUFDbkQsUUFBUSxFQUFFO1lBQ2pCbUQsT0FBUSxLQUFLLElBQUksQ0FBQ25ELFFBQVE7WUFDMUIsSUFBSW1ELE9BQU92QyxPQUNUdUMsT0FBT3ZDO1lBQ1QsSUFBS3JHLElBQUksR0FBR0EsSUFBSTRJLE1BQU01SSxJQUNwQixJQUFJLENBQUNzRixNQUFNLENBQUMsSUFBSSxDQUFDRyxRQUFRLEdBQUd6RixFQUFFLEdBQUcyRSxDQUFDLENBQUNDLE9BQUs1RSxFQUFFO1lBQzVDcUcsU0FBU3VDO1lBQ1RoRSxRQUFRZ0U7WUFDUixJQUFJLENBQUNuRCxRQUFRLElBQUltRDtZQUNqQixJQUFJLElBQUksQ0FBQ25ELFFBQVEsR0FBRyxJQUNsQjtZQUNGLElBQUksQ0FBQ1csTUFBTSxDQUFDLElBQUksQ0FBQ2QsTUFBTSxFQUFFLEdBQUc7WUFDNUIsSUFBSSxDQUFDRyxRQUFRLEdBQUc7UUFDbEI7UUFFQSxJQUFJWSxTQUFTLElBQUk7WUFDZnVDLE9BQU92QyxRQUFTQSxRQUFRO1lBQ3hCLElBQUksQ0FBQ0QsTUFBTSxDQUFDekIsR0FBR0MsTUFBTWdFO1lBQ3JCaEUsUUFBUWdFO1lBQ1J2QyxTQUFTdUM7UUFDWDtRQUVBLElBQUl2QyxPQUFPO1lBQ1QsSUFBS3JHLElBQUksR0FBR0EsSUFBSXFHLE9BQU9yRyxJQUNyQixJQUFJLENBQUNzRixNQUFNLENBQUMsSUFBSSxDQUFDRyxRQUFRLEdBQUd6RixFQUFFLEdBQUcyRSxDQUFDLENBQUNDLE9BQUs1RSxFQUFFO1lBQzVDLElBQUksQ0FBQ3lGLFFBQVEsSUFBSVk7UUFDbkI7SUFDRjtJQUVBLFNBQVN3QyxtQkFBbUJ4RSxHQUFHLEVBQUV5RSxNQUFNLEVBQUVuRSxDQUFDLEVBQUVDLElBQUksRUFBRW5ELENBQUMsRUFBRU8sQ0FBQztRQUNwRCxJQUFJaUQsSUFBSSxJQUFJRyxTQUFTcEQ7UUFDckJpRCxFQUFFMEQsTUFBTSxDQUFDaEUsR0FBR0MsTUFBTW5EO1FBQ2xCd0QsRUFBRW9ELE1BQU0sQ0FBQ2hFLEtBQUt5RTtRQUNkLE9BQU87SUFDVDtJQUVBLFNBQVNDLDBCQUEwQjVILENBQUMsRUFBRTZILElBQUksRUFBRXJFLENBQUMsRUFBRUMsSUFBSSxFQUFFbkQsQ0FBQyxFQUFFTyxDQUFDO1FBQ3ZELElBQUlkLElBQUksSUFBSVgsV0FBVztRQUN2QnNJLG1CQUFtQjNILEdBQUUsR0FBRXlELEdBQUVDLE1BQUtuRCxHQUFFTztRQUNoQyxPQUFPTCxpQkFBaUJSLEdBQUU2SCxNQUFLOUgsR0FBRTtJQUNuQztJQUVBLFNBQVMrSCxpQkFBaUJoSCxDQUFDLEVBQUMwQyxDQUFDLEVBQUNqRCxDQUFDLEVBQUNELENBQUMsRUFBQ08sQ0FBQztRQUNqQyxJQUFJaEM7UUFDSixJQUFJMEIsSUFBSSxJQUFJLE9BQU8sQ0FBQztRQUNwQnlELGtCQUFrQmxELEdBQUUsR0FBRTBDLEdBQUUsR0FBRWpELEdBQUVELEdBQUVPO1FBQzlCNkcsbUJBQW1CNUcsR0FBRyxJQUFJQSxHQUFHLElBQUlQLElBQUksSUFBSU87UUFDekMsSUFBS2pDLElBQUksR0FBR0EsSUFBSSxJQUFJQSxJQUFLaUMsQ0FBQyxDQUFDakMsRUFBRSxHQUFHO1FBQ2hDLE9BQU87SUFDVDtJQUVBLFNBQVNrSixzQkFBc0J2RSxDQUFDLEVBQUMxQyxDQUFDLEVBQUNQLENBQUMsRUFBQ0QsQ0FBQyxFQUFDTyxDQUFDO1FBQ3RDLElBQUloQztRQUNKLElBQUlrQixJQUFJLElBQUlYLFdBQVc7UUFDdkIsSUFBSW1CLElBQUksSUFBSSxPQUFPLENBQUM7UUFDcEJzRCxjQUFjOUQsR0FBRSxHQUFFLElBQUdPLEdBQUVPO1FBQ3ZCLElBQUkrRywwQkFBMEI5RyxHQUFHLElBQUdBLEdBQUcsSUFBR1AsSUFBSSxJQUFHUixPQUFPLEdBQUcsT0FBTyxDQUFDO1FBQ25FaUUsa0JBQWtCUixHQUFFLEdBQUUxQyxHQUFFLEdBQUVQLEdBQUVELEdBQUVPO1FBQzlCLElBQUtoQyxJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBSzJFLENBQUMsQ0FBQzNFLEVBQUUsR0FBRztRQUNoQyxPQUFPO0lBQ1Q7SUFFQSxTQUFTbUosU0FBU2xKLENBQUMsRUFBRW1KLENBQUM7UUFDcEIsSUFBSXBKO1FBQ0osSUFBS0EsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUtDLENBQUMsQ0FBQ0QsRUFBRSxHQUFHb0osQ0FBQyxDQUFDcEosRUFBRSxHQUFDO0lBQ3ZDO0lBRUEsU0FBU3FKLFNBQVN2SCxDQUFDO1FBQ2pCLElBQUk5QixHQUFHc0osR0FBR3JILElBQUk7UUFDZCxJQUFLakMsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs7WUFDdkJzSixJQUFJeEgsQ0FBQyxDQUFDOUIsRUFBRSxHQUFHaUMsSUFBSTtZQUNmQSxJQUFJc0gsS0FBS0MsS0FBSyxDQUFDRixJQUFJO1lBQ25CeEgsQ0FBQyxDQUFDOUIsRUFBRSxHQUFHc0osSUFBSXJILElBQUk7UUFDakI7UUFDQUgsQ0FBQyxDQUFDLEVBQUUsSUFBSUcsSUFBRSxJQUFJLEtBQU1BLENBQUFBLElBQUU7SUFDeEI7SUFFQSxTQUFTd0gsU0FBUzFILENBQUMsRUFBRTJILENBQUMsRUFBRTdFLENBQUM7UUFDdkIsSUFBSThFLEdBQUcxSCxJQUFJLENBQUU0QyxDQUFBQSxJQUFFO1FBQ2YsSUFBSyxJQUFJN0UsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs7WUFDM0IySixJQUFJMUgsSUFBS0YsQ0FBQUEsQ0FBQyxDQUFDL0IsRUFBRSxHQUFHMEosQ0FBQyxDQUFDMUosRUFBRTtZQUNwQitCLENBQUMsQ0FBQy9CLEVBQUUsSUFBSTJKO1lBQ1JELENBQUMsQ0FBQzFKLEVBQUUsSUFBSTJKO1FBQ1Y7SUFDRjtJQUVBLFNBQVNDLFVBQVU5SCxDQUFDLEVBQUVMLENBQUM7UUFDckIsSUFBSXpCLEdBQUc2SixHQUFHaEY7UUFDVixJQUFJRixJQUFJN0UsTUFBTTZKLElBQUk3SjtRQUNsQixJQUFLRSxJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBSzJKLENBQUMsQ0FBQzNKLEVBQUUsR0FBR3lCLENBQUMsQ0FBQ3pCLEVBQUU7UUFDcENxSixTQUFTTTtRQUNUTixTQUFTTTtRQUNUTixTQUFTTTtRQUNULElBQUtFLElBQUksR0FBR0EsSUFBSSxHQUFHQSxJQUFLO1lBQ3RCbEYsQ0FBQyxDQUFDLEVBQUUsR0FBR2dGLENBQUMsQ0FBQyxFQUFFLEdBQUc7WUFDZCxJQUFLM0osSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs7Z0JBQ3ZCMkUsQ0FBQyxDQUFDM0UsRUFBRSxHQUFHMkosQ0FBQyxDQUFDM0osRUFBRSxHQUFHLFNBQVUsRUFBRSxDQUFDQSxJQUFFLEVBQUUsSUFBRSxLQUFNO2dCQUN2QzJFLENBQUMsQ0FBQzNFLElBQUUsRUFBRSxJQUFJO1lBQ1o7WUFDQTJFLENBQUMsQ0FBQyxHQUFHLEdBQUdnRixDQUFDLENBQUMsR0FBRyxHQUFHLFNBQVUsRUFBRSxDQUFDLEdBQUcsSUFBRSxLQUFNO1lBQ3hDOUUsSUFBSSxDQUFFLENBQUMsR0FBRyxJQUFFLEtBQU07WUFDbEJGLENBQUMsQ0FBQyxHQUFHLElBQUk7WUFDVDhFLFNBQVNFLEdBQUdoRixHQUFHLElBQUVFO1FBQ25CO1FBQ0EsSUFBSzdFLElBQUksR0FBR0EsSUFBSSxJQUFJQSxJQUFLO1lBQ3ZCOEIsQ0FBQyxDQUFDLElBQUU5QixFQUFFLEdBQUcySixDQUFDLENBQUMzSixFQUFFLEdBQUc7WUFDaEI4QixDQUFDLENBQUMsSUFBRTlCLElBQUUsRUFBRSxHQUFHMkosQ0FBQyxDQUFDM0osRUFBRSxJQUFFO1FBQ25CO0lBQ0Y7SUFFQSxTQUFTOEosU0FBU1YsQ0FBQyxFQUFFdkUsQ0FBQztRQUNwQixJQUFJNUMsSUFBSSxJQUFJMUIsV0FBVyxLQUFLbUIsSUFBSSxJQUFJbkIsV0FBVztRQUMvQ3FKLFVBQVUzSCxHQUFHbUg7UUFDYlEsVUFBVWxJLEdBQUdtRDtRQUNiLE9BQU9qRCxpQkFBaUJLLEdBQUcsR0FBR1AsR0FBRztJQUNuQztJQUVBLFNBQVNxSSxTQUFTWCxDQUFDO1FBQ2pCLElBQUkxSCxJQUFJLElBQUluQixXQUFXO1FBQ3ZCcUosVUFBVWxJLEdBQUcwSDtRQUNiLE9BQU8xSCxDQUFDLENBQUMsRUFBRSxHQUFHO0lBQ2hCO0lBRUEsU0FBU3NJLFlBQVlsSSxDQUFDLEVBQUVMLENBQUM7UUFDdkIsSUFBSXpCO1FBQ0osSUFBS0EsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs4QixDQUFDLENBQUM5QixFQUFFLEdBQUd5QixDQUFDLENBQUMsSUFBRXpCLEVBQUUsR0FBSXlCLENBQUFBLENBQUMsQ0FBQyxJQUFFekIsSUFBRSxFQUFFLElBQUk7UUFDdEQ4QixDQUFDLENBQUMsR0FBRyxJQUFJO0lBQ1g7SUFFQSxTQUFTbUksRUFBRW5JLENBQUMsRUFBRXNILENBQUMsRUFBRXZFLENBQUM7UUFDaEIsSUFBSyxJQUFJN0UsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs4QixDQUFDLENBQUM5QixFQUFFLEdBQUdvSixDQUFDLENBQUNwSixFQUFFLEdBQUc2RSxDQUFDLENBQUM3RSxFQUFFO0lBQ2pEO0lBRUEsU0FBU2tLLEVBQUVwSSxDQUFDLEVBQUVzSCxDQUFDLEVBQUV2RSxDQUFDO1FBQ2hCLElBQUssSUFBSTdFLElBQUksR0FBR0EsSUFBSSxJQUFJQSxJQUFLOEIsQ0FBQyxDQUFDOUIsRUFBRSxHQUFHb0osQ0FBQyxDQUFDcEosRUFBRSxHQUFHNkUsQ0FBQyxDQUFDN0UsRUFBRTtJQUNqRDtJQUVBLFNBQVNtSyxFQUFFckksQ0FBQyxFQUFFc0gsQ0FBQyxFQUFFdkUsQ0FBQztRQUNoQixJQUFJeUUsR0FBR3JILEdBQ0owRCxLQUFLLEdBQUlDLEtBQUssR0FBSUMsS0FBSyxHQUFJQyxLQUFLLEdBQUlDLEtBQUssR0FBSUMsS0FBSyxHQUFJQyxLQUFLLEdBQUlDLEtBQUssR0FDcEVrRSxLQUFLLEdBQUlDLEtBQUssR0FBR0MsTUFBTSxHQUFHQyxNQUFNLEdBQUdDLE1BQU0sR0FBR0MsTUFBTSxHQUFHQyxNQUFNLEdBQUdDLE1BQU0sR0FDckVDLE1BQU0sR0FBR0MsTUFBTSxHQUFHQyxNQUFNLEdBQUdDLE1BQU0sR0FBR0MsTUFBTSxHQUFHQyxNQUFNLEdBQUdDLE1BQU0sR0FBR0MsTUFBTSxHQUNyRUMsTUFBTSxHQUFHQyxNQUFNLEdBQUdDLE1BQU0sR0FBR0MsTUFBTSxHQUFHQyxNQUFNLEdBQUdDLE1BQU0sR0FBR0MsTUFBTSxHQUM1REMsS0FBSzlHLENBQUMsQ0FBQyxFQUFFLEVBQ1QrRyxLQUFLL0csQ0FBQyxDQUFDLEVBQUUsRUFDVGdILEtBQUtoSCxDQUFDLENBQUMsRUFBRSxFQUNUaUgsS0FBS2pILENBQUMsQ0FBQyxFQUFFLEVBQ1RrSCxLQUFLbEgsQ0FBQyxDQUFDLEVBQUUsRUFDVG1ILEtBQUtuSCxDQUFDLENBQUMsRUFBRSxFQUNUb0gsS0FBS3BILENBQUMsQ0FBQyxFQUFFLEVBQ1RxSCxLQUFLckgsQ0FBQyxDQUFDLEVBQUUsRUFDVHNILEtBQUt0SCxDQUFDLENBQUMsRUFBRSxFQUNUdUgsS0FBS3ZILENBQUMsQ0FBQyxFQUFFLEVBQ1R3SCxNQUFNeEgsQ0FBQyxDQUFDLEdBQUcsRUFDWHlILE1BQU16SCxDQUFDLENBQUMsR0FBRyxFQUNYMEgsTUFBTTFILENBQUMsQ0FBQyxHQUFHLEVBQ1gySCxNQUFNM0gsQ0FBQyxDQUFDLEdBQUcsRUFDWDRILE1BQU01SCxDQUFDLENBQUMsR0FBRyxFQUNYNkgsTUFBTTdILENBQUMsQ0FBQyxHQUFHO1FBRWJ5RSxJQUFJRixDQUFDLENBQUMsRUFBRTtRQUNSekQsTUFBTTJELElBQUlxQztRQUNWL0YsTUFBTTBELElBQUlzQztRQUNWL0YsTUFBTXlELElBQUl1QztRQUNWL0YsTUFBTXdELElBQUl3QztRQUNWL0YsTUFBTXVELElBQUl5QztRQUNWL0YsTUFBTXNELElBQUkwQztRQUNWL0YsTUFBTXFELElBQUkyQztRQUNWL0YsTUFBTW9ELElBQUk0QztRQUNWOUIsTUFBTWQsSUFBSTZDO1FBQ1Y5QixNQUFNZixJQUFJOEM7UUFDVjlCLE9BQU9oQixJQUFJK0M7UUFDWDlCLE9BQU9qQixJQUFJZ0Q7UUFDWDlCLE9BQU9sQixJQUFJaUQ7UUFDWDlCLE9BQU9uQixJQUFJa0Q7UUFDWDlCLE9BQU9wQixJQUFJbUQ7UUFDWDlCLE9BQU9yQixJQUFJb0Q7UUFDWHBELElBQUlGLENBQUMsQ0FBQyxFQUFFO1FBQ1J4RCxNQUFNMEQsSUFBSXFDO1FBQ1Y5RixNQUFNeUQsSUFBSXNDO1FBQ1Y5RixNQUFNd0QsSUFBSXVDO1FBQ1Y5RixNQUFNdUQsSUFBSXdDO1FBQ1Y5RixNQUFNc0QsSUFBSXlDO1FBQ1Y5RixNQUFNcUQsSUFBSTBDO1FBQ1Y5RixNQUFNb0QsSUFBSTJDO1FBQ1Y3QixNQUFNZCxJQUFJNEM7UUFDVjdCLE1BQU1mLElBQUk2QztRQUNWN0IsT0FBT2hCLElBQUk4QztRQUNYN0IsT0FBT2pCLElBQUkrQztRQUNYN0IsT0FBT2xCLElBQUlnRDtRQUNYN0IsT0FBT25CLElBQUlpRDtRQUNYN0IsT0FBT3BCLElBQUlrRDtRQUNYN0IsT0FBT3JCLElBQUltRDtRQUNYN0IsT0FBT3RCLElBQUlvRDtRQUNYcEQsSUFBSUYsQ0FBQyxDQUFDLEVBQUU7UUFDUnZELE1BQU15RCxJQUFJcUM7UUFDVjdGLE1BQU13RCxJQUFJc0M7UUFDVjdGLE1BQU11RCxJQUFJdUM7UUFDVjdGLE1BQU1zRCxJQUFJd0M7UUFDVjdGLE1BQU1xRCxJQUFJeUM7UUFDVjdGLE1BQU1vRCxJQUFJMEM7UUFDVjVCLE1BQU1kLElBQUkyQztRQUNWNUIsTUFBTWYsSUFBSTRDO1FBQ1Y1QixPQUFPaEIsSUFBSTZDO1FBQ1g1QixPQUFPakIsSUFBSThDO1FBQ1g1QixPQUFPbEIsSUFBSStDO1FBQ1g1QixPQUFPbkIsSUFBSWdEO1FBQ1g1QixPQUFPcEIsSUFBSWlEO1FBQ1g1QixPQUFPckIsSUFBSWtEO1FBQ1g1QixPQUFPdEIsSUFBSW1EO1FBQ1g1QixPQUFPdkIsSUFBSW9EO1FBQ1hwRCxJQUFJRixDQUFDLENBQUMsRUFBRTtRQUNSdEQsTUFBTXdELElBQUlxQztRQUNWNUYsTUFBTXVELElBQUlzQztRQUNWNUYsTUFBTXNELElBQUl1QztRQUNWNUYsTUFBTXFELElBQUl3QztRQUNWNUYsTUFBTW9ELElBQUl5QztRQUNWM0IsTUFBTWQsSUFBSTBDO1FBQ1YzQixNQUFNZixJQUFJMkM7UUFDVjNCLE9BQU9oQixJQUFJNEM7UUFDWDNCLE9BQU9qQixJQUFJNkM7UUFDWDNCLE9BQU9sQixJQUFJOEM7UUFDWDNCLE9BQU9uQixJQUFJK0M7UUFDWDNCLE9BQU9wQixJQUFJZ0Q7UUFDWDNCLE9BQU9yQixJQUFJaUQ7UUFDWDNCLE9BQU90QixJQUFJa0Q7UUFDWDNCLE9BQU92QixJQUFJbUQ7UUFDWDNCLE9BQU94QixJQUFJb0Q7UUFDWHBELElBQUlGLENBQUMsQ0FBQyxFQUFFO1FBQ1JyRCxNQUFNdUQsSUFBSXFDO1FBQ1YzRixNQUFNc0QsSUFBSXNDO1FBQ1YzRixNQUFNcUQsSUFBSXVDO1FBQ1YzRixNQUFNb0QsSUFBSXdDO1FBQ1YxQixNQUFNZCxJQUFJeUM7UUFDVjFCLE1BQU1mLElBQUkwQztRQUNWMUIsT0FBT2hCLElBQUkyQztRQUNYMUIsT0FBT2pCLElBQUk0QztRQUNYMUIsT0FBT2xCLElBQUk2QztRQUNYMUIsT0FBT25CLElBQUk4QztRQUNYMUIsT0FBT3BCLElBQUkrQztRQUNYMUIsT0FBT3JCLElBQUlnRDtRQUNYMUIsT0FBT3RCLElBQUlpRDtRQUNYMUIsT0FBT3ZCLElBQUlrRDtRQUNYMUIsT0FBT3hCLElBQUltRDtRQUNYMUIsT0FBT3pCLElBQUlvRDtRQUNYcEQsSUFBSUYsQ0FBQyxDQUFDLEVBQUU7UUFDUnBELE1BQU1zRCxJQUFJcUM7UUFDVjFGLE1BQU1xRCxJQUFJc0M7UUFDVjFGLE1BQU1vRCxJQUFJdUM7UUFDVnpCLE1BQU1kLElBQUl3QztRQUNWekIsTUFBTWYsSUFBSXlDO1FBQ1Z6QixPQUFPaEIsSUFBSTBDO1FBQ1h6QixPQUFPakIsSUFBSTJDO1FBQ1h6QixPQUFPbEIsSUFBSTRDO1FBQ1h6QixPQUFPbkIsSUFBSTZDO1FBQ1h6QixPQUFPcEIsSUFBSThDO1FBQ1h6QixPQUFPckIsSUFBSStDO1FBQ1h6QixPQUFPdEIsSUFBSWdEO1FBQ1h6QixPQUFPdkIsSUFBSWlEO1FBQ1h6QixPQUFPeEIsSUFBSWtEO1FBQ1h6QixPQUFPekIsSUFBSW1EO1FBQ1h6QixPQUFPMUIsSUFBSW9EO1FBQ1hwRCxJQUFJRixDQUFDLENBQUMsRUFBRTtRQUNSbkQsTUFBTXFELElBQUlxQztRQUNWekYsTUFBTW9ELElBQUlzQztRQUNWeEIsTUFBTWQsSUFBSXVDO1FBQ1Z4QixNQUFNZixJQUFJd0M7UUFDVnhCLE9BQU9oQixJQUFJeUM7UUFDWHhCLE9BQU9qQixJQUFJMEM7UUFDWHhCLE9BQU9sQixJQUFJMkM7UUFDWHhCLE9BQU9uQixJQUFJNEM7UUFDWHhCLE9BQU9wQixJQUFJNkM7UUFDWHhCLE9BQU9yQixJQUFJOEM7UUFDWHhCLE9BQU90QixJQUFJK0M7UUFDWHhCLE9BQU92QixJQUFJZ0Q7UUFDWHhCLE9BQU94QixJQUFJaUQ7UUFDWHhCLE9BQU96QixJQUFJa0Q7UUFDWHhCLE9BQU8xQixJQUFJbUQ7UUFDWHhCLE9BQU8zQixJQUFJb0Q7UUFDWHBELElBQUlGLENBQUMsQ0FBQyxFQUFFO1FBQ1JsRCxNQUFNb0QsSUFBSXFDO1FBQ1Z2QixNQUFNZCxJQUFJc0M7UUFDVnZCLE1BQU1mLElBQUl1QztRQUNWdkIsT0FBT2hCLElBQUl3QztRQUNYdkIsT0FBT2pCLElBQUl5QztRQUNYdkIsT0FBT2xCLElBQUkwQztRQUNYdkIsT0FBT25CLElBQUkyQztRQUNYdkIsT0FBT3BCLElBQUk0QztRQUNYdkIsT0FBT3JCLElBQUk2QztRQUNYdkIsT0FBT3RCLElBQUk4QztRQUNYdkIsT0FBT3ZCLElBQUkrQztRQUNYdkIsT0FBT3hCLElBQUlnRDtRQUNYdkIsT0FBT3pCLElBQUlpRDtRQUNYdkIsT0FBTzFCLElBQUlrRDtRQUNYdkIsT0FBTzNCLElBQUltRDtRQUNYdkIsT0FBTzVCLElBQUlvRDtRQUNYcEQsSUFBSUYsQ0FBQyxDQUFDLEVBQUU7UUFDUmdCLE1BQU1kLElBQUlxQztRQUNWdEIsTUFBTWYsSUFBSXNDO1FBQ1Z0QixPQUFPaEIsSUFBSXVDO1FBQ1h0QixPQUFPakIsSUFBSXdDO1FBQ1h0QixPQUFPbEIsSUFBSXlDO1FBQ1h0QixPQUFPbkIsSUFBSTBDO1FBQ1h0QixPQUFPcEIsSUFBSTJDO1FBQ1h0QixPQUFPckIsSUFBSTRDO1FBQ1h0QixPQUFPdEIsSUFBSTZDO1FBQ1h0QixPQUFPdkIsSUFBSThDO1FBQ1h0QixPQUFPeEIsSUFBSStDO1FBQ1h0QixPQUFPekIsSUFBSWdEO1FBQ1h0QixPQUFPMUIsSUFBSWlEO1FBQ1h0QixPQUFPM0IsSUFBSWtEO1FBQ1h0QixPQUFPNUIsSUFBSW1EO1FBQ1h0QixPQUFPN0IsSUFBSW9EO1FBQ1hwRCxJQUFJRixDQUFDLENBQUMsRUFBRTtRQUNSaUIsTUFBTWYsSUFBSXFDO1FBQ1ZyQixPQUFPaEIsSUFBSXNDO1FBQ1hyQixPQUFPakIsSUFBSXVDO1FBQ1hyQixPQUFPbEIsSUFBSXdDO1FBQ1hyQixPQUFPbkIsSUFBSXlDO1FBQ1hyQixPQUFPcEIsSUFBSTBDO1FBQ1hyQixPQUFPckIsSUFBSTJDO1FBQ1hyQixPQUFPdEIsSUFBSTRDO1FBQ1hyQixPQUFPdkIsSUFBSTZDO1FBQ1hyQixPQUFPeEIsSUFBSThDO1FBQ1hyQixPQUFPekIsSUFBSStDO1FBQ1hyQixPQUFPMUIsSUFBSWdEO1FBQ1hyQixPQUFPM0IsSUFBSWlEO1FBQ1hyQixPQUFPNUIsSUFBSWtEO1FBQ1hyQixPQUFPN0IsSUFBSW1EO1FBQ1hyQixPQUFPOUIsSUFBSW9EO1FBQ1hwRCxJQUFJRixDQUFDLENBQUMsR0FBRztRQUNUa0IsT0FBT2hCLElBQUlxQztRQUNYcEIsT0FBT2pCLElBQUlzQztRQUNYcEIsT0FBT2xCLElBQUl1QztRQUNYcEIsT0FBT25CLElBQUl3QztRQUNYcEIsT0FBT3BCLElBQUl5QztRQUNYcEIsT0FBT3JCLElBQUkwQztRQUNYcEIsT0FBT3RCLElBQUkyQztRQUNYcEIsT0FBT3ZCLElBQUk0QztRQUNYcEIsT0FBT3hCLElBQUk2QztRQUNYcEIsT0FBT3pCLElBQUk4QztRQUNYcEIsT0FBTzFCLElBQUkrQztRQUNYcEIsT0FBTzNCLElBQUlnRDtRQUNYcEIsT0FBTzVCLElBQUlpRDtRQUNYcEIsT0FBTzdCLElBQUlrRDtRQUNYcEIsT0FBTzlCLElBQUltRDtRQUNYcEIsT0FBTy9CLElBQUlvRDtRQUNYcEQsSUFBSUYsQ0FBQyxDQUFDLEdBQUc7UUFDVG1CLE9BQU9qQixJQUFJcUM7UUFDWG5CLE9BQU9sQixJQUFJc0M7UUFDWG5CLE9BQU9uQixJQUFJdUM7UUFDWG5CLE9BQU9wQixJQUFJd0M7UUFDWG5CLE9BQU9yQixJQUFJeUM7UUFDWG5CLE9BQU90QixJQUFJMEM7UUFDWG5CLE9BQU92QixJQUFJMkM7UUFDWG5CLE9BQU94QixJQUFJNEM7UUFDWG5CLE9BQU96QixJQUFJNkM7UUFDWG5CLE9BQU8xQixJQUFJOEM7UUFDWG5CLE9BQU8zQixJQUFJK0M7UUFDWG5CLE9BQU81QixJQUFJZ0Q7UUFDWG5CLE9BQU83QixJQUFJaUQ7UUFDWG5CLE9BQU85QixJQUFJa0Q7UUFDWG5CLE9BQU8vQixJQUFJbUQ7UUFDWG5CLE9BQU9oQyxJQUFJb0Q7UUFDWHBELElBQUlGLENBQUMsQ0FBQyxHQUFHO1FBQ1RvQixPQUFPbEIsSUFBSXFDO1FBQ1hsQixPQUFPbkIsSUFBSXNDO1FBQ1hsQixPQUFPcEIsSUFBSXVDO1FBQ1hsQixPQUFPckIsSUFBSXdDO1FBQ1hsQixPQUFPdEIsSUFBSXlDO1FBQ1hsQixPQUFPdkIsSUFBSTBDO1FBQ1hsQixPQUFPeEIsSUFBSTJDO1FBQ1hsQixPQUFPekIsSUFBSTRDO1FBQ1hsQixPQUFPMUIsSUFBSTZDO1FBQ1hsQixPQUFPM0IsSUFBSThDO1FBQ1hsQixPQUFPNUIsSUFBSStDO1FBQ1hsQixPQUFPN0IsSUFBSWdEO1FBQ1hsQixPQUFPOUIsSUFBSWlEO1FBQ1hsQixPQUFPL0IsSUFBSWtEO1FBQ1hsQixPQUFPaEMsSUFBSW1EO1FBQ1hsQixPQUFPakMsSUFBSW9EO1FBQ1hwRCxJQUFJRixDQUFDLENBQUMsR0FBRztRQUNUcUIsT0FBT25CLElBQUlxQztRQUNYakIsT0FBT3BCLElBQUlzQztRQUNYakIsT0FBT3JCLElBQUl1QztRQUNYakIsT0FBT3RCLElBQUl3QztRQUNYakIsT0FBT3ZCLElBQUl5QztRQUNYakIsT0FBT3hCLElBQUkwQztRQUNYakIsT0FBT3pCLElBQUkyQztRQUNYakIsT0FBTzFCLElBQUk0QztRQUNYakIsT0FBTzNCLElBQUk2QztRQUNYakIsT0FBTzVCLElBQUk4QztRQUNYakIsT0FBTzdCLElBQUkrQztRQUNYakIsT0FBTzlCLElBQUlnRDtRQUNYakIsT0FBTy9CLElBQUlpRDtRQUNYakIsT0FBT2hDLElBQUlrRDtRQUNYakIsT0FBT2pDLElBQUltRDtRQUNYakIsT0FBT2xDLElBQUlvRDtRQUNYcEQsSUFBSUYsQ0FBQyxDQUFDLEdBQUc7UUFDVHNCLE9BQU9wQixJQUFJcUM7UUFDWGhCLE9BQU9yQixJQUFJc0M7UUFDWGhCLE9BQU90QixJQUFJdUM7UUFDWGhCLE9BQU92QixJQUFJd0M7UUFDWGhCLE9BQU94QixJQUFJeUM7UUFDWGhCLE9BQU96QixJQUFJMEM7UUFDWGhCLE9BQU8xQixJQUFJMkM7UUFDWGhCLE9BQU8zQixJQUFJNEM7UUFDWGhCLE9BQU81QixJQUFJNkM7UUFDWGhCLE9BQU83QixJQUFJOEM7UUFDWGhCLE9BQU85QixJQUFJK0M7UUFDWGhCLE9BQU8vQixJQUFJZ0Q7UUFDWGhCLE9BQU9oQyxJQUFJaUQ7UUFDWGhCLE9BQU9qQyxJQUFJa0Q7UUFDWGhCLE9BQU9sQyxJQUFJbUQ7UUFDWGhCLE9BQU9uQyxJQUFJb0Q7UUFDWHBELElBQUlGLENBQUMsQ0FBQyxHQUFHO1FBQ1R1QixPQUFPckIsSUFBSXFDO1FBQ1hmLE9BQU90QixJQUFJc0M7UUFDWGYsT0FBT3ZCLElBQUl1QztRQUNYZixPQUFPeEIsSUFBSXdDO1FBQ1hmLE9BQU96QixJQUFJeUM7UUFDWGYsT0FBTzFCLElBQUkwQztRQUNYZixPQUFPM0IsSUFBSTJDO1FBQ1hmLE9BQU81QixJQUFJNEM7UUFDWGYsT0FBTzdCLElBQUk2QztRQUNYZixPQUFPOUIsSUFBSThDO1FBQ1hmLE9BQU8vQixJQUFJK0M7UUFDWGYsT0FBT2hDLElBQUlnRDtRQUNYZixPQUFPakMsSUFBSWlEO1FBQ1hmLE9BQU9sQyxJQUFJa0Q7UUFDWGYsT0FBT25DLElBQUltRDtRQUNYZixPQUFPcEMsSUFBSW9EO1FBRVgvRyxNQUFPLEtBQUtpRjtRQUNaaEYsTUFBTyxLQUFLaUY7UUFDWmhGLE1BQU8sS0FBS2lGO1FBQ1poRixNQUFPLEtBQUtpRjtRQUNaaEYsTUFBTyxLQUFLaUY7UUFDWmhGLE1BQU8sS0FBS2lGO1FBQ1poRixNQUFPLEtBQUtpRjtRQUNaaEYsTUFBTyxLQUFLaUY7UUFDWmYsTUFBTyxLQUFLZ0I7UUFDWmYsTUFBTyxLQUFLZ0I7UUFDWmYsT0FBTyxLQUFLZ0I7UUFDWmYsT0FBTyxLQUFLZ0I7UUFDWmYsT0FBTyxLQUFLZ0I7UUFDWmYsT0FBTyxLQUFLZ0I7UUFDWmYsT0FBTyxLQUFLZ0I7UUFDWixpQkFBaUI7UUFFakIsWUFBWTtRQUNaekosSUFBSTtRQUNKcUgsSUFBSzNELEtBQUsxRCxJQUFJO1FBQU9BLElBQUlzSCxLQUFLQyxLQUFLLENBQUNGLElBQUk7UUFBUzNELEtBQUsyRCxJQUFJckgsSUFBSTtRQUM5RHFILElBQUsxRCxLQUFLM0QsSUFBSTtRQUFPQSxJQUFJc0gsS0FBS0MsS0FBSyxDQUFDRixJQUFJO1FBQVMxRCxLQUFLMEQsSUFBSXJILElBQUk7UUFDOURxSCxJQUFLekQsS0FBSzVELElBQUk7UUFBT0EsSUFBSXNILEtBQUtDLEtBQUssQ0FBQ0YsSUFBSTtRQUFTekQsS0FBS3lELElBQUlySCxJQUFJO1FBQzlEcUgsSUFBS3hELEtBQUs3RCxJQUFJO1FBQU9BLElBQUlzSCxLQUFLQyxLQUFLLENBQUNGLElBQUk7UUFBU3hELEtBQUt3RCxJQUFJckgsSUFBSTtRQUM5RHFILElBQUt2RCxLQUFLOUQsSUFBSTtRQUFPQSxJQUFJc0gsS0FBS0MsS0FBSyxDQUFDRixJQUFJO1FBQVN2RCxLQUFLdUQsSUFBSXJILElBQUk7UUFDOURxSCxJQUFLdEQsS0FBSy9ELElBQUk7UUFBT0EsSUFBSXNILEtBQUtDLEtBQUssQ0FBQ0YsSUFBSTtRQUFTdEQsS0FBS3NELElBQUlySCxJQUFJO1FBQzlEcUgsSUFBS3JELEtBQUtoRSxJQUFJO1FBQU9BLElBQUlzSCxLQUFLQyxLQUFLLENBQUNGLElBQUk7UUFBU3JELEtBQUtxRCxJQUFJckgsSUFBSTtRQUM5RHFILElBQUtwRCxLQUFLakUsSUFBSTtRQUFPQSxJQUFJc0gsS0FBS0MsS0FBSyxDQUFDRixJQUFJO1FBQVNwRCxLQUFLb0QsSUFBSXJILElBQUk7UUFDOURxSCxJQUFLYyxLQUFLbkksSUFBSTtRQUFPQSxJQUFJc0gsS0FBS0MsS0FBSyxDQUFDRixJQUFJO1FBQVNjLEtBQUtkLElBQUlySCxJQUFJO1FBQzlEcUgsSUFBS2UsS0FBS3BJLElBQUk7UUFBT0EsSUFBSXNILEtBQUtDLEtBQUssQ0FBQ0YsSUFBSTtRQUFTZSxLQUFLZixJQUFJckgsSUFBSTtRQUM5RHFILElBQUlnQixNQUFNckksSUFBSTtRQUFPQSxJQUFJc0gsS0FBS0MsS0FBSyxDQUFDRixJQUFJO1FBQVFnQixNQUFNaEIsSUFBSXJILElBQUk7UUFDOURxSCxJQUFJaUIsTUFBTXRJLElBQUk7UUFBT0EsSUFBSXNILEtBQUtDLEtBQUssQ0FBQ0YsSUFBSTtRQUFRaUIsTUFBTWpCLElBQUlySCxJQUFJO1FBQzlEcUgsSUFBSWtCLE1BQU12SSxJQUFJO1FBQU9BLElBQUlzSCxLQUFLQyxLQUFLLENBQUNGLElBQUk7UUFBUWtCLE1BQU1sQixJQUFJckgsSUFBSTtRQUM5RHFILElBQUltQixNQUFNeEksSUFBSTtRQUFPQSxJQUFJc0gsS0FBS0MsS0FBSyxDQUFDRixJQUFJO1FBQVFtQixNQUFNbkIsSUFBSXJILElBQUk7UUFDOURxSCxJQUFJb0IsTUFBTXpJLElBQUk7UUFBT0EsSUFBSXNILEtBQUtDLEtBQUssQ0FBQ0YsSUFBSTtRQUFRb0IsTUFBTXBCLElBQUlySCxJQUFJO1FBQzlEcUgsSUFBSXFCLE1BQU0xSSxJQUFJO1FBQU9BLElBQUlzSCxLQUFLQyxLQUFLLENBQUNGLElBQUk7UUFBUXFCLE1BQU1yQixJQUFJckgsSUFBSTtRQUM5RDBELE1BQU0xRCxJQUFFLElBQUksS0FBTUEsQ0FBQUEsSUFBRTtRQUVwQixhQUFhO1FBQ2JBLElBQUk7UUFDSnFILElBQUszRCxLQUFLMUQsSUFBSTtRQUFPQSxJQUFJc0gsS0FBS0MsS0FBSyxDQUFDRixJQUFJO1FBQVMzRCxLQUFLMkQsSUFBSXJILElBQUk7UUFDOURxSCxJQUFLMUQsS0FBSzNELElBQUk7UUFBT0EsSUFBSXNILEtBQUtDLEtBQUssQ0FBQ0YsSUFBSTtRQUFTMUQsS0FBSzBELElBQUlySCxJQUFJO1FBQzlEcUgsSUFBS3pELEtBQUs1RCxJQUFJO1FBQU9BLElBQUlzSCxLQUFLQyxLQUFLLENBQUNGLElBQUk7UUFBU3pELEtBQUt5RCxJQUFJckgsSUFBSTtRQUM5RHFILElBQUt4RCxLQUFLN0QsSUFBSTtRQUFPQSxJQUFJc0gsS0FBS0MsS0FBSyxDQUFDRixJQUFJO1FBQVN4RCxLQUFLd0QsSUFBSXJILElBQUk7UUFDOURxSCxJQUFLdkQsS0FBSzlELElBQUk7UUFBT0EsSUFBSXNILEtBQUtDLEtBQUssQ0FBQ0YsSUFBSTtRQUFTdkQsS0FBS3VELElBQUlySCxJQUFJO1FBQzlEcUgsSUFBS3RELEtBQUsvRCxJQUFJO1FBQU9BLElBQUlzSCxLQUFLQyxLQUFLLENBQUNGLElBQUk7UUFBU3RELEtBQUtzRCxJQUFJckgsSUFBSTtRQUM5RHFILElBQUtyRCxLQUFLaEUsSUFBSTtRQUFPQSxJQUFJc0gsS0FBS0MsS0FBSyxDQUFDRixJQUFJO1FBQVNyRCxLQUFLcUQsSUFBSXJILElBQUk7UUFDOURxSCxJQUFLcEQsS0FBS2pFLElBQUk7UUFBT0EsSUFBSXNILEtBQUtDLEtBQUssQ0FBQ0YsSUFBSTtRQUFTcEQsS0FBS29ELElBQUlySCxJQUFJO1FBQzlEcUgsSUFBS2MsS0FBS25JLElBQUk7UUFBT0EsSUFBSXNILEtBQUtDLEtBQUssQ0FBQ0YsSUFBSTtRQUFTYyxLQUFLZCxJQUFJckgsSUFBSTtRQUM5RHFILElBQUtlLEtBQUtwSSxJQUFJO1FBQU9BLElBQUlzSCxLQUFLQyxLQUFLLENBQUNGLElBQUk7UUFBU2UsS0FBS2YsSUFBSXJILElBQUk7UUFDOURxSCxJQUFJZ0IsTUFBTXJJLElBQUk7UUFBT0EsSUFBSXNILEtBQUtDLEtBQUssQ0FBQ0YsSUFBSTtRQUFRZ0IsTUFBTWhCLElBQUlySCxJQUFJO1FBQzlEcUgsSUFBSWlCLE1BQU10SSxJQUFJO1FBQU9BLElBQUlzSCxLQUFLQyxLQUFLLENBQUNGLElBQUk7UUFBUWlCLE1BQU1qQixJQUFJckgsSUFBSTtRQUM5RHFILElBQUlrQixNQUFNdkksSUFBSTtRQUFPQSxJQUFJc0gsS0FBS0MsS0FBSyxDQUFDRixJQUFJO1FBQVFrQixNQUFNbEIsSUFBSXJILElBQUk7UUFDOURxSCxJQUFJbUIsTUFBTXhJLElBQUk7UUFBT0EsSUFBSXNILEtBQUtDLEtBQUssQ0FBQ0YsSUFBSTtRQUFRbUIsTUFBTW5CLElBQUlySCxJQUFJO1FBQzlEcUgsSUFBSW9CLE1BQU16SSxJQUFJO1FBQU9BLElBQUlzSCxLQUFLQyxLQUFLLENBQUNGLElBQUk7UUFBUW9CLE1BQU1wQixJQUFJckgsSUFBSTtRQUM5RHFILElBQUlxQixNQUFNMUksSUFBSTtRQUFPQSxJQUFJc0gsS0FBS0MsS0FBSyxDQUFDRixJQUFJO1FBQVFxQixNQUFNckIsSUFBSXJILElBQUk7UUFDOUQwRCxNQUFNMUQsSUFBRSxJQUFJLEtBQU1BLENBQUFBLElBQUU7UUFFcEJILENBQUMsQ0FBRSxFQUFFLEdBQUc2RDtRQUNSN0QsQ0FBQyxDQUFFLEVBQUUsR0FBRzhEO1FBQ1I5RCxDQUFDLENBQUUsRUFBRSxHQUFHK0Q7UUFDUi9ELENBQUMsQ0FBRSxFQUFFLEdBQUdnRTtRQUNSaEUsQ0FBQyxDQUFFLEVBQUUsR0FBR2lFO1FBQ1JqRSxDQUFDLENBQUUsRUFBRSxHQUFHa0U7UUFDUmxFLENBQUMsQ0FBRSxFQUFFLEdBQUdtRTtRQUNSbkUsQ0FBQyxDQUFFLEVBQUUsR0FBR29FO1FBQ1JwRSxDQUFDLENBQUUsRUFBRSxHQUFHc0k7UUFDUnRJLENBQUMsQ0FBRSxFQUFFLEdBQUd1STtRQUNSdkksQ0FBQyxDQUFDLEdBQUcsR0FBR3dJO1FBQ1J4SSxDQUFDLENBQUMsR0FBRyxHQUFHeUk7UUFDUnpJLENBQUMsQ0FBQyxHQUFHLEdBQUcwSTtRQUNSMUksQ0FBQyxDQUFDLEdBQUcsR0FBRzJJO1FBQ1IzSSxDQUFDLENBQUMsR0FBRyxHQUFHNEk7UUFDUjVJLENBQUMsQ0FBQyxHQUFHLEdBQUc2STtJQUNWO0lBRUEsU0FBU2dDLEVBQUU3SyxDQUFDLEVBQUVzSCxDQUFDO1FBQ2JlLEVBQUVySSxHQUFHc0gsR0FBR0E7SUFDVjtJQUVBLFNBQVN3RCxTQUFTOUssQ0FBQyxFQUFFOUIsQ0FBQztRQUNwQixJQUFJaUMsSUFBSW5DO1FBQ1IsSUFBSXNKO1FBQ0osSUFBS0EsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUtuSCxDQUFDLENBQUNtSCxFQUFFLEdBQUdwSixDQUFDLENBQUNvSixFQUFFO1FBQ3BDLElBQUtBLElBQUksS0FBS0EsS0FBSyxHQUFHQSxJQUFLO1lBQ3pCdUQsRUFBRTFLLEdBQUdBO1lBQ0wsSUFBR21ILE1BQU0sS0FBS0EsTUFBTSxHQUFHZSxFQUFFbEksR0FBR0EsR0FBR2pDO1FBQ2pDO1FBQ0EsSUFBS29KLElBQUksR0FBR0EsSUFBSSxJQUFJQSxJQUFLdEgsQ0FBQyxDQUFDc0gsRUFBRSxHQUFHbkgsQ0FBQyxDQUFDbUgsRUFBRTtJQUN0QztJQUVBLFNBQVN5RCxRQUFRL0ssQ0FBQyxFQUFFOUIsQ0FBQztRQUNuQixJQUFJaUMsSUFBSW5DO1FBQ1IsSUFBSXNKO1FBQ0osSUFBS0EsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUtuSCxDQUFDLENBQUNtSCxFQUFFLEdBQUdwSixDQUFDLENBQUNvSixFQUFFO1FBQ3BDLElBQUtBLElBQUksS0FBS0EsS0FBSyxHQUFHQSxJQUFLO1lBQ3ZCdUQsRUFBRTFLLEdBQUdBO1lBQ0wsSUFBR21ILE1BQU0sR0FBR2UsRUFBRWxJLEdBQUdBLEdBQUdqQztRQUN4QjtRQUNBLElBQUtvSixJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBS3RILENBQUMsQ0FBQ3NILEVBQUUsR0FBR25ILENBQUMsQ0FBQ21ILEVBQUU7SUFDdEM7SUFFQSxTQUFTMEQsa0JBQWtCcEQsQ0FBQyxFQUFFakksQ0FBQyxFQUFFTSxDQUFDO1FBQ2hDLElBQUkrQyxJQUFJLElBQUl2RSxXQUFXO1FBQ3ZCLElBQUlXLElBQUksSUFBSWhCLGFBQWEsS0FBS0QsR0FBR0Q7UUFDakMsSUFBSW9KLElBQUl0SixNQUFNK0UsSUFBSS9FLE1BQU1tQyxJQUFJbkMsTUFDeEI0QixJQUFJNUIsTUFBTWlOLElBQUlqTixNQUFNNEksSUFBSTVJO1FBQzVCLElBQUtFLElBQUksR0FBR0EsSUFBSSxJQUFJQSxJQUFLOEUsQ0FBQyxDQUFDOUUsRUFBRSxHQUFHeUIsQ0FBQyxDQUFDekIsRUFBRTtRQUNwQzhFLENBQUMsQ0FBQyxHQUFHLEdBQUMsQ0FBRSxDQUFDLEdBQUcsR0FBQyxNQUFLO1FBQ2xCQSxDQUFDLENBQUMsRUFBRSxJQUFFO1FBQ05rRixZQUFZOUksR0FBRWE7UUFDZCxJQUFLL0IsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs7WUFDdkI2RSxDQUFDLENBQUM3RSxFQUFFLEdBQUNrQixDQUFDLENBQUNsQixFQUFFO1lBQ1QwQixDQUFDLENBQUMxQixFQUFFLEdBQUNvSixDQUFDLENBQUNwSixFQUFFLEdBQUNpQyxDQUFDLENBQUNqQyxFQUFFLEdBQUM7UUFDakI7UUFDQW9KLENBQUMsQ0FBQyxFQUFFLEdBQUMxSCxDQUFDLENBQUMsRUFBRSxHQUFDO1FBQ1YsSUFBSzFCLElBQUUsS0FBS0EsS0FBRyxHQUFHLEVBQUVBLEVBQUc7WUFDckJDLElBQUUsQ0FBRSxDQUFDRCxNQUFJLEVBQUUsS0FBSUEsQ0FBQUEsSUFBRSxLQUFJO1lBQ3JCeUosU0FBU0wsR0FBRXZFLEdBQUU1RTtZQUNid0osU0FBU3hILEdBQUVQLEdBQUV6QjtZQUNiZ0ssRUFBRThDLEdBQUUzRCxHQUFFbkg7WUFDTmlJLEVBQUVkLEdBQUVBLEdBQUVuSDtZQUNOZ0ksRUFBRWhJLEdBQUU0QyxHQUFFbkQ7WUFDTndJLEVBQUVyRixHQUFFQSxHQUFFbkQ7WUFDTmlMLEVBQUVqTCxHQUFFcUw7WUFDSkosRUFBRWpFLEdBQUVVO1lBQ0plLEVBQUVmLEdBQUVuSCxHQUFFbUg7WUFDTmUsRUFBRWxJLEdBQUU0QyxHQUFFa0k7WUFDTjlDLEVBQUU4QyxHQUFFM0QsR0FBRW5IO1lBQ05pSSxFQUFFZCxHQUFFQSxHQUFFbkg7WUFDTjBLLEVBQUU5SCxHQUFFdUU7WUFDSmMsRUFBRWpJLEdBQUVQLEdBQUVnSDtZQUNOeUIsRUFBRWYsR0FBRW5ILEdBQUV0QjtZQUNOc0osRUFBRWIsR0FBRUEsR0FBRTFIO1lBQ055SSxFQUFFbEksR0FBRUEsR0FBRW1IO1lBQ05lLEVBQUVmLEdBQUUxSCxHQUFFZ0g7WUFDTnlCLEVBQUV6SSxHQUFFbUQsR0FBRTNEO1lBQ055TCxFQUFFOUgsR0FBRWtJO1lBQ0p0RCxTQUFTTCxHQUFFdkUsR0FBRTVFO1lBQ2J3SixTQUFTeEgsR0FBRVAsR0FBRXpCO1FBQ2Y7UUFDQSxJQUFLRCxJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBSztZQUN2QmtCLENBQUMsQ0FBQ2xCLElBQUUsR0FBRyxHQUFDb0osQ0FBQyxDQUFDcEosRUFBRTtZQUNaa0IsQ0FBQyxDQUFDbEIsSUFBRSxHQUFHLEdBQUNpQyxDQUFDLENBQUNqQyxFQUFFO1lBQ1prQixDQUFDLENBQUNsQixJQUFFLEdBQUcsR0FBQzZFLENBQUMsQ0FBQzdFLEVBQUU7WUFDWmtCLENBQUMsQ0FBQ2xCLElBQUUsR0FBRyxHQUFDMEIsQ0FBQyxDQUFDMUIsRUFBRTtRQUNkO1FBQ0EsSUFBSWdOLE1BQU05TCxFQUFFK0wsUUFBUSxDQUFDO1FBQ3JCLElBQUlDLE1BQU1oTSxFQUFFK0wsUUFBUSxDQUFDO1FBQ3JCTCxTQUFTSSxLQUFJQTtRQUNiN0MsRUFBRStDLEtBQUlBLEtBQUlGO1FBQ1ZwRCxVQUFVRixHQUFFd0Q7UUFDWixPQUFPO0lBQ1Q7SUFFQSxTQUFTQyx1QkFBdUJ6RCxDQUFDLEVBQUVqSSxDQUFDO1FBQ2xDLE9BQU9xTCxrQkFBa0JwRCxHQUFHakksR0FBR2pCO0lBQ2pDO0lBRUEsU0FBUzRNLG1CQUFtQjdMLENBQUMsRUFBRUwsQ0FBQztRQUM5QmQsWUFBWWMsR0FBRztRQUNmLE9BQU9pTSx1QkFBdUI1TCxHQUFHTDtJQUNuQztJQUVBLFNBQVNtTSxvQkFBb0JyTCxDQUFDLEVBQUVULENBQUMsRUFBRUwsQ0FBQztRQUNsQyxJQUFJK0QsSUFBSSxJQUFJMUUsV0FBVztRQUN2QnVNLGtCQUFrQjdILEdBQUcvRCxHQUFHSztRQUN4QixPQUFPZ0QscUJBQXFCdkMsR0FBRzFCLElBQUkyRSxHQUFHVDtJQUN4QztJQUVBLElBQUk4SSxxQkFBcUJyRTtJQUN6QixJQUFJc0UsMEJBQTBCckU7SUFFOUIsU0FBU3NFLFdBQVd2TCxDQUFDLEVBQUUwQyxDQUFDLEVBQUVqRCxDQUFDLEVBQUVELENBQUMsRUFBRUYsQ0FBQyxFQUFFTCxDQUFDO1FBQ2xDLElBQUljLElBQUksSUFBSXpCLFdBQVc7UUFDdkI4TSxvQkFBb0JyTCxHQUFHVCxHQUFHTDtRQUMxQixPQUFPb00sbUJBQW1CckwsR0FBRzBDLEdBQUdqRCxHQUFHRCxHQUFHTztJQUN4QztJQUVBLFNBQVN5TCxnQkFBZ0I5SSxDQUFDLEVBQUUxQyxDQUFDLEVBQUVQLENBQUMsRUFBRUQsQ0FBQyxFQUFFRixDQUFDLEVBQUVMLENBQUM7UUFDdkMsSUFBSWMsSUFBSSxJQUFJekIsV0FBVztRQUN2QjhNLG9CQUFvQnJMLEdBQUdULEdBQUdMO1FBQzFCLE9BQU9xTSx3QkFBd0I1SSxHQUFHMUMsR0FBR1AsR0FBR0QsR0FBR087SUFDN0M7SUFFQSxJQUFJMEwsSUFBSTtRQUNOO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO1FBQ3BDO1FBQVk7UUFBWTtRQUFZO0tBQ3JDO0lBRUQsU0FBU0MscUJBQXFCQyxFQUFFLEVBQUVDLEVBQUUsRUFBRWxKLENBQUMsRUFBRWxELENBQUM7UUFDeEMsSUFBSXFNLEtBQUssSUFBSUMsV0FBVyxLQUFLQyxLQUFLLElBQUlELFdBQVcsS0FDN0NFLEtBQUtDLEtBQUtDLEtBQUtDLEtBQUtDLEtBQUtDLEtBQUtDLEtBQUtDLEtBQ25DQyxLQUFLQyxLQUFLQyxLQUFLQyxLQUFLQyxLQUFLQyxLQUFLQyxLQUFLQyxLQUNuQ0MsSUFBSUMsSUFBSWxQLEdBQUc2SixHQUFHMUksR0FBR0MsR0FBR2dJLEdBQUd2RSxHQUFHNUMsR0FBR1A7UUFFakMsSUFBSXlOLE1BQU12QixFQUFFLENBQUMsRUFBRSxFQUNYd0IsTUFBTXhCLEVBQUUsQ0FBQyxFQUFFLEVBQ1h5QixNQUFNekIsRUFBRSxDQUFDLEVBQUUsRUFDWDBCLE1BQU0xQixFQUFFLENBQUMsRUFBRSxFQUNYMkIsTUFBTTNCLEVBQUUsQ0FBQyxFQUFFLEVBQ1g0QixNQUFNNUIsRUFBRSxDQUFDLEVBQUUsRUFDWDZCLE1BQU03QixFQUFFLENBQUMsRUFBRSxFQUNYOEIsTUFBTTlCLEVBQUUsQ0FBQyxFQUFFLEVBRVgrQixNQUFNOUIsRUFBRSxDQUFDLEVBQUUsRUFDWCtCLE1BQU0vQixFQUFFLENBQUMsRUFBRSxFQUNYZ0MsTUFBTWhDLEVBQUUsQ0FBQyxFQUFFLEVBQ1hpQyxNQUFNakMsRUFBRSxDQUFDLEVBQUUsRUFDWGtDLE1BQU1sQyxFQUFFLENBQUMsRUFBRSxFQUNYbUMsTUFBTW5DLEVBQUUsQ0FBQyxFQUFFLEVBQ1hvQyxNQUFNcEMsRUFBRSxDQUFDLEVBQUUsRUFDWHFDLE1BQU1yQyxFQUFFLENBQUMsRUFBRTtRQUVmLElBQUlzQyxNQUFNO1FBQ1YsTUFBTzFPLEtBQUssSUFBSztZQUNmLElBQUt6QixJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBSztnQkFDdkI2SixJQUFJLElBQUk3SixJQUFJbVE7Z0JBQ1pyQyxFQUFFLENBQUM5TixFQUFFLEdBQUcsQ0FBRSxDQUFDNkosSUFBRSxFQUFFLElBQUksS0FBT2xGLENBQUMsQ0FBQ2tGLElBQUUsRUFBRSxJQUFJLEtBQU9sRixDQUFDLENBQUNrRixJQUFFLEVBQUUsSUFBSSxJQUFLbEYsQ0FBQyxDQUFDa0YsSUFBRSxFQUFFO2dCQUNoRW1FLEVBQUUsQ0FBQ2hPLEVBQUUsR0FBRyxDQUFFLENBQUM2SixJQUFFLEVBQUUsSUFBSSxLQUFPbEYsQ0FBQyxDQUFDa0YsSUFBRSxFQUFFLElBQUksS0FBT2xGLENBQUMsQ0FBQ2tGLElBQUUsRUFBRSxJQUFJLElBQUtsRixDQUFDLENBQUNrRixJQUFFLEVBQUU7WUFDbEU7WUFDQSxJQUFLN0osSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs7Z0JBQ3ZCaU8sTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUVOakIsTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUNOakIsTUFBTWtCO2dCQUVOLE1BQU07Z0JBQ04vTyxJQUFJdU87Z0JBQ0p0TyxJQUFJOE87Z0JBRUo5RyxJQUFJaEksSUFBSTtnQkFBUXlELElBQUl6RCxNQUFNO2dCQUMxQmEsSUFBSWQsSUFBSTtnQkFBUU8sSUFBSVAsTUFBTTtnQkFFMUIsU0FBUztnQkFDVEEsSUFBSSxDQUFDLFFBQVMsS0FBTzRPLE9BQVEsS0FBRyxFQUFHLElBQU0sU0FBUyxLQUFPQSxPQUFRLEtBQUcsRUFBRyxJQUFNLFNBQVUsS0FBRyxLQUFRUixPQUFRLEtBQUksTUFBRyxFQUFDLENBQUc7Z0JBQ3JIbk8sSUFBSSxDQUFDLFFBQVMsS0FBT21PLE9BQVEsS0FBRyxFQUFHLElBQU0sU0FBUyxLQUFPQSxPQUFRLEtBQUcsRUFBRyxJQUFNLFNBQVUsS0FBRyxLQUFRUSxPQUFRLEtBQUksTUFBRyxFQUFDLENBQUc7Z0JBRXJIM0csS0FBS2hJLElBQUk7Z0JBQVF5RCxLQUFLekQsTUFBTTtnQkFDNUJhLEtBQUtkLElBQUk7Z0JBQVFPLEtBQUtQLE1BQU07Z0JBRTVCLEtBQUs7Z0JBQ0xBLElBQUksTUFBT3FPLE1BQVEsQ0FBQ0QsTUFBTUU7Z0JBQzFCck8sSUFBSSxNQUFPNE8sTUFBUSxDQUFDRCxNQUFNRTtnQkFFMUI3RyxLQUFLaEksSUFBSTtnQkFBUXlELEtBQUt6RCxNQUFNO2dCQUM1QmEsS0FBS2QsSUFBSTtnQkFBUU8sS0FBS1AsTUFBTTtnQkFFNUIsSUFBSTtnQkFDSkEsSUFBSXVNLENBQUMsQ0FBQzFOLElBQUUsRUFBRTtnQkFDVm9CLElBQUlzTSxDQUFDLENBQUMxTixJQUFFLElBQUUsRUFBRTtnQkFFWm9KLEtBQUtoSSxJQUFJO2dCQUFReUQsS0FBS3pELE1BQU07Z0JBQzVCYSxLQUFLZCxJQUFJO2dCQUFRTyxLQUFLUCxNQUFNO2dCQUU1QixJQUFJO2dCQUNKQSxJQUFJMk0sRUFBRSxDQUFDOU4sSUFBRSxHQUFHO2dCQUNab0IsSUFBSTRNLEVBQUUsQ0FBQ2hPLElBQUUsR0FBRztnQkFFWm9KLEtBQUtoSSxJQUFJO2dCQUFReUQsS0FBS3pELE1BQU07Z0JBQzVCYSxLQUFLZCxJQUFJO2dCQUFRTyxLQUFLUCxNQUFNO2dCQUU1QjBELEtBQUt1RSxNQUFNO2dCQUNYbkgsS0FBSzRDLE1BQU07Z0JBQ1huRCxLQUFLTyxNQUFNO2dCQUVYZ04sS0FBS2hOLElBQUksU0FBU1AsS0FBSztnQkFDdkJ3TixLQUFLOUYsSUFBSSxTQUFTdkUsS0FBSztnQkFFdkIsTUFBTTtnQkFDTjFELElBQUk4TjtnQkFDSjdOLElBQUk4TjtnQkFFSjlGLElBQUloSSxJQUFJO2dCQUFReUQsSUFBSXpELE1BQU07Z0JBQzFCYSxJQUFJZCxJQUFJO2dCQUFRTyxJQUFJUCxNQUFNO2dCQUUxQixTQUFTO2dCQUNUQSxJQUFJLENBQUMsUUFBUyxLQUFPd08sT0FBUSxLQUFHLEVBQUcsSUFBTSxTQUFVLEtBQUcsS0FBUVIsT0FBUSxLQUFJLE1BQUcsRUFBQyxDQUFHLElBQU0sU0FBVSxLQUFHLEtBQVFBLE9BQVEsS0FBSSxNQUFHLEVBQUMsQ0FBRztnQkFDL0gvTixJQUFJLENBQUMsUUFBUyxLQUFPK04sT0FBUSxLQUFHLEVBQUcsSUFBTSxTQUFVLEtBQUcsS0FBUVEsT0FBUSxLQUFJLE1BQUcsRUFBQyxDQUFHLElBQU0sU0FBVSxLQUFHLEtBQVFBLE9BQVEsS0FBSSxNQUFHLEVBQUMsQ0FBRztnQkFFL0h2RyxLQUFLaEksSUFBSTtnQkFBUXlELEtBQUt6RCxNQUFNO2dCQUM1QmEsS0FBS2QsSUFBSTtnQkFBUU8sS0FBS1AsTUFBTTtnQkFFNUIsTUFBTTtnQkFDTkEsSUFBSSxNQUFPaU8sTUFBUUQsTUFBTUUsTUFBUUQsTUFBTUM7Z0JBQ3ZDak8sSUFBSSxNQUFPd08sTUFBUUQsTUFBTUUsTUFBUUQsTUFBTUM7Z0JBRXZDekcsS0FBS2hJLElBQUk7Z0JBQVF5RCxLQUFLekQsTUFBTTtnQkFDNUJhLEtBQUtkLElBQUk7Z0JBQVFPLEtBQUtQLE1BQU07Z0JBRTVCMEQsS0FBS3VFLE1BQU07Z0JBQ1huSCxLQUFLNEMsTUFBTTtnQkFDWG5ELEtBQUtPLE1BQU07Z0JBRVh1TSxNQUFNLElBQUssU0FBVzlNLEtBQUs7Z0JBQzNCc04sTUFBTSxJQUFLLFNBQVduSyxLQUFLO2dCQUUzQixNQUFNO2dCQUNOMUQsSUFBSWlOO2dCQUNKaE4sSUFBSXdOO2dCQUVKeEYsSUFBSWhJLElBQUk7Z0JBQVF5RCxJQUFJekQsTUFBTTtnQkFDMUJhLElBQUlkLElBQUk7Z0JBQVFPLElBQUlQLE1BQU07Z0JBRTFCQSxJQUFJOE47Z0JBQ0o3TixJQUFJOE47Z0JBRUo5RixLQUFLaEksSUFBSTtnQkFBUXlELEtBQUt6RCxNQUFNO2dCQUM1QmEsS0FBS2QsSUFBSTtnQkFBUU8sS0FBS1AsTUFBTTtnQkFFNUIwRCxLQUFLdUUsTUFBTTtnQkFDWG5ILEtBQUs0QyxNQUFNO2dCQUNYbkQsS0FBS08sTUFBTTtnQkFFWG1NLE1BQU0sSUFBSyxTQUFXMU0sS0FBSztnQkFDM0JrTixNQUFNLElBQUssU0FBVy9KLEtBQUs7Z0JBRTNCdUssTUFBTW5CO2dCQUNOb0IsTUFBTW5CO2dCQUNOb0IsTUFBTW5CO2dCQUNOb0IsTUFBTW5CO2dCQUNOb0IsTUFBTW5CO2dCQUNOb0IsTUFBTW5CO2dCQUNOb0IsTUFBTW5CO2dCQUNOWSxNQUFNWDtnQkFFTm9CLE1BQU1uQjtnQkFDTm9CLE1BQU1uQjtnQkFDTm9CLE1BQU1uQjtnQkFDTm9CLE1BQU1uQjtnQkFDTm9CLE1BQU1uQjtnQkFDTm9CLE1BQU1uQjtnQkFDTm9CLE1BQU1uQjtnQkFDTlksTUFBTVg7Z0JBRU4sSUFBSWhQLElBQUUsT0FBTyxJQUFJO29CQUNmLElBQUs2SixJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBSzt3QkFDdkIsTUFBTTt3QkFDTjFJLElBQUkyTSxFQUFFLENBQUNqRSxFQUFFO3dCQUNUekksSUFBSTRNLEVBQUUsQ0FBQ25FLEVBQUU7d0JBRVRULElBQUloSSxJQUFJO3dCQUFReUQsSUFBSXpELE1BQU07d0JBQzFCYSxJQUFJZCxJQUFJO3dCQUFRTyxJQUFJUCxNQUFNO3dCQUUxQkEsSUFBSTJNLEVBQUUsQ0FBQyxDQUFDakUsSUFBRSxLQUFHLEdBQUc7d0JBQ2hCekksSUFBSTRNLEVBQUUsQ0FBQyxDQUFDbkUsSUFBRSxLQUFHLEdBQUc7d0JBRWhCVCxLQUFLaEksSUFBSTt3QkFBUXlELEtBQUt6RCxNQUFNO3dCQUM1QmEsS0FBS2QsSUFBSTt3QkFBUU8sS0FBS1AsTUFBTTt3QkFFNUIsU0FBUzt3QkFDVDhOLEtBQUtuQixFQUFFLENBQUMsQ0FBQ2pFLElBQUUsS0FBRyxHQUFHO3dCQUNqQnFGLEtBQUtsQixFQUFFLENBQUMsQ0FBQ25FLElBQUUsS0FBRyxHQUFHO3dCQUNqQjFJLElBQUksQ0FBQyxPQUFRLElBQU0rTixNQUFPLEtBQUcsQ0FBRSxJQUFNLFFBQVEsSUFBTUEsTUFBTyxLQUFHLENBQUUsSUFBTUQsT0FBTzt3QkFDNUU3TixJQUFJLENBQUMsT0FBUSxJQUFNNk4sTUFBTyxLQUFHLENBQUUsSUFBTSxRQUFRLElBQU1BLE1BQU8sS0FBRyxDQUFFLElBQU0sUUFBUSxJQUFNQSxNQUFPLEtBQUcsQ0FBRTt3QkFFL0Y3RixLQUFLaEksSUFBSTt3QkFBUXlELEtBQUt6RCxNQUFNO3dCQUM1QmEsS0FBS2QsSUFBSTt3QkFBUU8sS0FBS1AsTUFBTTt3QkFFNUIsU0FBUzt3QkFDVDhOLEtBQUtuQixFQUFFLENBQUMsQ0FBQ2pFLElBQUUsRUFBQyxJQUFHLEdBQUc7d0JBQ2xCcUYsS0FBS2xCLEVBQUUsQ0FBQyxDQUFDbkUsSUFBRSxFQUFDLElBQUcsR0FBRzt3QkFDbEIxSSxJQUFJLENBQUMsT0FBUSxLQUFPK04sTUFBTyxLQUFHLEVBQUcsSUFBTSxRQUFTLEtBQUcsS0FBUUQsTUFBTyxLQUFJLE1BQUcsRUFBQyxDQUFHLElBQU1BLE9BQU87d0JBQzFGN04sSUFBSSxDQUFDLE9BQVEsS0FBTzZOLE1BQU8sS0FBRyxFQUFHLElBQU0sUUFBUyxLQUFHLEtBQVFDLE1BQU8sS0FBSSxNQUFHLEVBQUMsQ0FBRyxJQUFNLFFBQVEsSUFBTUQsTUFBTyxLQUFHLENBQUU7d0JBRTdHN0YsS0FBS2hJLElBQUk7d0JBQVF5RCxLQUFLekQsTUFBTTt3QkFDNUJhLEtBQUtkLElBQUk7d0JBQVFPLEtBQUtQLE1BQU07d0JBRTVCMEQsS0FBS3VFLE1BQU07d0JBQ1huSCxLQUFLNEMsTUFBTTt3QkFDWG5ELEtBQUtPLE1BQU07d0JBRVg2TCxFQUFFLENBQUNqRSxFQUFFLEdBQUcsSUFBSyxTQUFXbkksS0FBSzt3QkFDN0JzTSxFQUFFLENBQUNuRSxFQUFFLEdBQUcsSUFBSyxTQUFXaEYsS0FBSztvQkFDL0I7Z0JBQ0Y7WUFDRjtZQUVBLE1BQU07WUFDTjFELElBQUlnTztZQUNKL04sSUFBSXVPO1lBRUp2RyxJQUFJaEksSUFBSTtZQUFReUQsSUFBSXpELE1BQU07WUFDMUJhLElBQUlkLElBQUk7WUFBUU8sSUFBSVAsTUFBTTtZQUUxQkEsSUFBSXlNLEVBQUUsQ0FBQyxFQUFFO1lBQ1R4TSxJQUFJeU0sRUFBRSxDQUFDLEVBQUU7WUFFVHpFLEtBQUtoSSxJQUFJO1lBQVF5RCxLQUFLekQsTUFBTTtZQUM1QmEsS0FBS2QsSUFBSTtZQUFRTyxLQUFLUCxNQUFNO1lBRTVCMEQsS0FBS3VFLE1BQU07WUFDWG5ILEtBQUs0QyxNQUFNO1lBQ1huRCxLQUFLTyxNQUFNO1lBRVgyTCxFQUFFLENBQUMsRUFBRSxHQUFHdUIsTUFBTSxJQUFLLFNBQVd6TixLQUFLO1lBQ25DbU0sRUFBRSxDQUFDLEVBQUUsR0FBRzhCLE1BQU0sSUFBSyxTQUFXOUssS0FBSztZQUVuQzFELElBQUlpTztZQUNKaE8sSUFBSXdPO1lBRUp4RyxJQUFJaEksSUFBSTtZQUFReUQsSUFBSXpELE1BQU07WUFDMUJhLElBQUlkLElBQUk7WUFBUU8sSUFBSVAsTUFBTTtZQUUxQkEsSUFBSXlNLEVBQUUsQ0FBQyxFQUFFO1lBQ1R4TSxJQUFJeU0sRUFBRSxDQUFDLEVBQUU7WUFFVHpFLEtBQUtoSSxJQUFJO1lBQVF5RCxLQUFLekQsTUFBTTtZQUM1QmEsS0FBS2QsSUFBSTtZQUFRTyxLQUFLUCxNQUFNO1lBRTVCMEQsS0FBS3VFLE1BQU07WUFDWG5ILEtBQUs0QyxNQUFNO1lBQ1huRCxLQUFLTyxNQUFNO1lBRVgyTCxFQUFFLENBQUMsRUFBRSxHQUFHd0IsTUFBTSxJQUFLLFNBQVcxTixLQUFLO1lBQ25DbU0sRUFBRSxDQUFDLEVBQUUsR0FBRytCLE1BQU0sSUFBSyxTQUFXL0ssS0FBSztZQUVuQzFELElBQUlrTztZQUNKak8sSUFBSXlPO1lBRUp6RyxJQUFJaEksSUFBSTtZQUFReUQsSUFBSXpELE1BQU07WUFDMUJhLElBQUlkLElBQUk7WUFBUU8sSUFBSVAsTUFBTTtZQUUxQkEsSUFBSXlNLEVBQUUsQ0FBQyxFQUFFO1lBQ1R4TSxJQUFJeU0sRUFBRSxDQUFDLEVBQUU7WUFFVHpFLEtBQUtoSSxJQUFJO1lBQVF5RCxLQUFLekQsTUFBTTtZQUM1QmEsS0FBS2QsSUFBSTtZQUFRTyxLQUFLUCxNQUFNO1lBRTVCMEQsS0FBS3VFLE1BQU07WUFDWG5ILEtBQUs0QyxNQUFNO1lBQ1huRCxLQUFLTyxNQUFNO1lBRVgyTCxFQUFFLENBQUMsRUFBRSxHQUFHeUIsTUFBTSxJQUFLLFNBQVczTixLQUFLO1lBQ25DbU0sRUFBRSxDQUFDLEVBQUUsR0FBR2dDLE1BQU0sSUFBSyxTQUFXaEwsS0FBSztZQUVuQzFELElBQUltTztZQUNKbE8sSUFBSTBPO1lBRUoxRyxJQUFJaEksSUFBSTtZQUFReUQsSUFBSXpELE1BQU07WUFDMUJhLElBQUlkLElBQUk7WUFBUU8sSUFBSVAsTUFBTTtZQUUxQkEsSUFBSXlNLEVBQUUsQ0FBQyxFQUFFO1lBQ1R4TSxJQUFJeU0sRUFBRSxDQUFDLEVBQUU7WUFFVHpFLEtBQUtoSSxJQUFJO1lBQVF5RCxLQUFLekQsTUFBTTtZQUM1QmEsS0FBS2QsSUFBSTtZQUFRTyxLQUFLUCxNQUFNO1lBRTVCMEQsS0FBS3VFLE1BQU07WUFDWG5ILEtBQUs0QyxNQUFNO1lBQ1huRCxLQUFLTyxNQUFNO1lBRVgyTCxFQUFFLENBQUMsRUFBRSxHQUFHMEIsTUFBTSxJQUFLLFNBQVc1TixLQUFLO1lBQ25DbU0sRUFBRSxDQUFDLEVBQUUsR0FBR2lDLE1BQU0sSUFBSyxTQUFXakwsS0FBSztZQUVuQzFELElBQUlvTztZQUNKbk8sSUFBSTJPO1lBRUozRyxJQUFJaEksSUFBSTtZQUFReUQsSUFBSXpELE1BQU07WUFDMUJhLElBQUlkLElBQUk7WUFBUU8sSUFBSVAsTUFBTTtZQUUxQkEsSUFBSXlNLEVBQUUsQ0FBQyxFQUFFO1lBQ1R4TSxJQUFJeU0sRUFBRSxDQUFDLEVBQUU7WUFFVHpFLEtBQUtoSSxJQUFJO1lBQVF5RCxLQUFLekQsTUFBTTtZQUM1QmEsS0FBS2QsSUFBSTtZQUFRTyxLQUFLUCxNQUFNO1lBRTVCMEQsS0FBS3VFLE1BQU07WUFDWG5ILEtBQUs0QyxNQUFNO1lBQ1huRCxLQUFLTyxNQUFNO1lBRVgyTCxFQUFFLENBQUMsRUFBRSxHQUFHMkIsTUFBTSxJQUFLLFNBQVc3TixLQUFLO1lBQ25DbU0sRUFBRSxDQUFDLEVBQUUsR0FBR2tDLE1BQU0sSUFBSyxTQUFXbEwsS0FBSztZQUVuQzFELElBQUlxTztZQUNKcE8sSUFBSTRPO1lBRUo1RyxJQUFJaEksSUFBSTtZQUFReUQsSUFBSXpELE1BQU07WUFDMUJhLElBQUlkLElBQUk7WUFBUU8sSUFBSVAsTUFBTTtZQUUxQkEsSUFBSXlNLEVBQUUsQ0FBQyxFQUFFO1lBQ1R4TSxJQUFJeU0sRUFBRSxDQUFDLEVBQUU7WUFFVHpFLEtBQUtoSSxJQUFJO1lBQVF5RCxLQUFLekQsTUFBTTtZQUM1QmEsS0FBS2QsSUFBSTtZQUFRTyxLQUFLUCxNQUFNO1lBRTVCMEQsS0FBS3VFLE1BQU07WUFDWG5ILEtBQUs0QyxNQUFNO1lBQ1huRCxLQUFLTyxNQUFNO1lBRVgyTCxFQUFFLENBQUMsRUFBRSxHQUFHNEIsTUFBTSxJQUFLLFNBQVc5TixLQUFLO1lBQ25DbU0sRUFBRSxDQUFDLEVBQUUsR0FBR21DLE1BQU0sSUFBSyxTQUFXbkwsS0FBSztZQUVuQzFELElBQUlzTztZQUNKck8sSUFBSTZPO1lBRUo3RyxJQUFJaEksSUFBSTtZQUFReUQsSUFBSXpELE1BQU07WUFDMUJhLElBQUlkLElBQUk7WUFBUU8sSUFBSVAsTUFBTTtZQUUxQkEsSUFBSXlNLEVBQUUsQ0FBQyxFQUFFO1lBQ1R4TSxJQUFJeU0sRUFBRSxDQUFDLEVBQUU7WUFFVHpFLEtBQUtoSSxJQUFJO1lBQVF5RCxLQUFLekQsTUFBTTtZQUM1QmEsS0FBS2QsSUFBSTtZQUFRTyxLQUFLUCxNQUFNO1lBRTVCMEQsS0FBS3VFLE1BQU07WUFDWG5ILEtBQUs0QyxNQUFNO1lBQ1huRCxLQUFLTyxNQUFNO1lBRVgyTCxFQUFFLENBQUMsRUFBRSxHQUFHNkIsTUFBTSxJQUFLLFNBQVcvTixLQUFLO1lBQ25DbU0sRUFBRSxDQUFDLEVBQUUsR0FBR29DLE1BQU0sSUFBSyxTQUFXcEwsS0FBSztZQUVuQzFELElBQUl1TztZQUNKdE8sSUFBSThPO1lBRUo5RyxJQUFJaEksSUFBSTtZQUFReUQsSUFBSXpELE1BQU07WUFDMUJhLElBQUlkLElBQUk7WUFBUU8sSUFBSVAsTUFBTTtZQUUxQkEsSUFBSXlNLEVBQUUsQ0FBQyxFQUFFO1lBQ1R4TSxJQUFJeU0sRUFBRSxDQUFDLEVBQUU7WUFFVHpFLEtBQUtoSSxJQUFJO1lBQVF5RCxLQUFLekQsTUFBTTtZQUM1QmEsS0FBS2QsSUFBSTtZQUFRTyxLQUFLUCxNQUFNO1lBRTVCMEQsS0FBS3VFLE1BQU07WUFDWG5ILEtBQUs0QyxNQUFNO1lBQ1huRCxLQUFLTyxNQUFNO1lBRVgyTCxFQUFFLENBQUMsRUFBRSxHQUFHOEIsTUFBTSxJQUFLLFNBQVdoTyxLQUFLO1lBQ25DbU0sRUFBRSxDQUFDLEVBQUUsR0FBR3FDLE1BQU0sSUFBSyxTQUFXckwsS0FBSztZQUVuQ3NMLE9BQU87WUFDUDFPLEtBQUs7UUFDUDtRQUVBLE9BQU9BO0lBQ1Q7SUFFQSxTQUFTMk8sWUFBWS9MLEdBQUcsRUFBRU0sQ0FBQyxFQUFFbEQsQ0FBQztRQUM1QixJQUFJbU0sS0FBSyxJQUFJRyxXQUFXLElBQ3BCRixLQUFLLElBQUlFLFdBQVcsSUFDcEI3TSxJQUFJLElBQUlYLFdBQVcsTUFDbkJQLEdBQUc2RSxJQUFJcEQ7UUFFWG1NLEVBQUUsQ0FBQyxFQUFFLEdBQUc7UUFDUkEsRUFBRSxDQUFDLEVBQUUsR0FBRztRQUNSQSxFQUFFLENBQUMsRUFBRSxHQUFHO1FBQ1JBLEVBQUUsQ0FBQyxFQUFFLEdBQUc7UUFDUkEsRUFBRSxDQUFDLEVBQUUsR0FBRztRQUNSQSxFQUFFLENBQUMsRUFBRSxHQUFHO1FBQ1JBLEVBQUUsQ0FBQyxFQUFFLEdBQUc7UUFDUkEsRUFBRSxDQUFDLEVBQUUsR0FBRztRQUVSQyxFQUFFLENBQUMsRUFBRSxHQUFHO1FBQ1JBLEVBQUUsQ0FBQyxFQUFFLEdBQUc7UUFDUkEsRUFBRSxDQUFDLEVBQUUsR0FBRztRQUNSQSxFQUFFLENBQUMsRUFBRSxHQUFHO1FBQ1JBLEVBQUUsQ0FBQyxFQUFFLEdBQUc7UUFDUkEsRUFBRSxDQUFDLEVBQUUsR0FBRztRQUNSQSxFQUFFLENBQUMsRUFBRSxHQUFHO1FBQ1JBLEVBQUUsQ0FBQyxFQUFFLEdBQUc7UUFFUkYscUJBQXFCQyxJQUFJQyxJQUFJbEosR0FBR2xEO1FBQ2hDQSxLQUFLO1FBRUwsSUFBS3pCLElBQUksR0FBR0EsSUFBSXlCLEdBQUd6QixJQUFLa0IsQ0FBQyxDQUFDbEIsRUFBRSxHQUFHMkUsQ0FBQyxDQUFDRSxJQUFFcEQsSUFBRXpCLEVBQUU7UUFDdkNrQixDQUFDLENBQUNPLEVBQUUsR0FBRztRQUVQQSxJQUFJLE1BQUksTUFBS0EsQ0FBQUEsSUFBRSxNQUFJLElBQUU7UUFDckJQLENBQUMsQ0FBQ08sSUFBRSxFQUFFLEdBQUc7UUFDVFIsS0FBS0MsR0FBR08sSUFBRSxHQUFJLElBQUssYUFBYyxHQUFHb0QsS0FBSztRQUN6QzhJLHFCQUFxQkMsSUFBSUMsSUFBSTNNLEdBQUdPO1FBRWhDLElBQUt6QixJQUFJLEdBQUdBLElBQUksR0FBR0EsSUFBS2lCLEtBQUtvRCxLQUFLLElBQUVyRSxHQUFHNE4sRUFBRSxDQUFDNU4sRUFBRSxFQUFFNk4sRUFBRSxDQUFDN04sRUFBRTtRQUVuRCxPQUFPO0lBQ1Q7SUFFQSxTQUFTcVEsSUFBSXRPLENBQUMsRUFBRTJILENBQUM7UUFDZixJQUFJTixJQUFJdEosTUFBTStFLElBQUkvRSxNQUFNbUMsSUFBSW5DLE1BQ3hCNEIsSUFBSTVCLE1BQU1pTixJQUFJak4sTUFBTTRJLElBQUk1SSxNQUN4QjBJLElBQUkxSSxNQUFNcUIsSUFBSXJCLE1BQU02SixJQUFJN0o7UUFFNUJvSyxFQUFFZCxHQUFHckgsQ0FBQyxDQUFDLEVBQUUsRUFBRUEsQ0FBQyxDQUFDLEVBQUU7UUFDZm1JLEVBQUVQLEdBQUdELENBQUMsQ0FBQyxFQUFFLEVBQUVBLENBQUMsQ0FBQyxFQUFFO1FBQ2ZTLEVBQUVmLEdBQUdBLEdBQUdPO1FBQ1JNLEVBQUVwRixHQUFHOUMsQ0FBQyxDQUFDLEVBQUUsRUFBRUEsQ0FBQyxDQUFDLEVBQUU7UUFDZmtJLEVBQUVOLEdBQUdELENBQUMsQ0FBQyxFQUFFLEVBQUVBLENBQUMsQ0FBQyxFQUFFO1FBQ2ZTLEVBQUV0RixHQUFHQSxHQUFHOEU7UUFDUlEsRUFBRWxJLEdBQUdGLENBQUMsQ0FBQyxFQUFFLEVBQUUySCxDQUFDLENBQUMsRUFBRTtRQUNmUyxFQUFFbEksR0FBR0EsR0FBR3BCO1FBQ1JzSixFQUFFekksR0FBR0ssQ0FBQyxDQUFDLEVBQUUsRUFBRTJILENBQUMsQ0FBQyxFQUFFO1FBQ2ZPLEVBQUV2SSxHQUFHQSxHQUFHQTtRQUNSd0ksRUFBRTZDLEdBQUdsSSxHQUFHdUU7UUFDUmMsRUFBRXhCLEdBQUdoSCxHQUFHTztRQUNSZ0ksRUFBRXpCLEdBQUc5RyxHQUFHTztRQUNSZ0ksRUFBRTlJLEdBQUcwRCxHQUFHdUU7UUFFUmUsRUFBRXBJLENBQUMsQ0FBQyxFQUFFLEVBQUVnTCxHQUFHckU7UUFDWHlCLEVBQUVwSSxDQUFDLENBQUMsRUFBRSxFQUFFWixHQUFHcUg7UUFDWDJCLEVBQUVwSSxDQUFDLENBQUMsRUFBRSxFQUFFeUcsR0FBR0U7UUFDWHlCLEVBQUVwSSxDQUFDLENBQUMsRUFBRSxFQUFFZ0wsR0FBRzVMO0lBQ2I7SUFFQSxTQUFTbVAsTUFBTXZPLENBQUMsRUFBRTJILENBQUMsRUFBRTdFLENBQUM7UUFDcEIsSUFBSTdFO1FBQ0osSUFBS0EsSUFBSSxHQUFHQSxJQUFJLEdBQUdBLElBQUs7WUFDdEJ5SixTQUFTMUgsQ0FBQyxDQUFDL0IsRUFBRSxFQUFFMEosQ0FBQyxDQUFDMUosRUFBRSxFQUFFNkU7UUFDdkI7SUFDRjtJQUVBLFNBQVMwTCxLQUFLdFEsQ0FBQyxFQUFFOEIsQ0FBQztRQUNoQixJQUFJeU8sS0FBSzFRLE1BQU0yUSxLQUFLM1EsTUFBTTRRLEtBQUs1UTtRQUMvQjhNLFNBQVM4RCxJQUFJM08sQ0FBQyxDQUFDLEVBQUU7UUFDakJvSSxFQUFFcUcsSUFBSXpPLENBQUMsQ0FBQyxFQUFFLEVBQUUyTztRQUNadkcsRUFBRXNHLElBQUkxTyxDQUFDLENBQUMsRUFBRSxFQUFFMk87UUFDWjlHLFVBQVUzSixHQUFHd1E7UUFDYnhRLENBQUMsQ0FBQyxHQUFHLElBQUk4SixTQUFTeUcsT0FBTztJQUMzQjtJQUVBLFNBQVNHLFdBQVc1TyxDQUFDLEVBQUUySCxDQUFDLEVBQUV6RSxDQUFDO1FBQ3pCLElBQUlKLEdBQUc3RTtRQUNQbUosU0FBU3BILENBQUMsQ0FBQyxFQUFFLEVBQUV0QjtRQUNmMEksU0FBU3BILENBQUMsQ0FBQyxFQUFFLEVBQUVyQjtRQUNmeUksU0FBU3BILENBQUMsQ0FBQyxFQUFFLEVBQUVyQjtRQUNmeUksU0FBU3BILENBQUMsQ0FBQyxFQUFFLEVBQUV0QjtRQUNmLElBQUtULElBQUksS0FBS0EsS0FBSyxHQUFHLEVBQUVBLEVBQUc7WUFDekI2RSxJQUFJLENBQUUsQ0FBQyxJQUFHLElBQUcsRUFBRSxJQUFLN0UsQ0FBQUEsSUFBRSxLQUFNO1lBQzVCc1EsTUFBTXZPLEdBQUcySCxHQUFHN0U7WUFDWndMLElBQUkzRyxHQUFHM0g7WUFDUHNPLElBQUl0TyxHQUFHQTtZQUNQdU8sTUFBTXZPLEdBQUcySCxHQUFHN0U7UUFDZDtJQUNGO0lBRUEsU0FBUytMLFdBQVc3TyxDQUFDLEVBQUVrRCxDQUFDO1FBQ3RCLElBQUl5RSxJQUFJO1lBQUM1SjtZQUFNQTtZQUFNQTtZQUFNQTtTQUFLO1FBQ2hDcUosU0FBU08sQ0FBQyxDQUFDLEVBQUUsRUFBRTVJO1FBQ2ZxSSxTQUFTTyxDQUFDLENBQUMsRUFBRSxFQUFFM0k7UUFDZm9JLFNBQVNPLENBQUMsQ0FBQyxFQUFFLEVBQUVoSjtRQUNmeUosRUFBRVQsQ0FBQyxDQUFDLEVBQUUsRUFBRTVJLEdBQUdDO1FBQ1g0UCxXQUFXNU8sR0FBRzJILEdBQUd6RTtJQUNuQjtJQUVBLFNBQVM0TCxvQkFBb0JDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxNQUFNO1FBQ3pDLElBQUl0UCxJQUFJLElBQUluQixXQUFXO1FBQ3ZCLElBQUl3QixJQUFJO1lBQUNqQztZQUFNQTtZQUFNQTtZQUFNQTtTQUFLO1FBQ2hDLElBQUlFO1FBRUosSUFBSSxDQUFDZ1IsUUFBUTVRLFlBQVkyUSxJQUFJO1FBQzdCWCxZQUFZMU8sR0FBR3FQLElBQUk7UUFDbkJyUCxDQUFDLENBQUMsRUFBRSxJQUFJO1FBQ1JBLENBQUMsQ0FBQyxHQUFHLElBQUk7UUFDVEEsQ0FBQyxDQUFDLEdBQUcsSUFBSTtRQUVUa1AsV0FBVzdPLEdBQUdMO1FBQ2Q2TyxLQUFLTyxJQUFJL087UUFFVCxJQUFLL0IsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUsrUSxFQUFFLENBQUMvUSxJQUFFLEdBQUcsR0FBRzhRLEVBQUUsQ0FBQzlRLEVBQUU7UUFDekMsT0FBTztJQUNUO0lBRUEsSUFBSWlSLElBQUksSUFBSS9RLGFBQWE7UUFBQztRQUFNO1FBQU07UUFBTTtRQUFNO1FBQU07UUFBTTtRQUFNO1FBQU07UUFBTTtRQUFNO1FBQU07UUFBTTtRQUFNO1FBQU07UUFBTTtRQUFNO1FBQUc7UUFBRztRQUFHO1FBQUc7UUFBRztRQUFHO1FBQUc7UUFBRztRQUFHO1FBQUc7UUFBRztRQUFHO1FBQUc7UUFBRztRQUFHO0tBQUs7SUFFNUssU0FBU2dSLEtBQUtqUixDQUFDLEVBQUVpQixDQUFDO1FBQ2hCLElBQUlpUSxPQUFPblIsR0FBRzZKLEdBQUc3SDtRQUNqQixJQUFLaEMsSUFBSSxJQUFJQSxLQUFLLElBQUksRUFBRUEsRUFBRztZQUN6Qm1SLFFBQVE7WUFDUixJQUFLdEgsSUFBSTdKLElBQUksSUFBSWdDLElBQUloQyxJQUFJLElBQUk2SixJQUFJN0gsR0FBRyxFQUFFNkgsRUFBRztnQkFDdkMzSSxDQUFDLENBQUMySSxFQUFFLElBQUlzSCxRQUFRLEtBQUtqUSxDQUFDLENBQUNsQixFQUFFLEdBQUdpUixDQUFDLENBQUNwSCxJQUFLN0osQ0FBQUEsSUFBSSxFQUFDLEVBQUc7Z0JBQzNDbVIsUUFBUTVILEtBQUtDLEtBQUssQ0FBQyxDQUFDdEksQ0FBQyxDQUFDMkksRUFBRSxHQUFHLEdBQUUsSUFBSztnQkFDbEMzSSxDQUFDLENBQUMySSxFQUFFLElBQUlzSCxRQUFRO1lBQ2xCO1lBQ0FqUSxDQUFDLENBQUMySSxFQUFFLElBQUlzSDtZQUNSalEsQ0FBQyxDQUFDbEIsRUFBRSxHQUFHO1FBQ1Q7UUFDQW1SLFFBQVE7UUFDUixJQUFLdEgsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs7WUFDdkIzSSxDQUFDLENBQUMySSxFQUFFLElBQUlzSCxRQUFRLENBQUNqUSxDQUFDLENBQUMsR0FBRyxJQUFJLEtBQUsrUCxDQUFDLENBQUNwSCxFQUFFO1lBQ25Dc0gsUUFBUWpRLENBQUMsQ0FBQzJJLEVBQUUsSUFBSTtZQUNoQjNJLENBQUMsQ0FBQzJJLEVBQUUsSUFBSTtRQUNWO1FBQ0EsSUFBS0EsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUszSSxDQUFDLENBQUMySSxFQUFFLElBQUlzSCxRQUFRRixDQUFDLENBQUNwSCxFQUFFO1FBQzdDLElBQUs3SixJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBSztZQUN2QmtCLENBQUMsQ0FBQ2xCLElBQUUsRUFBRSxJQUFJa0IsQ0FBQyxDQUFDbEIsRUFBRSxJQUFJO1lBQ2xCQyxDQUFDLENBQUNELEVBQUUsR0FBR2tCLENBQUMsQ0FBQ2xCLEVBQUUsR0FBRztRQUNoQjtJQUNGO0lBRUEsU0FBU29SLE9BQU9uUixDQUFDO1FBQ2YsSUFBSWlCLElBQUksSUFBSWhCLGFBQWEsS0FBS0Y7UUFDOUIsSUFBS0EsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUtrQixDQUFDLENBQUNsQixFQUFFLEdBQUdDLENBQUMsQ0FBQ0QsRUFBRTtRQUNwQyxJQUFLQSxJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBS0MsQ0FBQyxDQUFDRCxFQUFFLEdBQUc7UUFDaENrUixLQUFLalIsR0FBR2lCO0lBQ1Y7SUFFQSxvRUFBb0U7SUFDcEUsU0FBU21RLFlBQVlDLEVBQUUsRUFBRTNNLENBQUMsRUFBRWxELENBQUMsRUFBRXNQLEVBQUU7UUFDL0IsSUFBSXJQLElBQUksSUFBSW5CLFdBQVcsS0FBS1ksSUFBSSxJQUFJWixXQUFXLEtBQUtOLElBQUksSUFBSU0sV0FBVztRQUN2RSxJQUFJUCxHQUFHNkosR0FBRzNJLElBQUksSUFBSWhCLGFBQWE7UUFDL0IsSUFBSTZCLElBQUk7WUFBQ2pDO1lBQU1BO1lBQU1BO1lBQU1BO1NBQUs7UUFFaENzUSxZQUFZMU8sR0FBR3FQLElBQUk7UUFDbkJyUCxDQUFDLENBQUMsRUFBRSxJQUFJO1FBQ1JBLENBQUMsQ0FBQyxHQUFHLElBQUk7UUFDVEEsQ0FBQyxDQUFDLEdBQUcsSUFBSTtRQUVULElBQUk2UCxRQUFROVAsSUFBSTtRQUNoQixJQUFLekIsSUFBSSxHQUFHQSxJQUFJeUIsR0FBR3pCLElBQUtzUixFQUFFLENBQUMsS0FBS3RSLEVBQUUsR0FBRzJFLENBQUMsQ0FBQzNFLEVBQUU7UUFDekMsSUFBS0EsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUtzUixFQUFFLENBQUMsS0FBS3RSLEVBQUUsR0FBRzBCLENBQUMsQ0FBQyxLQUFLMUIsRUFBRTtRQUUvQ29RLFlBQVluUSxHQUFHcVIsR0FBR3JFLFFBQVEsQ0FBQyxLQUFLeEwsSUFBRTtRQUNsQzJQLE9BQU9uUjtRQUNQMlEsV0FBVzdPLEdBQUc5QjtRQUNkc1EsS0FBS2UsSUFBSXZQO1FBRVQsSUFBSy9CLElBQUksSUFBSUEsSUFBSSxJQUFJQSxJQUFLc1IsRUFBRSxDQUFDdFIsRUFBRSxHQUFHK1EsRUFBRSxDQUFDL1EsRUFBRTtRQUN2Q29RLFlBQVlqUCxHQUFHbVEsSUFBSTdQLElBQUk7UUFDdkIyUCxPQUFPalE7UUFFUCxJQUFLbkIsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUtrQixDQUFDLENBQUNsQixFQUFFLEdBQUc7UUFDaEMsSUFBS0EsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUtrQixDQUFDLENBQUNsQixFQUFFLEdBQUdDLENBQUMsQ0FBQ0QsRUFBRTtRQUNwQyxJQUFLQSxJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBSztZQUN2QixJQUFLNkosSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs7Z0JBQ3ZCM0ksQ0FBQyxDQUFDbEIsSUFBRTZKLEVBQUUsSUFBSTFJLENBQUMsQ0FBQ25CLEVBQUUsR0FBRzBCLENBQUMsQ0FBQ21JLEVBQUU7WUFDdkI7UUFDRjtRQUVBcUgsS0FBS0ksR0FBR3JFLFFBQVEsQ0FBQyxLQUFLL0w7UUFDdEIsT0FBT3FRO0lBQ1Q7SUFFQSxTQUFTQyxVQUFVdlIsQ0FBQyxFQUFFOEIsQ0FBQztRQUNyQixJQUFJNEgsSUFBSTdKLE1BQU0yUixNQUFNM1IsTUFBTTRSLE1BQU01UixNQUM1QjZSLE1BQU03UixNQUFNOFIsT0FBTzlSLE1BQU0rUixPQUFPL1IsTUFDaENnUyxPQUFPaFM7UUFFWHFKLFNBQVNsSixDQUFDLENBQUMsRUFBRSxFQUFFUztRQUNmc0osWUFBWS9KLENBQUMsQ0FBQyxFQUFFLEVBQUU4QjtRQUNsQjRLLEVBQUUrRSxLQUFLelIsQ0FBQyxDQUFDLEVBQUU7UUFDWGtLLEVBQUV3SCxLQUFLRCxLQUFLOVE7UUFDWnNKLEVBQUV3SCxLQUFLQSxLQUFLelIsQ0FBQyxDQUFDLEVBQUU7UUFDaEJnSyxFQUFFMEgsS0FBSzFSLENBQUMsQ0FBQyxFQUFFLEVBQUUwUjtRQUViaEYsRUFBRWlGLE1BQU1EO1FBQ1JoRixFQUFFa0YsTUFBTUQ7UUFDUnpILEVBQUUySCxNQUFNRCxNQUFNRDtRQUNkekgsRUFBRVIsR0FBR21JLE1BQU1KO1FBQ1h2SCxFQUFFUixHQUFHQSxHQUFHZ0k7UUFFUjlFLFFBQVFsRCxHQUFHQTtRQUNYUSxFQUFFUixHQUFHQSxHQUFHK0g7UUFDUnZILEVBQUVSLEdBQUdBLEdBQUdnSTtRQUNSeEgsRUFBRVIsR0FBR0EsR0FBR2dJO1FBQ1J4SCxFQUFFbEssQ0FBQyxDQUFDLEVBQUUsRUFBRTBKLEdBQUdnSTtRQUVYaEYsRUFBRThFLEtBQUt4UixDQUFDLENBQUMsRUFBRTtRQUNYa0ssRUFBRXNILEtBQUtBLEtBQUtFO1FBQ1osSUFBSTdILFNBQVMySCxLQUFLQyxNQUFNdkgsRUFBRWxLLENBQUMsQ0FBQyxFQUFFLEVBQUVBLENBQUMsQ0FBQyxFQUFFLEVBQUVlO1FBRXRDMkwsRUFBRThFLEtBQUt4UixDQUFDLENBQUMsRUFBRTtRQUNYa0ssRUFBRXNILEtBQUtBLEtBQUtFO1FBQ1osSUFBSTdILFNBQVMySCxLQUFLQyxNQUFNLE9BQU8sQ0FBQztRQUVoQyxJQUFJM0gsU0FBUzlKLENBQUMsQ0FBQyxFQUFFLE1BQU84QixDQUFDLENBQUMsR0FBRyxJQUFFLEdBQUltSSxFQUFFakssQ0FBQyxDQUFDLEVBQUUsRUFBRVEsS0FBS1IsQ0FBQyxDQUFDLEVBQUU7UUFFcERrSyxFQUFFbEssQ0FBQyxDQUFDLEVBQUUsRUFBRUEsQ0FBQyxDQUFDLEVBQUUsRUFBRUEsQ0FBQyxDQUFDLEVBQUU7UUFDbEIsT0FBTztJQUNUO0lBRUEsU0FBUzhSLGlCQUFpQnBOLENBQUMsRUFBRTJNLEVBQUUsRUFBRTdQLENBQUMsRUFBRXFQLEVBQUU7UUFDcEMsSUFBSTlRO1FBQ0osSUFBSTJKLElBQUksSUFBSXBKLFdBQVcsS0FBS1ksSUFBSSxJQUFJWixXQUFXO1FBQy9DLElBQUl3QixJQUFJO1lBQUNqQztZQUFNQTtZQUFNQTtZQUFNQTtTQUFLLEVBQzVCNEosSUFBSTtZQUFDNUo7WUFBTUE7WUFBTUE7WUFBTUE7U0FBSztRQUVoQyxJQUFJMkIsSUFBSSxJQUFJLE9BQU8sQ0FBQztRQUVwQixJQUFJK1AsVUFBVTlILEdBQUdvSCxLQUFLLE9BQU8sQ0FBQztRQUU5QixJQUFLOVEsSUFBSSxHQUFHQSxJQUFJeUIsR0FBR3pCLElBQUsyRSxDQUFDLENBQUMzRSxFQUFFLEdBQUdzUixFQUFFLENBQUN0UixFQUFFO1FBQ3BDLElBQUtBLElBQUksR0FBR0EsSUFBSSxJQUFJQSxJQUFLMkUsQ0FBQyxDQUFDM0UsSUFBRSxHQUFHLEdBQUc4USxFQUFFLENBQUM5USxFQUFFO1FBQ3hDb1EsWUFBWWpQLEdBQUd3RCxHQUFHbEQ7UUFDbEIyUCxPQUFPalE7UUFDUHdQLFdBQVc1TyxHQUFHMkgsR0FBR3ZJO1FBRWpCeVAsV0FBV2xILEdBQUc0SCxHQUFHckUsUUFBUSxDQUFDO1FBQzFCb0QsSUFBSXRPLEdBQUcySDtRQUNQNkcsS0FBSzVHLEdBQUc1SDtRQUVSTixLQUFLO1FBQ0wsSUFBSUcsaUJBQWlCMFAsSUFBSSxHQUFHM0gsR0FBRyxJQUFJO1lBQ2pDLElBQUszSixJQUFJLEdBQUdBLElBQUl5QixHQUFHekIsSUFBSzJFLENBQUMsQ0FBQzNFLEVBQUUsR0FBRztZQUMvQixPQUFPLENBQUM7UUFDVjtRQUVBLElBQUtBLElBQUksR0FBR0EsSUFBSXlCLEdBQUd6QixJQUFLMkUsQ0FBQyxDQUFDM0UsRUFBRSxHQUFHc1IsRUFBRSxDQUFDdFIsSUFBSSxHQUFHO1FBQ3pDLE9BQU95QjtJQUNUO0lBRUEsSUFBSXVRLDRCQUE0QixJQUM1QkMsOEJBQThCLElBQzlCQyw2QkFBNkIsSUFDN0JDLGdDQUFnQyxJQUNoQ0MsMEJBQTBCLElBQzFCQyxnQ0FBZ0MsSUFDaENDLDRCQUE0QixJQUM1QkMsNEJBQTRCLElBQzVCQywyQkFBMkIsSUFDM0JDLHdCQUF3QlIsNkJBQ3hCUyx1QkFBdUJSLDRCQUN2QlMsMEJBQTBCUiwrQkFDMUJTLG9CQUFvQixJQUNwQkMsNkJBQTZCLElBQzdCQyw2QkFBNkIsSUFDN0JDLHdCQUF3QixJQUN4QkMsb0JBQW9CO0lBRXhCblQsS0FBS29ULFFBQVEsR0FBRztRQUNkMU8sc0JBQXNCQTtRQUN0QlksbUJBQW1CQTtRQUNuQkgsZUFBZUE7UUFDZlAsMkJBQTJCQTtRQUMzQk0sdUJBQXVCQTtRQUN2QjhELG9CQUFvQkE7UUFDcEJFLDJCQUEyQkE7UUFDM0JwSCxrQkFBa0JBO1FBQ2xCQyxrQkFBa0JBO1FBQ2xCcUgsa0JBQWtCQTtRQUNsQkMsdUJBQXVCQTtRQUN2QjRELG1CQUFtQkE7UUFDbkJLLHdCQUF3QkE7UUFDeEJFLHFCQUFxQkE7UUFDckJDLG9CQUFvQkE7UUFDcEJFLFlBQVlBO1FBQ1pDLGlCQUFpQkE7UUFDakJMLG9CQUFvQkE7UUFDcEJnRCxhQUFhQTtRQUNiaUIsYUFBYUE7UUFDYlIscUJBQXFCQTtRQUNyQmtCLGtCQUFrQkE7UUFFbEJDLDJCQUEyQkE7UUFDM0JDLDZCQUE2QkE7UUFDN0JDLDRCQUE0QkE7UUFDNUJDLCtCQUErQkE7UUFDL0JDLHlCQUF5QkE7UUFDekJDLCtCQUErQkE7UUFDL0JDLDJCQUEyQkE7UUFDM0JDLDJCQUEyQkE7UUFDM0JDLDBCQUEwQkE7UUFDMUJDLHVCQUF1QkE7UUFDdkJDLHNCQUFzQkE7UUFDdEJDLHlCQUF5QkE7UUFDekJDLG1CQUFtQkE7UUFDbkJDLDRCQUE0QkE7UUFDNUJDLDRCQUE0QkE7UUFDNUJDLHVCQUF1QkE7UUFDdkJDLG1CQUFtQkE7UUFFbkJsVCxJQUFJQTtRQUNKYyxHQUFHQTtRQUNIcVEsR0FBR0E7UUFDSHJILFdBQVdBO1FBQ1hJLGFBQWFBO1FBQ2JHLEdBQUdBO1FBQ0hGLEdBQUdBO1FBQ0gwQyxHQUFHQTtRQUNIekMsR0FBR0E7UUFDSDJDLFNBQVNBO1FBQ1R3RCxLQUFLQTtRQUNMbEgsVUFBVUE7UUFDVitILE1BQU1BO1FBQ05QLFlBQVlBO1FBQ1pDLFlBQVlBO0lBQ2Q7SUFFQSxrQkFBa0IsR0FFbEIsU0FBU3NDLGFBQWFsUixDQUFDLEVBQUVQLENBQUM7UUFDeEIsSUFBSU8sRUFBRTdCLE1BQU0sS0FBSzZSLDJCQUEyQixNQUFNLElBQUkzUixNQUFNO1FBQzVELElBQUlvQixFQUFFdEIsTUFBTSxLQUFLOFIsNkJBQTZCLE1BQU0sSUFBSTVSLE1BQU07SUFDaEU7SUFFQSxTQUFTOFMsZ0JBQWdCckMsRUFBRSxFQUFFQyxFQUFFO1FBQzdCLElBQUlELEdBQUczUSxNQUFNLEtBQUttUywyQkFBMkIsTUFBTSxJQUFJalMsTUFBTTtRQUM3RCxJQUFJMFEsR0FBRzVRLE1BQU0sS0FBS29TLDJCQUEyQixNQUFNLElBQUlsUyxNQUFNO0lBQy9EO0lBRUEsU0FBUytTO1FBQ1AsSUFBSyxJQUFJcFQsSUFBSSxHQUFHQSxJQUFJcVQsVUFBVWxULE1BQU0sRUFBRUgsSUFBSztZQUN6QyxJQUFJLENBQUVxVCxDQUFBQSxTQUFTLENBQUNyVCxFQUFFLFlBQVlPLFVBQVMsR0FDckMsTUFBTSxJQUFJK1MsVUFBVTtRQUN4QjtJQUNGO0lBRUEsU0FBU0MsUUFBUUMsR0FBRztRQUNsQixJQUFLLElBQUl4VCxJQUFJLEdBQUdBLElBQUl3VCxJQUFJclQsTUFBTSxFQUFFSCxJQUFLd1QsR0FBRyxDQUFDeFQsRUFBRSxHQUFHO0lBQ2hEO0lBRUFILEtBQUs0VCxXQUFXLEdBQUcsU0FBU2hTLENBQUM7UUFDM0IsSUFBSW9ELElBQUksSUFBSXRFLFdBQVdrQjtRQUN2QnJCLFlBQVl5RSxHQUFHcEQ7UUFDZixPQUFPb0Q7SUFDVDtJQUVBaEYsS0FBSzZULFNBQVMsR0FBRyxTQUFTQyxHQUFHLEVBQUVDLEtBQUssRUFBRXZPLEdBQUc7UUFDdkMrTixnQkFBZ0JPLEtBQUtDLE9BQU92TztRQUM1QjZOLGFBQWE3TixLQUFLdU87UUFDbEIsSUFBSWpQLElBQUksSUFBSXBFLFdBQVcyUiw2QkFBNkJ5QixJQUFJeFQsTUFBTTtRQUM5RCxJQUFJOEIsSUFBSSxJQUFJMUIsV0FBV29FLEVBQUV4RSxNQUFNO1FBQy9CLElBQUssSUFBSUgsSUFBSSxHQUFHQSxJQUFJMlQsSUFBSXhULE1BQU0sRUFBRUgsSUFBSzJFLENBQUMsQ0FBQzNFLElBQUVrUywyQkFBMkIsR0FBR3lCLEdBQUcsQ0FBQzNULEVBQUU7UUFDN0VpSixpQkFBaUJoSCxHQUFHMEMsR0FBR0EsRUFBRXhFLE1BQU0sRUFBRXlULE9BQU92TztRQUN4QyxPQUFPcEQsRUFBRWdMLFFBQVEsQ0FBQ2tGO0lBQ3BCO0lBRUF0UyxLQUFLNlQsU0FBUyxDQUFDRyxJQUFJLEdBQUcsU0FBU0MsR0FBRyxFQUFFRixLQUFLLEVBQUV2TyxHQUFHO1FBQzVDK04sZ0JBQWdCVSxLQUFLRixPQUFPdk87UUFDNUI2TixhQUFhN04sS0FBS3VPO1FBQ2xCLElBQUkzUixJQUFJLElBQUkxQixXQUFXNFIsZ0NBQWdDMkIsSUFBSTNULE1BQU07UUFDakUsSUFBSXdFLElBQUksSUFBSXBFLFdBQVcwQixFQUFFOUIsTUFBTTtRQUMvQixJQUFLLElBQUlILElBQUksR0FBR0EsSUFBSThULElBQUkzVCxNQUFNLEVBQUVILElBQUtpQyxDQUFDLENBQUNqQyxJQUFFbVMsOEJBQThCLEdBQUcyQixHQUFHLENBQUM5VCxFQUFFO1FBQ2hGLElBQUlpQyxFQUFFOUIsTUFBTSxHQUFHLElBQUksT0FBTztRQUMxQixJQUFJK0ksc0JBQXNCdkUsR0FBRzFDLEdBQUdBLEVBQUU5QixNQUFNLEVBQUV5VCxPQUFPdk8sU0FBUyxHQUFHLE9BQU87UUFDcEUsT0FBT1YsRUFBRXNJLFFBQVEsQ0FBQ2lGO0lBQ3BCO0lBRUFyUyxLQUFLNlQsU0FBUyxDQUFDSyxTQUFTLEdBQUcvQjtJQUMzQm5TLEtBQUs2VCxTQUFTLENBQUNNLFdBQVcsR0FBRy9CO0lBQzdCcFMsS0FBSzZULFNBQVMsQ0FBQ08sY0FBYyxHQUFHOUI7SUFFaEN0UyxLQUFLcVUsVUFBVSxHQUFHLFNBQVN6UyxDQUFDLEVBQUVNLENBQUM7UUFDN0JxUixnQkFBZ0IzUixHQUFHTTtRQUNuQixJQUFJTixFQUFFdEIsTUFBTSxLQUFLa1MsK0JBQStCLE1BQU0sSUFBSWhTLE1BQU07UUFDaEUsSUFBSTBCLEVBQUU1QixNQUFNLEtBQUtpUyx5QkFBeUIsTUFBTSxJQUFJL1IsTUFBTTtRQUMxRCxJQUFJcUosSUFBSSxJQUFJbkosV0FBVzZSO1FBQ3ZCdEYsa0JBQWtCcEQsR0FBR2pJLEdBQUdNO1FBQ3hCLE9BQU8ySDtJQUNUO0lBRUE3SixLQUFLcVUsVUFBVSxDQUFDQyxJQUFJLEdBQUcsU0FBUzFTLENBQUM7UUFDL0IyUixnQkFBZ0IzUjtRQUNoQixJQUFJQSxFQUFFdEIsTUFBTSxLQUFLa1MsK0JBQStCLE1BQU0sSUFBSWhTLE1BQU07UUFDaEUsSUFBSXFKLElBQUksSUFBSW5KLFdBQVc2UjtRQUN2QmpGLHVCQUF1QnpELEdBQUdqSTtRQUMxQixPQUFPaUk7SUFDVDtJQUVBN0osS0FBS3FVLFVBQVUsQ0FBQ0UsWUFBWSxHQUFHL0I7SUFDL0J4UyxLQUFLcVUsVUFBVSxDQUFDRyxrQkFBa0IsR0FBR2pDO0lBRXJDdlMsS0FBS2lVLEdBQUcsR0FBRyxTQUFTSCxHQUFHLEVBQUVDLEtBQUssRUFBRVUsU0FBUyxFQUFFQyxTQUFTO1FBQ2xELElBQUl2UyxJQUFJbkMsS0FBS2lVLEdBQUcsQ0FBQ1UsTUFBTSxDQUFDRixXQUFXQztRQUNuQyxPQUFPMVUsS0FBSzZULFNBQVMsQ0FBQ0MsS0FBS0MsT0FBTzVSO0lBQ3BDO0lBRUFuQyxLQUFLaVUsR0FBRyxDQUFDVSxNQUFNLEdBQUcsU0FBU0YsU0FBUyxFQUFFQyxTQUFTO1FBQzdDbkIsZ0JBQWdCa0IsV0FBV0M7UUFDM0JwQixnQkFBZ0JtQixXQUFXQztRQUMzQixJQUFJdlMsSUFBSSxJQUFJekIsV0FBV2lTO1FBQ3ZCbkYsb0JBQW9CckwsR0FBR3NTLFdBQVdDO1FBQ2xDLE9BQU92UztJQUNUO0lBRUFuQyxLQUFLaVUsR0FBRyxDQUFDVyxLQUFLLEdBQUc1VSxLQUFLNlQsU0FBUztJQUUvQjdULEtBQUtpVSxHQUFHLENBQUNELElBQUksR0FBRyxTQUFTRixHQUFHLEVBQUVDLEtBQUssRUFBRVUsU0FBUyxFQUFFQyxTQUFTO1FBQ3ZELElBQUl2UyxJQUFJbkMsS0FBS2lVLEdBQUcsQ0FBQ1UsTUFBTSxDQUFDRixXQUFXQztRQUNuQyxPQUFPMVUsS0FBSzZULFNBQVMsQ0FBQ0csSUFBSSxDQUFDRixLQUFLQyxPQUFPNVI7SUFDekM7SUFFQW5DLEtBQUtpVSxHQUFHLENBQUNELElBQUksQ0FBQ1ksS0FBSyxHQUFHNVUsS0FBSzZULFNBQVMsQ0FBQ0csSUFBSTtJQUV6Q2hVLEtBQUtpVSxHQUFHLENBQUNZLE9BQU8sR0FBRztRQUNqQixJQUFJNUQsS0FBSyxJQUFJdlEsV0FBVytSO1FBQ3hCLElBQUl2QixLQUFLLElBQUl4USxXQUFXZ1M7UUFDeEJuRixtQkFBbUIwRCxJQUFJQztRQUN2QixPQUFPO1lBQUN1RCxXQUFXeEQ7WUFBSXlELFdBQVd4RDtRQUFFO0lBQ3RDO0lBRUFsUixLQUFLaVUsR0FBRyxDQUFDWSxPQUFPLENBQUNDLGFBQWEsR0FBRyxTQUFTSixTQUFTO1FBQ2pEbkIsZ0JBQWdCbUI7UUFDaEIsSUFBSUEsVUFBVXBVLE1BQU0sS0FBS29TLDJCQUN2QixNQUFNLElBQUlsUyxNQUFNO1FBQ2xCLElBQUl5USxLQUFLLElBQUl2USxXQUFXK1I7UUFDeEJuRix1QkFBdUIyRCxJQUFJeUQ7UUFDM0IsT0FBTztZQUFDRCxXQUFXeEQ7WUFBSXlELFdBQVcsSUFBSWhVLFdBQVdnVTtRQUFVO0lBQzdEO0lBRUExVSxLQUFLaVUsR0FBRyxDQUFDYyxlQUFlLEdBQUd0QztJQUMzQnpTLEtBQUtpVSxHQUFHLENBQUNlLGVBQWUsR0FBR3RDO0lBQzNCMVMsS0FBS2lVLEdBQUcsQ0FBQ2dCLGVBQWUsR0FBR3RDO0lBQzNCM1MsS0FBS2lVLEdBQUcsQ0FBQ0UsV0FBVyxHQUFHdkI7SUFDdkI1UyxLQUFLaVUsR0FBRyxDQUFDRyxjQUFjLEdBQUdwVSxLQUFLNlQsU0FBUyxDQUFDTyxjQUFjO0lBRXZEcFUsS0FBS2tWLElBQUksR0FBRyxTQUFTcEIsR0FBRyxFQUFFWSxTQUFTO1FBQ2pDbkIsZ0JBQWdCTyxLQUFLWTtRQUNyQixJQUFJQSxVQUFVcFUsTUFBTSxLQUFLMlMsNEJBQ3ZCLE1BQU0sSUFBSXpTLE1BQU07UUFDbEIsSUFBSTJVLFlBQVksSUFBSXpVLFdBQVdxUyxvQkFBa0JlLElBQUl4VCxNQUFNO1FBQzNEa1IsWUFBWTJELFdBQVdyQixLQUFLQSxJQUFJeFQsTUFBTSxFQUFFb1U7UUFDeEMsT0FBT1M7SUFDVDtJQUVBblYsS0FBS2tWLElBQUksQ0FBQ2xCLElBQUksR0FBRyxTQUFTbUIsU0FBUyxFQUFFVixTQUFTO1FBQzVDbEIsZ0JBQWdCNEIsV0FBV1Y7UUFDM0IsSUFBSUEsVUFBVW5VLE1BQU0sS0FBSzBTLDRCQUN2QixNQUFNLElBQUl4UyxNQUFNO1FBQ2xCLElBQUk0VSxNQUFNLElBQUkxVSxXQUFXeVUsVUFBVTdVLE1BQU07UUFDekMsSUFBSStVLE9BQU9uRCxpQkFBaUJrRCxLQUFLRCxXQUFXQSxVQUFVN1UsTUFBTSxFQUFFbVU7UUFDOUQsSUFBSVksT0FBTyxHQUFHLE9BQU87UUFDckIsSUFBSXZRLElBQUksSUFBSXBFLFdBQVcyVTtRQUN2QixJQUFLLElBQUlsVixJQUFJLEdBQUdBLElBQUkyRSxFQUFFeEUsTUFBTSxFQUFFSCxJQUFLMkUsQ0FBQyxDQUFDM0UsRUFBRSxHQUFHaVYsR0FBRyxDQUFDalYsRUFBRTtRQUNoRCxPQUFPMkU7SUFDVDtJQUVBOUUsS0FBS2tWLElBQUksQ0FBQ0ksUUFBUSxHQUFHLFNBQVN4QixHQUFHLEVBQUVZLFNBQVM7UUFDMUMsSUFBSVMsWUFBWW5WLEtBQUtrVixJQUFJLENBQUNwQixLQUFLWTtRQUMvQixJQUFJYSxNQUFNLElBQUk3VSxXQUFXcVM7UUFDekIsSUFBSyxJQUFJNVMsSUFBSSxHQUFHQSxJQUFJb1YsSUFBSWpWLE1BQU0sRUFBRUgsSUFBS29WLEdBQUcsQ0FBQ3BWLEVBQUUsR0FBR2dWLFNBQVMsQ0FBQ2hWLEVBQUU7UUFDMUQsT0FBT29WO0lBQ1Q7SUFFQXZWLEtBQUtrVixJQUFJLENBQUNJLFFBQVEsQ0FBQ0UsTUFBTSxHQUFHLFNBQVMxQixHQUFHLEVBQUV5QixHQUFHLEVBQUVkLFNBQVM7UUFDdERsQixnQkFBZ0JPLEtBQUt5QixLQUFLZDtRQUMxQixJQUFJYyxJQUFJalYsTUFBTSxLQUFLeVMsbUJBQ2pCLE1BQU0sSUFBSXZTLE1BQU07UUFDbEIsSUFBSWlVLFVBQVVuVSxNQUFNLEtBQUswUyw0QkFDdkIsTUFBTSxJQUFJeFMsTUFBTTtRQUNsQixJQUFJaVIsS0FBSyxJQUFJL1EsV0FBV3FTLG9CQUFvQmUsSUFBSXhULE1BQU07UUFDdEQsSUFBSXdFLElBQUksSUFBSXBFLFdBQVdxUyxvQkFBb0JlLElBQUl4VCxNQUFNO1FBQ3JELElBQUlIO1FBQ0osSUFBS0EsSUFBSSxHQUFHQSxJQUFJNFMsbUJBQW1CNVMsSUFBS3NSLEVBQUUsQ0FBQ3RSLEVBQUUsR0FBR29WLEdBQUcsQ0FBQ3BWLEVBQUU7UUFDdEQsSUFBS0EsSUFBSSxHQUFHQSxJQUFJMlQsSUFBSXhULE1BQU0sRUFBRUgsSUFBS3NSLEVBQUUsQ0FBQ3RSLElBQUU0UyxrQkFBa0IsR0FBR2UsR0FBRyxDQUFDM1QsRUFBRTtRQUNqRSxPQUFRK1IsaUJBQWlCcE4sR0FBRzJNLElBQUlBLEdBQUduUixNQUFNLEVBQUVtVSxjQUFjO0lBQzNEO0lBRUF6VSxLQUFLa1YsSUFBSSxDQUFDTCxPQUFPLEdBQUc7UUFDbEIsSUFBSTVELEtBQUssSUFBSXZRLFdBQVdzUztRQUN4QixJQUFJOUIsS0FBSyxJQUFJeFEsV0FBV3VTO1FBQ3hCakMsb0JBQW9CQyxJQUFJQztRQUN4QixPQUFPO1lBQUN1RCxXQUFXeEQ7WUFBSXlELFdBQVd4RDtRQUFFO0lBQ3RDO0lBRUFsUixLQUFLa1YsSUFBSSxDQUFDTCxPQUFPLENBQUNDLGFBQWEsR0FBRyxTQUFTSixTQUFTO1FBQ2xEbkIsZ0JBQWdCbUI7UUFDaEIsSUFBSUEsVUFBVXBVLE1BQU0sS0FBSzJTLDRCQUN2QixNQUFNLElBQUl6UyxNQUFNO1FBQ2xCLElBQUl5USxLQUFLLElBQUl2USxXQUFXc1M7UUFDeEIsSUFBSyxJQUFJN1MsSUFBSSxHQUFHQSxJQUFJOFEsR0FBRzNRLE1BQU0sRUFBRUgsSUFBSzhRLEVBQUUsQ0FBQzlRLEVBQUUsR0FBR3VVLFNBQVMsQ0FBQyxLQUFHdlUsRUFBRTtRQUMzRCxPQUFPO1lBQUNzVSxXQUFXeEQ7WUFBSXlELFdBQVcsSUFBSWhVLFdBQVdnVTtRQUFVO0lBQzdEO0lBRUExVSxLQUFLa1YsSUFBSSxDQUFDTCxPQUFPLENBQUNZLFFBQVEsR0FBRyxTQUFTQyxJQUFJO1FBQ3hDbkMsZ0JBQWdCbUM7UUFDaEIsSUFBSUEsS0FBS3BWLE1BQU0sS0FBSzRTLHVCQUNsQixNQUFNLElBQUkxUyxNQUFNO1FBQ2xCLElBQUl5USxLQUFLLElBQUl2USxXQUFXc1M7UUFDeEIsSUFBSTlCLEtBQUssSUFBSXhRLFdBQVd1UztRQUN4QixJQUFLLElBQUk5UyxJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBSytRLEVBQUUsQ0FBQy9RLEVBQUUsR0FBR3VWLElBQUksQ0FBQ3ZWLEVBQUU7UUFDNUM2USxvQkFBb0JDLElBQUlDLElBQUk7UUFDNUIsT0FBTztZQUFDdUQsV0FBV3hEO1lBQUl5RCxXQUFXeEQ7UUFBRTtJQUN0QztJQUVBbFIsS0FBS2tWLElBQUksQ0FBQ0gsZUFBZSxHQUFHL0I7SUFDNUJoVCxLQUFLa1YsSUFBSSxDQUFDRixlQUFlLEdBQUcvQjtJQUM1QmpULEtBQUtrVixJQUFJLENBQUNTLFVBQVUsR0FBR3pDO0lBQ3ZCbFQsS0FBS2tWLElBQUksQ0FBQ1UsZUFBZSxHQUFHN0M7SUFFNUIvUyxLQUFLNlYsSUFBSSxHQUFHLFNBQVMvQixHQUFHO1FBQ3RCUCxnQkFBZ0JPO1FBQ2hCLElBQUl4UyxJQUFJLElBQUlaLFdBQVd5UztRQUN2QjVDLFlBQVlqUCxHQUFHd1MsS0FBS0EsSUFBSXhULE1BQU07UUFDOUIsT0FBT2dCO0lBQ1Q7SUFFQXRCLEtBQUs2VixJQUFJLENBQUNDLFVBQVUsR0FBRzNDO0lBRXZCblQsS0FBS3dWLE1BQU0sR0FBRyxTQUFTblUsQ0FBQyxFQUFFSyxDQUFDO1FBQ3pCNlIsZ0JBQWdCbFMsR0FBR0s7UUFDbkIsa0RBQWtEO1FBQ2xELElBQUlMLEVBQUVmLE1BQU0sS0FBSyxLQUFLb0IsRUFBRXBCLE1BQU0sS0FBSyxHQUFHLE9BQU87UUFDN0MsSUFBSWUsRUFBRWYsTUFBTSxLQUFLb0IsRUFBRXBCLE1BQU0sRUFBRSxPQUFPO1FBQ2xDLE9BQU8sR0FBSWUsR0FBRyxHQUFHSyxHQUFHLEdBQUdMLEVBQUVmLE1BQU0sTUFBTSxJQUFLLE9BQU87SUFDbkQ7SUFFQU4sS0FBSytWLE9BQU8sR0FBRyxTQUFTQyxFQUFFO1FBQ3hCelYsY0FBY3lWO0lBQ2hCO0lBRUM7UUFDQyxrREFBa0Q7UUFDbEQsa0RBQWtEO1FBQ2xELElBQUlDLFNBQVMsT0FBT0MsU0FBUyxjQUFlQSxLQUFLRCxNQUFNLElBQUlDLEtBQUtDLFFBQVEsR0FBSTtRQUM1RSxJQUFJRixVQUFVQSxPQUFPRyxlQUFlLEVBQUU7WUFDcEMsWUFBWTtZQUNaLElBQUlDLFFBQVE7WUFDWnJXLEtBQUsrVixPQUFPLENBQUMsU0FBUzFVLENBQUMsRUFBRU8sQ0FBQztnQkFDeEIsSUFBSXpCLEdBQUdzSixJQUFJLElBQUkvSSxXQUFXa0I7Z0JBQzFCLElBQUt6QixJQUFJLEdBQUdBLElBQUl5QixHQUFHekIsS0FBS2tXLE1BQU87b0JBQzdCSixPQUFPRyxlQUFlLENBQUMzTSxFQUFFMkQsUUFBUSxDQUFDak4sR0FBR0EsSUFBSXVKLEtBQUs0TSxHQUFHLENBQUMxVSxJQUFJekIsR0FBR2tXO2dCQUMzRDtnQkFDQSxJQUFLbFcsSUFBSSxHQUFHQSxJQUFJeUIsR0FBR3pCLElBQUtrQixDQUFDLENBQUNsQixFQUFFLEdBQUdzSixDQUFDLENBQUN0SixFQUFFO2dCQUNuQ3VULFFBQVFqSztZQUNWO1FBQ0YsT0FBTyxJQUFJLElBQW1CLEVBQWE7WUFDekMsV0FBVztZQUNYd00sU0FBU00sbUJBQU9BLENBQUM7WUFDakIsSUFBSU4sVUFBVUEsT0FBT3JDLFdBQVcsRUFBRTtnQkFDaEM1VCxLQUFLK1YsT0FBTyxDQUFDLFNBQVMxVSxDQUFDLEVBQUVPLENBQUM7b0JBQ3hCLElBQUl6QixHQUFHc0osSUFBSXdNLE9BQU9yQyxXQUFXLENBQUNoUztvQkFDOUIsSUFBS3pCLElBQUksR0FBR0EsSUFBSXlCLEdBQUd6QixJQUFLa0IsQ0FBQyxDQUFDbEIsRUFBRSxHQUFHc0osQ0FBQyxDQUFDdEosRUFBRTtvQkFDbkN1VCxRQUFRaks7Z0JBQ1Y7WUFDRjtRQUNGO0lBQ0Y7QUFFQSxHQUFHLEtBQWtCLElBQWUrTSxPQUFPQyxPQUFPLEdBQUdELE9BQU9DLE9BQU8sR0FBSVAsS0FBS2xXLElBQUksR0FBR2tXLEtBQUtsVyxJQUFJLElBQUksQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXdlYjMtY3JlYXRvci1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy90d2VldG5hY2wvbmFjbC1mYXN0LmpzPzJhMDciXSwic291cmNlc0NvbnRlbnQiOlsiKGZ1bmN0aW9uKG5hY2wpIHtcbid1c2Ugc3RyaWN0JztcblxuLy8gUG9ydGVkIGluIDIwMTQgYnkgRG1pdHJ5IENoZXN0bnlraCBhbmQgRGV2aSBNYW5kaXJpLlxuLy8gUHVibGljIGRvbWFpbi5cbi8vXG4vLyBJbXBsZW1lbnRhdGlvbiBkZXJpdmVkIGZyb20gVHdlZXROYUNsIHZlcnNpb24gMjAxNDA0MjcuXG4vLyBTZWUgZm9yIGRldGFpbHM6IGh0dHA6Ly90d2VldG5hY2wuY3IueXAudG8vXG5cbnZhciBnZiA9IGZ1bmN0aW9uKGluaXQpIHtcbiAgdmFyIGksIHIgPSBuZXcgRmxvYXQ2NEFycmF5KDE2KTtcbiAgaWYgKGluaXQpIGZvciAoaSA9IDA7IGkgPCBpbml0Lmxlbmd0aDsgaSsrKSByW2ldID0gaW5pdFtpXTtcbiAgcmV0dXJuIHI7XG59O1xuXG4vLyAgUGx1Z2dhYmxlLCBpbml0aWFsaXplZCBpbiBoaWdoLWxldmVsIEFQSSBiZWxvdy5cbnZhciByYW5kb21ieXRlcyA9IGZ1bmN0aW9uKC8qIHgsIG4gKi8pIHsgdGhyb3cgbmV3IEVycm9yKCdubyBQUk5HJyk7IH07XG5cbnZhciBfMCA9IG5ldyBVaW50OEFycmF5KDE2KTtcbnZhciBfOSA9IG5ldyBVaW50OEFycmF5KDMyKTsgXzlbMF0gPSA5O1xuXG52YXIgZ2YwID0gZ2YoKSxcbiAgICBnZjEgPSBnZihbMV0pLFxuICAgIF8xMjE2NjUgPSBnZihbMHhkYjQxLCAxXSksXG4gICAgRCA9IGdmKFsweDc4YTMsIDB4MTM1OSwgMHg0ZGNhLCAweDc1ZWIsIDB4ZDhhYiwgMHg0MTQxLCAweDBhNGQsIDB4MDA3MCwgMHhlODk4LCAweDc3NzksIDB4NDA3OSwgMHg4Y2M3LCAweGZlNzMsIDB4MmI2ZiwgMHg2Y2VlLCAweDUyMDNdKSxcbiAgICBEMiA9IGdmKFsweGYxNTksIDB4MjZiMiwgMHg5Yjk0LCAweGViZDYsIDB4YjE1NiwgMHg4MjgzLCAweDE0OWEsIDB4MDBlMCwgMHhkMTMwLCAweGVlZjMsIDB4ODBmMiwgMHgxOThlLCAweGZjZTcsIDB4NTZkZiwgMHhkOWRjLCAweDI0MDZdKSxcbiAgICBYID0gZ2YoWzB4ZDUxYSwgMHg4ZjI1LCAweDJkNjAsIDB4Yzk1NiwgMHhhN2IyLCAweDk1MjUsIDB4Yzc2MCwgMHg2OTJjLCAweGRjNWMsIDB4ZmRkNiwgMHhlMjMxLCAweGMwYTQsIDB4NTNmZSwgMHhjZDZlLCAweDM2ZDMsIDB4MjE2OV0pLFxuICAgIFkgPSBnZihbMHg2NjU4LCAweDY2NjYsIDB4NjY2NiwgMHg2NjY2LCAweDY2NjYsIDB4NjY2NiwgMHg2NjY2LCAweDY2NjYsIDB4NjY2NiwgMHg2NjY2LCAweDY2NjYsIDB4NjY2NiwgMHg2NjY2LCAweDY2NjYsIDB4NjY2NiwgMHg2NjY2XSksXG4gICAgSSA9IGdmKFsweGEwYjAsIDB4NGEwZSwgMHgxYjI3LCAweGM0ZWUsIDB4ZTQ3OCwgMHhhZDJmLCAweDE4MDYsIDB4MmY0MywgMHhkN2E3LCAweDNkZmIsIDB4MDA5OSwgMHgyYjRkLCAweGRmMGIsIDB4NGZjMSwgMHgyNDgwLCAweDJiODNdKTtcblxuZnVuY3Rpb24gdHM2NCh4LCBpLCBoLCBsKSB7XG4gIHhbaV0gICA9IChoID4+IDI0KSAmIDB4ZmY7XG4gIHhbaSsxXSA9IChoID4+IDE2KSAmIDB4ZmY7XG4gIHhbaSsyXSA9IChoID4+ICA4KSAmIDB4ZmY7XG4gIHhbaSszXSA9IGggJiAweGZmO1xuICB4W2krNF0gPSAobCA+PiAyNCkgICYgMHhmZjtcbiAgeFtpKzVdID0gKGwgPj4gMTYpICAmIDB4ZmY7XG4gIHhbaSs2XSA9IChsID4+ICA4KSAgJiAweGZmO1xuICB4W2krN10gPSBsICYgMHhmZjtcbn1cblxuZnVuY3Rpb24gdm4oeCwgeGksIHksIHlpLCBuKSB7XG4gIHZhciBpLGQgPSAwO1xuICBmb3IgKGkgPSAwOyBpIDwgbjsgaSsrKSBkIHw9IHhbeGkraV1eeVt5aStpXTtcbiAgcmV0dXJuICgxICYgKChkIC0gMSkgPj4+IDgpKSAtIDE7XG59XG5cbmZ1bmN0aW9uIGNyeXB0b192ZXJpZnlfMTYoeCwgeGksIHksIHlpKSB7XG4gIHJldHVybiB2bih4LHhpLHkseWksMTYpO1xufVxuXG5mdW5jdGlvbiBjcnlwdG9fdmVyaWZ5XzMyKHgsIHhpLCB5LCB5aSkge1xuICByZXR1cm4gdm4oeCx4aSx5LHlpLDMyKTtcbn1cblxuZnVuY3Rpb24gY29yZV9zYWxzYTIwKG8sIHAsIGssIGMpIHtcbiAgdmFyIGowICA9IGNbIDBdICYgMHhmZiB8IChjWyAxXSAmIDB4ZmYpPDw4IHwgKGNbIDJdICYgMHhmZik8PDE2IHwgKGNbIDNdICYgMHhmZik8PDI0LFxuICAgICAgajEgID0ga1sgMF0gJiAweGZmIHwgKGtbIDFdICYgMHhmZik8PDggfCAoa1sgMl0gJiAweGZmKTw8MTYgfCAoa1sgM10gJiAweGZmKTw8MjQsXG4gICAgICBqMiAgPSBrWyA0XSAmIDB4ZmYgfCAoa1sgNV0gJiAweGZmKTw8OCB8IChrWyA2XSAmIDB4ZmYpPDwxNiB8IChrWyA3XSAmIDB4ZmYpPDwyNCxcbiAgICAgIGozICA9IGtbIDhdICYgMHhmZiB8IChrWyA5XSAmIDB4ZmYpPDw4IHwgKGtbMTBdICYgMHhmZik8PDE2IHwgKGtbMTFdICYgMHhmZik8PDI0LFxuICAgICAgajQgID0ga1sxMl0gJiAweGZmIHwgKGtbMTNdICYgMHhmZik8PDggfCAoa1sxNF0gJiAweGZmKTw8MTYgfCAoa1sxNV0gJiAweGZmKTw8MjQsXG4gICAgICBqNSAgPSBjWyA0XSAmIDB4ZmYgfCAoY1sgNV0gJiAweGZmKTw8OCB8IChjWyA2XSAmIDB4ZmYpPDwxNiB8IChjWyA3XSAmIDB4ZmYpPDwyNCxcbiAgICAgIGo2ICA9IHBbIDBdICYgMHhmZiB8IChwWyAxXSAmIDB4ZmYpPDw4IHwgKHBbIDJdICYgMHhmZik8PDE2IHwgKHBbIDNdICYgMHhmZik8PDI0LFxuICAgICAgajcgID0gcFsgNF0gJiAweGZmIHwgKHBbIDVdICYgMHhmZik8PDggfCAocFsgNl0gJiAweGZmKTw8MTYgfCAocFsgN10gJiAweGZmKTw8MjQsXG4gICAgICBqOCAgPSBwWyA4XSAmIDB4ZmYgfCAocFsgOV0gJiAweGZmKTw8OCB8IChwWzEwXSAmIDB4ZmYpPDwxNiB8IChwWzExXSAmIDB4ZmYpPDwyNCxcbiAgICAgIGo5ICA9IHBbMTJdICYgMHhmZiB8IChwWzEzXSAmIDB4ZmYpPDw4IHwgKHBbMTRdICYgMHhmZik8PDE2IHwgKHBbMTVdICYgMHhmZik8PDI0LFxuICAgICAgajEwID0gY1sgOF0gJiAweGZmIHwgKGNbIDldICYgMHhmZik8PDggfCAoY1sxMF0gJiAweGZmKTw8MTYgfCAoY1sxMV0gJiAweGZmKTw8MjQsXG4gICAgICBqMTEgPSBrWzE2XSAmIDB4ZmYgfCAoa1sxN10gJiAweGZmKTw8OCB8IChrWzE4XSAmIDB4ZmYpPDwxNiB8IChrWzE5XSAmIDB4ZmYpPDwyNCxcbiAgICAgIGoxMiA9IGtbMjBdICYgMHhmZiB8IChrWzIxXSAmIDB4ZmYpPDw4IHwgKGtbMjJdICYgMHhmZik8PDE2IHwgKGtbMjNdICYgMHhmZik8PDI0LFxuICAgICAgajEzID0ga1syNF0gJiAweGZmIHwgKGtbMjVdICYgMHhmZik8PDggfCAoa1syNl0gJiAweGZmKTw8MTYgfCAoa1syN10gJiAweGZmKTw8MjQsXG4gICAgICBqMTQgPSBrWzI4XSAmIDB4ZmYgfCAoa1syOV0gJiAweGZmKTw8OCB8IChrWzMwXSAmIDB4ZmYpPDwxNiB8IChrWzMxXSAmIDB4ZmYpPDwyNCxcbiAgICAgIGoxNSA9IGNbMTJdICYgMHhmZiB8IChjWzEzXSAmIDB4ZmYpPDw4IHwgKGNbMTRdICYgMHhmZik8PDE2IHwgKGNbMTVdICYgMHhmZik8PDI0O1xuXG4gIHZhciB4MCA9IGowLCB4MSA9IGoxLCB4MiA9IGoyLCB4MyA9IGozLCB4NCA9IGo0LCB4NSA9IGo1LCB4NiA9IGo2LCB4NyA9IGo3LFxuICAgICAgeDggPSBqOCwgeDkgPSBqOSwgeDEwID0gajEwLCB4MTEgPSBqMTEsIHgxMiA9IGoxMiwgeDEzID0gajEzLCB4MTQgPSBqMTQsXG4gICAgICB4MTUgPSBqMTUsIHU7XG5cbiAgZm9yICh2YXIgaSA9IDA7IGkgPCAyMDsgaSArPSAyKSB7XG4gICAgdSA9IHgwICsgeDEyIHwgMDtcbiAgICB4NCBePSB1PDw3IHwgdT4+PigzMi03KTtcbiAgICB1ID0geDQgKyB4MCB8IDA7XG4gICAgeDggXj0gdTw8OSB8IHU+Pj4oMzItOSk7XG4gICAgdSA9IHg4ICsgeDQgfCAwO1xuICAgIHgxMiBePSB1PDwxMyB8IHU+Pj4oMzItMTMpO1xuICAgIHUgPSB4MTIgKyB4OCB8IDA7XG4gICAgeDAgXj0gdTw8MTggfCB1Pj4+KDMyLTE4KTtcblxuICAgIHUgPSB4NSArIHgxIHwgMDtcbiAgICB4OSBePSB1PDw3IHwgdT4+PigzMi03KTtcbiAgICB1ID0geDkgKyB4NSB8IDA7XG4gICAgeDEzIF49IHU8PDkgfCB1Pj4+KDMyLTkpO1xuICAgIHUgPSB4MTMgKyB4OSB8IDA7XG4gICAgeDEgXj0gdTw8MTMgfCB1Pj4+KDMyLTEzKTtcbiAgICB1ID0geDEgKyB4MTMgfCAwO1xuICAgIHg1IF49IHU8PDE4IHwgdT4+PigzMi0xOCk7XG5cbiAgICB1ID0geDEwICsgeDYgfCAwO1xuICAgIHgxNCBePSB1PDw3IHwgdT4+PigzMi03KTtcbiAgICB1ID0geDE0ICsgeDEwIHwgMDtcbiAgICB4MiBePSB1PDw5IHwgdT4+PigzMi05KTtcbiAgICB1ID0geDIgKyB4MTQgfCAwO1xuICAgIHg2IF49IHU8PDEzIHwgdT4+PigzMi0xMyk7XG4gICAgdSA9IHg2ICsgeDIgfCAwO1xuICAgIHgxMCBePSB1PDwxOCB8IHU+Pj4oMzItMTgpO1xuXG4gICAgdSA9IHgxNSArIHgxMSB8IDA7XG4gICAgeDMgXj0gdTw8NyB8IHU+Pj4oMzItNyk7XG4gICAgdSA9IHgzICsgeDE1IHwgMDtcbiAgICB4NyBePSB1PDw5IHwgdT4+PigzMi05KTtcbiAgICB1ID0geDcgKyB4MyB8IDA7XG4gICAgeDExIF49IHU8PDEzIHwgdT4+PigzMi0xMyk7XG4gICAgdSA9IHgxMSArIHg3IHwgMDtcbiAgICB4MTUgXj0gdTw8MTggfCB1Pj4+KDMyLTE4KTtcblxuICAgIHUgPSB4MCArIHgzIHwgMDtcbiAgICB4MSBePSB1PDw3IHwgdT4+PigzMi03KTtcbiAgICB1ID0geDEgKyB4MCB8IDA7XG4gICAgeDIgXj0gdTw8OSB8IHU+Pj4oMzItOSk7XG4gICAgdSA9IHgyICsgeDEgfCAwO1xuICAgIHgzIF49IHU8PDEzIHwgdT4+PigzMi0xMyk7XG4gICAgdSA9IHgzICsgeDIgfCAwO1xuICAgIHgwIF49IHU8PDE4IHwgdT4+PigzMi0xOCk7XG5cbiAgICB1ID0geDUgKyB4NCB8IDA7XG4gICAgeDYgXj0gdTw8NyB8IHU+Pj4oMzItNyk7XG4gICAgdSA9IHg2ICsgeDUgfCAwO1xuICAgIHg3IF49IHU8PDkgfCB1Pj4+KDMyLTkpO1xuICAgIHUgPSB4NyArIHg2IHwgMDtcbiAgICB4NCBePSB1PDwxMyB8IHU+Pj4oMzItMTMpO1xuICAgIHUgPSB4NCArIHg3IHwgMDtcbiAgICB4NSBePSB1PDwxOCB8IHU+Pj4oMzItMTgpO1xuXG4gICAgdSA9IHgxMCArIHg5IHwgMDtcbiAgICB4MTEgXj0gdTw8NyB8IHU+Pj4oMzItNyk7XG4gICAgdSA9IHgxMSArIHgxMCB8IDA7XG4gICAgeDggXj0gdTw8OSB8IHU+Pj4oMzItOSk7XG4gICAgdSA9IHg4ICsgeDExIHwgMDtcbiAgICB4OSBePSB1PDwxMyB8IHU+Pj4oMzItMTMpO1xuICAgIHUgPSB4OSArIHg4IHwgMDtcbiAgICB4MTAgXj0gdTw8MTggfCB1Pj4+KDMyLTE4KTtcblxuICAgIHUgPSB4MTUgKyB4MTQgfCAwO1xuICAgIHgxMiBePSB1PDw3IHwgdT4+PigzMi03KTtcbiAgICB1ID0geDEyICsgeDE1IHwgMDtcbiAgICB4MTMgXj0gdTw8OSB8IHU+Pj4oMzItOSk7XG4gICAgdSA9IHgxMyArIHgxMiB8IDA7XG4gICAgeDE0IF49IHU8PDEzIHwgdT4+PigzMi0xMyk7XG4gICAgdSA9IHgxNCArIHgxMyB8IDA7XG4gICAgeDE1IF49IHU8PDE4IHwgdT4+PigzMi0xOCk7XG4gIH1cbiAgIHgwID0gIHgwICsgIGowIHwgMDtcbiAgIHgxID0gIHgxICsgIGoxIHwgMDtcbiAgIHgyID0gIHgyICsgIGoyIHwgMDtcbiAgIHgzID0gIHgzICsgIGozIHwgMDtcbiAgIHg0ID0gIHg0ICsgIGo0IHwgMDtcbiAgIHg1ID0gIHg1ICsgIGo1IHwgMDtcbiAgIHg2ID0gIHg2ICsgIGo2IHwgMDtcbiAgIHg3ID0gIHg3ICsgIGo3IHwgMDtcbiAgIHg4ID0gIHg4ICsgIGo4IHwgMDtcbiAgIHg5ID0gIHg5ICsgIGo5IHwgMDtcbiAgeDEwID0geDEwICsgajEwIHwgMDtcbiAgeDExID0geDExICsgajExIHwgMDtcbiAgeDEyID0geDEyICsgajEyIHwgMDtcbiAgeDEzID0geDEzICsgajEzIHwgMDtcbiAgeDE0ID0geDE0ICsgajE0IHwgMDtcbiAgeDE1ID0geDE1ICsgajE1IHwgMDtcblxuICBvWyAwXSA9IHgwID4+PiAgMCAmIDB4ZmY7XG4gIG9bIDFdID0geDAgPj4+ICA4ICYgMHhmZjtcbiAgb1sgMl0gPSB4MCA+Pj4gMTYgJiAweGZmO1xuICBvWyAzXSA9IHgwID4+PiAyNCAmIDB4ZmY7XG5cbiAgb1sgNF0gPSB4MSA+Pj4gIDAgJiAweGZmO1xuICBvWyA1XSA9IHgxID4+PiAgOCAmIDB4ZmY7XG4gIG9bIDZdID0geDEgPj4+IDE2ICYgMHhmZjtcbiAgb1sgN10gPSB4MSA+Pj4gMjQgJiAweGZmO1xuXG4gIG9bIDhdID0geDIgPj4+ICAwICYgMHhmZjtcbiAgb1sgOV0gPSB4MiA+Pj4gIDggJiAweGZmO1xuICBvWzEwXSA9IHgyID4+PiAxNiAmIDB4ZmY7XG4gIG9bMTFdID0geDIgPj4+IDI0ICYgMHhmZjtcblxuICBvWzEyXSA9IHgzID4+PiAgMCAmIDB4ZmY7XG4gIG9bMTNdID0geDMgPj4+ICA4ICYgMHhmZjtcbiAgb1sxNF0gPSB4MyA+Pj4gMTYgJiAweGZmO1xuICBvWzE1XSA9IHgzID4+PiAyNCAmIDB4ZmY7XG5cbiAgb1sxNl0gPSB4NCA+Pj4gIDAgJiAweGZmO1xuICBvWzE3XSA9IHg0ID4+PiAgOCAmIDB4ZmY7XG4gIG9bMThdID0geDQgPj4+IDE2ICYgMHhmZjtcbiAgb1sxOV0gPSB4NCA+Pj4gMjQgJiAweGZmO1xuXG4gIG9bMjBdID0geDUgPj4+ICAwICYgMHhmZjtcbiAgb1syMV0gPSB4NSA+Pj4gIDggJiAweGZmO1xuICBvWzIyXSA9IHg1ID4+PiAxNiAmIDB4ZmY7XG4gIG9bMjNdID0geDUgPj4+IDI0ICYgMHhmZjtcblxuICBvWzI0XSA9IHg2ID4+PiAgMCAmIDB4ZmY7XG4gIG9bMjVdID0geDYgPj4+ICA4ICYgMHhmZjtcbiAgb1syNl0gPSB4NiA+Pj4gMTYgJiAweGZmO1xuICBvWzI3XSA9IHg2ID4+PiAyNCAmIDB4ZmY7XG5cbiAgb1syOF0gPSB4NyA+Pj4gIDAgJiAweGZmO1xuICBvWzI5XSA9IHg3ID4+PiAgOCAmIDB4ZmY7XG4gIG9bMzBdID0geDcgPj4+IDE2ICYgMHhmZjtcbiAgb1szMV0gPSB4NyA+Pj4gMjQgJiAweGZmO1xuXG4gIG9bMzJdID0geDggPj4+ICAwICYgMHhmZjtcbiAgb1szM10gPSB4OCA+Pj4gIDggJiAweGZmO1xuICBvWzM0XSA9IHg4ID4+PiAxNiAmIDB4ZmY7XG4gIG9bMzVdID0geDggPj4+IDI0ICYgMHhmZjtcblxuICBvWzM2XSA9IHg5ID4+PiAgMCAmIDB4ZmY7XG4gIG9bMzddID0geDkgPj4+ICA4ICYgMHhmZjtcbiAgb1szOF0gPSB4OSA+Pj4gMTYgJiAweGZmO1xuICBvWzM5XSA9IHg5ID4+PiAyNCAmIDB4ZmY7XG5cbiAgb1s0MF0gPSB4MTAgPj4+ICAwICYgMHhmZjtcbiAgb1s0MV0gPSB4MTAgPj4+ICA4ICYgMHhmZjtcbiAgb1s0Ml0gPSB4MTAgPj4+IDE2ICYgMHhmZjtcbiAgb1s0M10gPSB4MTAgPj4+IDI0ICYgMHhmZjtcblxuICBvWzQ0XSA9IHgxMSA+Pj4gIDAgJiAweGZmO1xuICBvWzQ1XSA9IHgxMSA+Pj4gIDggJiAweGZmO1xuICBvWzQ2XSA9IHgxMSA+Pj4gMTYgJiAweGZmO1xuICBvWzQ3XSA9IHgxMSA+Pj4gMjQgJiAweGZmO1xuXG4gIG9bNDhdID0geDEyID4+PiAgMCAmIDB4ZmY7XG4gIG9bNDldID0geDEyID4+PiAgOCAmIDB4ZmY7XG4gIG9bNTBdID0geDEyID4+PiAxNiAmIDB4ZmY7XG4gIG9bNTFdID0geDEyID4+PiAyNCAmIDB4ZmY7XG5cbiAgb1s1Ml0gPSB4MTMgPj4+ICAwICYgMHhmZjtcbiAgb1s1M10gPSB4MTMgPj4+ICA4ICYgMHhmZjtcbiAgb1s1NF0gPSB4MTMgPj4+IDE2ICYgMHhmZjtcbiAgb1s1NV0gPSB4MTMgPj4+IDI0ICYgMHhmZjtcblxuICBvWzU2XSA9IHgxNCA+Pj4gIDAgJiAweGZmO1xuICBvWzU3XSA9IHgxNCA+Pj4gIDggJiAweGZmO1xuICBvWzU4XSA9IHgxNCA+Pj4gMTYgJiAweGZmO1xuICBvWzU5XSA9IHgxNCA+Pj4gMjQgJiAweGZmO1xuXG4gIG9bNjBdID0geDE1ID4+PiAgMCAmIDB4ZmY7XG4gIG9bNjFdID0geDE1ID4+PiAgOCAmIDB4ZmY7XG4gIG9bNjJdID0geDE1ID4+PiAxNiAmIDB4ZmY7XG4gIG9bNjNdID0geDE1ID4+PiAyNCAmIDB4ZmY7XG59XG5cbmZ1bmN0aW9uIGNvcmVfaHNhbHNhMjAobyxwLGssYykge1xuICB2YXIgajAgID0gY1sgMF0gJiAweGZmIHwgKGNbIDFdICYgMHhmZik8PDggfCAoY1sgMl0gJiAweGZmKTw8MTYgfCAoY1sgM10gJiAweGZmKTw8MjQsXG4gICAgICBqMSAgPSBrWyAwXSAmIDB4ZmYgfCAoa1sgMV0gJiAweGZmKTw8OCB8IChrWyAyXSAmIDB4ZmYpPDwxNiB8IChrWyAzXSAmIDB4ZmYpPDwyNCxcbiAgICAgIGoyICA9IGtbIDRdICYgMHhmZiB8IChrWyA1XSAmIDB4ZmYpPDw4IHwgKGtbIDZdICYgMHhmZik8PDE2IHwgKGtbIDddICYgMHhmZik8PDI0LFxuICAgICAgajMgID0ga1sgOF0gJiAweGZmIHwgKGtbIDldICYgMHhmZik8PDggfCAoa1sxMF0gJiAweGZmKTw8MTYgfCAoa1sxMV0gJiAweGZmKTw8MjQsXG4gICAgICBqNCAgPSBrWzEyXSAmIDB4ZmYgfCAoa1sxM10gJiAweGZmKTw8OCB8IChrWzE0XSAmIDB4ZmYpPDwxNiB8IChrWzE1XSAmIDB4ZmYpPDwyNCxcbiAgICAgIGo1ICA9IGNbIDRdICYgMHhmZiB8IChjWyA1XSAmIDB4ZmYpPDw4IHwgKGNbIDZdICYgMHhmZik8PDE2IHwgKGNbIDddICYgMHhmZik8PDI0LFxuICAgICAgajYgID0gcFsgMF0gJiAweGZmIHwgKHBbIDFdICYgMHhmZik8PDggfCAocFsgMl0gJiAweGZmKTw8MTYgfCAocFsgM10gJiAweGZmKTw8MjQsXG4gICAgICBqNyAgPSBwWyA0XSAmIDB4ZmYgfCAocFsgNV0gJiAweGZmKTw8OCB8IChwWyA2XSAmIDB4ZmYpPDwxNiB8IChwWyA3XSAmIDB4ZmYpPDwyNCxcbiAgICAgIGo4ICA9IHBbIDhdICYgMHhmZiB8IChwWyA5XSAmIDB4ZmYpPDw4IHwgKHBbMTBdICYgMHhmZik8PDE2IHwgKHBbMTFdICYgMHhmZik8PDI0LFxuICAgICAgajkgID0gcFsxMl0gJiAweGZmIHwgKHBbMTNdICYgMHhmZik8PDggfCAocFsxNF0gJiAweGZmKTw8MTYgfCAocFsxNV0gJiAweGZmKTw8MjQsXG4gICAgICBqMTAgPSBjWyA4XSAmIDB4ZmYgfCAoY1sgOV0gJiAweGZmKTw8OCB8IChjWzEwXSAmIDB4ZmYpPDwxNiB8IChjWzExXSAmIDB4ZmYpPDwyNCxcbiAgICAgIGoxMSA9IGtbMTZdICYgMHhmZiB8IChrWzE3XSAmIDB4ZmYpPDw4IHwgKGtbMThdICYgMHhmZik8PDE2IHwgKGtbMTldICYgMHhmZik8PDI0LFxuICAgICAgajEyID0ga1syMF0gJiAweGZmIHwgKGtbMjFdICYgMHhmZik8PDggfCAoa1syMl0gJiAweGZmKTw8MTYgfCAoa1syM10gJiAweGZmKTw8MjQsXG4gICAgICBqMTMgPSBrWzI0XSAmIDB4ZmYgfCAoa1syNV0gJiAweGZmKTw8OCB8IChrWzI2XSAmIDB4ZmYpPDwxNiB8IChrWzI3XSAmIDB4ZmYpPDwyNCxcbiAgICAgIGoxNCA9IGtbMjhdICYgMHhmZiB8IChrWzI5XSAmIDB4ZmYpPDw4IHwgKGtbMzBdICYgMHhmZik8PDE2IHwgKGtbMzFdICYgMHhmZik8PDI0LFxuICAgICAgajE1ID0gY1sxMl0gJiAweGZmIHwgKGNbMTNdICYgMHhmZik8PDggfCAoY1sxNF0gJiAweGZmKTw8MTYgfCAoY1sxNV0gJiAweGZmKTw8MjQ7XG5cbiAgdmFyIHgwID0gajAsIHgxID0gajEsIHgyID0gajIsIHgzID0gajMsIHg0ID0gajQsIHg1ID0gajUsIHg2ID0gajYsIHg3ID0gajcsXG4gICAgICB4OCA9IGo4LCB4OSA9IGo5LCB4MTAgPSBqMTAsIHgxMSA9IGoxMSwgeDEyID0gajEyLCB4MTMgPSBqMTMsIHgxNCA9IGoxNCxcbiAgICAgIHgxNSA9IGoxNSwgdTtcblxuICBmb3IgKHZhciBpID0gMDsgaSA8IDIwOyBpICs9IDIpIHtcbiAgICB1ID0geDAgKyB4MTIgfCAwO1xuICAgIHg0IF49IHU8PDcgfCB1Pj4+KDMyLTcpO1xuICAgIHUgPSB4NCArIHgwIHwgMDtcbiAgICB4OCBePSB1PDw5IHwgdT4+PigzMi05KTtcbiAgICB1ID0geDggKyB4NCB8IDA7XG4gICAgeDEyIF49IHU8PDEzIHwgdT4+PigzMi0xMyk7XG4gICAgdSA9IHgxMiArIHg4IHwgMDtcbiAgICB4MCBePSB1PDwxOCB8IHU+Pj4oMzItMTgpO1xuXG4gICAgdSA9IHg1ICsgeDEgfCAwO1xuICAgIHg5IF49IHU8PDcgfCB1Pj4+KDMyLTcpO1xuICAgIHUgPSB4OSArIHg1IHwgMDtcbiAgICB4MTMgXj0gdTw8OSB8IHU+Pj4oMzItOSk7XG4gICAgdSA9IHgxMyArIHg5IHwgMDtcbiAgICB4MSBePSB1PDwxMyB8IHU+Pj4oMzItMTMpO1xuICAgIHUgPSB4MSArIHgxMyB8IDA7XG4gICAgeDUgXj0gdTw8MTggfCB1Pj4+KDMyLTE4KTtcblxuICAgIHUgPSB4MTAgKyB4NiB8IDA7XG4gICAgeDE0IF49IHU8PDcgfCB1Pj4+KDMyLTcpO1xuICAgIHUgPSB4MTQgKyB4MTAgfCAwO1xuICAgIHgyIF49IHU8PDkgfCB1Pj4+KDMyLTkpO1xuICAgIHUgPSB4MiArIHgxNCB8IDA7XG4gICAgeDYgXj0gdTw8MTMgfCB1Pj4+KDMyLTEzKTtcbiAgICB1ID0geDYgKyB4MiB8IDA7XG4gICAgeDEwIF49IHU8PDE4IHwgdT4+PigzMi0xOCk7XG5cbiAgICB1ID0geDE1ICsgeDExIHwgMDtcbiAgICB4MyBePSB1PDw3IHwgdT4+PigzMi03KTtcbiAgICB1ID0geDMgKyB4MTUgfCAwO1xuICAgIHg3IF49IHU8PDkgfCB1Pj4+KDMyLTkpO1xuICAgIHUgPSB4NyArIHgzIHwgMDtcbiAgICB4MTEgXj0gdTw8MTMgfCB1Pj4+KDMyLTEzKTtcbiAgICB1ID0geDExICsgeDcgfCAwO1xuICAgIHgxNSBePSB1PDwxOCB8IHU+Pj4oMzItMTgpO1xuXG4gICAgdSA9IHgwICsgeDMgfCAwO1xuICAgIHgxIF49IHU8PDcgfCB1Pj4+KDMyLTcpO1xuICAgIHUgPSB4MSArIHgwIHwgMDtcbiAgICB4MiBePSB1PDw5IHwgdT4+PigzMi05KTtcbiAgICB1ID0geDIgKyB4MSB8IDA7XG4gICAgeDMgXj0gdTw8MTMgfCB1Pj4+KDMyLTEzKTtcbiAgICB1ID0geDMgKyB4MiB8IDA7XG4gICAgeDAgXj0gdTw8MTggfCB1Pj4+KDMyLTE4KTtcblxuICAgIHUgPSB4NSArIHg0IHwgMDtcbiAgICB4NiBePSB1PDw3IHwgdT4+PigzMi03KTtcbiAgICB1ID0geDYgKyB4NSB8IDA7XG4gICAgeDcgXj0gdTw8OSB8IHU+Pj4oMzItOSk7XG4gICAgdSA9IHg3ICsgeDYgfCAwO1xuICAgIHg0IF49IHU8PDEzIHwgdT4+PigzMi0xMyk7XG4gICAgdSA9IHg0ICsgeDcgfCAwO1xuICAgIHg1IF49IHU8PDE4IHwgdT4+PigzMi0xOCk7XG5cbiAgICB1ID0geDEwICsgeDkgfCAwO1xuICAgIHgxMSBePSB1PDw3IHwgdT4+PigzMi03KTtcbiAgICB1ID0geDExICsgeDEwIHwgMDtcbiAgICB4OCBePSB1PDw5IHwgdT4+PigzMi05KTtcbiAgICB1ID0geDggKyB4MTEgfCAwO1xuICAgIHg5IF49IHU8PDEzIHwgdT4+PigzMi0xMyk7XG4gICAgdSA9IHg5ICsgeDggfCAwO1xuICAgIHgxMCBePSB1PDwxOCB8IHU+Pj4oMzItMTgpO1xuXG4gICAgdSA9IHgxNSArIHgxNCB8IDA7XG4gICAgeDEyIF49IHU8PDcgfCB1Pj4+KDMyLTcpO1xuICAgIHUgPSB4MTIgKyB4MTUgfCAwO1xuICAgIHgxMyBePSB1PDw5IHwgdT4+PigzMi05KTtcbiAgICB1ID0geDEzICsgeDEyIHwgMDtcbiAgICB4MTQgXj0gdTw8MTMgfCB1Pj4+KDMyLTEzKTtcbiAgICB1ID0geDE0ICsgeDEzIHwgMDtcbiAgICB4MTUgXj0gdTw8MTggfCB1Pj4+KDMyLTE4KTtcbiAgfVxuXG4gIG9bIDBdID0geDAgPj4+ICAwICYgMHhmZjtcbiAgb1sgMV0gPSB4MCA+Pj4gIDggJiAweGZmO1xuICBvWyAyXSA9IHgwID4+PiAxNiAmIDB4ZmY7XG4gIG9bIDNdID0geDAgPj4+IDI0ICYgMHhmZjtcblxuICBvWyA0XSA9IHg1ID4+PiAgMCAmIDB4ZmY7XG4gIG9bIDVdID0geDUgPj4+ICA4ICYgMHhmZjtcbiAgb1sgNl0gPSB4NSA+Pj4gMTYgJiAweGZmO1xuICBvWyA3XSA9IHg1ID4+PiAyNCAmIDB4ZmY7XG5cbiAgb1sgOF0gPSB4MTAgPj4+ICAwICYgMHhmZjtcbiAgb1sgOV0gPSB4MTAgPj4+ICA4ICYgMHhmZjtcbiAgb1sxMF0gPSB4MTAgPj4+IDE2ICYgMHhmZjtcbiAgb1sxMV0gPSB4MTAgPj4+IDI0ICYgMHhmZjtcblxuICBvWzEyXSA9IHgxNSA+Pj4gIDAgJiAweGZmO1xuICBvWzEzXSA9IHgxNSA+Pj4gIDggJiAweGZmO1xuICBvWzE0XSA9IHgxNSA+Pj4gMTYgJiAweGZmO1xuICBvWzE1XSA9IHgxNSA+Pj4gMjQgJiAweGZmO1xuXG4gIG9bMTZdID0geDYgPj4+ICAwICYgMHhmZjtcbiAgb1sxN10gPSB4NiA+Pj4gIDggJiAweGZmO1xuICBvWzE4XSA9IHg2ID4+PiAxNiAmIDB4ZmY7XG4gIG9bMTldID0geDYgPj4+IDI0ICYgMHhmZjtcblxuICBvWzIwXSA9IHg3ID4+PiAgMCAmIDB4ZmY7XG4gIG9bMjFdID0geDcgPj4+ICA4ICYgMHhmZjtcbiAgb1syMl0gPSB4NyA+Pj4gMTYgJiAweGZmO1xuICBvWzIzXSA9IHg3ID4+PiAyNCAmIDB4ZmY7XG5cbiAgb1syNF0gPSB4OCA+Pj4gIDAgJiAweGZmO1xuICBvWzI1XSA9IHg4ID4+PiAgOCAmIDB4ZmY7XG4gIG9bMjZdID0geDggPj4+IDE2ICYgMHhmZjtcbiAgb1syN10gPSB4OCA+Pj4gMjQgJiAweGZmO1xuXG4gIG9bMjhdID0geDkgPj4+ICAwICYgMHhmZjtcbiAgb1syOV0gPSB4OSA+Pj4gIDggJiAweGZmO1xuICBvWzMwXSA9IHg5ID4+PiAxNiAmIDB4ZmY7XG4gIG9bMzFdID0geDkgPj4+IDI0ICYgMHhmZjtcbn1cblxuZnVuY3Rpb24gY3J5cHRvX2NvcmVfc2Fsc2EyMChvdXQsaW5wLGssYykge1xuICBjb3JlX3NhbHNhMjAob3V0LGlucCxrLGMpO1xufVxuXG5mdW5jdGlvbiBjcnlwdG9fY29yZV9oc2Fsc2EyMChvdXQsaW5wLGssYykge1xuICBjb3JlX2hzYWxzYTIwKG91dCxpbnAsayxjKTtcbn1cblxudmFyIHNpZ21hID0gbmV3IFVpbnQ4QXJyYXkoWzEwMSwgMTIwLCAxMTIsIDk3LCAxMTAsIDEwMCwgMzIsIDUxLCA1MCwgNDUsIDk4LCAxMjEsIDExNiwgMTAxLCAzMiwgMTA3XSk7XG4gICAgICAgICAgICAvLyBcImV4cGFuZCAzMi1ieXRlIGtcIlxuXG5mdW5jdGlvbiBjcnlwdG9fc3RyZWFtX3NhbHNhMjBfeG9yKGMsY3BvcyxtLG1wb3MsYixuLGspIHtcbiAgdmFyIHogPSBuZXcgVWludDhBcnJheSgxNiksIHggPSBuZXcgVWludDhBcnJheSg2NCk7XG4gIHZhciB1LCBpO1xuICBmb3IgKGkgPSAwOyBpIDwgMTY7IGkrKykgeltpXSA9IDA7XG4gIGZvciAoaSA9IDA7IGkgPCA4OyBpKyspIHpbaV0gPSBuW2ldO1xuICB3aGlsZSAoYiA+PSA2NCkge1xuICAgIGNyeXB0b19jb3JlX3NhbHNhMjAoeCx6LGssc2lnbWEpO1xuICAgIGZvciAoaSA9IDA7IGkgPCA2NDsgaSsrKSBjW2Nwb3MraV0gPSBtW21wb3MraV0gXiB4W2ldO1xuICAgIHUgPSAxO1xuICAgIGZvciAoaSA9IDg7IGkgPCAxNjsgaSsrKSB7XG4gICAgICB1ID0gdSArICh6W2ldICYgMHhmZikgfCAwO1xuICAgICAgeltpXSA9IHUgJiAweGZmO1xuICAgICAgdSA+Pj49IDg7XG4gICAgfVxuICAgIGIgLT0gNjQ7XG4gICAgY3BvcyArPSA2NDtcbiAgICBtcG9zICs9IDY0O1xuICB9XG4gIGlmIChiID4gMCkge1xuICAgIGNyeXB0b19jb3JlX3NhbHNhMjAoeCx6LGssc2lnbWEpO1xuICAgIGZvciAoaSA9IDA7IGkgPCBiOyBpKyspIGNbY3BvcytpXSA9IG1bbXBvcytpXSBeIHhbaV07XG4gIH1cbiAgcmV0dXJuIDA7XG59XG5cbmZ1bmN0aW9uIGNyeXB0b19zdHJlYW1fc2Fsc2EyMChjLGNwb3MsYixuLGspIHtcbiAgdmFyIHogPSBuZXcgVWludDhBcnJheSgxNiksIHggPSBuZXcgVWludDhBcnJheSg2NCk7XG4gIHZhciB1LCBpO1xuICBmb3IgKGkgPSAwOyBpIDwgMTY7IGkrKykgeltpXSA9IDA7XG4gIGZvciAoaSA9IDA7IGkgPCA4OyBpKyspIHpbaV0gPSBuW2ldO1xuICB3aGlsZSAoYiA+PSA2NCkge1xuICAgIGNyeXB0b19jb3JlX3NhbHNhMjAoeCx6LGssc2lnbWEpO1xuICAgIGZvciAoaSA9IDA7IGkgPCA2NDsgaSsrKSBjW2Nwb3MraV0gPSB4W2ldO1xuICAgIHUgPSAxO1xuICAgIGZvciAoaSA9IDg7IGkgPCAxNjsgaSsrKSB7XG4gICAgICB1ID0gdSArICh6W2ldICYgMHhmZikgfCAwO1xuICAgICAgeltpXSA9IHUgJiAweGZmO1xuICAgICAgdSA+Pj49IDg7XG4gICAgfVxuICAgIGIgLT0gNjQ7XG4gICAgY3BvcyArPSA2NDtcbiAgfVxuICBpZiAoYiA+IDApIHtcbiAgICBjcnlwdG9fY29yZV9zYWxzYTIwKHgseixrLHNpZ21hKTtcbiAgICBmb3IgKGkgPSAwOyBpIDwgYjsgaSsrKSBjW2Nwb3MraV0gPSB4W2ldO1xuICB9XG4gIHJldHVybiAwO1xufVxuXG5mdW5jdGlvbiBjcnlwdG9fc3RyZWFtKGMsY3BvcyxkLG4saykge1xuICB2YXIgcyA9IG5ldyBVaW50OEFycmF5KDMyKTtcbiAgY3J5cHRvX2NvcmVfaHNhbHNhMjAocyxuLGssc2lnbWEpO1xuICB2YXIgc24gPSBuZXcgVWludDhBcnJheSg4KTtcbiAgZm9yICh2YXIgaSA9IDA7IGkgPCA4OyBpKyspIHNuW2ldID0gbltpKzE2XTtcbiAgcmV0dXJuIGNyeXB0b19zdHJlYW1fc2Fsc2EyMChjLGNwb3MsZCxzbixzKTtcbn1cblxuZnVuY3Rpb24gY3J5cHRvX3N0cmVhbV94b3IoYyxjcG9zLG0sbXBvcyxkLG4saykge1xuICB2YXIgcyA9IG5ldyBVaW50OEFycmF5KDMyKTtcbiAgY3J5cHRvX2NvcmVfaHNhbHNhMjAocyxuLGssc2lnbWEpO1xuICB2YXIgc24gPSBuZXcgVWludDhBcnJheSg4KTtcbiAgZm9yICh2YXIgaSA9IDA7IGkgPCA4OyBpKyspIHNuW2ldID0gbltpKzE2XTtcbiAgcmV0dXJuIGNyeXB0b19zdHJlYW1fc2Fsc2EyMF94b3IoYyxjcG9zLG0sbXBvcyxkLHNuLHMpO1xufVxuXG4vKlxuKiBQb3J0IG9mIEFuZHJldyBNb29uJ3MgUG9seTEzMDUtZG9ubmEtMTYuIFB1YmxpYyBkb21haW4uXG4qIGh0dHBzOi8vZ2l0aHViLmNvbS9mbG9vZHliZXJyeS9wb2x5MTMwNS1kb25uYVxuKi9cblxudmFyIHBvbHkxMzA1ID0gZnVuY3Rpb24oa2V5KSB7XG4gIHRoaXMuYnVmZmVyID0gbmV3IFVpbnQ4QXJyYXkoMTYpO1xuICB0aGlzLnIgPSBuZXcgVWludDE2QXJyYXkoMTApO1xuICB0aGlzLmggPSBuZXcgVWludDE2QXJyYXkoMTApO1xuICB0aGlzLnBhZCA9IG5ldyBVaW50MTZBcnJheSg4KTtcbiAgdGhpcy5sZWZ0b3ZlciA9IDA7XG4gIHRoaXMuZmluID0gMDtcblxuICB2YXIgdDAsIHQxLCB0MiwgdDMsIHQ0LCB0NSwgdDYsIHQ3O1xuXG4gIHQwID0ga2V5WyAwXSAmIDB4ZmYgfCAoa2V5WyAxXSAmIDB4ZmYpIDw8IDg7IHRoaXMuclswXSA9ICggdDAgICAgICAgICAgICAgICAgICAgICApICYgMHgxZmZmO1xuICB0MSA9IGtleVsgMl0gJiAweGZmIHwgKGtleVsgM10gJiAweGZmKSA8PCA4OyB0aGlzLnJbMV0gPSAoKHQwID4+PiAxMykgfCAodDEgPDwgIDMpKSAmIDB4MWZmZjtcbiAgdDIgPSBrZXlbIDRdICYgMHhmZiB8IChrZXlbIDVdICYgMHhmZikgPDwgODsgdGhpcy5yWzJdID0gKCh0MSA+Pj4gMTApIHwgKHQyIDw8ICA2KSkgJiAweDFmMDM7XG4gIHQzID0ga2V5WyA2XSAmIDB4ZmYgfCAoa2V5WyA3XSAmIDB4ZmYpIDw8IDg7IHRoaXMuclszXSA9ICgodDIgPj4+ICA3KSB8ICh0MyA8PCAgOSkpICYgMHgxZmZmO1xuICB0NCA9IGtleVsgOF0gJiAweGZmIHwgKGtleVsgOV0gJiAweGZmKSA8PCA4OyB0aGlzLnJbNF0gPSAoKHQzID4+PiAgNCkgfCAodDQgPDwgMTIpKSAmIDB4MDBmZjtcbiAgdGhpcy5yWzVdID0gKCh0NCA+Pj4gIDEpKSAmIDB4MWZmZTtcbiAgdDUgPSBrZXlbMTBdICYgMHhmZiB8IChrZXlbMTFdICYgMHhmZikgPDwgODsgdGhpcy5yWzZdID0gKCh0NCA+Pj4gMTQpIHwgKHQ1IDw8ICAyKSkgJiAweDFmZmY7XG4gIHQ2ID0ga2V5WzEyXSAmIDB4ZmYgfCAoa2V5WzEzXSAmIDB4ZmYpIDw8IDg7IHRoaXMucls3XSA9ICgodDUgPj4+IDExKSB8ICh0NiA8PCAgNSkpICYgMHgxZjgxO1xuICB0NyA9IGtleVsxNF0gJiAweGZmIHwgKGtleVsxNV0gJiAweGZmKSA8PCA4OyB0aGlzLnJbOF0gPSAoKHQ2ID4+PiAgOCkgfCAodDcgPDwgIDgpKSAmIDB4MWZmZjtcbiAgdGhpcy5yWzldID0gKCh0NyA+Pj4gIDUpKSAmIDB4MDA3ZjtcblxuICB0aGlzLnBhZFswXSA9IGtleVsxNl0gJiAweGZmIHwgKGtleVsxN10gJiAweGZmKSA8PCA4O1xuICB0aGlzLnBhZFsxXSA9IGtleVsxOF0gJiAweGZmIHwgKGtleVsxOV0gJiAweGZmKSA8PCA4O1xuICB0aGlzLnBhZFsyXSA9IGtleVsyMF0gJiAweGZmIHwgKGtleVsyMV0gJiAweGZmKSA8PCA4O1xuICB0aGlzLnBhZFszXSA9IGtleVsyMl0gJiAweGZmIHwgKGtleVsyM10gJiAweGZmKSA8PCA4O1xuICB0aGlzLnBhZFs0XSA9IGtleVsyNF0gJiAweGZmIHwgKGtleVsyNV0gJiAweGZmKSA8PCA4O1xuICB0aGlzLnBhZFs1XSA9IGtleVsyNl0gJiAweGZmIHwgKGtleVsyN10gJiAweGZmKSA8PCA4O1xuICB0aGlzLnBhZFs2XSA9IGtleVsyOF0gJiAweGZmIHwgKGtleVsyOV0gJiAweGZmKSA8PCA4O1xuICB0aGlzLnBhZFs3XSA9IGtleVszMF0gJiAweGZmIHwgKGtleVszMV0gJiAweGZmKSA8PCA4O1xufTtcblxucG9seTEzMDUucHJvdG90eXBlLmJsb2NrcyA9IGZ1bmN0aW9uKG0sIG1wb3MsIGJ5dGVzKSB7XG4gIHZhciBoaWJpdCA9IHRoaXMuZmluID8gMCA6ICgxIDw8IDExKTtcbiAgdmFyIHQwLCB0MSwgdDIsIHQzLCB0NCwgdDUsIHQ2LCB0NywgYztcbiAgdmFyIGQwLCBkMSwgZDIsIGQzLCBkNCwgZDUsIGQ2LCBkNywgZDgsIGQ5O1xuXG4gIHZhciBoMCA9IHRoaXMuaFswXSxcbiAgICAgIGgxID0gdGhpcy5oWzFdLFxuICAgICAgaDIgPSB0aGlzLmhbMl0sXG4gICAgICBoMyA9IHRoaXMuaFszXSxcbiAgICAgIGg0ID0gdGhpcy5oWzRdLFxuICAgICAgaDUgPSB0aGlzLmhbNV0sXG4gICAgICBoNiA9IHRoaXMuaFs2XSxcbiAgICAgIGg3ID0gdGhpcy5oWzddLFxuICAgICAgaDggPSB0aGlzLmhbOF0sXG4gICAgICBoOSA9IHRoaXMuaFs5XTtcblxuICB2YXIgcjAgPSB0aGlzLnJbMF0sXG4gICAgICByMSA9IHRoaXMuclsxXSxcbiAgICAgIHIyID0gdGhpcy5yWzJdLFxuICAgICAgcjMgPSB0aGlzLnJbM10sXG4gICAgICByNCA9IHRoaXMucls0XSxcbiAgICAgIHI1ID0gdGhpcy5yWzVdLFxuICAgICAgcjYgPSB0aGlzLnJbNl0sXG4gICAgICByNyA9IHRoaXMucls3XSxcbiAgICAgIHI4ID0gdGhpcy5yWzhdLFxuICAgICAgcjkgPSB0aGlzLnJbOV07XG5cbiAgd2hpbGUgKGJ5dGVzID49IDE2KSB7XG4gICAgdDAgPSBtW21wb3MrIDBdICYgMHhmZiB8IChtW21wb3MrIDFdICYgMHhmZikgPDwgODsgaDAgKz0gKCB0MCAgICAgICAgICAgICAgICAgICAgICkgJiAweDFmZmY7XG4gICAgdDEgPSBtW21wb3MrIDJdICYgMHhmZiB8IChtW21wb3MrIDNdICYgMHhmZikgPDwgODsgaDEgKz0gKCh0MCA+Pj4gMTMpIHwgKHQxIDw8ICAzKSkgJiAweDFmZmY7XG4gICAgdDIgPSBtW21wb3MrIDRdICYgMHhmZiB8IChtW21wb3MrIDVdICYgMHhmZikgPDwgODsgaDIgKz0gKCh0MSA+Pj4gMTApIHwgKHQyIDw8ICA2KSkgJiAweDFmZmY7XG4gICAgdDMgPSBtW21wb3MrIDZdICYgMHhmZiB8IChtW21wb3MrIDddICYgMHhmZikgPDwgODsgaDMgKz0gKCh0MiA+Pj4gIDcpIHwgKHQzIDw8ICA5KSkgJiAweDFmZmY7XG4gICAgdDQgPSBtW21wb3MrIDhdICYgMHhmZiB8IChtW21wb3MrIDldICYgMHhmZikgPDwgODsgaDQgKz0gKCh0MyA+Pj4gIDQpIHwgKHQ0IDw8IDEyKSkgJiAweDFmZmY7XG4gICAgaDUgKz0gKCh0NCA+Pj4gIDEpKSAmIDB4MWZmZjtcbiAgICB0NSA9IG1bbXBvcysxMF0gJiAweGZmIHwgKG1bbXBvcysxMV0gJiAweGZmKSA8PCA4OyBoNiArPSAoKHQ0ID4+PiAxNCkgfCAodDUgPDwgIDIpKSAmIDB4MWZmZjtcbiAgICB0NiA9IG1bbXBvcysxMl0gJiAweGZmIHwgKG1bbXBvcysxM10gJiAweGZmKSA8PCA4OyBoNyArPSAoKHQ1ID4+PiAxMSkgfCAodDYgPDwgIDUpKSAmIDB4MWZmZjtcbiAgICB0NyA9IG1bbXBvcysxNF0gJiAweGZmIHwgKG1bbXBvcysxNV0gJiAweGZmKSA8PCA4OyBoOCArPSAoKHQ2ID4+PiAgOCkgfCAodDcgPDwgIDgpKSAmIDB4MWZmZjtcbiAgICBoOSArPSAoKHQ3ID4+PiA1KSkgfCBoaWJpdDtcblxuICAgIGMgPSAwO1xuXG4gICAgZDAgPSBjO1xuICAgIGQwICs9IGgwICogcjA7XG4gICAgZDAgKz0gaDEgKiAoNSAqIHI5KTtcbiAgICBkMCArPSBoMiAqICg1ICogcjgpO1xuICAgIGQwICs9IGgzICogKDUgKiByNyk7XG4gICAgZDAgKz0gaDQgKiAoNSAqIHI2KTtcbiAgICBjID0gKGQwID4+PiAxMyk7IGQwICY9IDB4MWZmZjtcbiAgICBkMCArPSBoNSAqICg1ICogcjUpO1xuICAgIGQwICs9IGg2ICogKDUgKiByNCk7XG4gICAgZDAgKz0gaDcgKiAoNSAqIHIzKTtcbiAgICBkMCArPSBoOCAqICg1ICogcjIpO1xuICAgIGQwICs9IGg5ICogKDUgKiByMSk7XG4gICAgYyArPSAoZDAgPj4+IDEzKTsgZDAgJj0gMHgxZmZmO1xuXG4gICAgZDEgPSBjO1xuICAgIGQxICs9IGgwICogcjE7XG4gICAgZDEgKz0gaDEgKiByMDtcbiAgICBkMSArPSBoMiAqICg1ICogcjkpO1xuICAgIGQxICs9IGgzICogKDUgKiByOCk7XG4gICAgZDEgKz0gaDQgKiAoNSAqIHI3KTtcbiAgICBjID0gKGQxID4+PiAxMyk7IGQxICY9IDB4MWZmZjtcbiAgICBkMSArPSBoNSAqICg1ICogcjYpO1xuICAgIGQxICs9IGg2ICogKDUgKiByNSk7XG4gICAgZDEgKz0gaDcgKiAoNSAqIHI0KTtcbiAgICBkMSArPSBoOCAqICg1ICogcjMpO1xuICAgIGQxICs9IGg5ICogKDUgKiByMik7XG4gICAgYyArPSAoZDEgPj4+IDEzKTsgZDEgJj0gMHgxZmZmO1xuXG4gICAgZDIgPSBjO1xuICAgIGQyICs9IGgwICogcjI7XG4gICAgZDIgKz0gaDEgKiByMTtcbiAgICBkMiArPSBoMiAqIHIwO1xuICAgIGQyICs9IGgzICogKDUgKiByOSk7XG4gICAgZDIgKz0gaDQgKiAoNSAqIHI4KTtcbiAgICBjID0gKGQyID4+PiAxMyk7IGQyICY9IDB4MWZmZjtcbiAgICBkMiArPSBoNSAqICg1ICogcjcpO1xuICAgIGQyICs9IGg2ICogKDUgKiByNik7XG4gICAgZDIgKz0gaDcgKiAoNSAqIHI1KTtcbiAgICBkMiArPSBoOCAqICg1ICogcjQpO1xuICAgIGQyICs9IGg5ICogKDUgKiByMyk7XG4gICAgYyArPSAoZDIgPj4+IDEzKTsgZDIgJj0gMHgxZmZmO1xuXG4gICAgZDMgPSBjO1xuICAgIGQzICs9IGgwICogcjM7XG4gICAgZDMgKz0gaDEgKiByMjtcbiAgICBkMyArPSBoMiAqIHIxO1xuICAgIGQzICs9IGgzICogcjA7XG4gICAgZDMgKz0gaDQgKiAoNSAqIHI5KTtcbiAgICBjID0gKGQzID4+PiAxMyk7IGQzICY9IDB4MWZmZjtcbiAgICBkMyArPSBoNSAqICg1ICogcjgpO1xuICAgIGQzICs9IGg2ICogKDUgKiByNyk7XG4gICAgZDMgKz0gaDcgKiAoNSAqIHI2KTtcbiAgICBkMyArPSBoOCAqICg1ICogcjUpO1xuICAgIGQzICs9IGg5ICogKDUgKiByNCk7XG4gICAgYyArPSAoZDMgPj4+IDEzKTsgZDMgJj0gMHgxZmZmO1xuXG4gICAgZDQgPSBjO1xuICAgIGQ0ICs9IGgwICogcjQ7XG4gICAgZDQgKz0gaDEgKiByMztcbiAgICBkNCArPSBoMiAqIHIyO1xuICAgIGQ0ICs9IGgzICogcjE7XG4gICAgZDQgKz0gaDQgKiByMDtcbiAgICBjID0gKGQ0ID4+PiAxMyk7IGQ0ICY9IDB4MWZmZjtcbiAgICBkNCArPSBoNSAqICg1ICogcjkpO1xuICAgIGQ0ICs9IGg2ICogKDUgKiByOCk7XG4gICAgZDQgKz0gaDcgKiAoNSAqIHI3KTtcbiAgICBkNCArPSBoOCAqICg1ICogcjYpO1xuICAgIGQ0ICs9IGg5ICogKDUgKiByNSk7XG4gICAgYyArPSAoZDQgPj4+IDEzKTsgZDQgJj0gMHgxZmZmO1xuXG4gICAgZDUgPSBjO1xuICAgIGQ1ICs9IGgwICogcjU7XG4gICAgZDUgKz0gaDEgKiByNDtcbiAgICBkNSArPSBoMiAqIHIzO1xuICAgIGQ1ICs9IGgzICogcjI7XG4gICAgZDUgKz0gaDQgKiByMTtcbiAgICBjID0gKGQ1ID4+PiAxMyk7IGQ1ICY9IDB4MWZmZjtcbiAgICBkNSArPSBoNSAqIHIwO1xuICAgIGQ1ICs9IGg2ICogKDUgKiByOSk7XG4gICAgZDUgKz0gaDcgKiAoNSAqIHI4KTtcbiAgICBkNSArPSBoOCAqICg1ICogcjcpO1xuICAgIGQ1ICs9IGg5ICogKDUgKiByNik7XG4gICAgYyArPSAoZDUgPj4+IDEzKTsgZDUgJj0gMHgxZmZmO1xuXG4gICAgZDYgPSBjO1xuICAgIGQ2ICs9IGgwICogcjY7XG4gICAgZDYgKz0gaDEgKiByNTtcbiAgICBkNiArPSBoMiAqIHI0O1xuICAgIGQ2ICs9IGgzICogcjM7XG4gICAgZDYgKz0gaDQgKiByMjtcbiAgICBjID0gKGQ2ID4+PiAxMyk7IGQ2ICY9IDB4MWZmZjtcbiAgICBkNiArPSBoNSAqIHIxO1xuICAgIGQ2ICs9IGg2ICogcjA7XG4gICAgZDYgKz0gaDcgKiAoNSAqIHI5KTtcbiAgICBkNiArPSBoOCAqICg1ICogcjgpO1xuICAgIGQ2ICs9IGg5ICogKDUgKiByNyk7XG4gICAgYyArPSAoZDYgPj4+IDEzKTsgZDYgJj0gMHgxZmZmO1xuXG4gICAgZDcgPSBjO1xuICAgIGQ3ICs9IGgwICogcjc7XG4gICAgZDcgKz0gaDEgKiByNjtcbiAgICBkNyArPSBoMiAqIHI1O1xuICAgIGQ3ICs9IGgzICogcjQ7XG4gICAgZDcgKz0gaDQgKiByMztcbiAgICBjID0gKGQ3ID4+PiAxMyk7IGQ3ICY9IDB4MWZmZjtcbiAgICBkNyArPSBoNSAqIHIyO1xuICAgIGQ3ICs9IGg2ICogcjE7XG4gICAgZDcgKz0gaDcgKiByMDtcbiAgICBkNyArPSBoOCAqICg1ICogcjkpO1xuICAgIGQ3ICs9IGg5ICogKDUgKiByOCk7XG4gICAgYyArPSAoZDcgPj4+IDEzKTsgZDcgJj0gMHgxZmZmO1xuXG4gICAgZDggPSBjO1xuICAgIGQ4ICs9IGgwICogcjg7XG4gICAgZDggKz0gaDEgKiByNztcbiAgICBkOCArPSBoMiAqIHI2O1xuICAgIGQ4ICs9IGgzICogcjU7XG4gICAgZDggKz0gaDQgKiByNDtcbiAgICBjID0gKGQ4ID4+PiAxMyk7IGQ4ICY9IDB4MWZmZjtcbiAgICBkOCArPSBoNSAqIHIzO1xuICAgIGQ4ICs9IGg2ICogcjI7XG4gICAgZDggKz0gaDcgKiByMTtcbiAgICBkOCArPSBoOCAqIHIwO1xuICAgIGQ4ICs9IGg5ICogKDUgKiByOSk7XG4gICAgYyArPSAoZDggPj4+IDEzKTsgZDggJj0gMHgxZmZmO1xuXG4gICAgZDkgPSBjO1xuICAgIGQ5ICs9IGgwICogcjk7XG4gICAgZDkgKz0gaDEgKiByODtcbiAgICBkOSArPSBoMiAqIHI3O1xuICAgIGQ5ICs9IGgzICogcjY7XG4gICAgZDkgKz0gaDQgKiByNTtcbiAgICBjID0gKGQ5ID4+PiAxMyk7IGQ5ICY9IDB4MWZmZjtcbiAgICBkOSArPSBoNSAqIHI0O1xuICAgIGQ5ICs9IGg2ICogcjM7XG4gICAgZDkgKz0gaDcgKiByMjtcbiAgICBkOSArPSBoOCAqIHIxO1xuICAgIGQ5ICs9IGg5ICogcjA7XG4gICAgYyArPSAoZDkgPj4+IDEzKTsgZDkgJj0gMHgxZmZmO1xuXG4gICAgYyA9ICgoKGMgPDwgMikgKyBjKSkgfCAwO1xuICAgIGMgPSAoYyArIGQwKSB8IDA7XG4gICAgZDAgPSBjICYgMHgxZmZmO1xuICAgIGMgPSAoYyA+Pj4gMTMpO1xuICAgIGQxICs9IGM7XG5cbiAgICBoMCA9IGQwO1xuICAgIGgxID0gZDE7XG4gICAgaDIgPSBkMjtcbiAgICBoMyA9IGQzO1xuICAgIGg0ID0gZDQ7XG4gICAgaDUgPSBkNTtcbiAgICBoNiA9IGQ2O1xuICAgIGg3ID0gZDc7XG4gICAgaDggPSBkODtcbiAgICBoOSA9IGQ5O1xuXG4gICAgbXBvcyArPSAxNjtcbiAgICBieXRlcyAtPSAxNjtcbiAgfVxuICB0aGlzLmhbMF0gPSBoMDtcbiAgdGhpcy5oWzFdID0gaDE7XG4gIHRoaXMuaFsyXSA9IGgyO1xuICB0aGlzLmhbM10gPSBoMztcbiAgdGhpcy5oWzRdID0gaDQ7XG4gIHRoaXMuaFs1XSA9IGg1O1xuICB0aGlzLmhbNl0gPSBoNjtcbiAgdGhpcy5oWzddID0gaDc7XG4gIHRoaXMuaFs4XSA9IGg4O1xuICB0aGlzLmhbOV0gPSBoOTtcbn07XG5cbnBvbHkxMzA1LnByb3RvdHlwZS5maW5pc2ggPSBmdW5jdGlvbihtYWMsIG1hY3Bvcykge1xuICB2YXIgZyA9IG5ldyBVaW50MTZBcnJheSgxMCk7XG4gIHZhciBjLCBtYXNrLCBmLCBpO1xuXG4gIGlmICh0aGlzLmxlZnRvdmVyKSB7XG4gICAgaSA9IHRoaXMubGVmdG92ZXI7XG4gICAgdGhpcy5idWZmZXJbaSsrXSA9IDE7XG4gICAgZm9yICg7IGkgPCAxNjsgaSsrKSB0aGlzLmJ1ZmZlcltpXSA9IDA7XG4gICAgdGhpcy5maW4gPSAxO1xuICAgIHRoaXMuYmxvY2tzKHRoaXMuYnVmZmVyLCAwLCAxNik7XG4gIH1cblxuICBjID0gdGhpcy5oWzFdID4+PiAxMztcbiAgdGhpcy5oWzFdICY9IDB4MWZmZjtcbiAgZm9yIChpID0gMjsgaSA8IDEwOyBpKyspIHtcbiAgICB0aGlzLmhbaV0gKz0gYztcbiAgICBjID0gdGhpcy5oW2ldID4+PiAxMztcbiAgICB0aGlzLmhbaV0gJj0gMHgxZmZmO1xuICB9XG4gIHRoaXMuaFswXSArPSAoYyAqIDUpO1xuICBjID0gdGhpcy5oWzBdID4+PiAxMztcbiAgdGhpcy5oWzBdICY9IDB4MWZmZjtcbiAgdGhpcy5oWzFdICs9IGM7XG4gIGMgPSB0aGlzLmhbMV0gPj4+IDEzO1xuICB0aGlzLmhbMV0gJj0gMHgxZmZmO1xuICB0aGlzLmhbMl0gKz0gYztcblxuICBnWzBdID0gdGhpcy5oWzBdICsgNTtcbiAgYyA9IGdbMF0gPj4+IDEzO1xuICBnWzBdICY9IDB4MWZmZjtcbiAgZm9yIChpID0gMTsgaSA8IDEwOyBpKyspIHtcbiAgICBnW2ldID0gdGhpcy5oW2ldICsgYztcbiAgICBjID0gZ1tpXSA+Pj4gMTM7XG4gICAgZ1tpXSAmPSAweDFmZmY7XG4gIH1cbiAgZ1s5XSAtPSAoMSA8PCAxMyk7XG5cbiAgbWFzayA9IChjIF4gMSkgLSAxO1xuICBmb3IgKGkgPSAwOyBpIDwgMTA7IGkrKykgZ1tpXSAmPSBtYXNrO1xuICBtYXNrID0gfm1hc2s7XG4gIGZvciAoaSA9IDA7IGkgPCAxMDsgaSsrKSB0aGlzLmhbaV0gPSAodGhpcy5oW2ldICYgbWFzaykgfCBnW2ldO1xuXG4gIHRoaXMuaFswXSA9ICgodGhpcy5oWzBdICAgICAgICkgfCAodGhpcy5oWzFdIDw8IDEzKSAgICAgICAgICAgICAgICAgICAgKSAmIDB4ZmZmZjtcbiAgdGhpcy5oWzFdID0gKCh0aGlzLmhbMV0gPj4+ICAzKSB8ICh0aGlzLmhbMl0gPDwgMTApICAgICAgICAgICAgICAgICAgICApICYgMHhmZmZmO1xuICB0aGlzLmhbMl0gPSAoKHRoaXMuaFsyXSA+Pj4gIDYpIHwgKHRoaXMuaFszXSA8PCAgNykgICAgICAgICAgICAgICAgICAgICkgJiAweGZmZmY7XG4gIHRoaXMuaFszXSA9ICgodGhpcy5oWzNdID4+PiAgOSkgfCAodGhpcy5oWzRdIDw8ICA0KSAgICAgICAgICAgICAgICAgICAgKSAmIDB4ZmZmZjtcbiAgdGhpcy5oWzRdID0gKCh0aGlzLmhbNF0gPj4+IDEyKSB8ICh0aGlzLmhbNV0gPDwgIDEpIHwgKHRoaXMuaFs2XSA8PCAxNCkpICYgMHhmZmZmO1xuICB0aGlzLmhbNV0gPSAoKHRoaXMuaFs2XSA+Pj4gIDIpIHwgKHRoaXMuaFs3XSA8PCAxMSkgICAgICAgICAgICAgICAgICAgICkgJiAweGZmZmY7XG4gIHRoaXMuaFs2XSA9ICgodGhpcy5oWzddID4+PiAgNSkgfCAodGhpcy5oWzhdIDw8ICA4KSAgICAgICAgICAgICAgICAgICAgKSAmIDB4ZmZmZjtcbiAgdGhpcy5oWzddID0gKCh0aGlzLmhbOF0gPj4+ICA4KSB8ICh0aGlzLmhbOV0gPDwgIDUpICAgICAgICAgICAgICAgICAgICApICYgMHhmZmZmO1xuXG4gIGYgPSB0aGlzLmhbMF0gKyB0aGlzLnBhZFswXTtcbiAgdGhpcy5oWzBdID0gZiAmIDB4ZmZmZjtcbiAgZm9yIChpID0gMTsgaSA8IDg7IGkrKykge1xuICAgIGYgPSAoKCh0aGlzLmhbaV0gKyB0aGlzLnBhZFtpXSkgfCAwKSArIChmID4+PiAxNikpIHwgMDtcbiAgICB0aGlzLmhbaV0gPSBmICYgMHhmZmZmO1xuICB9XG5cbiAgbWFjW21hY3BvcysgMF0gPSAodGhpcy5oWzBdID4+PiAwKSAmIDB4ZmY7XG4gIG1hY1ttYWNwb3MrIDFdID0gKHRoaXMuaFswXSA+Pj4gOCkgJiAweGZmO1xuICBtYWNbbWFjcG9zKyAyXSA9ICh0aGlzLmhbMV0gPj4+IDApICYgMHhmZjtcbiAgbWFjW21hY3BvcysgM10gPSAodGhpcy5oWzFdID4+PiA4KSAmIDB4ZmY7XG4gIG1hY1ttYWNwb3MrIDRdID0gKHRoaXMuaFsyXSA+Pj4gMCkgJiAweGZmO1xuICBtYWNbbWFjcG9zKyA1XSA9ICh0aGlzLmhbMl0gPj4+IDgpICYgMHhmZjtcbiAgbWFjW21hY3BvcysgNl0gPSAodGhpcy5oWzNdID4+PiAwKSAmIDB4ZmY7XG4gIG1hY1ttYWNwb3MrIDddID0gKHRoaXMuaFszXSA+Pj4gOCkgJiAweGZmO1xuICBtYWNbbWFjcG9zKyA4XSA9ICh0aGlzLmhbNF0gPj4+IDApICYgMHhmZjtcbiAgbWFjW21hY3BvcysgOV0gPSAodGhpcy5oWzRdID4+PiA4KSAmIDB4ZmY7XG4gIG1hY1ttYWNwb3MrMTBdID0gKHRoaXMuaFs1XSA+Pj4gMCkgJiAweGZmO1xuICBtYWNbbWFjcG9zKzExXSA9ICh0aGlzLmhbNV0gPj4+IDgpICYgMHhmZjtcbiAgbWFjW21hY3BvcysxMl0gPSAodGhpcy5oWzZdID4+PiAwKSAmIDB4ZmY7XG4gIG1hY1ttYWNwb3MrMTNdID0gKHRoaXMuaFs2XSA+Pj4gOCkgJiAweGZmO1xuICBtYWNbbWFjcG9zKzE0XSA9ICh0aGlzLmhbN10gPj4+IDApICYgMHhmZjtcbiAgbWFjW21hY3BvcysxNV0gPSAodGhpcy5oWzddID4+PiA4KSAmIDB4ZmY7XG59O1xuXG5wb2x5MTMwNS5wcm90b3R5cGUudXBkYXRlID0gZnVuY3Rpb24obSwgbXBvcywgYnl0ZXMpIHtcbiAgdmFyIGksIHdhbnQ7XG5cbiAgaWYgKHRoaXMubGVmdG92ZXIpIHtcbiAgICB3YW50ID0gKDE2IC0gdGhpcy5sZWZ0b3Zlcik7XG4gICAgaWYgKHdhbnQgPiBieXRlcylcbiAgICAgIHdhbnQgPSBieXRlcztcbiAgICBmb3IgKGkgPSAwOyBpIDwgd2FudDsgaSsrKVxuICAgICAgdGhpcy5idWZmZXJbdGhpcy5sZWZ0b3ZlciArIGldID0gbVttcG9zK2ldO1xuICAgIGJ5dGVzIC09IHdhbnQ7XG4gICAgbXBvcyArPSB3YW50O1xuICAgIHRoaXMubGVmdG92ZXIgKz0gd2FudDtcbiAgICBpZiAodGhpcy5sZWZ0b3ZlciA8IDE2KVxuICAgICAgcmV0dXJuO1xuICAgIHRoaXMuYmxvY2tzKHRoaXMuYnVmZmVyLCAwLCAxNik7XG4gICAgdGhpcy5sZWZ0b3ZlciA9IDA7XG4gIH1cblxuICBpZiAoYnl0ZXMgPj0gMTYpIHtcbiAgICB3YW50ID0gYnl0ZXMgLSAoYnl0ZXMgJSAxNik7XG4gICAgdGhpcy5ibG9ja3MobSwgbXBvcywgd2FudCk7XG4gICAgbXBvcyArPSB3YW50O1xuICAgIGJ5dGVzIC09IHdhbnQ7XG4gIH1cblxuICBpZiAoYnl0ZXMpIHtcbiAgICBmb3IgKGkgPSAwOyBpIDwgYnl0ZXM7IGkrKylcbiAgICAgIHRoaXMuYnVmZmVyW3RoaXMubGVmdG92ZXIgKyBpXSA9IG1bbXBvcytpXTtcbiAgICB0aGlzLmxlZnRvdmVyICs9IGJ5dGVzO1xuICB9XG59O1xuXG5mdW5jdGlvbiBjcnlwdG9fb25ldGltZWF1dGgob3V0LCBvdXRwb3MsIG0sIG1wb3MsIG4sIGspIHtcbiAgdmFyIHMgPSBuZXcgcG9seTEzMDUoayk7XG4gIHMudXBkYXRlKG0sIG1wb3MsIG4pO1xuICBzLmZpbmlzaChvdXQsIG91dHBvcyk7XG4gIHJldHVybiAwO1xufVxuXG5mdW5jdGlvbiBjcnlwdG9fb25ldGltZWF1dGhfdmVyaWZ5KGgsIGhwb3MsIG0sIG1wb3MsIG4sIGspIHtcbiAgdmFyIHggPSBuZXcgVWludDhBcnJheSgxNik7XG4gIGNyeXB0b19vbmV0aW1lYXV0aCh4LDAsbSxtcG9zLG4sayk7XG4gIHJldHVybiBjcnlwdG9fdmVyaWZ5XzE2KGgsaHBvcyx4LDApO1xufVxuXG5mdW5jdGlvbiBjcnlwdG9fc2VjcmV0Ym94KGMsbSxkLG4saykge1xuICB2YXIgaTtcbiAgaWYgKGQgPCAzMikgcmV0dXJuIC0xO1xuICBjcnlwdG9fc3RyZWFtX3hvcihjLDAsbSwwLGQsbixrKTtcbiAgY3J5cHRvX29uZXRpbWVhdXRoKGMsIDE2LCBjLCAzMiwgZCAtIDMyLCBjKTtcbiAgZm9yIChpID0gMDsgaSA8IDE2OyBpKyspIGNbaV0gPSAwO1xuICByZXR1cm4gMDtcbn1cblxuZnVuY3Rpb24gY3J5cHRvX3NlY3JldGJveF9vcGVuKG0sYyxkLG4saykge1xuICB2YXIgaTtcbiAgdmFyIHggPSBuZXcgVWludDhBcnJheSgzMik7XG4gIGlmIChkIDwgMzIpIHJldHVybiAtMTtcbiAgY3J5cHRvX3N0cmVhbSh4LDAsMzIsbixrKTtcbiAgaWYgKGNyeXB0b19vbmV0aW1lYXV0aF92ZXJpZnkoYywgMTYsYywgMzIsZCAtIDMyLHgpICE9PSAwKSByZXR1cm4gLTE7XG4gIGNyeXB0b19zdHJlYW1feG9yKG0sMCxjLDAsZCxuLGspO1xuICBmb3IgKGkgPSAwOyBpIDwgMzI7IGkrKykgbVtpXSA9IDA7XG4gIHJldHVybiAwO1xufVxuXG5mdW5jdGlvbiBzZXQyNTUxOShyLCBhKSB7XG4gIHZhciBpO1xuICBmb3IgKGkgPSAwOyBpIDwgMTY7IGkrKykgcltpXSA9IGFbaV18MDtcbn1cblxuZnVuY3Rpb24gY2FyMjU1MTkobykge1xuICB2YXIgaSwgdiwgYyA9IDE7XG4gIGZvciAoaSA9IDA7IGkgPCAxNjsgaSsrKSB7XG4gICAgdiA9IG9baV0gKyBjICsgNjU1MzU7XG4gICAgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTtcbiAgICBvW2ldID0gdiAtIGMgKiA2NTUzNjtcbiAgfVxuICBvWzBdICs9IGMtMSArIDM3ICogKGMtMSk7XG59XG5cbmZ1bmN0aW9uIHNlbDI1NTE5KHAsIHEsIGIpIHtcbiAgdmFyIHQsIGMgPSB+KGItMSk7XG4gIGZvciAodmFyIGkgPSAwOyBpIDwgMTY7IGkrKykge1xuICAgIHQgPSBjICYgKHBbaV0gXiBxW2ldKTtcbiAgICBwW2ldIF49IHQ7XG4gICAgcVtpXSBePSB0O1xuICB9XG59XG5cbmZ1bmN0aW9uIHBhY2syNTUxOShvLCBuKSB7XG4gIHZhciBpLCBqLCBiO1xuICB2YXIgbSA9IGdmKCksIHQgPSBnZigpO1xuICBmb3IgKGkgPSAwOyBpIDwgMTY7IGkrKykgdFtpXSA9IG5baV07XG4gIGNhcjI1NTE5KHQpO1xuICBjYXIyNTUxOSh0KTtcbiAgY2FyMjU1MTkodCk7XG4gIGZvciAoaiA9IDA7IGogPCAyOyBqKyspIHtcbiAgICBtWzBdID0gdFswXSAtIDB4ZmZlZDtcbiAgICBmb3IgKGkgPSAxOyBpIDwgMTU7IGkrKykge1xuICAgICAgbVtpXSA9IHRbaV0gLSAweGZmZmYgLSAoKG1baS0xXT4+MTYpICYgMSk7XG4gICAgICBtW2ktMV0gJj0gMHhmZmZmO1xuICAgIH1cbiAgICBtWzE1XSA9IHRbMTVdIC0gMHg3ZmZmIC0gKChtWzE0XT4+MTYpICYgMSk7XG4gICAgYiA9IChtWzE1XT4+MTYpICYgMTtcbiAgICBtWzE0XSAmPSAweGZmZmY7XG4gICAgc2VsMjU1MTkodCwgbSwgMS1iKTtcbiAgfVxuICBmb3IgKGkgPSAwOyBpIDwgMTY7IGkrKykge1xuICAgIG9bMippXSA9IHRbaV0gJiAweGZmO1xuICAgIG9bMippKzFdID0gdFtpXT4+ODtcbiAgfVxufVxuXG5mdW5jdGlvbiBuZXEyNTUxOShhLCBiKSB7XG4gIHZhciBjID0gbmV3IFVpbnQ4QXJyYXkoMzIpLCBkID0gbmV3IFVpbnQ4QXJyYXkoMzIpO1xuICBwYWNrMjU1MTkoYywgYSk7XG4gIHBhY2syNTUxOShkLCBiKTtcbiAgcmV0dXJuIGNyeXB0b192ZXJpZnlfMzIoYywgMCwgZCwgMCk7XG59XG5cbmZ1bmN0aW9uIHBhcjI1NTE5KGEpIHtcbiAgdmFyIGQgPSBuZXcgVWludDhBcnJheSgzMik7XG4gIHBhY2syNTUxOShkLCBhKTtcbiAgcmV0dXJuIGRbMF0gJiAxO1xufVxuXG5mdW5jdGlvbiB1bnBhY2syNTUxOShvLCBuKSB7XG4gIHZhciBpO1xuICBmb3IgKGkgPSAwOyBpIDwgMTY7IGkrKykgb1tpXSA9IG5bMippXSArIChuWzIqaSsxXSA8PCA4KTtcbiAgb1sxNV0gJj0gMHg3ZmZmO1xufVxuXG5mdW5jdGlvbiBBKG8sIGEsIGIpIHtcbiAgZm9yICh2YXIgaSA9IDA7IGkgPCAxNjsgaSsrKSBvW2ldID0gYVtpXSArIGJbaV07XG59XG5cbmZ1bmN0aW9uIFoobywgYSwgYikge1xuICBmb3IgKHZhciBpID0gMDsgaSA8IDE2OyBpKyspIG9baV0gPSBhW2ldIC0gYltpXTtcbn1cblxuZnVuY3Rpb24gTShvLCBhLCBiKSB7XG4gIHZhciB2LCBjLFxuICAgICB0MCA9IDAsICB0MSA9IDAsICB0MiA9IDAsICB0MyA9IDAsICB0NCA9IDAsICB0NSA9IDAsICB0NiA9IDAsICB0NyA9IDAsXG4gICAgIHQ4ID0gMCwgIHQ5ID0gMCwgdDEwID0gMCwgdDExID0gMCwgdDEyID0gMCwgdDEzID0gMCwgdDE0ID0gMCwgdDE1ID0gMCxcbiAgICB0MTYgPSAwLCB0MTcgPSAwLCB0MTggPSAwLCB0MTkgPSAwLCB0MjAgPSAwLCB0MjEgPSAwLCB0MjIgPSAwLCB0MjMgPSAwLFxuICAgIHQyNCA9IDAsIHQyNSA9IDAsIHQyNiA9IDAsIHQyNyA9IDAsIHQyOCA9IDAsIHQyOSA9IDAsIHQzMCA9IDAsXG4gICAgYjAgPSBiWzBdLFxuICAgIGIxID0gYlsxXSxcbiAgICBiMiA9IGJbMl0sXG4gICAgYjMgPSBiWzNdLFxuICAgIGI0ID0gYls0XSxcbiAgICBiNSA9IGJbNV0sXG4gICAgYjYgPSBiWzZdLFxuICAgIGI3ID0gYls3XSxcbiAgICBiOCA9IGJbOF0sXG4gICAgYjkgPSBiWzldLFxuICAgIGIxMCA9IGJbMTBdLFxuICAgIGIxMSA9IGJbMTFdLFxuICAgIGIxMiA9IGJbMTJdLFxuICAgIGIxMyA9IGJbMTNdLFxuICAgIGIxNCA9IGJbMTRdLFxuICAgIGIxNSA9IGJbMTVdO1xuXG4gIHYgPSBhWzBdO1xuICB0MCArPSB2ICogYjA7XG4gIHQxICs9IHYgKiBiMTtcbiAgdDIgKz0gdiAqIGIyO1xuICB0MyArPSB2ICogYjM7XG4gIHQ0ICs9IHYgKiBiNDtcbiAgdDUgKz0gdiAqIGI1O1xuICB0NiArPSB2ICogYjY7XG4gIHQ3ICs9IHYgKiBiNztcbiAgdDggKz0gdiAqIGI4O1xuICB0OSArPSB2ICogYjk7XG4gIHQxMCArPSB2ICogYjEwO1xuICB0MTEgKz0gdiAqIGIxMTtcbiAgdDEyICs9IHYgKiBiMTI7XG4gIHQxMyArPSB2ICogYjEzO1xuICB0MTQgKz0gdiAqIGIxNDtcbiAgdDE1ICs9IHYgKiBiMTU7XG4gIHYgPSBhWzFdO1xuICB0MSArPSB2ICogYjA7XG4gIHQyICs9IHYgKiBiMTtcbiAgdDMgKz0gdiAqIGIyO1xuICB0NCArPSB2ICogYjM7XG4gIHQ1ICs9IHYgKiBiNDtcbiAgdDYgKz0gdiAqIGI1O1xuICB0NyArPSB2ICogYjY7XG4gIHQ4ICs9IHYgKiBiNztcbiAgdDkgKz0gdiAqIGI4O1xuICB0MTAgKz0gdiAqIGI5O1xuICB0MTEgKz0gdiAqIGIxMDtcbiAgdDEyICs9IHYgKiBiMTE7XG4gIHQxMyArPSB2ICogYjEyO1xuICB0MTQgKz0gdiAqIGIxMztcbiAgdDE1ICs9IHYgKiBiMTQ7XG4gIHQxNiArPSB2ICogYjE1O1xuICB2ID0gYVsyXTtcbiAgdDIgKz0gdiAqIGIwO1xuICB0MyArPSB2ICogYjE7XG4gIHQ0ICs9IHYgKiBiMjtcbiAgdDUgKz0gdiAqIGIzO1xuICB0NiArPSB2ICogYjQ7XG4gIHQ3ICs9IHYgKiBiNTtcbiAgdDggKz0gdiAqIGI2O1xuICB0OSArPSB2ICogYjc7XG4gIHQxMCArPSB2ICogYjg7XG4gIHQxMSArPSB2ICogYjk7XG4gIHQxMiArPSB2ICogYjEwO1xuICB0MTMgKz0gdiAqIGIxMTtcbiAgdDE0ICs9IHYgKiBiMTI7XG4gIHQxNSArPSB2ICogYjEzO1xuICB0MTYgKz0gdiAqIGIxNDtcbiAgdDE3ICs9IHYgKiBiMTU7XG4gIHYgPSBhWzNdO1xuICB0MyArPSB2ICogYjA7XG4gIHQ0ICs9IHYgKiBiMTtcbiAgdDUgKz0gdiAqIGIyO1xuICB0NiArPSB2ICogYjM7XG4gIHQ3ICs9IHYgKiBiNDtcbiAgdDggKz0gdiAqIGI1O1xuICB0OSArPSB2ICogYjY7XG4gIHQxMCArPSB2ICogYjc7XG4gIHQxMSArPSB2ICogYjg7XG4gIHQxMiArPSB2ICogYjk7XG4gIHQxMyArPSB2ICogYjEwO1xuICB0MTQgKz0gdiAqIGIxMTtcbiAgdDE1ICs9IHYgKiBiMTI7XG4gIHQxNiArPSB2ICogYjEzO1xuICB0MTcgKz0gdiAqIGIxNDtcbiAgdDE4ICs9IHYgKiBiMTU7XG4gIHYgPSBhWzRdO1xuICB0NCArPSB2ICogYjA7XG4gIHQ1ICs9IHYgKiBiMTtcbiAgdDYgKz0gdiAqIGIyO1xuICB0NyArPSB2ICogYjM7XG4gIHQ4ICs9IHYgKiBiNDtcbiAgdDkgKz0gdiAqIGI1O1xuICB0MTAgKz0gdiAqIGI2O1xuICB0MTEgKz0gdiAqIGI3O1xuICB0MTIgKz0gdiAqIGI4O1xuICB0MTMgKz0gdiAqIGI5O1xuICB0MTQgKz0gdiAqIGIxMDtcbiAgdDE1ICs9IHYgKiBiMTE7XG4gIHQxNiArPSB2ICogYjEyO1xuICB0MTcgKz0gdiAqIGIxMztcbiAgdDE4ICs9IHYgKiBiMTQ7XG4gIHQxOSArPSB2ICogYjE1O1xuICB2ID0gYVs1XTtcbiAgdDUgKz0gdiAqIGIwO1xuICB0NiArPSB2ICogYjE7XG4gIHQ3ICs9IHYgKiBiMjtcbiAgdDggKz0gdiAqIGIzO1xuICB0OSArPSB2ICogYjQ7XG4gIHQxMCArPSB2ICogYjU7XG4gIHQxMSArPSB2ICogYjY7XG4gIHQxMiArPSB2ICogYjc7XG4gIHQxMyArPSB2ICogYjg7XG4gIHQxNCArPSB2ICogYjk7XG4gIHQxNSArPSB2ICogYjEwO1xuICB0MTYgKz0gdiAqIGIxMTtcbiAgdDE3ICs9IHYgKiBiMTI7XG4gIHQxOCArPSB2ICogYjEzO1xuICB0MTkgKz0gdiAqIGIxNDtcbiAgdDIwICs9IHYgKiBiMTU7XG4gIHYgPSBhWzZdO1xuICB0NiArPSB2ICogYjA7XG4gIHQ3ICs9IHYgKiBiMTtcbiAgdDggKz0gdiAqIGIyO1xuICB0OSArPSB2ICogYjM7XG4gIHQxMCArPSB2ICogYjQ7XG4gIHQxMSArPSB2ICogYjU7XG4gIHQxMiArPSB2ICogYjY7XG4gIHQxMyArPSB2ICogYjc7XG4gIHQxNCArPSB2ICogYjg7XG4gIHQxNSArPSB2ICogYjk7XG4gIHQxNiArPSB2ICogYjEwO1xuICB0MTcgKz0gdiAqIGIxMTtcbiAgdDE4ICs9IHYgKiBiMTI7XG4gIHQxOSArPSB2ICogYjEzO1xuICB0MjAgKz0gdiAqIGIxNDtcbiAgdDIxICs9IHYgKiBiMTU7XG4gIHYgPSBhWzddO1xuICB0NyArPSB2ICogYjA7XG4gIHQ4ICs9IHYgKiBiMTtcbiAgdDkgKz0gdiAqIGIyO1xuICB0MTAgKz0gdiAqIGIzO1xuICB0MTEgKz0gdiAqIGI0O1xuICB0MTIgKz0gdiAqIGI1O1xuICB0MTMgKz0gdiAqIGI2O1xuICB0MTQgKz0gdiAqIGI3O1xuICB0MTUgKz0gdiAqIGI4O1xuICB0MTYgKz0gdiAqIGI5O1xuICB0MTcgKz0gdiAqIGIxMDtcbiAgdDE4ICs9IHYgKiBiMTE7XG4gIHQxOSArPSB2ICogYjEyO1xuICB0MjAgKz0gdiAqIGIxMztcbiAgdDIxICs9IHYgKiBiMTQ7XG4gIHQyMiArPSB2ICogYjE1O1xuICB2ID0gYVs4XTtcbiAgdDggKz0gdiAqIGIwO1xuICB0OSArPSB2ICogYjE7XG4gIHQxMCArPSB2ICogYjI7XG4gIHQxMSArPSB2ICogYjM7XG4gIHQxMiArPSB2ICogYjQ7XG4gIHQxMyArPSB2ICogYjU7XG4gIHQxNCArPSB2ICogYjY7XG4gIHQxNSArPSB2ICogYjc7XG4gIHQxNiArPSB2ICogYjg7XG4gIHQxNyArPSB2ICogYjk7XG4gIHQxOCArPSB2ICogYjEwO1xuICB0MTkgKz0gdiAqIGIxMTtcbiAgdDIwICs9IHYgKiBiMTI7XG4gIHQyMSArPSB2ICogYjEzO1xuICB0MjIgKz0gdiAqIGIxNDtcbiAgdDIzICs9IHYgKiBiMTU7XG4gIHYgPSBhWzldO1xuICB0OSArPSB2ICogYjA7XG4gIHQxMCArPSB2ICogYjE7XG4gIHQxMSArPSB2ICogYjI7XG4gIHQxMiArPSB2ICogYjM7XG4gIHQxMyArPSB2ICogYjQ7XG4gIHQxNCArPSB2ICogYjU7XG4gIHQxNSArPSB2ICogYjY7XG4gIHQxNiArPSB2ICogYjc7XG4gIHQxNyArPSB2ICogYjg7XG4gIHQxOCArPSB2ICogYjk7XG4gIHQxOSArPSB2ICogYjEwO1xuICB0MjAgKz0gdiAqIGIxMTtcbiAgdDIxICs9IHYgKiBiMTI7XG4gIHQyMiArPSB2ICogYjEzO1xuICB0MjMgKz0gdiAqIGIxNDtcbiAgdDI0ICs9IHYgKiBiMTU7XG4gIHYgPSBhWzEwXTtcbiAgdDEwICs9IHYgKiBiMDtcbiAgdDExICs9IHYgKiBiMTtcbiAgdDEyICs9IHYgKiBiMjtcbiAgdDEzICs9IHYgKiBiMztcbiAgdDE0ICs9IHYgKiBiNDtcbiAgdDE1ICs9IHYgKiBiNTtcbiAgdDE2ICs9IHYgKiBiNjtcbiAgdDE3ICs9IHYgKiBiNztcbiAgdDE4ICs9IHYgKiBiODtcbiAgdDE5ICs9IHYgKiBiOTtcbiAgdDIwICs9IHYgKiBiMTA7XG4gIHQyMSArPSB2ICogYjExO1xuICB0MjIgKz0gdiAqIGIxMjtcbiAgdDIzICs9IHYgKiBiMTM7XG4gIHQyNCArPSB2ICogYjE0O1xuICB0MjUgKz0gdiAqIGIxNTtcbiAgdiA9IGFbMTFdO1xuICB0MTEgKz0gdiAqIGIwO1xuICB0MTIgKz0gdiAqIGIxO1xuICB0MTMgKz0gdiAqIGIyO1xuICB0MTQgKz0gdiAqIGIzO1xuICB0MTUgKz0gdiAqIGI0O1xuICB0MTYgKz0gdiAqIGI1O1xuICB0MTcgKz0gdiAqIGI2O1xuICB0MTggKz0gdiAqIGI3O1xuICB0MTkgKz0gdiAqIGI4O1xuICB0MjAgKz0gdiAqIGI5O1xuICB0MjEgKz0gdiAqIGIxMDtcbiAgdDIyICs9IHYgKiBiMTE7XG4gIHQyMyArPSB2ICogYjEyO1xuICB0MjQgKz0gdiAqIGIxMztcbiAgdDI1ICs9IHYgKiBiMTQ7XG4gIHQyNiArPSB2ICogYjE1O1xuICB2ID0gYVsxMl07XG4gIHQxMiArPSB2ICogYjA7XG4gIHQxMyArPSB2ICogYjE7XG4gIHQxNCArPSB2ICogYjI7XG4gIHQxNSArPSB2ICogYjM7XG4gIHQxNiArPSB2ICogYjQ7XG4gIHQxNyArPSB2ICogYjU7XG4gIHQxOCArPSB2ICogYjY7XG4gIHQxOSArPSB2ICogYjc7XG4gIHQyMCArPSB2ICogYjg7XG4gIHQyMSArPSB2ICogYjk7XG4gIHQyMiArPSB2ICogYjEwO1xuICB0MjMgKz0gdiAqIGIxMTtcbiAgdDI0ICs9IHYgKiBiMTI7XG4gIHQyNSArPSB2ICogYjEzO1xuICB0MjYgKz0gdiAqIGIxNDtcbiAgdDI3ICs9IHYgKiBiMTU7XG4gIHYgPSBhWzEzXTtcbiAgdDEzICs9IHYgKiBiMDtcbiAgdDE0ICs9IHYgKiBiMTtcbiAgdDE1ICs9IHYgKiBiMjtcbiAgdDE2ICs9IHYgKiBiMztcbiAgdDE3ICs9IHYgKiBiNDtcbiAgdDE4ICs9IHYgKiBiNTtcbiAgdDE5ICs9IHYgKiBiNjtcbiAgdDIwICs9IHYgKiBiNztcbiAgdDIxICs9IHYgKiBiODtcbiAgdDIyICs9IHYgKiBiOTtcbiAgdDIzICs9IHYgKiBiMTA7XG4gIHQyNCArPSB2ICogYjExO1xuICB0MjUgKz0gdiAqIGIxMjtcbiAgdDI2ICs9IHYgKiBiMTM7XG4gIHQyNyArPSB2ICogYjE0O1xuICB0MjggKz0gdiAqIGIxNTtcbiAgdiA9IGFbMTRdO1xuICB0MTQgKz0gdiAqIGIwO1xuICB0MTUgKz0gdiAqIGIxO1xuICB0MTYgKz0gdiAqIGIyO1xuICB0MTcgKz0gdiAqIGIzO1xuICB0MTggKz0gdiAqIGI0O1xuICB0MTkgKz0gdiAqIGI1O1xuICB0MjAgKz0gdiAqIGI2O1xuICB0MjEgKz0gdiAqIGI3O1xuICB0MjIgKz0gdiAqIGI4O1xuICB0MjMgKz0gdiAqIGI5O1xuICB0MjQgKz0gdiAqIGIxMDtcbiAgdDI1ICs9IHYgKiBiMTE7XG4gIHQyNiArPSB2ICogYjEyO1xuICB0MjcgKz0gdiAqIGIxMztcbiAgdDI4ICs9IHYgKiBiMTQ7XG4gIHQyOSArPSB2ICogYjE1O1xuICB2ID0gYVsxNV07XG4gIHQxNSArPSB2ICogYjA7XG4gIHQxNiArPSB2ICogYjE7XG4gIHQxNyArPSB2ICogYjI7XG4gIHQxOCArPSB2ICogYjM7XG4gIHQxOSArPSB2ICogYjQ7XG4gIHQyMCArPSB2ICogYjU7XG4gIHQyMSArPSB2ICogYjY7XG4gIHQyMiArPSB2ICogYjc7XG4gIHQyMyArPSB2ICogYjg7XG4gIHQyNCArPSB2ICogYjk7XG4gIHQyNSArPSB2ICogYjEwO1xuICB0MjYgKz0gdiAqIGIxMTtcbiAgdDI3ICs9IHYgKiBiMTI7XG4gIHQyOCArPSB2ICogYjEzO1xuICB0MjkgKz0gdiAqIGIxNDtcbiAgdDMwICs9IHYgKiBiMTU7XG5cbiAgdDAgICs9IDM4ICogdDE2O1xuICB0MSAgKz0gMzggKiB0MTc7XG4gIHQyICArPSAzOCAqIHQxODtcbiAgdDMgICs9IDM4ICogdDE5O1xuICB0NCAgKz0gMzggKiB0MjA7XG4gIHQ1ICArPSAzOCAqIHQyMTtcbiAgdDYgICs9IDM4ICogdDIyO1xuICB0NyAgKz0gMzggKiB0MjM7XG4gIHQ4ICArPSAzOCAqIHQyNDtcbiAgdDkgICs9IDM4ICogdDI1O1xuICB0MTAgKz0gMzggKiB0MjY7XG4gIHQxMSArPSAzOCAqIHQyNztcbiAgdDEyICs9IDM4ICogdDI4O1xuICB0MTMgKz0gMzggKiB0Mjk7XG4gIHQxNCArPSAzOCAqIHQzMDtcbiAgLy8gdDE1IGxlZnQgYXMgaXNcblxuICAvLyBmaXJzdCBjYXJcbiAgYyA9IDE7XG4gIHYgPSAgdDAgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7ICB0MCA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSAgdDEgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7ICB0MSA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSAgdDIgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7ICB0MiA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSAgdDMgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7ICB0MyA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSAgdDQgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7ICB0NCA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSAgdDUgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7ICB0NSA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSAgdDYgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7ICB0NiA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSAgdDcgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7ICB0NyA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSAgdDggKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7ICB0OCA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSAgdDkgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7ICB0OSA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSB0MTAgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7IHQxMCA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSB0MTEgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7IHQxMSA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSB0MTIgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7IHQxMiA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSB0MTMgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7IHQxMyA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSB0MTQgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7IHQxNCA9IHYgLSBjICogNjU1MzY7XG4gIHYgPSB0MTUgKyBjICsgNjU1MzU7IGMgPSBNYXRoLmZsb29yKHYgLyA2NTUzNik7IHQxNSA9IHYgLSBjICogNjU1MzY7XG4gIHQwICs9IGMtMSArIDM3ICogKGMtMSk7XG5cbiAgLy8gc2Vjb25kIGNhclxuICBjID0gMTtcbiAgdiA9ICB0MCArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgIHQwID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9ICB0MSArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgIHQxID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9ICB0MiArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgIHQyID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9ICB0MyArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgIHQzID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9ICB0NCArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgIHQ0ID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9ICB0NSArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgIHQ1ID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9ICB0NiArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgIHQ2ID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9ICB0NyArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgIHQ3ID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9ICB0OCArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgIHQ4ID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9ICB0OSArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgIHQ5ID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9IHQxMCArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgdDEwID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9IHQxMSArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgdDExID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9IHQxMiArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgdDEyID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9IHQxMyArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgdDEzID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9IHQxNCArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgdDE0ID0gdiAtIGMgKiA2NTUzNjtcbiAgdiA9IHQxNSArIGMgKyA2NTUzNTsgYyA9IE1hdGguZmxvb3IodiAvIDY1NTM2KTsgdDE1ID0gdiAtIGMgKiA2NTUzNjtcbiAgdDAgKz0gYy0xICsgMzcgKiAoYy0xKTtcblxuICBvWyAwXSA9IHQwO1xuICBvWyAxXSA9IHQxO1xuICBvWyAyXSA9IHQyO1xuICBvWyAzXSA9IHQzO1xuICBvWyA0XSA9IHQ0O1xuICBvWyA1XSA9IHQ1O1xuICBvWyA2XSA9IHQ2O1xuICBvWyA3XSA9IHQ3O1xuICBvWyA4XSA9IHQ4O1xuICBvWyA5XSA9IHQ5O1xuICBvWzEwXSA9IHQxMDtcbiAgb1sxMV0gPSB0MTE7XG4gIG9bMTJdID0gdDEyO1xuICBvWzEzXSA9IHQxMztcbiAgb1sxNF0gPSB0MTQ7XG4gIG9bMTVdID0gdDE1O1xufVxuXG5mdW5jdGlvbiBTKG8sIGEpIHtcbiAgTShvLCBhLCBhKTtcbn1cblxuZnVuY3Rpb24gaW52MjU1MTkobywgaSkge1xuICB2YXIgYyA9IGdmKCk7XG4gIHZhciBhO1xuICBmb3IgKGEgPSAwOyBhIDwgMTY7IGErKykgY1thXSA9IGlbYV07XG4gIGZvciAoYSA9IDI1MzsgYSA+PSAwOyBhLS0pIHtcbiAgICBTKGMsIGMpO1xuICAgIGlmKGEgIT09IDIgJiYgYSAhPT0gNCkgTShjLCBjLCBpKTtcbiAgfVxuICBmb3IgKGEgPSAwOyBhIDwgMTY7IGErKykgb1thXSA9IGNbYV07XG59XG5cbmZ1bmN0aW9uIHBvdzI1MjMobywgaSkge1xuICB2YXIgYyA9IGdmKCk7XG4gIHZhciBhO1xuICBmb3IgKGEgPSAwOyBhIDwgMTY7IGErKykgY1thXSA9IGlbYV07XG4gIGZvciAoYSA9IDI1MDsgYSA+PSAwOyBhLS0pIHtcbiAgICAgIFMoYywgYyk7XG4gICAgICBpZihhICE9PSAxKSBNKGMsIGMsIGkpO1xuICB9XG4gIGZvciAoYSA9IDA7IGEgPCAxNjsgYSsrKSBvW2FdID0gY1thXTtcbn1cblxuZnVuY3Rpb24gY3J5cHRvX3NjYWxhcm11bHQocSwgbiwgcCkge1xuICB2YXIgeiA9IG5ldyBVaW50OEFycmF5KDMyKTtcbiAgdmFyIHggPSBuZXcgRmxvYXQ2NEFycmF5KDgwKSwgciwgaTtcbiAgdmFyIGEgPSBnZigpLCBiID0gZ2YoKSwgYyA9IGdmKCksXG4gICAgICBkID0gZ2YoKSwgZSA9IGdmKCksIGYgPSBnZigpO1xuICBmb3IgKGkgPSAwOyBpIDwgMzE7IGkrKykgeltpXSA9IG5baV07XG4gIHpbMzFdPShuWzMxXSYxMjcpfDY0O1xuICB6WzBdJj0yNDg7XG4gIHVucGFjazI1NTE5KHgscCk7XG4gIGZvciAoaSA9IDA7IGkgPCAxNjsgaSsrKSB7XG4gICAgYltpXT14W2ldO1xuICAgIGRbaV09YVtpXT1jW2ldPTA7XG4gIH1cbiAgYVswXT1kWzBdPTE7XG4gIGZvciAoaT0yNTQ7IGk+PTA7IC0taSkge1xuICAgIHI9KHpbaT4+PjNdPj4+KGkmNykpJjE7XG4gICAgc2VsMjU1MTkoYSxiLHIpO1xuICAgIHNlbDI1NTE5KGMsZCxyKTtcbiAgICBBKGUsYSxjKTtcbiAgICBaKGEsYSxjKTtcbiAgICBBKGMsYixkKTtcbiAgICBaKGIsYixkKTtcbiAgICBTKGQsZSk7XG4gICAgUyhmLGEpO1xuICAgIE0oYSxjLGEpO1xuICAgIE0oYyxiLGUpO1xuICAgIEEoZSxhLGMpO1xuICAgIFooYSxhLGMpO1xuICAgIFMoYixhKTtcbiAgICBaKGMsZCxmKTtcbiAgICBNKGEsYyxfMTIxNjY1KTtcbiAgICBBKGEsYSxkKTtcbiAgICBNKGMsYyxhKTtcbiAgICBNKGEsZCxmKTtcbiAgICBNKGQsYix4KTtcbiAgICBTKGIsZSk7XG4gICAgc2VsMjU1MTkoYSxiLHIpO1xuICAgIHNlbDI1NTE5KGMsZCxyKTtcbiAgfVxuICBmb3IgKGkgPSAwOyBpIDwgMTY7IGkrKykge1xuICAgIHhbaSsxNl09YVtpXTtcbiAgICB4W2krMzJdPWNbaV07XG4gICAgeFtpKzQ4XT1iW2ldO1xuICAgIHhbaSs2NF09ZFtpXTtcbiAgfVxuICB2YXIgeDMyID0geC5zdWJhcnJheSgzMik7XG4gIHZhciB4MTYgPSB4LnN1YmFycmF5KDE2KTtcbiAgaW52MjU1MTkoeDMyLHgzMik7XG4gIE0oeDE2LHgxNix4MzIpO1xuICBwYWNrMjU1MTkocSx4MTYpO1xuICByZXR1cm4gMDtcbn1cblxuZnVuY3Rpb24gY3J5cHRvX3NjYWxhcm11bHRfYmFzZShxLCBuKSB7XG4gIHJldHVybiBjcnlwdG9fc2NhbGFybXVsdChxLCBuLCBfOSk7XG59XG5cbmZ1bmN0aW9uIGNyeXB0b19ib3hfa2V5cGFpcih5LCB4KSB7XG4gIHJhbmRvbWJ5dGVzKHgsIDMyKTtcbiAgcmV0dXJuIGNyeXB0b19zY2FsYXJtdWx0X2Jhc2UoeSwgeCk7XG59XG5cbmZ1bmN0aW9uIGNyeXB0b19ib3hfYmVmb3Jlbm0oaywgeSwgeCkge1xuICB2YXIgcyA9IG5ldyBVaW50OEFycmF5KDMyKTtcbiAgY3J5cHRvX3NjYWxhcm11bHQocywgeCwgeSk7XG4gIHJldHVybiBjcnlwdG9fY29yZV9oc2Fsc2EyMChrLCBfMCwgcywgc2lnbWEpO1xufVxuXG52YXIgY3J5cHRvX2JveF9hZnRlcm5tID0gY3J5cHRvX3NlY3JldGJveDtcbnZhciBjcnlwdG9fYm94X29wZW5fYWZ0ZXJubSA9IGNyeXB0b19zZWNyZXRib3hfb3BlbjtcblxuZnVuY3Rpb24gY3J5cHRvX2JveChjLCBtLCBkLCBuLCB5LCB4KSB7XG4gIHZhciBrID0gbmV3IFVpbnQ4QXJyYXkoMzIpO1xuICBjcnlwdG9fYm94X2JlZm9yZW5tKGssIHksIHgpO1xuICByZXR1cm4gY3J5cHRvX2JveF9hZnRlcm5tKGMsIG0sIGQsIG4sIGspO1xufVxuXG5mdW5jdGlvbiBjcnlwdG9fYm94X29wZW4obSwgYywgZCwgbiwgeSwgeCkge1xuICB2YXIgayA9IG5ldyBVaW50OEFycmF5KDMyKTtcbiAgY3J5cHRvX2JveF9iZWZvcmVubShrLCB5LCB4KTtcbiAgcmV0dXJuIGNyeXB0b19ib3hfb3Blbl9hZnRlcm5tKG0sIGMsIGQsIG4sIGspO1xufVxuXG52YXIgSyA9IFtcbiAgMHg0MjhhMmY5OCwgMHhkNzI4YWUyMiwgMHg3MTM3NDQ5MSwgMHgyM2VmNjVjZCxcbiAgMHhiNWMwZmJjZiwgMHhlYzRkM2IyZiwgMHhlOWI1ZGJhNSwgMHg4MTg5ZGJiYyxcbiAgMHgzOTU2YzI1YiwgMHhmMzQ4YjUzOCwgMHg1OWYxMTFmMSwgMHhiNjA1ZDAxOSxcbiAgMHg5MjNmODJhNCwgMHhhZjE5NGY5YiwgMHhhYjFjNWVkNSwgMHhkYTZkODExOCxcbiAgMHhkODA3YWE5OCwgMHhhMzAzMDI0MiwgMHgxMjgzNWIwMSwgMHg0NTcwNmZiZSxcbiAgMHgyNDMxODViZSwgMHg0ZWU0YjI4YywgMHg1NTBjN2RjMywgMHhkNWZmYjRlMixcbiAgMHg3MmJlNWQ3NCwgMHhmMjdiODk2ZiwgMHg4MGRlYjFmZSwgMHgzYjE2OTZiMSxcbiAgMHg5YmRjMDZhNywgMHgyNWM3MTIzNSwgMHhjMTliZjE3NCwgMHhjZjY5MjY5NCxcbiAgMHhlNDliNjljMSwgMHg5ZWYxNGFkMiwgMHhlZmJlNDc4NiwgMHgzODRmMjVlMyxcbiAgMHgwZmMxOWRjNiwgMHg4YjhjZDViNSwgMHgyNDBjYTFjYywgMHg3N2FjOWM2NSxcbiAgMHgyZGU5MmM2ZiwgMHg1OTJiMDI3NSwgMHg0YTc0ODRhYSwgMHg2ZWE2ZTQ4MyxcbiAgMHg1Y2IwYTlkYywgMHhiZDQxZmJkNCwgMHg3NmY5ODhkYSwgMHg4MzExNTNiNSxcbiAgMHg5ODNlNTE1MiwgMHhlZTY2ZGZhYiwgMHhhODMxYzY2ZCwgMHgyZGI0MzIxMCxcbiAgMHhiMDAzMjdjOCwgMHg5OGZiMjEzZiwgMHhiZjU5N2ZjNywgMHhiZWVmMGVlNCxcbiAgMHhjNmUwMGJmMywgMHgzZGE4OGZjMiwgMHhkNWE3OTE0NywgMHg5MzBhYTcyNSxcbiAgMHgwNmNhNjM1MSwgMHhlMDAzODI2ZiwgMHgxNDI5Mjk2NywgMHgwYTBlNmU3MCxcbiAgMHgyN2I3MGE4NSwgMHg0NmQyMmZmYywgMHgyZTFiMjEzOCwgMHg1YzI2YzkyNixcbiAgMHg0ZDJjNmRmYywgMHg1YWM0MmFlZCwgMHg1MzM4MGQxMywgMHg5ZDk1YjNkZixcbiAgMHg2NTBhNzM1NCwgMHg4YmFmNjNkZSwgMHg3NjZhMGFiYiwgMHgzYzc3YjJhOCxcbiAgMHg4MWMyYzkyZSwgMHg0N2VkYWVlNiwgMHg5MjcyMmM4NSwgMHgxNDgyMzUzYixcbiAgMHhhMmJmZThhMSwgMHg0Y2YxMDM2NCwgMHhhODFhNjY0YiwgMHhiYzQyMzAwMSxcbiAgMHhjMjRiOGI3MCwgMHhkMGY4OTc5MSwgMHhjNzZjNTFhMywgMHgwNjU0YmUzMCxcbiAgMHhkMTkyZTgxOSwgMHhkNmVmNTIxOCwgMHhkNjk5MDYyNCwgMHg1NTY1YTkxMCxcbiAgMHhmNDBlMzU4NSwgMHg1NzcxMjAyYSwgMHgxMDZhYTA3MCwgMHgzMmJiZDFiOCxcbiAgMHgxOWE0YzExNiwgMHhiOGQyZDBjOCwgMHgxZTM3NmMwOCwgMHg1MTQxYWI1MyxcbiAgMHgyNzQ4Nzc0YywgMHhkZjhlZWI5OSwgMHgzNGIwYmNiNSwgMHhlMTliNDhhOCxcbiAgMHgzOTFjMGNiMywgMHhjNWM5NWE2MywgMHg0ZWQ4YWE0YSwgMHhlMzQxOGFjYixcbiAgMHg1YjljY2E0ZiwgMHg3NzYzZTM3MywgMHg2ODJlNmZmMywgMHhkNmIyYjhhMyxcbiAgMHg3NDhmODJlZSwgMHg1ZGVmYjJmYywgMHg3OGE1NjM2ZiwgMHg0MzE3MmY2MCxcbiAgMHg4NGM4NzgxNCwgMHhhMWYwYWI3MiwgMHg4Y2M3MDIwOCwgMHgxYTY0MzllYyxcbiAgMHg5MGJlZmZmYSwgMHgyMzYzMWUyOCwgMHhhNDUwNmNlYiwgMHhkZTgyYmRlOSxcbiAgMHhiZWY5YTNmNywgMHhiMmM2NzkxNSwgMHhjNjcxNzhmMiwgMHhlMzcyNTMyYixcbiAgMHhjYTI3M2VjZSwgMHhlYTI2NjE5YywgMHhkMTg2YjhjNywgMHgyMWMwYzIwNyxcbiAgMHhlYWRhN2RkNiwgMHhjZGUwZWIxZSwgMHhmNTdkNGY3ZiwgMHhlZTZlZDE3OCxcbiAgMHgwNmYwNjdhYSwgMHg3MjE3NmZiYSwgMHgwYTYzN2RjNSwgMHhhMmM4OThhNixcbiAgMHgxMTNmOTgwNCwgMHhiZWY5MGRhZSwgMHgxYjcxMGIzNSwgMHgxMzFjNDcxYixcbiAgMHgyOGRiNzdmNSwgMHgyMzA0N2Q4NCwgMHgzMmNhYWI3YiwgMHg0MGM3MjQ5MyxcbiAgMHgzYzllYmUwYSwgMHgxNWM5YmViYywgMHg0MzFkNjdjNCwgMHg5YzEwMGQ0YyxcbiAgMHg0Y2M1ZDRiZSwgMHhjYjNlNDJiNiwgMHg1OTdmMjk5YywgMHhmYzY1N2UyYSxcbiAgMHg1ZmNiNmZhYiwgMHgzYWQ2ZmFlYywgMHg2YzQ0MTk4YywgMHg0YTQ3NTgxN1xuXTtcblxuZnVuY3Rpb24gY3J5cHRvX2hhc2hibG9ja3NfaGwoaGgsIGhsLCBtLCBuKSB7XG4gIHZhciB3aCA9IG5ldyBJbnQzMkFycmF5KDE2KSwgd2wgPSBuZXcgSW50MzJBcnJheSgxNiksXG4gICAgICBiaDAsIGJoMSwgYmgyLCBiaDMsIGJoNCwgYmg1LCBiaDYsIGJoNyxcbiAgICAgIGJsMCwgYmwxLCBibDIsIGJsMywgYmw0LCBibDUsIGJsNiwgYmw3LFxuICAgICAgdGgsIHRsLCBpLCBqLCBoLCBsLCBhLCBiLCBjLCBkO1xuXG4gIHZhciBhaDAgPSBoaFswXSxcbiAgICAgIGFoMSA9IGhoWzFdLFxuICAgICAgYWgyID0gaGhbMl0sXG4gICAgICBhaDMgPSBoaFszXSxcbiAgICAgIGFoNCA9IGhoWzRdLFxuICAgICAgYWg1ID0gaGhbNV0sXG4gICAgICBhaDYgPSBoaFs2XSxcbiAgICAgIGFoNyA9IGhoWzddLFxuXG4gICAgICBhbDAgPSBobFswXSxcbiAgICAgIGFsMSA9IGhsWzFdLFxuICAgICAgYWwyID0gaGxbMl0sXG4gICAgICBhbDMgPSBobFszXSxcbiAgICAgIGFsNCA9IGhsWzRdLFxuICAgICAgYWw1ID0gaGxbNV0sXG4gICAgICBhbDYgPSBobFs2XSxcbiAgICAgIGFsNyA9IGhsWzddO1xuXG4gIHZhciBwb3MgPSAwO1xuICB3aGlsZSAobiA+PSAxMjgpIHtcbiAgICBmb3IgKGkgPSAwOyBpIDwgMTY7IGkrKykge1xuICAgICAgaiA9IDggKiBpICsgcG9zO1xuICAgICAgd2hbaV0gPSAobVtqKzBdIDw8IDI0KSB8IChtW2orMV0gPDwgMTYpIHwgKG1baisyXSA8PCA4KSB8IG1baiszXTtcbiAgICAgIHdsW2ldID0gKG1bais0XSA8PCAyNCkgfCAobVtqKzVdIDw8IDE2KSB8IChtW2orNl0gPDwgOCkgfCBtW2orN107XG4gICAgfVxuICAgIGZvciAoaSA9IDA7IGkgPCA4MDsgaSsrKSB7XG4gICAgICBiaDAgPSBhaDA7XG4gICAgICBiaDEgPSBhaDE7XG4gICAgICBiaDIgPSBhaDI7XG4gICAgICBiaDMgPSBhaDM7XG4gICAgICBiaDQgPSBhaDQ7XG4gICAgICBiaDUgPSBhaDU7XG4gICAgICBiaDYgPSBhaDY7XG4gICAgICBiaDcgPSBhaDc7XG5cbiAgICAgIGJsMCA9IGFsMDtcbiAgICAgIGJsMSA9IGFsMTtcbiAgICAgIGJsMiA9IGFsMjtcbiAgICAgIGJsMyA9IGFsMztcbiAgICAgIGJsNCA9IGFsNDtcbiAgICAgIGJsNSA9IGFsNTtcbiAgICAgIGJsNiA9IGFsNjtcbiAgICAgIGJsNyA9IGFsNztcblxuICAgICAgLy8gYWRkXG4gICAgICBoID0gYWg3O1xuICAgICAgbCA9IGFsNztcblxuICAgICAgYSA9IGwgJiAweGZmZmY7IGIgPSBsID4+PiAxNjtcbiAgICAgIGMgPSBoICYgMHhmZmZmOyBkID0gaCA+Pj4gMTY7XG5cbiAgICAgIC8vIFNpZ21hMVxuICAgICAgaCA9ICgoYWg0ID4+PiAxNCkgfCAoYWw0IDw8ICgzMi0xNCkpKSBeICgoYWg0ID4+PiAxOCkgfCAoYWw0IDw8ICgzMi0xOCkpKSBeICgoYWw0ID4+PiAoNDEtMzIpKSB8IChhaDQgPDwgKDMyLSg0MS0zMikpKSk7XG4gICAgICBsID0gKChhbDQgPj4+IDE0KSB8IChhaDQgPDwgKDMyLTE0KSkpIF4gKChhbDQgPj4+IDE4KSB8IChhaDQgPDwgKDMyLTE4KSkpIF4gKChhaDQgPj4+ICg0MS0zMikpIHwgKGFsNCA8PCAoMzItKDQxLTMyKSkpKTtcblxuICAgICAgYSArPSBsICYgMHhmZmZmOyBiICs9IGwgPj4+IDE2O1xuICAgICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgICAvLyBDaFxuICAgICAgaCA9IChhaDQgJiBhaDUpIF4gKH5haDQgJiBhaDYpO1xuICAgICAgbCA9IChhbDQgJiBhbDUpIF4gKH5hbDQgJiBhbDYpO1xuXG4gICAgICBhICs9IGwgJiAweGZmZmY7IGIgKz0gbCA+Pj4gMTY7XG4gICAgICBjICs9IGggJiAweGZmZmY7IGQgKz0gaCA+Pj4gMTY7XG5cbiAgICAgIC8vIEtcbiAgICAgIGggPSBLW2kqMl07XG4gICAgICBsID0gS1tpKjIrMV07XG5cbiAgICAgIGEgKz0gbCAmIDB4ZmZmZjsgYiArPSBsID4+PiAxNjtcbiAgICAgIGMgKz0gaCAmIDB4ZmZmZjsgZCArPSBoID4+PiAxNjtcblxuICAgICAgLy8gd1xuICAgICAgaCA9IHdoW2klMTZdO1xuICAgICAgbCA9IHdsW2klMTZdO1xuXG4gICAgICBhICs9IGwgJiAweGZmZmY7IGIgKz0gbCA+Pj4gMTY7XG4gICAgICBjICs9IGggJiAweGZmZmY7IGQgKz0gaCA+Pj4gMTY7XG5cbiAgICAgIGIgKz0gYSA+Pj4gMTY7XG4gICAgICBjICs9IGIgPj4+IDE2O1xuICAgICAgZCArPSBjID4+PiAxNjtcblxuICAgICAgdGggPSBjICYgMHhmZmZmIHwgZCA8PCAxNjtcbiAgICAgIHRsID0gYSAmIDB4ZmZmZiB8IGIgPDwgMTY7XG5cbiAgICAgIC8vIGFkZFxuICAgICAgaCA9IHRoO1xuICAgICAgbCA9IHRsO1xuXG4gICAgICBhID0gbCAmIDB4ZmZmZjsgYiA9IGwgPj4+IDE2O1xuICAgICAgYyA9IGggJiAweGZmZmY7IGQgPSBoID4+PiAxNjtcblxuICAgICAgLy8gU2lnbWEwXG4gICAgICBoID0gKChhaDAgPj4+IDI4KSB8IChhbDAgPDwgKDMyLTI4KSkpIF4gKChhbDAgPj4+ICgzNC0zMikpIHwgKGFoMCA8PCAoMzItKDM0LTMyKSkpKSBeICgoYWwwID4+PiAoMzktMzIpKSB8IChhaDAgPDwgKDMyLSgzOS0zMikpKSk7XG4gICAgICBsID0gKChhbDAgPj4+IDI4KSB8IChhaDAgPDwgKDMyLTI4KSkpIF4gKChhaDAgPj4+ICgzNC0zMikpIHwgKGFsMCA8PCAoMzItKDM0LTMyKSkpKSBeICgoYWgwID4+PiAoMzktMzIpKSB8IChhbDAgPDwgKDMyLSgzOS0zMikpKSk7XG5cbiAgICAgIGEgKz0gbCAmIDB4ZmZmZjsgYiArPSBsID4+PiAxNjtcbiAgICAgIGMgKz0gaCAmIDB4ZmZmZjsgZCArPSBoID4+PiAxNjtcblxuICAgICAgLy8gTWFqXG4gICAgICBoID0gKGFoMCAmIGFoMSkgXiAoYWgwICYgYWgyKSBeIChhaDEgJiBhaDIpO1xuICAgICAgbCA9IChhbDAgJiBhbDEpIF4gKGFsMCAmIGFsMikgXiAoYWwxICYgYWwyKTtcblxuICAgICAgYSArPSBsICYgMHhmZmZmOyBiICs9IGwgPj4+IDE2O1xuICAgICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgICBiICs9IGEgPj4+IDE2O1xuICAgICAgYyArPSBiID4+PiAxNjtcbiAgICAgIGQgKz0gYyA+Pj4gMTY7XG5cbiAgICAgIGJoNyA9IChjICYgMHhmZmZmKSB8IChkIDw8IDE2KTtcbiAgICAgIGJsNyA9IChhICYgMHhmZmZmKSB8IChiIDw8IDE2KTtcblxuICAgICAgLy8gYWRkXG4gICAgICBoID0gYmgzO1xuICAgICAgbCA9IGJsMztcblxuICAgICAgYSA9IGwgJiAweGZmZmY7IGIgPSBsID4+PiAxNjtcbiAgICAgIGMgPSBoICYgMHhmZmZmOyBkID0gaCA+Pj4gMTY7XG5cbiAgICAgIGggPSB0aDtcbiAgICAgIGwgPSB0bDtcblxuICAgICAgYSArPSBsICYgMHhmZmZmOyBiICs9IGwgPj4+IDE2O1xuICAgICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgICBiICs9IGEgPj4+IDE2O1xuICAgICAgYyArPSBiID4+PiAxNjtcbiAgICAgIGQgKz0gYyA+Pj4gMTY7XG5cbiAgICAgIGJoMyA9IChjICYgMHhmZmZmKSB8IChkIDw8IDE2KTtcbiAgICAgIGJsMyA9IChhICYgMHhmZmZmKSB8IChiIDw8IDE2KTtcblxuICAgICAgYWgxID0gYmgwO1xuICAgICAgYWgyID0gYmgxO1xuICAgICAgYWgzID0gYmgyO1xuICAgICAgYWg0ID0gYmgzO1xuICAgICAgYWg1ID0gYmg0O1xuICAgICAgYWg2ID0gYmg1O1xuICAgICAgYWg3ID0gYmg2O1xuICAgICAgYWgwID0gYmg3O1xuXG4gICAgICBhbDEgPSBibDA7XG4gICAgICBhbDIgPSBibDE7XG4gICAgICBhbDMgPSBibDI7XG4gICAgICBhbDQgPSBibDM7XG4gICAgICBhbDUgPSBibDQ7XG4gICAgICBhbDYgPSBibDU7XG4gICAgICBhbDcgPSBibDY7XG4gICAgICBhbDAgPSBibDc7XG5cbiAgICAgIGlmIChpJTE2ID09PSAxNSkge1xuICAgICAgICBmb3IgKGogPSAwOyBqIDwgMTY7IGorKykge1xuICAgICAgICAgIC8vIGFkZFxuICAgICAgICAgIGggPSB3aFtqXTtcbiAgICAgICAgICBsID0gd2xbal07XG5cbiAgICAgICAgICBhID0gbCAmIDB4ZmZmZjsgYiA9IGwgPj4+IDE2O1xuICAgICAgICAgIGMgPSBoICYgMHhmZmZmOyBkID0gaCA+Pj4gMTY7XG5cbiAgICAgICAgICBoID0gd2hbKGorOSklMTZdO1xuICAgICAgICAgIGwgPSB3bFsoais5KSUxNl07XG5cbiAgICAgICAgICBhICs9IGwgJiAweGZmZmY7IGIgKz0gbCA+Pj4gMTY7XG4gICAgICAgICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgICAgICAgLy8gc2lnbWEwXG4gICAgICAgICAgdGggPSB3aFsoaisxKSUxNl07XG4gICAgICAgICAgdGwgPSB3bFsoaisxKSUxNl07XG4gICAgICAgICAgaCA9ICgodGggPj4+IDEpIHwgKHRsIDw8ICgzMi0xKSkpIF4gKCh0aCA+Pj4gOCkgfCAodGwgPDwgKDMyLTgpKSkgXiAodGggPj4+IDcpO1xuICAgICAgICAgIGwgPSAoKHRsID4+PiAxKSB8ICh0aCA8PCAoMzItMSkpKSBeICgodGwgPj4+IDgpIHwgKHRoIDw8ICgzMi04KSkpIF4gKCh0bCA+Pj4gNykgfCAodGggPDwgKDMyLTcpKSk7XG5cbiAgICAgICAgICBhICs9IGwgJiAweGZmZmY7IGIgKz0gbCA+Pj4gMTY7XG4gICAgICAgICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgICAgICAgLy8gc2lnbWExXG4gICAgICAgICAgdGggPSB3aFsoaisxNCklMTZdO1xuICAgICAgICAgIHRsID0gd2xbKGorMTQpJTE2XTtcbiAgICAgICAgICBoID0gKCh0aCA+Pj4gMTkpIHwgKHRsIDw8ICgzMi0xOSkpKSBeICgodGwgPj4+ICg2MS0zMikpIHwgKHRoIDw8ICgzMi0oNjEtMzIpKSkpIF4gKHRoID4+PiA2KTtcbiAgICAgICAgICBsID0gKCh0bCA+Pj4gMTkpIHwgKHRoIDw8ICgzMi0xOSkpKSBeICgodGggPj4+ICg2MS0zMikpIHwgKHRsIDw8ICgzMi0oNjEtMzIpKSkpIF4gKCh0bCA+Pj4gNikgfCAodGggPDwgKDMyLTYpKSk7XG5cbiAgICAgICAgICBhICs9IGwgJiAweGZmZmY7IGIgKz0gbCA+Pj4gMTY7XG4gICAgICAgICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgICAgICAgYiArPSBhID4+PiAxNjtcbiAgICAgICAgICBjICs9IGIgPj4+IDE2O1xuICAgICAgICAgIGQgKz0gYyA+Pj4gMTY7XG5cbiAgICAgICAgICB3aFtqXSA9IChjICYgMHhmZmZmKSB8IChkIDw8IDE2KTtcbiAgICAgICAgICB3bFtqXSA9IChhICYgMHhmZmZmKSB8IChiIDw8IDE2KTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIGFkZFxuICAgIGggPSBhaDA7XG4gICAgbCA9IGFsMDtcblxuICAgIGEgPSBsICYgMHhmZmZmOyBiID0gbCA+Pj4gMTY7XG4gICAgYyA9IGggJiAweGZmZmY7IGQgPSBoID4+PiAxNjtcblxuICAgIGggPSBoaFswXTtcbiAgICBsID0gaGxbMF07XG5cbiAgICBhICs9IGwgJiAweGZmZmY7IGIgKz0gbCA+Pj4gMTY7XG4gICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgYiArPSBhID4+PiAxNjtcbiAgICBjICs9IGIgPj4+IDE2O1xuICAgIGQgKz0gYyA+Pj4gMTY7XG5cbiAgICBoaFswXSA9IGFoMCA9IChjICYgMHhmZmZmKSB8IChkIDw8IDE2KTtcbiAgICBobFswXSA9IGFsMCA9IChhICYgMHhmZmZmKSB8IChiIDw8IDE2KTtcblxuICAgIGggPSBhaDE7XG4gICAgbCA9IGFsMTtcblxuICAgIGEgPSBsICYgMHhmZmZmOyBiID0gbCA+Pj4gMTY7XG4gICAgYyA9IGggJiAweGZmZmY7IGQgPSBoID4+PiAxNjtcblxuICAgIGggPSBoaFsxXTtcbiAgICBsID0gaGxbMV07XG5cbiAgICBhICs9IGwgJiAweGZmZmY7IGIgKz0gbCA+Pj4gMTY7XG4gICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgYiArPSBhID4+PiAxNjtcbiAgICBjICs9IGIgPj4+IDE2O1xuICAgIGQgKz0gYyA+Pj4gMTY7XG5cbiAgICBoaFsxXSA9IGFoMSA9IChjICYgMHhmZmZmKSB8IChkIDw8IDE2KTtcbiAgICBobFsxXSA9IGFsMSA9IChhICYgMHhmZmZmKSB8IChiIDw8IDE2KTtcblxuICAgIGggPSBhaDI7XG4gICAgbCA9IGFsMjtcblxuICAgIGEgPSBsICYgMHhmZmZmOyBiID0gbCA+Pj4gMTY7XG4gICAgYyA9IGggJiAweGZmZmY7IGQgPSBoID4+PiAxNjtcblxuICAgIGggPSBoaFsyXTtcbiAgICBsID0gaGxbMl07XG5cbiAgICBhICs9IGwgJiAweGZmZmY7IGIgKz0gbCA+Pj4gMTY7XG4gICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgYiArPSBhID4+PiAxNjtcbiAgICBjICs9IGIgPj4+IDE2O1xuICAgIGQgKz0gYyA+Pj4gMTY7XG5cbiAgICBoaFsyXSA9IGFoMiA9IChjICYgMHhmZmZmKSB8IChkIDw8IDE2KTtcbiAgICBobFsyXSA9IGFsMiA9IChhICYgMHhmZmZmKSB8IChiIDw8IDE2KTtcblxuICAgIGggPSBhaDM7XG4gICAgbCA9IGFsMztcblxuICAgIGEgPSBsICYgMHhmZmZmOyBiID0gbCA+Pj4gMTY7XG4gICAgYyA9IGggJiAweGZmZmY7IGQgPSBoID4+PiAxNjtcblxuICAgIGggPSBoaFszXTtcbiAgICBsID0gaGxbM107XG5cbiAgICBhICs9IGwgJiAweGZmZmY7IGIgKz0gbCA+Pj4gMTY7XG4gICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgYiArPSBhID4+PiAxNjtcbiAgICBjICs9IGIgPj4+IDE2O1xuICAgIGQgKz0gYyA+Pj4gMTY7XG5cbiAgICBoaFszXSA9IGFoMyA9IChjICYgMHhmZmZmKSB8IChkIDw8IDE2KTtcbiAgICBobFszXSA9IGFsMyA9IChhICYgMHhmZmZmKSB8IChiIDw8IDE2KTtcblxuICAgIGggPSBhaDQ7XG4gICAgbCA9IGFsNDtcblxuICAgIGEgPSBsICYgMHhmZmZmOyBiID0gbCA+Pj4gMTY7XG4gICAgYyA9IGggJiAweGZmZmY7IGQgPSBoID4+PiAxNjtcblxuICAgIGggPSBoaFs0XTtcbiAgICBsID0gaGxbNF07XG5cbiAgICBhICs9IGwgJiAweGZmZmY7IGIgKz0gbCA+Pj4gMTY7XG4gICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgYiArPSBhID4+PiAxNjtcbiAgICBjICs9IGIgPj4+IDE2O1xuICAgIGQgKz0gYyA+Pj4gMTY7XG5cbiAgICBoaFs0XSA9IGFoNCA9IChjICYgMHhmZmZmKSB8IChkIDw8IDE2KTtcbiAgICBobFs0XSA9IGFsNCA9IChhICYgMHhmZmZmKSB8IChiIDw8IDE2KTtcblxuICAgIGggPSBhaDU7XG4gICAgbCA9IGFsNTtcblxuICAgIGEgPSBsICYgMHhmZmZmOyBiID0gbCA+Pj4gMTY7XG4gICAgYyA9IGggJiAweGZmZmY7IGQgPSBoID4+PiAxNjtcblxuICAgIGggPSBoaFs1XTtcbiAgICBsID0gaGxbNV07XG5cbiAgICBhICs9IGwgJiAweGZmZmY7IGIgKz0gbCA+Pj4gMTY7XG4gICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgYiArPSBhID4+PiAxNjtcbiAgICBjICs9IGIgPj4+IDE2O1xuICAgIGQgKz0gYyA+Pj4gMTY7XG5cbiAgICBoaFs1XSA9IGFoNSA9IChjICYgMHhmZmZmKSB8IChkIDw8IDE2KTtcbiAgICBobFs1XSA9IGFsNSA9IChhICYgMHhmZmZmKSB8IChiIDw8IDE2KTtcblxuICAgIGggPSBhaDY7XG4gICAgbCA9IGFsNjtcblxuICAgIGEgPSBsICYgMHhmZmZmOyBiID0gbCA+Pj4gMTY7XG4gICAgYyA9IGggJiAweGZmZmY7IGQgPSBoID4+PiAxNjtcblxuICAgIGggPSBoaFs2XTtcbiAgICBsID0gaGxbNl07XG5cbiAgICBhICs9IGwgJiAweGZmZmY7IGIgKz0gbCA+Pj4gMTY7XG4gICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgYiArPSBhID4+PiAxNjtcbiAgICBjICs9IGIgPj4+IDE2O1xuICAgIGQgKz0gYyA+Pj4gMTY7XG5cbiAgICBoaFs2XSA9IGFoNiA9IChjICYgMHhmZmZmKSB8IChkIDw8IDE2KTtcbiAgICBobFs2XSA9IGFsNiA9IChhICYgMHhmZmZmKSB8IChiIDw8IDE2KTtcblxuICAgIGggPSBhaDc7XG4gICAgbCA9IGFsNztcblxuICAgIGEgPSBsICYgMHhmZmZmOyBiID0gbCA+Pj4gMTY7XG4gICAgYyA9IGggJiAweGZmZmY7IGQgPSBoID4+PiAxNjtcblxuICAgIGggPSBoaFs3XTtcbiAgICBsID0gaGxbN107XG5cbiAgICBhICs9IGwgJiAweGZmZmY7IGIgKz0gbCA+Pj4gMTY7XG4gICAgYyArPSBoICYgMHhmZmZmOyBkICs9IGggPj4+IDE2O1xuXG4gICAgYiArPSBhID4+PiAxNjtcbiAgICBjICs9IGIgPj4+IDE2O1xuICAgIGQgKz0gYyA+Pj4gMTY7XG5cbiAgICBoaFs3XSA9IGFoNyA9IChjICYgMHhmZmZmKSB8IChkIDw8IDE2KTtcbiAgICBobFs3XSA9IGFsNyA9IChhICYgMHhmZmZmKSB8IChiIDw8IDE2KTtcblxuICAgIHBvcyArPSAxMjg7XG4gICAgbiAtPSAxMjg7XG4gIH1cblxuICByZXR1cm4gbjtcbn1cblxuZnVuY3Rpb24gY3J5cHRvX2hhc2gob3V0LCBtLCBuKSB7XG4gIHZhciBoaCA9IG5ldyBJbnQzMkFycmF5KDgpLFxuICAgICAgaGwgPSBuZXcgSW50MzJBcnJheSg4KSxcbiAgICAgIHggPSBuZXcgVWludDhBcnJheSgyNTYpLFxuICAgICAgaSwgYiA9IG47XG5cbiAgaGhbMF0gPSAweDZhMDllNjY3O1xuICBoaFsxXSA9IDB4YmI2N2FlODU7XG4gIGhoWzJdID0gMHgzYzZlZjM3MjtcbiAgaGhbM10gPSAweGE1NGZmNTNhO1xuICBoaFs0XSA9IDB4NTEwZTUyN2Y7XG4gIGhoWzVdID0gMHg5YjA1Njg4YztcbiAgaGhbNl0gPSAweDFmODNkOWFiO1xuICBoaFs3XSA9IDB4NWJlMGNkMTk7XG5cbiAgaGxbMF0gPSAweGYzYmNjOTA4O1xuICBobFsxXSA9IDB4ODRjYWE3M2I7XG4gIGhsWzJdID0gMHhmZTk0ZjgyYjtcbiAgaGxbM10gPSAweDVmMWQzNmYxO1xuICBobFs0XSA9IDB4YWRlNjgyZDE7XG4gIGhsWzVdID0gMHgyYjNlNmMxZjtcbiAgaGxbNl0gPSAweGZiNDFiZDZiO1xuICBobFs3XSA9IDB4MTM3ZTIxNzk7XG5cbiAgY3J5cHRvX2hhc2hibG9ja3NfaGwoaGgsIGhsLCBtLCBuKTtcbiAgbiAlPSAxMjg7XG5cbiAgZm9yIChpID0gMDsgaSA8IG47IGkrKykgeFtpXSA9IG1bYi1uK2ldO1xuICB4W25dID0gMTI4O1xuXG4gIG4gPSAyNTYtMTI4KihuPDExMj8xOjApO1xuICB4W24tOV0gPSAwO1xuICB0czY0KHgsIG4tOCwgIChiIC8gMHgyMDAwMDAwMCkgfCAwLCBiIDw8IDMpO1xuICBjcnlwdG9faGFzaGJsb2Nrc19obChoaCwgaGwsIHgsIG4pO1xuXG4gIGZvciAoaSA9IDA7IGkgPCA4OyBpKyspIHRzNjQob3V0LCA4KmksIGhoW2ldLCBobFtpXSk7XG5cbiAgcmV0dXJuIDA7XG59XG5cbmZ1bmN0aW9uIGFkZChwLCBxKSB7XG4gIHZhciBhID0gZ2YoKSwgYiA9IGdmKCksIGMgPSBnZigpLFxuICAgICAgZCA9IGdmKCksIGUgPSBnZigpLCBmID0gZ2YoKSxcbiAgICAgIGcgPSBnZigpLCBoID0gZ2YoKSwgdCA9IGdmKCk7XG5cbiAgWihhLCBwWzFdLCBwWzBdKTtcbiAgWih0LCBxWzFdLCBxWzBdKTtcbiAgTShhLCBhLCB0KTtcbiAgQShiLCBwWzBdLCBwWzFdKTtcbiAgQSh0LCBxWzBdLCBxWzFdKTtcbiAgTShiLCBiLCB0KTtcbiAgTShjLCBwWzNdLCBxWzNdKTtcbiAgTShjLCBjLCBEMik7XG4gIE0oZCwgcFsyXSwgcVsyXSk7XG4gIEEoZCwgZCwgZCk7XG4gIFooZSwgYiwgYSk7XG4gIFooZiwgZCwgYyk7XG4gIEEoZywgZCwgYyk7XG4gIEEoaCwgYiwgYSk7XG5cbiAgTShwWzBdLCBlLCBmKTtcbiAgTShwWzFdLCBoLCBnKTtcbiAgTShwWzJdLCBnLCBmKTtcbiAgTShwWzNdLCBlLCBoKTtcbn1cblxuZnVuY3Rpb24gY3N3YXAocCwgcSwgYikge1xuICB2YXIgaTtcbiAgZm9yIChpID0gMDsgaSA8IDQ7IGkrKykge1xuICAgIHNlbDI1NTE5KHBbaV0sIHFbaV0sIGIpO1xuICB9XG59XG5cbmZ1bmN0aW9uIHBhY2sociwgcCkge1xuICB2YXIgdHggPSBnZigpLCB0eSA9IGdmKCksIHppID0gZ2YoKTtcbiAgaW52MjU1MTkoemksIHBbMl0pO1xuICBNKHR4LCBwWzBdLCB6aSk7XG4gIE0odHksIHBbMV0sIHppKTtcbiAgcGFjazI1NTE5KHIsIHR5KTtcbiAgclszMV0gXj0gcGFyMjU1MTkodHgpIDw8IDc7XG59XG5cbmZ1bmN0aW9uIHNjYWxhcm11bHQocCwgcSwgcykge1xuICB2YXIgYiwgaTtcbiAgc2V0MjU1MTkocFswXSwgZ2YwKTtcbiAgc2V0MjU1MTkocFsxXSwgZ2YxKTtcbiAgc2V0MjU1MTkocFsyXSwgZ2YxKTtcbiAgc2V0MjU1MTkocFszXSwgZ2YwKTtcbiAgZm9yIChpID0gMjU1OyBpID49IDA7IC0taSkge1xuICAgIGIgPSAoc1soaS84KXwwXSA+PiAoaSY3KSkgJiAxO1xuICAgIGNzd2FwKHAsIHEsIGIpO1xuICAgIGFkZChxLCBwKTtcbiAgICBhZGQocCwgcCk7XG4gICAgY3N3YXAocCwgcSwgYik7XG4gIH1cbn1cblxuZnVuY3Rpb24gc2NhbGFyYmFzZShwLCBzKSB7XG4gIHZhciBxID0gW2dmKCksIGdmKCksIGdmKCksIGdmKCldO1xuICBzZXQyNTUxOShxWzBdLCBYKTtcbiAgc2V0MjU1MTkocVsxXSwgWSk7XG4gIHNldDI1NTE5KHFbMl0sIGdmMSk7XG4gIE0ocVszXSwgWCwgWSk7XG4gIHNjYWxhcm11bHQocCwgcSwgcyk7XG59XG5cbmZ1bmN0aW9uIGNyeXB0b19zaWduX2tleXBhaXIocGssIHNrLCBzZWVkZWQpIHtcbiAgdmFyIGQgPSBuZXcgVWludDhBcnJheSg2NCk7XG4gIHZhciBwID0gW2dmKCksIGdmKCksIGdmKCksIGdmKCldO1xuICB2YXIgaTtcblxuICBpZiAoIXNlZWRlZCkgcmFuZG9tYnl0ZXMoc2ssIDMyKTtcbiAgY3J5cHRvX2hhc2goZCwgc2ssIDMyKTtcbiAgZFswXSAmPSAyNDg7XG4gIGRbMzFdICY9IDEyNztcbiAgZFszMV0gfD0gNjQ7XG5cbiAgc2NhbGFyYmFzZShwLCBkKTtcbiAgcGFjayhwaywgcCk7XG5cbiAgZm9yIChpID0gMDsgaSA8IDMyOyBpKyspIHNrW2krMzJdID0gcGtbaV07XG4gIHJldHVybiAwO1xufVxuXG52YXIgTCA9IG5ldyBGbG9hdDY0QXJyYXkoWzB4ZWQsIDB4ZDMsIDB4ZjUsIDB4NWMsIDB4MWEsIDB4NjMsIDB4MTIsIDB4NTgsIDB4ZDYsIDB4OWMsIDB4ZjcsIDB4YTIsIDB4ZGUsIDB4ZjksIDB4ZGUsIDB4MTQsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDB4MTBdKTtcblxuZnVuY3Rpb24gbW9kTChyLCB4KSB7XG4gIHZhciBjYXJyeSwgaSwgaiwgaztcbiAgZm9yIChpID0gNjM7IGkgPj0gMzI7IC0taSkge1xuICAgIGNhcnJ5ID0gMDtcbiAgICBmb3IgKGogPSBpIC0gMzIsIGsgPSBpIC0gMTI7IGogPCBrOyArK2opIHtcbiAgICAgIHhbal0gKz0gY2FycnkgLSAxNiAqIHhbaV0gKiBMW2ogLSAoaSAtIDMyKV07XG4gICAgICBjYXJyeSA9IE1hdGguZmxvb3IoKHhbal0gKyAxMjgpIC8gMjU2KTtcbiAgICAgIHhbal0gLT0gY2FycnkgKiAyNTY7XG4gICAgfVxuICAgIHhbal0gKz0gY2Fycnk7XG4gICAgeFtpXSA9IDA7XG4gIH1cbiAgY2FycnkgPSAwO1xuICBmb3IgKGogPSAwOyBqIDwgMzI7IGorKykge1xuICAgIHhbal0gKz0gY2FycnkgLSAoeFszMV0gPj4gNCkgKiBMW2pdO1xuICAgIGNhcnJ5ID0geFtqXSA+PiA4O1xuICAgIHhbal0gJj0gMjU1O1xuICB9XG4gIGZvciAoaiA9IDA7IGogPCAzMjsgaisrKSB4W2pdIC09IGNhcnJ5ICogTFtqXTtcbiAgZm9yIChpID0gMDsgaSA8IDMyOyBpKyspIHtcbiAgICB4W2krMV0gKz0geFtpXSA+PiA4O1xuICAgIHJbaV0gPSB4W2ldICYgMjU1O1xuICB9XG59XG5cbmZ1bmN0aW9uIHJlZHVjZShyKSB7XG4gIHZhciB4ID0gbmV3IEZsb2F0NjRBcnJheSg2NCksIGk7XG4gIGZvciAoaSA9IDA7IGkgPCA2NDsgaSsrKSB4W2ldID0gcltpXTtcbiAgZm9yIChpID0gMDsgaSA8IDY0OyBpKyspIHJbaV0gPSAwO1xuICBtb2RMKHIsIHgpO1xufVxuXG4vLyBOb3RlOiBkaWZmZXJlbmNlIGZyb20gQyAtIHNtbGVuIHJldHVybmVkLCBub3QgcGFzc2VkIGFzIGFyZ3VtZW50LlxuZnVuY3Rpb24gY3J5cHRvX3NpZ24oc20sIG0sIG4sIHNrKSB7XG4gIHZhciBkID0gbmV3IFVpbnQ4QXJyYXkoNjQpLCBoID0gbmV3IFVpbnQ4QXJyYXkoNjQpLCByID0gbmV3IFVpbnQ4QXJyYXkoNjQpO1xuICB2YXIgaSwgaiwgeCA9IG5ldyBGbG9hdDY0QXJyYXkoNjQpO1xuICB2YXIgcCA9IFtnZigpLCBnZigpLCBnZigpLCBnZigpXTtcblxuICBjcnlwdG9faGFzaChkLCBzaywgMzIpO1xuICBkWzBdICY9IDI0ODtcbiAgZFszMV0gJj0gMTI3O1xuICBkWzMxXSB8PSA2NDtcblxuICB2YXIgc21sZW4gPSBuICsgNjQ7XG4gIGZvciAoaSA9IDA7IGkgPCBuOyBpKyspIHNtWzY0ICsgaV0gPSBtW2ldO1xuICBmb3IgKGkgPSAwOyBpIDwgMzI7IGkrKykgc21bMzIgKyBpXSA9IGRbMzIgKyBpXTtcblxuICBjcnlwdG9faGFzaChyLCBzbS5zdWJhcnJheSgzMiksIG4rMzIpO1xuICByZWR1Y2Uocik7XG4gIHNjYWxhcmJhc2UocCwgcik7XG4gIHBhY2soc20sIHApO1xuXG4gIGZvciAoaSA9IDMyOyBpIDwgNjQ7IGkrKykgc21baV0gPSBza1tpXTtcbiAgY3J5cHRvX2hhc2goaCwgc20sIG4gKyA2NCk7XG4gIHJlZHVjZShoKTtcblxuICBmb3IgKGkgPSAwOyBpIDwgNjQ7IGkrKykgeFtpXSA9IDA7XG4gIGZvciAoaSA9IDA7IGkgPCAzMjsgaSsrKSB4W2ldID0gcltpXTtcbiAgZm9yIChpID0gMDsgaSA8IDMyOyBpKyspIHtcbiAgICBmb3IgKGogPSAwOyBqIDwgMzI7IGorKykge1xuICAgICAgeFtpK2pdICs9IGhbaV0gKiBkW2pdO1xuICAgIH1cbiAgfVxuXG4gIG1vZEwoc20uc3ViYXJyYXkoMzIpLCB4KTtcbiAgcmV0dXJuIHNtbGVuO1xufVxuXG5mdW5jdGlvbiB1bnBhY2tuZWcociwgcCkge1xuICB2YXIgdCA9IGdmKCksIGNoayA9IGdmKCksIG51bSA9IGdmKCksXG4gICAgICBkZW4gPSBnZigpLCBkZW4yID0gZ2YoKSwgZGVuNCA9IGdmKCksXG4gICAgICBkZW42ID0gZ2YoKTtcblxuICBzZXQyNTUxOShyWzJdLCBnZjEpO1xuICB1bnBhY2syNTUxOShyWzFdLCBwKTtcbiAgUyhudW0sIHJbMV0pO1xuICBNKGRlbiwgbnVtLCBEKTtcbiAgWihudW0sIG51bSwgclsyXSk7XG4gIEEoZGVuLCByWzJdLCBkZW4pO1xuXG4gIFMoZGVuMiwgZGVuKTtcbiAgUyhkZW40LCBkZW4yKTtcbiAgTShkZW42LCBkZW40LCBkZW4yKTtcbiAgTSh0LCBkZW42LCBudW0pO1xuICBNKHQsIHQsIGRlbik7XG5cbiAgcG93MjUyMyh0LCB0KTtcbiAgTSh0LCB0LCBudW0pO1xuICBNKHQsIHQsIGRlbik7XG4gIE0odCwgdCwgZGVuKTtcbiAgTShyWzBdLCB0LCBkZW4pO1xuXG4gIFMoY2hrLCByWzBdKTtcbiAgTShjaGssIGNoaywgZGVuKTtcbiAgaWYgKG5lcTI1NTE5KGNoaywgbnVtKSkgTShyWzBdLCByWzBdLCBJKTtcblxuICBTKGNoaywgclswXSk7XG4gIE0oY2hrLCBjaGssIGRlbik7XG4gIGlmIChuZXEyNTUxOShjaGssIG51bSkpIHJldHVybiAtMTtcblxuICBpZiAocGFyMjU1MTkoclswXSkgPT09IChwWzMxXT4+NykpIFooclswXSwgZ2YwLCByWzBdKTtcblxuICBNKHJbM10sIHJbMF0sIHJbMV0pO1xuICByZXR1cm4gMDtcbn1cblxuZnVuY3Rpb24gY3J5cHRvX3NpZ25fb3BlbihtLCBzbSwgbiwgcGspIHtcbiAgdmFyIGk7XG4gIHZhciB0ID0gbmV3IFVpbnQ4QXJyYXkoMzIpLCBoID0gbmV3IFVpbnQ4QXJyYXkoNjQpO1xuICB2YXIgcCA9IFtnZigpLCBnZigpLCBnZigpLCBnZigpXSxcbiAgICAgIHEgPSBbZ2YoKSwgZ2YoKSwgZ2YoKSwgZ2YoKV07XG5cbiAgaWYgKG4gPCA2NCkgcmV0dXJuIC0xO1xuXG4gIGlmICh1bnBhY2tuZWcocSwgcGspKSByZXR1cm4gLTE7XG5cbiAgZm9yIChpID0gMDsgaSA8IG47IGkrKykgbVtpXSA9IHNtW2ldO1xuICBmb3IgKGkgPSAwOyBpIDwgMzI7IGkrKykgbVtpKzMyXSA9IHBrW2ldO1xuICBjcnlwdG9faGFzaChoLCBtLCBuKTtcbiAgcmVkdWNlKGgpO1xuICBzY2FsYXJtdWx0KHAsIHEsIGgpO1xuXG4gIHNjYWxhcmJhc2UocSwgc20uc3ViYXJyYXkoMzIpKTtcbiAgYWRkKHAsIHEpO1xuICBwYWNrKHQsIHApO1xuXG4gIG4gLT0gNjQ7XG4gIGlmIChjcnlwdG9fdmVyaWZ5XzMyKHNtLCAwLCB0LCAwKSkge1xuICAgIGZvciAoaSA9IDA7IGkgPCBuOyBpKyspIG1baV0gPSAwO1xuICAgIHJldHVybiAtMTtcbiAgfVxuXG4gIGZvciAoaSA9IDA7IGkgPCBuOyBpKyspIG1baV0gPSBzbVtpICsgNjRdO1xuICByZXR1cm4gbjtcbn1cblxudmFyIGNyeXB0b19zZWNyZXRib3hfS0VZQllURVMgPSAzMixcbiAgICBjcnlwdG9fc2VjcmV0Ym94X05PTkNFQllURVMgPSAyNCxcbiAgICBjcnlwdG9fc2VjcmV0Ym94X1pFUk9CWVRFUyA9IDMyLFxuICAgIGNyeXB0b19zZWNyZXRib3hfQk9YWkVST0JZVEVTID0gMTYsXG4gICAgY3J5cHRvX3NjYWxhcm11bHRfQllURVMgPSAzMixcbiAgICBjcnlwdG9fc2NhbGFybXVsdF9TQ0FMQVJCWVRFUyA9IDMyLFxuICAgIGNyeXB0b19ib3hfUFVCTElDS0VZQllURVMgPSAzMixcbiAgICBjcnlwdG9fYm94X1NFQ1JFVEtFWUJZVEVTID0gMzIsXG4gICAgY3J5cHRvX2JveF9CRUZPUkVOTUJZVEVTID0gMzIsXG4gICAgY3J5cHRvX2JveF9OT05DRUJZVEVTID0gY3J5cHRvX3NlY3JldGJveF9OT05DRUJZVEVTLFxuICAgIGNyeXB0b19ib3hfWkVST0JZVEVTID0gY3J5cHRvX3NlY3JldGJveF9aRVJPQllURVMsXG4gICAgY3J5cHRvX2JveF9CT1haRVJPQllURVMgPSBjcnlwdG9fc2VjcmV0Ym94X0JPWFpFUk9CWVRFUyxcbiAgICBjcnlwdG9fc2lnbl9CWVRFUyA9IDY0LFxuICAgIGNyeXB0b19zaWduX1BVQkxJQ0tFWUJZVEVTID0gMzIsXG4gICAgY3J5cHRvX3NpZ25fU0VDUkVUS0VZQllURVMgPSA2NCxcbiAgICBjcnlwdG9fc2lnbl9TRUVEQllURVMgPSAzMixcbiAgICBjcnlwdG9faGFzaF9CWVRFUyA9IDY0O1xuXG5uYWNsLmxvd2xldmVsID0ge1xuICBjcnlwdG9fY29yZV9oc2Fsc2EyMDogY3J5cHRvX2NvcmVfaHNhbHNhMjAsXG4gIGNyeXB0b19zdHJlYW1feG9yOiBjcnlwdG9fc3RyZWFtX3hvcixcbiAgY3J5cHRvX3N0cmVhbTogY3J5cHRvX3N0cmVhbSxcbiAgY3J5cHRvX3N0cmVhbV9zYWxzYTIwX3hvcjogY3J5cHRvX3N0cmVhbV9zYWxzYTIwX3hvcixcbiAgY3J5cHRvX3N0cmVhbV9zYWxzYTIwOiBjcnlwdG9fc3RyZWFtX3NhbHNhMjAsXG4gIGNyeXB0b19vbmV0aW1lYXV0aDogY3J5cHRvX29uZXRpbWVhdXRoLFxuICBjcnlwdG9fb25ldGltZWF1dGhfdmVyaWZ5OiBjcnlwdG9fb25ldGltZWF1dGhfdmVyaWZ5LFxuICBjcnlwdG9fdmVyaWZ5XzE2OiBjcnlwdG9fdmVyaWZ5XzE2LFxuICBjcnlwdG9fdmVyaWZ5XzMyOiBjcnlwdG9fdmVyaWZ5XzMyLFxuICBjcnlwdG9fc2VjcmV0Ym94OiBjcnlwdG9fc2VjcmV0Ym94LFxuICBjcnlwdG9fc2VjcmV0Ym94X29wZW46IGNyeXB0b19zZWNyZXRib3hfb3BlbixcbiAgY3J5cHRvX3NjYWxhcm11bHQ6IGNyeXB0b19zY2FsYXJtdWx0LFxuICBjcnlwdG9fc2NhbGFybXVsdF9iYXNlOiBjcnlwdG9fc2NhbGFybXVsdF9iYXNlLFxuICBjcnlwdG9fYm94X2JlZm9yZW5tOiBjcnlwdG9fYm94X2JlZm9yZW5tLFxuICBjcnlwdG9fYm94X2FmdGVybm06IGNyeXB0b19ib3hfYWZ0ZXJubSxcbiAgY3J5cHRvX2JveDogY3J5cHRvX2JveCxcbiAgY3J5cHRvX2JveF9vcGVuOiBjcnlwdG9fYm94X29wZW4sXG4gIGNyeXB0b19ib3hfa2V5cGFpcjogY3J5cHRvX2JveF9rZXlwYWlyLFxuICBjcnlwdG9faGFzaDogY3J5cHRvX2hhc2gsXG4gIGNyeXB0b19zaWduOiBjcnlwdG9fc2lnbixcbiAgY3J5cHRvX3NpZ25fa2V5cGFpcjogY3J5cHRvX3NpZ25fa2V5cGFpcixcbiAgY3J5cHRvX3NpZ25fb3BlbjogY3J5cHRvX3NpZ25fb3BlbixcblxuICBjcnlwdG9fc2VjcmV0Ym94X0tFWUJZVEVTOiBjcnlwdG9fc2VjcmV0Ym94X0tFWUJZVEVTLFxuICBjcnlwdG9fc2VjcmV0Ym94X05PTkNFQllURVM6IGNyeXB0b19zZWNyZXRib3hfTk9OQ0VCWVRFUyxcbiAgY3J5cHRvX3NlY3JldGJveF9aRVJPQllURVM6IGNyeXB0b19zZWNyZXRib3hfWkVST0JZVEVTLFxuICBjcnlwdG9fc2VjcmV0Ym94X0JPWFpFUk9CWVRFUzogY3J5cHRvX3NlY3JldGJveF9CT1haRVJPQllURVMsXG4gIGNyeXB0b19zY2FsYXJtdWx0X0JZVEVTOiBjcnlwdG9fc2NhbGFybXVsdF9CWVRFUyxcbiAgY3J5cHRvX3NjYWxhcm11bHRfU0NBTEFSQllURVM6IGNyeXB0b19zY2FsYXJtdWx0X1NDQUxBUkJZVEVTLFxuICBjcnlwdG9fYm94X1BVQkxJQ0tFWUJZVEVTOiBjcnlwdG9fYm94X1BVQkxJQ0tFWUJZVEVTLFxuICBjcnlwdG9fYm94X1NFQ1JFVEtFWUJZVEVTOiBjcnlwdG9fYm94X1NFQ1JFVEtFWUJZVEVTLFxuICBjcnlwdG9fYm94X0JFRk9SRU5NQllURVM6IGNyeXB0b19ib3hfQkVGT1JFTk1CWVRFUyxcbiAgY3J5cHRvX2JveF9OT05DRUJZVEVTOiBjcnlwdG9fYm94X05PTkNFQllURVMsXG4gIGNyeXB0b19ib3hfWkVST0JZVEVTOiBjcnlwdG9fYm94X1pFUk9CWVRFUyxcbiAgY3J5cHRvX2JveF9CT1haRVJPQllURVM6IGNyeXB0b19ib3hfQk9YWkVST0JZVEVTLFxuICBjcnlwdG9fc2lnbl9CWVRFUzogY3J5cHRvX3NpZ25fQllURVMsXG4gIGNyeXB0b19zaWduX1BVQkxJQ0tFWUJZVEVTOiBjcnlwdG9fc2lnbl9QVUJMSUNLRVlCWVRFUyxcbiAgY3J5cHRvX3NpZ25fU0VDUkVUS0VZQllURVM6IGNyeXB0b19zaWduX1NFQ1JFVEtFWUJZVEVTLFxuICBjcnlwdG9fc2lnbl9TRUVEQllURVM6IGNyeXB0b19zaWduX1NFRURCWVRFUyxcbiAgY3J5cHRvX2hhc2hfQllURVM6IGNyeXB0b19oYXNoX0JZVEVTLFxuXG4gIGdmOiBnZixcbiAgRDogRCxcbiAgTDogTCxcbiAgcGFjazI1NTE5OiBwYWNrMjU1MTksXG4gIHVucGFjazI1NTE5OiB1bnBhY2syNTUxOSxcbiAgTTogTSxcbiAgQTogQSxcbiAgUzogUyxcbiAgWjogWixcbiAgcG93MjUyMzogcG93MjUyMyxcbiAgYWRkOiBhZGQsXG4gIHNldDI1NTE5OiBzZXQyNTUxOSxcbiAgbW9kTDogbW9kTCxcbiAgc2NhbGFybXVsdDogc2NhbGFybXVsdCxcbiAgc2NhbGFyYmFzZTogc2NhbGFyYmFzZSxcbn07XG5cbi8qIEhpZ2gtbGV2ZWwgQVBJICovXG5cbmZ1bmN0aW9uIGNoZWNrTGVuZ3RocyhrLCBuKSB7XG4gIGlmIChrLmxlbmd0aCAhPT0gY3J5cHRvX3NlY3JldGJveF9LRVlCWVRFUykgdGhyb3cgbmV3IEVycm9yKCdiYWQga2V5IHNpemUnKTtcbiAgaWYgKG4ubGVuZ3RoICE9PSBjcnlwdG9fc2VjcmV0Ym94X05PTkNFQllURVMpIHRocm93IG5ldyBFcnJvcignYmFkIG5vbmNlIHNpemUnKTtcbn1cblxuZnVuY3Rpb24gY2hlY2tCb3hMZW5ndGhzKHBrLCBzaykge1xuICBpZiAocGsubGVuZ3RoICE9PSBjcnlwdG9fYm94X1BVQkxJQ0tFWUJZVEVTKSB0aHJvdyBuZXcgRXJyb3IoJ2JhZCBwdWJsaWMga2V5IHNpemUnKTtcbiAgaWYgKHNrLmxlbmd0aCAhPT0gY3J5cHRvX2JveF9TRUNSRVRLRVlCWVRFUykgdGhyb3cgbmV3IEVycm9yKCdiYWQgc2VjcmV0IGtleSBzaXplJyk7XG59XG5cbmZ1bmN0aW9uIGNoZWNrQXJyYXlUeXBlcygpIHtcbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBhcmd1bWVudHMubGVuZ3RoOyBpKyspIHtcbiAgICBpZiAoIShhcmd1bWVudHNbaV0gaW5zdGFuY2VvZiBVaW50OEFycmF5KSlcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ3VuZXhwZWN0ZWQgdHlwZSwgdXNlIFVpbnQ4QXJyYXknKTtcbiAgfVxufVxuXG5mdW5jdGlvbiBjbGVhbnVwKGFycikge1xuICBmb3IgKHZhciBpID0gMDsgaSA8IGFyci5sZW5ndGg7IGkrKykgYXJyW2ldID0gMDtcbn1cblxubmFjbC5yYW5kb21CeXRlcyA9IGZ1bmN0aW9uKG4pIHtcbiAgdmFyIGIgPSBuZXcgVWludDhBcnJheShuKTtcbiAgcmFuZG9tYnl0ZXMoYiwgbik7XG4gIHJldHVybiBiO1xufTtcblxubmFjbC5zZWNyZXRib3ggPSBmdW5jdGlvbihtc2csIG5vbmNlLCBrZXkpIHtcbiAgY2hlY2tBcnJheVR5cGVzKG1zZywgbm9uY2UsIGtleSk7XG4gIGNoZWNrTGVuZ3RocyhrZXksIG5vbmNlKTtcbiAgdmFyIG0gPSBuZXcgVWludDhBcnJheShjcnlwdG9fc2VjcmV0Ym94X1pFUk9CWVRFUyArIG1zZy5sZW5ndGgpO1xuICB2YXIgYyA9IG5ldyBVaW50OEFycmF5KG0ubGVuZ3RoKTtcbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBtc2cubGVuZ3RoOyBpKyspIG1baStjcnlwdG9fc2VjcmV0Ym94X1pFUk9CWVRFU10gPSBtc2dbaV07XG4gIGNyeXB0b19zZWNyZXRib3goYywgbSwgbS5sZW5ndGgsIG5vbmNlLCBrZXkpO1xuICByZXR1cm4gYy5zdWJhcnJheShjcnlwdG9fc2VjcmV0Ym94X0JPWFpFUk9CWVRFUyk7XG59O1xuXG5uYWNsLnNlY3JldGJveC5vcGVuID0gZnVuY3Rpb24oYm94LCBub25jZSwga2V5KSB7XG4gIGNoZWNrQXJyYXlUeXBlcyhib3gsIG5vbmNlLCBrZXkpO1xuICBjaGVja0xlbmd0aHMoa2V5LCBub25jZSk7XG4gIHZhciBjID0gbmV3IFVpbnQ4QXJyYXkoY3J5cHRvX3NlY3JldGJveF9CT1haRVJPQllURVMgKyBib3gubGVuZ3RoKTtcbiAgdmFyIG0gPSBuZXcgVWludDhBcnJheShjLmxlbmd0aCk7XG4gIGZvciAodmFyIGkgPSAwOyBpIDwgYm94Lmxlbmd0aDsgaSsrKSBjW2krY3J5cHRvX3NlY3JldGJveF9CT1haRVJPQllURVNdID0gYm94W2ldO1xuICBpZiAoYy5sZW5ndGggPCAzMikgcmV0dXJuIG51bGw7XG4gIGlmIChjcnlwdG9fc2VjcmV0Ym94X29wZW4obSwgYywgYy5sZW5ndGgsIG5vbmNlLCBrZXkpICE9PSAwKSByZXR1cm4gbnVsbDtcbiAgcmV0dXJuIG0uc3ViYXJyYXkoY3J5cHRvX3NlY3JldGJveF9aRVJPQllURVMpO1xufTtcblxubmFjbC5zZWNyZXRib3gua2V5TGVuZ3RoID0gY3J5cHRvX3NlY3JldGJveF9LRVlCWVRFUztcbm5hY2wuc2VjcmV0Ym94Lm5vbmNlTGVuZ3RoID0gY3J5cHRvX3NlY3JldGJveF9OT05DRUJZVEVTO1xubmFjbC5zZWNyZXRib3gub3ZlcmhlYWRMZW5ndGggPSBjcnlwdG9fc2VjcmV0Ym94X0JPWFpFUk9CWVRFUztcblxubmFjbC5zY2FsYXJNdWx0ID0gZnVuY3Rpb24obiwgcCkge1xuICBjaGVja0FycmF5VHlwZXMobiwgcCk7XG4gIGlmIChuLmxlbmd0aCAhPT0gY3J5cHRvX3NjYWxhcm11bHRfU0NBTEFSQllURVMpIHRocm93IG5ldyBFcnJvcignYmFkIG4gc2l6ZScpO1xuICBpZiAocC5sZW5ndGggIT09IGNyeXB0b19zY2FsYXJtdWx0X0JZVEVTKSB0aHJvdyBuZXcgRXJyb3IoJ2JhZCBwIHNpemUnKTtcbiAgdmFyIHEgPSBuZXcgVWludDhBcnJheShjcnlwdG9fc2NhbGFybXVsdF9CWVRFUyk7XG4gIGNyeXB0b19zY2FsYXJtdWx0KHEsIG4sIHApO1xuICByZXR1cm4gcTtcbn07XG5cbm5hY2wuc2NhbGFyTXVsdC5iYXNlID0gZnVuY3Rpb24obikge1xuICBjaGVja0FycmF5VHlwZXMobik7XG4gIGlmIChuLmxlbmd0aCAhPT0gY3J5cHRvX3NjYWxhcm11bHRfU0NBTEFSQllURVMpIHRocm93IG5ldyBFcnJvcignYmFkIG4gc2l6ZScpO1xuICB2YXIgcSA9IG5ldyBVaW50OEFycmF5KGNyeXB0b19zY2FsYXJtdWx0X0JZVEVTKTtcbiAgY3J5cHRvX3NjYWxhcm11bHRfYmFzZShxLCBuKTtcbiAgcmV0dXJuIHE7XG59O1xuXG5uYWNsLnNjYWxhck11bHQuc2NhbGFyTGVuZ3RoID0gY3J5cHRvX3NjYWxhcm11bHRfU0NBTEFSQllURVM7XG5uYWNsLnNjYWxhck11bHQuZ3JvdXBFbGVtZW50TGVuZ3RoID0gY3J5cHRvX3NjYWxhcm11bHRfQllURVM7XG5cbm5hY2wuYm94ID0gZnVuY3Rpb24obXNnLCBub25jZSwgcHVibGljS2V5LCBzZWNyZXRLZXkpIHtcbiAgdmFyIGsgPSBuYWNsLmJveC5iZWZvcmUocHVibGljS2V5LCBzZWNyZXRLZXkpO1xuICByZXR1cm4gbmFjbC5zZWNyZXRib3gobXNnLCBub25jZSwgayk7XG59O1xuXG5uYWNsLmJveC5iZWZvcmUgPSBmdW5jdGlvbihwdWJsaWNLZXksIHNlY3JldEtleSkge1xuICBjaGVja0FycmF5VHlwZXMocHVibGljS2V5LCBzZWNyZXRLZXkpO1xuICBjaGVja0JveExlbmd0aHMocHVibGljS2V5LCBzZWNyZXRLZXkpO1xuICB2YXIgayA9IG5ldyBVaW50OEFycmF5KGNyeXB0b19ib3hfQkVGT1JFTk1CWVRFUyk7XG4gIGNyeXB0b19ib3hfYmVmb3Jlbm0oaywgcHVibGljS2V5LCBzZWNyZXRLZXkpO1xuICByZXR1cm4gaztcbn07XG5cbm5hY2wuYm94LmFmdGVyID0gbmFjbC5zZWNyZXRib3g7XG5cbm5hY2wuYm94Lm9wZW4gPSBmdW5jdGlvbihtc2csIG5vbmNlLCBwdWJsaWNLZXksIHNlY3JldEtleSkge1xuICB2YXIgayA9IG5hY2wuYm94LmJlZm9yZShwdWJsaWNLZXksIHNlY3JldEtleSk7XG4gIHJldHVybiBuYWNsLnNlY3JldGJveC5vcGVuKG1zZywgbm9uY2UsIGspO1xufTtcblxubmFjbC5ib3gub3Blbi5hZnRlciA9IG5hY2wuc2VjcmV0Ym94Lm9wZW47XG5cbm5hY2wuYm94LmtleVBhaXIgPSBmdW5jdGlvbigpIHtcbiAgdmFyIHBrID0gbmV3IFVpbnQ4QXJyYXkoY3J5cHRvX2JveF9QVUJMSUNLRVlCWVRFUyk7XG4gIHZhciBzayA9IG5ldyBVaW50OEFycmF5KGNyeXB0b19ib3hfU0VDUkVUS0VZQllURVMpO1xuICBjcnlwdG9fYm94X2tleXBhaXIocGssIHNrKTtcbiAgcmV0dXJuIHtwdWJsaWNLZXk6IHBrLCBzZWNyZXRLZXk6IHNrfTtcbn07XG5cbm5hY2wuYm94LmtleVBhaXIuZnJvbVNlY3JldEtleSA9IGZ1bmN0aW9uKHNlY3JldEtleSkge1xuICBjaGVja0FycmF5VHlwZXMoc2VjcmV0S2V5KTtcbiAgaWYgKHNlY3JldEtleS5sZW5ndGggIT09IGNyeXB0b19ib3hfU0VDUkVUS0VZQllURVMpXG4gICAgdGhyb3cgbmV3IEVycm9yKCdiYWQgc2VjcmV0IGtleSBzaXplJyk7XG4gIHZhciBwayA9IG5ldyBVaW50OEFycmF5KGNyeXB0b19ib3hfUFVCTElDS0VZQllURVMpO1xuICBjcnlwdG9fc2NhbGFybXVsdF9iYXNlKHBrLCBzZWNyZXRLZXkpO1xuICByZXR1cm4ge3B1YmxpY0tleTogcGssIHNlY3JldEtleTogbmV3IFVpbnQ4QXJyYXkoc2VjcmV0S2V5KX07XG59O1xuXG5uYWNsLmJveC5wdWJsaWNLZXlMZW5ndGggPSBjcnlwdG9fYm94X1BVQkxJQ0tFWUJZVEVTO1xubmFjbC5ib3guc2VjcmV0S2V5TGVuZ3RoID0gY3J5cHRvX2JveF9TRUNSRVRLRVlCWVRFUztcbm5hY2wuYm94LnNoYXJlZEtleUxlbmd0aCA9IGNyeXB0b19ib3hfQkVGT1JFTk1CWVRFUztcbm5hY2wuYm94Lm5vbmNlTGVuZ3RoID0gY3J5cHRvX2JveF9OT05DRUJZVEVTO1xubmFjbC5ib3gub3ZlcmhlYWRMZW5ndGggPSBuYWNsLnNlY3JldGJveC5vdmVyaGVhZExlbmd0aDtcblxubmFjbC5zaWduID0gZnVuY3Rpb24obXNnLCBzZWNyZXRLZXkpIHtcbiAgY2hlY2tBcnJheVR5cGVzKG1zZywgc2VjcmV0S2V5KTtcbiAgaWYgKHNlY3JldEtleS5sZW5ndGggIT09IGNyeXB0b19zaWduX1NFQ1JFVEtFWUJZVEVTKVxuICAgIHRocm93IG5ldyBFcnJvcignYmFkIHNlY3JldCBrZXkgc2l6ZScpO1xuICB2YXIgc2lnbmVkTXNnID0gbmV3IFVpbnQ4QXJyYXkoY3J5cHRvX3NpZ25fQllURVMrbXNnLmxlbmd0aCk7XG4gIGNyeXB0b19zaWduKHNpZ25lZE1zZywgbXNnLCBtc2cubGVuZ3RoLCBzZWNyZXRLZXkpO1xuICByZXR1cm4gc2lnbmVkTXNnO1xufTtcblxubmFjbC5zaWduLm9wZW4gPSBmdW5jdGlvbihzaWduZWRNc2csIHB1YmxpY0tleSkge1xuICBjaGVja0FycmF5VHlwZXMoc2lnbmVkTXNnLCBwdWJsaWNLZXkpO1xuICBpZiAocHVibGljS2V5Lmxlbmd0aCAhPT0gY3J5cHRvX3NpZ25fUFVCTElDS0VZQllURVMpXG4gICAgdGhyb3cgbmV3IEVycm9yKCdiYWQgcHVibGljIGtleSBzaXplJyk7XG4gIHZhciB0bXAgPSBuZXcgVWludDhBcnJheShzaWduZWRNc2cubGVuZ3RoKTtcbiAgdmFyIG1sZW4gPSBjcnlwdG9fc2lnbl9vcGVuKHRtcCwgc2lnbmVkTXNnLCBzaWduZWRNc2cubGVuZ3RoLCBwdWJsaWNLZXkpO1xuICBpZiAobWxlbiA8IDApIHJldHVybiBudWxsO1xuICB2YXIgbSA9IG5ldyBVaW50OEFycmF5KG1sZW4pO1xuICBmb3IgKHZhciBpID0gMDsgaSA8IG0ubGVuZ3RoOyBpKyspIG1baV0gPSB0bXBbaV07XG4gIHJldHVybiBtO1xufTtcblxubmFjbC5zaWduLmRldGFjaGVkID0gZnVuY3Rpb24obXNnLCBzZWNyZXRLZXkpIHtcbiAgdmFyIHNpZ25lZE1zZyA9IG5hY2wuc2lnbihtc2csIHNlY3JldEtleSk7XG4gIHZhciBzaWcgPSBuZXcgVWludDhBcnJheShjcnlwdG9fc2lnbl9CWVRFUyk7XG4gIGZvciAodmFyIGkgPSAwOyBpIDwgc2lnLmxlbmd0aDsgaSsrKSBzaWdbaV0gPSBzaWduZWRNc2dbaV07XG4gIHJldHVybiBzaWc7XG59O1xuXG5uYWNsLnNpZ24uZGV0YWNoZWQudmVyaWZ5ID0gZnVuY3Rpb24obXNnLCBzaWcsIHB1YmxpY0tleSkge1xuICBjaGVja0FycmF5VHlwZXMobXNnLCBzaWcsIHB1YmxpY0tleSk7XG4gIGlmIChzaWcubGVuZ3RoICE9PSBjcnlwdG9fc2lnbl9CWVRFUylcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ2JhZCBzaWduYXR1cmUgc2l6ZScpO1xuICBpZiAocHVibGljS2V5Lmxlbmd0aCAhPT0gY3J5cHRvX3NpZ25fUFVCTElDS0VZQllURVMpXG4gICAgdGhyb3cgbmV3IEVycm9yKCdiYWQgcHVibGljIGtleSBzaXplJyk7XG4gIHZhciBzbSA9IG5ldyBVaW50OEFycmF5KGNyeXB0b19zaWduX0JZVEVTICsgbXNnLmxlbmd0aCk7XG4gIHZhciBtID0gbmV3IFVpbnQ4QXJyYXkoY3J5cHRvX3NpZ25fQllURVMgKyBtc2cubGVuZ3RoKTtcbiAgdmFyIGk7XG4gIGZvciAoaSA9IDA7IGkgPCBjcnlwdG9fc2lnbl9CWVRFUzsgaSsrKSBzbVtpXSA9IHNpZ1tpXTtcbiAgZm9yIChpID0gMDsgaSA8IG1zZy5sZW5ndGg7IGkrKykgc21baStjcnlwdG9fc2lnbl9CWVRFU10gPSBtc2dbaV07XG4gIHJldHVybiAoY3J5cHRvX3NpZ25fb3BlbihtLCBzbSwgc20ubGVuZ3RoLCBwdWJsaWNLZXkpID49IDApO1xufTtcblxubmFjbC5zaWduLmtleVBhaXIgPSBmdW5jdGlvbigpIHtcbiAgdmFyIHBrID0gbmV3IFVpbnQ4QXJyYXkoY3J5cHRvX3NpZ25fUFVCTElDS0VZQllURVMpO1xuICB2YXIgc2sgPSBuZXcgVWludDhBcnJheShjcnlwdG9fc2lnbl9TRUNSRVRLRVlCWVRFUyk7XG4gIGNyeXB0b19zaWduX2tleXBhaXIocGssIHNrKTtcbiAgcmV0dXJuIHtwdWJsaWNLZXk6IHBrLCBzZWNyZXRLZXk6IHNrfTtcbn07XG5cbm5hY2wuc2lnbi5rZXlQYWlyLmZyb21TZWNyZXRLZXkgPSBmdW5jdGlvbihzZWNyZXRLZXkpIHtcbiAgY2hlY2tBcnJheVR5cGVzKHNlY3JldEtleSk7XG4gIGlmIChzZWNyZXRLZXkubGVuZ3RoICE9PSBjcnlwdG9fc2lnbl9TRUNSRVRLRVlCWVRFUylcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ2JhZCBzZWNyZXQga2V5IHNpemUnKTtcbiAgdmFyIHBrID0gbmV3IFVpbnQ4QXJyYXkoY3J5cHRvX3NpZ25fUFVCTElDS0VZQllURVMpO1xuICBmb3IgKHZhciBpID0gMDsgaSA8IHBrLmxlbmd0aDsgaSsrKSBwa1tpXSA9IHNlY3JldEtleVszMitpXTtcbiAgcmV0dXJuIHtwdWJsaWNLZXk6IHBrLCBzZWNyZXRLZXk6IG5ldyBVaW50OEFycmF5KHNlY3JldEtleSl9O1xufTtcblxubmFjbC5zaWduLmtleVBhaXIuZnJvbVNlZWQgPSBmdW5jdGlvbihzZWVkKSB7XG4gIGNoZWNrQXJyYXlUeXBlcyhzZWVkKTtcbiAgaWYgKHNlZWQubGVuZ3RoICE9PSBjcnlwdG9fc2lnbl9TRUVEQllURVMpXG4gICAgdGhyb3cgbmV3IEVycm9yKCdiYWQgc2VlZCBzaXplJyk7XG4gIHZhciBwayA9IG5ldyBVaW50OEFycmF5KGNyeXB0b19zaWduX1BVQkxJQ0tFWUJZVEVTKTtcbiAgdmFyIHNrID0gbmV3IFVpbnQ4QXJyYXkoY3J5cHRvX3NpZ25fU0VDUkVUS0VZQllURVMpO1xuICBmb3IgKHZhciBpID0gMDsgaSA8IDMyOyBpKyspIHNrW2ldID0gc2VlZFtpXTtcbiAgY3J5cHRvX3NpZ25fa2V5cGFpcihwaywgc2ssIHRydWUpO1xuICByZXR1cm4ge3B1YmxpY0tleTogcGssIHNlY3JldEtleTogc2t9O1xufTtcblxubmFjbC5zaWduLnB1YmxpY0tleUxlbmd0aCA9IGNyeXB0b19zaWduX1BVQkxJQ0tFWUJZVEVTO1xubmFjbC5zaWduLnNlY3JldEtleUxlbmd0aCA9IGNyeXB0b19zaWduX1NFQ1JFVEtFWUJZVEVTO1xubmFjbC5zaWduLnNlZWRMZW5ndGggPSBjcnlwdG9fc2lnbl9TRUVEQllURVM7XG5uYWNsLnNpZ24uc2lnbmF0dXJlTGVuZ3RoID0gY3J5cHRvX3NpZ25fQllURVM7XG5cbm5hY2wuaGFzaCA9IGZ1bmN0aW9uKG1zZykge1xuICBjaGVja0FycmF5VHlwZXMobXNnKTtcbiAgdmFyIGggPSBuZXcgVWludDhBcnJheShjcnlwdG9faGFzaF9CWVRFUyk7XG4gIGNyeXB0b19oYXNoKGgsIG1zZywgbXNnLmxlbmd0aCk7XG4gIHJldHVybiBoO1xufTtcblxubmFjbC5oYXNoLmhhc2hMZW5ndGggPSBjcnlwdG9faGFzaF9CWVRFUztcblxubmFjbC52ZXJpZnkgPSBmdW5jdGlvbih4LCB5KSB7XG4gIGNoZWNrQXJyYXlUeXBlcyh4LCB5KTtcbiAgLy8gWmVybyBsZW5ndGggYXJndW1lbnRzIGFyZSBjb25zaWRlcmVkIG5vdCBlcXVhbC5cbiAgaWYgKHgubGVuZ3RoID09PSAwIHx8IHkubGVuZ3RoID09PSAwKSByZXR1cm4gZmFsc2U7XG4gIGlmICh4Lmxlbmd0aCAhPT0geS5sZW5ndGgpIHJldHVybiBmYWxzZTtcbiAgcmV0dXJuICh2bih4LCAwLCB5LCAwLCB4Lmxlbmd0aCkgPT09IDApID8gdHJ1ZSA6IGZhbHNlO1xufTtcblxubmFjbC5zZXRQUk5HID0gZnVuY3Rpb24oZm4pIHtcbiAgcmFuZG9tYnl0ZXMgPSBmbjtcbn07XG5cbihmdW5jdGlvbigpIHtcbiAgLy8gSW5pdGlhbGl6ZSBQUk5HIGlmIGVudmlyb25tZW50IHByb3ZpZGVzIENTUFJORy5cbiAgLy8gSWYgbm90LCBtZXRob2RzIGNhbGxpbmcgcmFuZG9tYnl0ZXMgd2lsbCB0aHJvdy5cbiAgdmFyIGNyeXB0byA9IHR5cGVvZiBzZWxmICE9PSAndW5kZWZpbmVkJyA/IChzZWxmLmNyeXB0byB8fCBzZWxmLm1zQ3J5cHRvKSA6IG51bGw7XG4gIGlmIChjcnlwdG8gJiYgY3J5cHRvLmdldFJhbmRvbVZhbHVlcykge1xuICAgIC8vIEJyb3dzZXJzLlxuICAgIHZhciBRVU9UQSA9IDY1NTM2O1xuICAgIG5hY2wuc2V0UFJORyhmdW5jdGlvbih4LCBuKSB7XG4gICAgICB2YXIgaSwgdiA9IG5ldyBVaW50OEFycmF5KG4pO1xuICAgICAgZm9yIChpID0gMDsgaSA8IG47IGkgKz0gUVVPVEEpIHtcbiAgICAgICAgY3J5cHRvLmdldFJhbmRvbVZhbHVlcyh2LnN1YmFycmF5KGksIGkgKyBNYXRoLm1pbihuIC0gaSwgUVVPVEEpKSk7XG4gICAgICB9XG4gICAgICBmb3IgKGkgPSAwOyBpIDwgbjsgaSsrKSB4W2ldID0gdltpXTtcbiAgICAgIGNsZWFudXAodik7XG4gICAgfSk7XG4gIH0gZWxzZSBpZiAodHlwZW9mIHJlcXVpcmUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgLy8gTm9kZS5qcy5cbiAgICBjcnlwdG8gPSByZXF1aXJlKCdjcnlwdG8nKTtcbiAgICBpZiAoY3J5cHRvICYmIGNyeXB0by5yYW5kb21CeXRlcykge1xuICAgICAgbmFjbC5zZXRQUk5HKGZ1bmN0aW9uKHgsIG4pIHtcbiAgICAgICAgdmFyIGksIHYgPSBjcnlwdG8ucmFuZG9tQnl0ZXMobik7XG4gICAgICAgIGZvciAoaSA9IDA7IGkgPCBuOyBpKyspIHhbaV0gPSB2W2ldO1xuICAgICAgICBjbGVhbnVwKHYpO1xuICAgICAgfSk7XG4gICAgfVxuICB9XG59KSgpO1xuXG59KSh0eXBlb2YgbW9kdWxlICE9PSAndW5kZWZpbmVkJyAmJiBtb2R1bGUuZXhwb3J0cyA/IG1vZHVsZS5leHBvcnRzIDogKHNlbGYubmFjbCA9IHNlbGYubmFjbCB8fCB7fSkpO1xuIl0sIm5hbWVzIjpbIm5hY2wiLCJnZiIsImluaXQiLCJpIiwiciIsIkZsb2F0NjRBcnJheSIsImxlbmd0aCIsInJhbmRvbWJ5dGVzIiwiRXJyb3IiLCJfMCIsIlVpbnQ4QXJyYXkiLCJfOSIsImdmMCIsImdmMSIsIl8xMjE2NjUiLCJEIiwiRDIiLCJYIiwiWSIsIkkiLCJ0czY0IiwieCIsImgiLCJsIiwidm4iLCJ4aSIsInkiLCJ5aSIsIm4iLCJkIiwiY3J5cHRvX3ZlcmlmeV8xNiIsImNyeXB0b192ZXJpZnlfMzIiLCJjb3JlX3NhbHNhMjAiLCJvIiwicCIsImsiLCJjIiwiajAiLCJqMSIsImoyIiwiajMiLCJqNCIsImo1IiwiajYiLCJqNyIsImo4IiwiajkiLCJqMTAiLCJqMTEiLCJqMTIiLCJqMTMiLCJqMTQiLCJqMTUiLCJ4MCIsIngxIiwieDIiLCJ4MyIsIng0IiwieDUiLCJ4NiIsIng3IiwieDgiLCJ4OSIsIngxMCIsIngxMSIsIngxMiIsIngxMyIsIngxNCIsIngxNSIsInUiLCJjb3JlX2hzYWxzYTIwIiwiY3J5cHRvX2NvcmVfc2Fsc2EyMCIsIm91dCIsImlucCIsImNyeXB0b19jb3JlX2hzYWxzYTIwIiwic2lnbWEiLCJjcnlwdG9fc3RyZWFtX3NhbHNhMjBfeG9yIiwiY3BvcyIsIm0iLCJtcG9zIiwiYiIsInoiLCJjcnlwdG9fc3RyZWFtX3NhbHNhMjAiLCJjcnlwdG9fc3RyZWFtIiwicyIsInNuIiwiY3J5cHRvX3N0cmVhbV94b3IiLCJwb2x5MTMwNSIsImtleSIsImJ1ZmZlciIsIlVpbnQxNkFycmF5IiwicGFkIiwibGVmdG92ZXIiLCJmaW4iLCJ0MCIsInQxIiwidDIiLCJ0MyIsInQ0IiwidDUiLCJ0NiIsInQ3IiwicHJvdG90eXBlIiwiYmxvY2tzIiwiYnl0ZXMiLCJoaWJpdCIsImQwIiwiZDEiLCJkMiIsImQzIiwiZDQiLCJkNSIsImQ2IiwiZDciLCJkOCIsImQ5IiwiaDAiLCJoMSIsImgyIiwiaDMiLCJoNCIsImg1IiwiaDYiLCJoNyIsImg4IiwiaDkiLCJyMCIsInIxIiwicjIiLCJyMyIsInI0IiwicjUiLCJyNiIsInI3IiwicjgiLCJyOSIsImZpbmlzaCIsIm1hYyIsIm1hY3BvcyIsImciLCJtYXNrIiwiZiIsInVwZGF0ZSIsIndhbnQiLCJjcnlwdG9fb25ldGltZWF1dGgiLCJvdXRwb3MiLCJjcnlwdG9fb25ldGltZWF1dGhfdmVyaWZ5IiwiaHBvcyIsImNyeXB0b19zZWNyZXRib3giLCJjcnlwdG9fc2VjcmV0Ym94X29wZW4iLCJzZXQyNTUxOSIsImEiLCJjYXIyNTUxOSIsInYiLCJNYXRoIiwiZmxvb3IiLCJzZWwyNTUxOSIsInEiLCJ0IiwicGFjazI1NTE5IiwiaiIsIm5lcTI1NTE5IiwicGFyMjU1MTkiLCJ1bnBhY2syNTUxOSIsIkEiLCJaIiwiTSIsInQ4IiwidDkiLCJ0MTAiLCJ0MTEiLCJ0MTIiLCJ0MTMiLCJ0MTQiLCJ0MTUiLCJ0MTYiLCJ0MTciLCJ0MTgiLCJ0MTkiLCJ0MjAiLCJ0MjEiLCJ0MjIiLCJ0MjMiLCJ0MjQiLCJ0MjUiLCJ0MjYiLCJ0MjciLCJ0MjgiLCJ0MjkiLCJ0MzAiLCJiMCIsImIxIiwiYjIiLCJiMyIsImI0IiwiYjUiLCJiNiIsImI3IiwiYjgiLCJiOSIsImIxMCIsImIxMSIsImIxMiIsImIxMyIsImIxNCIsImIxNSIsIlMiLCJpbnYyNTUxOSIsInBvdzI1MjMiLCJjcnlwdG9fc2NhbGFybXVsdCIsImUiLCJ4MzIiLCJzdWJhcnJheSIsIngxNiIsImNyeXB0b19zY2FsYXJtdWx0X2Jhc2UiLCJjcnlwdG9fYm94X2tleXBhaXIiLCJjcnlwdG9fYm94X2JlZm9yZW5tIiwiY3J5cHRvX2JveF9hZnRlcm5tIiwiY3J5cHRvX2JveF9vcGVuX2FmdGVybm0iLCJjcnlwdG9fYm94IiwiY3J5cHRvX2JveF9vcGVuIiwiSyIsImNyeXB0b19oYXNoYmxvY2tzX2hsIiwiaGgiLCJobCIsIndoIiwiSW50MzJBcnJheSIsIndsIiwiYmgwIiwiYmgxIiwiYmgyIiwiYmgzIiwiYmg0IiwiYmg1IiwiYmg2IiwiYmg3IiwiYmwwIiwiYmwxIiwiYmwyIiwiYmwzIiwiYmw0IiwiYmw1IiwiYmw2IiwiYmw3IiwidGgiLCJ0bCIsImFoMCIsImFoMSIsImFoMiIsImFoMyIsImFoNCIsImFoNSIsImFoNiIsImFoNyIsImFsMCIsImFsMSIsImFsMiIsImFsMyIsImFsNCIsImFsNSIsImFsNiIsImFsNyIsInBvcyIsImNyeXB0b19oYXNoIiwiYWRkIiwiY3N3YXAiLCJwYWNrIiwidHgiLCJ0eSIsInppIiwic2NhbGFybXVsdCIsInNjYWxhcmJhc2UiLCJjcnlwdG9fc2lnbl9rZXlwYWlyIiwicGsiLCJzayIsInNlZWRlZCIsIkwiLCJtb2RMIiwiY2FycnkiLCJyZWR1Y2UiLCJjcnlwdG9fc2lnbiIsInNtIiwic21sZW4iLCJ1bnBhY2tuZWciLCJjaGsiLCJudW0iLCJkZW4iLCJkZW4yIiwiZGVuNCIsImRlbjYiLCJjcnlwdG9fc2lnbl9vcGVuIiwiY3J5cHRvX3NlY3JldGJveF9LRVlCWVRFUyIsImNyeXB0b19zZWNyZXRib3hfTk9OQ0VCWVRFUyIsImNyeXB0b19zZWNyZXRib3hfWkVST0JZVEVTIiwiY3J5cHRvX3NlY3JldGJveF9CT1haRVJPQllURVMiLCJjcnlwdG9fc2NhbGFybXVsdF9CWVRFUyIsImNyeXB0b19zY2FsYXJtdWx0X1NDQUxBUkJZVEVTIiwiY3J5cHRvX2JveF9QVUJMSUNLRVlCWVRFUyIsImNyeXB0b19ib3hfU0VDUkVUS0VZQllURVMiLCJjcnlwdG9fYm94X0JFRk9SRU5NQllURVMiLCJjcnlwdG9fYm94X05PTkNFQllURVMiLCJjcnlwdG9fYm94X1pFUk9CWVRFUyIsImNyeXB0b19ib3hfQk9YWkVST0JZVEVTIiwiY3J5cHRvX3NpZ25fQllURVMiLCJjcnlwdG9fc2lnbl9QVUJMSUNLRVlCWVRFUyIsImNyeXB0b19zaWduX1NFQ1JFVEtFWUJZVEVTIiwiY3J5cHRvX3NpZ25fU0VFREJZVEVTIiwiY3J5cHRvX2hhc2hfQllURVMiLCJsb3dsZXZlbCIsImNoZWNrTGVuZ3RocyIsImNoZWNrQm94TGVuZ3RocyIsImNoZWNrQXJyYXlUeXBlcyIsImFyZ3VtZW50cyIsIlR5cGVFcnJvciIsImNsZWFudXAiLCJhcnIiLCJyYW5kb21CeXRlcyIsInNlY3JldGJveCIsIm1zZyIsIm5vbmNlIiwib3BlbiIsImJveCIsImtleUxlbmd0aCIsIm5vbmNlTGVuZ3RoIiwib3ZlcmhlYWRMZW5ndGgiLCJzY2FsYXJNdWx0IiwiYmFzZSIsInNjYWxhckxlbmd0aCIsImdyb3VwRWxlbWVudExlbmd0aCIsInB1YmxpY0tleSIsInNlY3JldEtleSIsImJlZm9yZSIsImFmdGVyIiwia2V5UGFpciIsImZyb21TZWNyZXRLZXkiLCJwdWJsaWNLZXlMZW5ndGgiLCJzZWNyZXRLZXlMZW5ndGgiLCJzaGFyZWRLZXlMZW5ndGgiLCJzaWduIiwic2lnbmVkTXNnIiwidG1wIiwibWxlbiIsImRldGFjaGVkIiwic2lnIiwidmVyaWZ5IiwiZnJvbVNlZWQiLCJzZWVkIiwic2VlZExlbmd0aCIsInNpZ25hdHVyZUxlbmd0aCIsImhhc2giLCJoYXNoTGVuZ3RoIiwic2V0UFJORyIsImZuIiwiY3J5cHRvIiwic2VsZiIsIm1zQ3J5cHRvIiwiZ2V0UmFuZG9tVmFsdWVzIiwiUVVPVEEiLCJtaW4iLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tweetnacl/nacl-fast.js\n");

/***/ })

};
;