/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/base64url";
exports.ids = ["vendor-chunks/base64url"];
exports.modules = {

/***/ "(ssr)/./node_modules/base64url/dist/base64url.js":
/*!**************************************************!*\
  !*** ./node_modules/base64url/dist/base64url.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar pad_string_1 = __webpack_require__(/*! ./pad-string */ \"(ssr)/./node_modules/base64url/dist/pad-string.js\");\nfunction encode(input, encoding) {\n    if (encoding === void 0) {\n        encoding = \"utf8\";\n    }\n    if (Buffer.isBuffer(input)) {\n        return fromBase64(input.toString(\"base64\"));\n    }\n    return fromBase64(Buffer.from(input, encoding).toString(\"base64\"));\n}\n;\nfunction decode(base64url, encoding) {\n    if (encoding === void 0) {\n        encoding = \"utf8\";\n    }\n    return Buffer.from(toBase64(base64url), \"base64\").toString(encoding);\n}\nfunction toBase64(base64url) {\n    base64url = base64url.toString();\n    return pad_string_1.default(base64url).replace(/\\-/g, \"+\").replace(/_/g, \"/\");\n}\nfunction fromBase64(base64) {\n    return base64.replace(/=/g, \"\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\");\n}\nfunction toBuffer(base64url) {\n    return Buffer.from(toBase64(base64url), \"base64\");\n}\nvar base64url = encode;\nbase64url.encode = encode;\nbase64url.decode = decode;\nbase64url.toBase64 = toBase64;\nbase64url.fromBase64 = fromBase64;\nbase64url.toBuffer = toBuffer;\nexports[\"default\"] = base64url;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/base64url/dist/base64url.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/base64url/dist/pad-string.js":
/*!***************************************************!*\
  !*** ./node_modules/base64url/dist/pad-string.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nfunction padString(input) {\n    var segmentLength = 4;\n    var stringLength = input.length;\n    var diff = stringLength % segmentLength;\n    if (!diff) {\n        return input;\n    }\n    var position = stringLength;\n    var padLength = segmentLength - diff;\n    var paddedStringLength = stringLength + padLength;\n    var buffer = Buffer.alloc(paddedStringLength);\n    buffer.write(input);\n    while(padLength--){\n        buffer.write(\"=\", position++);\n    }\n    return buffer.toString();\n}\nexports[\"default\"] = padString;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYmFzZTY0dXJsL2Rpc3QvcGFkLXN0cmluZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUM3RCxTQUFTQyxVQUFVQyxLQUFLO0lBQ3BCLElBQUlDLGdCQUFnQjtJQUNwQixJQUFJQyxlQUFlRixNQUFNRyxNQUFNO0lBQy9CLElBQUlDLE9BQU9GLGVBQWVEO0lBQzFCLElBQUksQ0FBQ0csTUFBTTtRQUNQLE9BQU9KO0lBQ1g7SUFDQSxJQUFJSyxXQUFXSDtJQUNmLElBQUlJLFlBQVlMLGdCQUFnQkc7SUFDaEMsSUFBSUcscUJBQXFCTCxlQUFlSTtJQUN4QyxJQUFJRSxTQUFTQyxPQUFPQyxLQUFLLENBQUNIO0lBQzFCQyxPQUFPRyxLQUFLLENBQUNYO0lBQ2IsTUFBT00sWUFBYTtRQUNoQkUsT0FBT0csS0FBSyxDQUFDLEtBQUtOO0lBQ3RCO0lBQ0EsT0FBT0csT0FBT0ksUUFBUTtBQUMxQjtBQUNBZixrQkFBZSxHQUFHRSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXdlYjMtY3JlYXRvci1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9iYXNlNjR1cmwvZGlzdC9wYWQtc3RyaW5nLmpzPzBjYTYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5mdW5jdGlvbiBwYWRTdHJpbmcoaW5wdXQpIHtcbiAgICB2YXIgc2VnbWVudExlbmd0aCA9IDQ7XG4gICAgdmFyIHN0cmluZ0xlbmd0aCA9IGlucHV0Lmxlbmd0aDtcbiAgICB2YXIgZGlmZiA9IHN0cmluZ0xlbmd0aCAlIHNlZ21lbnRMZW5ndGg7XG4gICAgaWYgKCFkaWZmKSB7XG4gICAgICAgIHJldHVybiBpbnB1dDtcbiAgICB9XG4gICAgdmFyIHBvc2l0aW9uID0gc3RyaW5nTGVuZ3RoO1xuICAgIHZhciBwYWRMZW5ndGggPSBzZWdtZW50TGVuZ3RoIC0gZGlmZjtcbiAgICB2YXIgcGFkZGVkU3RyaW5nTGVuZ3RoID0gc3RyaW5nTGVuZ3RoICsgcGFkTGVuZ3RoO1xuICAgIHZhciBidWZmZXIgPSBCdWZmZXIuYWxsb2MocGFkZGVkU3RyaW5nTGVuZ3RoKTtcbiAgICBidWZmZXIud3JpdGUoaW5wdXQpO1xuICAgIHdoaWxlIChwYWRMZW5ndGgtLSkge1xuICAgICAgICBidWZmZXIud3JpdGUoXCI9XCIsIHBvc2l0aW9uKyspO1xuICAgIH1cbiAgICByZXR1cm4gYnVmZmVyLnRvU3RyaW5nKCk7XG59XG5leHBvcnRzLmRlZmF1bHQgPSBwYWRTdHJpbmc7XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJwYWRTdHJpbmciLCJpbnB1dCIsInNlZ21lbnRMZW5ndGgiLCJzdHJpbmdMZW5ndGgiLCJsZW5ndGgiLCJkaWZmIiwicG9zaXRpb24iLCJwYWRMZW5ndGgiLCJwYWRkZWRTdHJpbmdMZW5ndGgiLCJidWZmZXIiLCJCdWZmZXIiLCJhbGxvYyIsIndyaXRlIiwidG9TdHJpbmciLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/base64url/dist/pad-string.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/base64url/index.js":
/*!*****************************************!*\
  !*** ./node_modules/base64url/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/base64url */ \"(ssr)/./node_modules/base64url/dist/base64url.js\")[\"default\"];\nmodule.exports[\"default\"] = module.exports;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYmFzZTY0dXJsL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBQSwySEFBb0Q7QUFDcERBLHlCQUFzQixHQUFHQSxPQUFPQyxPQUFPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL2Jhc2U2NHVybC9pbmRleC5qcz85ZTg3Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L2Jhc2U2NHVybCcpLmRlZmF1bHQ7XG5tb2R1bGUuZXhwb3J0cy5kZWZhdWx0ID0gbW9kdWxlLmV4cG9ydHM7XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/base64url/index.js\n");

/***/ })

};
;