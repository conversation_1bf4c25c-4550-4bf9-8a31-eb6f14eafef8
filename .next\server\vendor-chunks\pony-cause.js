"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pony-cause";
exports.ids = ["vendor-chunks/pony-cause"];
exports.modules = {

/***/ "(ssr)/./node_modules/pony-cause/index.js":
/*!******************************************!*\
  !*** ./node_modules/pony-cause/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { ErrorWithCause } = __webpack_require__(/*! ./lib/error-with-cause */ \"(ssr)/./node_modules/pony-cause/lib/error-with-cause.js\"); // linemod-replace-with: export { ErrorWithCause } from './lib/error-with-cause.mjs';\nconst { findCauseByReference, getErrorCause, messageWithCauses, stackWithCauses } = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/./node_modules/pony-cause/lib/helpers.js\"); // linemod-replace-with: } from './lib/helpers.mjs';\nmodule.exports = {\n    ErrorWithCause,\n    findCauseByReference,\n    getErrorCause,\n    stackWithCauses,\n    messageWithCauses\n}; // linemod-remove\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG9ueS1jYXVzZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLE1BQU0sRUFBRUEsY0FBYyxFQUFFLEdBQUdDLG1CQUFPQSxDQUFDLDBGQUEyQixxRkFBcUY7QUFFbkosTUFBTSxFQUNKQyxvQkFBb0IsRUFDcEJDLGFBQWEsRUFDYkMsaUJBQWlCLEVBQ2pCQyxlQUFlLEVBQ2hCLEdBQUdKLG1CQUFPQSxDQUFDLHdFQUFrQixvREFBb0Q7QUFFbEZLLE9BQU9DLE9BQU8sR0FBRztJQUNmUDtJQUNBRTtJQUNBQztJQUNBRTtJQUNBRDtBQUNGLEdBQXdCLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXdlYjMtY3JlYXRvci1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9wb255LWNhdXNlL2luZGV4LmpzPzE1NGMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5jb25zdCB7IEVycm9yV2l0aENhdXNlIH0gPSByZXF1aXJlKCcuL2xpYi9lcnJvci13aXRoLWNhdXNlJyk7IC8vIGxpbmVtb2QtcmVwbGFjZS13aXRoOiBleHBvcnQgeyBFcnJvcldpdGhDYXVzZSB9IGZyb20gJy4vbGliL2Vycm9yLXdpdGgtY2F1c2UubWpzJztcblxuY29uc3QgeyAvLyBsaW5lbW9kLXJlcGxhY2Utd2l0aDogZXhwb3J0IHtcbiAgZmluZENhdXNlQnlSZWZlcmVuY2UsXG4gIGdldEVycm9yQ2F1c2UsXG4gIG1lc3NhZ2VXaXRoQ2F1c2VzLFxuICBzdGFja1dpdGhDYXVzZXMsXG59ID0gcmVxdWlyZSgnLi9saWIvaGVscGVycycpOyAvLyBsaW5lbW9kLXJlcGxhY2Utd2l0aDogfSBmcm9tICcuL2xpYi9oZWxwZXJzLm1qcyc7XG5cbm1vZHVsZS5leHBvcnRzID0geyAgICAgIC8vIGxpbmVtb2QtcmVtb3ZlXG4gIEVycm9yV2l0aENhdXNlLCAgICAgICAvLyBsaW5lbW9kLXJlbW92ZVxuICBmaW5kQ2F1c2VCeVJlZmVyZW5jZSwgLy8gbGluZW1vZC1yZW1vdmVcbiAgZ2V0RXJyb3JDYXVzZSwgICAgICAgIC8vIGxpbmVtb2QtcmVtb3ZlXG4gIHN0YWNrV2l0aENhdXNlcywgICAgICAvLyBsaW5lbW9kLXJlbW92ZVxuICBtZXNzYWdlV2l0aENhdXNlcywgICAgLy8gbGluZW1vZC1yZW1vdmVcbn07ICAgICAgICAgICAgICAgICAgICAgIC8vIGxpbmVtb2QtcmVtb3ZlXG4iXSwibmFtZXMiOlsiRXJyb3JXaXRoQ2F1c2UiLCJyZXF1aXJlIiwiZmluZENhdXNlQnlSZWZlcmVuY2UiLCJnZXRFcnJvckNhdXNlIiwibWVzc2FnZVdpdGhDYXVzZXMiLCJzdGFja1dpdGhDYXVzZXMiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pony-cause/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pony-cause/lib/error-with-cause.js":
/*!*********************************************************!*\
  !*** ./node_modules/pony-cause/lib/error-with-cause.js ***!
  \*********************************************************/
/***/ ((module) => {

eval("\n/** @template [T=undefined] */ class ErrorWithCause extends Error {\n    /**\n   * @param {string} message\n   * @param {{ cause?: T }} options\n   */ constructor(message, { cause } = {}){\n        super(message);\n        /** @type {string} */ this.name = ErrorWithCause.name;\n        if (cause) {\n            /** @type {T} */ this.cause = cause;\n        }\n        /** @type {string} */ this.message = message;\n    }\n}\nmodule.exports = {\n    ErrorWithCause\n}; // linemod-remove\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG9ueS1jYXVzZS9saWIvZXJyb3Itd2l0aC1jYXVzZS5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLDRCQUE0QixHQUM1QixNQUFNQSx1QkFBdUJDO0lBQzNCOzs7R0FHQyxHQUNEQyxZQUFhQyxPQUFPLEVBQUUsRUFBRUMsS0FBSyxFQUFFLEdBQUcsQ0FBQyxDQUFDLENBQUU7UUFDcEMsS0FBSyxDQUFDRDtRQUVOLG1CQUFtQixHQUNuQixJQUFJLENBQUNFLElBQUksR0FBR0wsZUFBZUssSUFBSTtRQUMvQixJQUFJRCxPQUFPO1lBQ1QsY0FBYyxHQUNkLElBQUksQ0FBQ0EsS0FBSyxHQUFHQTtRQUNmO1FBQ0EsbUJBQW1CLEdBQ25CLElBQUksQ0FBQ0QsT0FBTyxHQUFHQTtJQUNqQjtBQUNGO0FBRUFHLE9BQU9DLE9BQU8sR0FBRztJQUNmUDtBQUNGLEdBQXdCLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXdlYjMtY3JlYXRvci1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9wb255LWNhdXNlL2xpYi9lcnJvci13aXRoLWNhdXNlLmpzPzY4OTQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG4vKiogQHRlbXBsYXRlIFtUPXVuZGVmaW5lZF0gKi9cbmNsYXNzIEVycm9yV2l0aENhdXNlIGV4dGVuZHMgRXJyb3IgeyAvLyBsaW5lbW9kLXByZWZpeC13aXRoOiBleHBvcnRcbiAgLyoqXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBtZXNzYWdlXG4gICAqIEBwYXJhbSB7eyBjYXVzZT86IFQgfX0gb3B0aW9uc1xuICAgKi9cbiAgY29uc3RydWN0b3IgKG1lc3NhZ2UsIHsgY2F1c2UgfSA9IHt9KSB7XG4gICAgc3VwZXIobWVzc2FnZSk7XG5cbiAgICAvKiogQHR5cGUge3N0cmluZ30gKi9cbiAgICB0aGlzLm5hbWUgPSBFcnJvcldpdGhDYXVzZS5uYW1lO1xuICAgIGlmIChjYXVzZSkge1xuICAgICAgLyoqIEB0eXBlIHtUfSAqL1xuICAgICAgdGhpcy5jYXVzZSA9IGNhdXNlO1xuICAgIH1cbiAgICAvKiogQHR5cGUge3N0cmluZ30gKi9cbiAgICB0aGlzLm1lc3NhZ2UgPSBtZXNzYWdlO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0geyAgICAgIC8vIGxpbmVtb2QtcmVtb3ZlXG4gIEVycm9yV2l0aENhdXNlLCAgICAgICAvLyBsaW5lbW9kLXJlbW92ZVxufTsgICAgICAgICAgICAgICAgICAgICAgLy8gbGluZW1vZC1yZW1vdmVcbiJdLCJuYW1lcyI6WyJFcnJvcldpdGhDYXVzZSIsIkVycm9yIiwiY29uc3RydWN0b3IiLCJtZXNzYWdlIiwiY2F1c2UiLCJuYW1lIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pony-cause/lib/error-with-cause.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pony-cause/lib/helpers.js":
/*!************************************************!*\
  !*** ./node_modules/pony-cause/lib/helpers.js ***!
  \************************************************/
/***/ ((module) => {

eval("\n/**\n * @template {Error} T\n * @param {unknown} err\n * @param {new(...args: any[]) => T} reference\n * @returns {T|undefined}\n */ const findCauseByReference = (err, reference)=>{\n    if (!err || !reference) return;\n    if (!(err instanceof Error)) return;\n    if (!(reference.prototype instanceof Error) && // @ts-ignore\n    reference !== Error) return;\n    /**\n   * Ensures we don't go circular\n   *\n   * @type {Set<Error>}\n   */ const seen = new Set();\n    /** @type {Error|undefined} */ let currentErr = err;\n    while(currentErr && !seen.has(currentErr)){\n        seen.add(currentErr);\n        if (currentErr instanceof reference) {\n            return currentErr;\n        }\n        currentErr = getErrorCause(currentErr);\n    }\n};\n/**\n * @param {Error|{ cause?: unknown|(()=>err)}} err\n * @returns {Error|undefined}\n */ const getErrorCause = (err)=>{\n    if (!err || typeof err !== \"object\" || !(\"cause\" in err)) {\n        return;\n    }\n    // VError / NError style causes\n    if (typeof err.cause === \"function\") {\n        const causeResult = err.cause();\n        return causeResult instanceof Error ? causeResult : undefined;\n    } else {\n        return err.cause instanceof Error ? err.cause : undefined;\n    }\n};\n/**\n * Internal method that keeps a track of which error we have already added, to avoid circular recursion\n *\n * @private\n * @param {Error} err\n * @param {Set<Error>} seen\n * @returns {string}\n */ const _stackWithCauses = (err, seen)=>{\n    if (!(err instanceof Error)) return \"\";\n    const stack = err.stack || \"\";\n    // Ensure we don't go circular or crazily deep\n    if (seen.has(err)) {\n        return stack + \"\\ncauses have become circular...\";\n    }\n    const cause = getErrorCause(err);\n    // TODO: Follow up in https://github.com/nodejs/node/issues/38725#issuecomment-920309092 on how to log stuff\n    if (cause) {\n        seen.add(err);\n        return stack + \"\\ncaused by: \" + _stackWithCauses(cause, seen);\n    } else {\n        return stack;\n    }\n};\n/**\n * @param {Error} err\n * @returns {string}\n */ const stackWithCauses = (err)=>_stackWithCauses(err, new Set()); // linemod-prefix-with: export\n/**\n * Internal method that keeps a track of which error we have already added, to avoid circular recursion\n *\n * @private\n * @param {Error} err\n * @param {Set<Error>} seen\n * @param {boolean} [skip]\n * @returns {string}\n */ const _messageWithCauses = (err, seen, skip)=>{\n    if (!(err instanceof Error)) return \"\";\n    const message = skip ? \"\" : err.message || \"\";\n    // Ensure we don't go circular or crazily deep\n    if (seen.has(err)) {\n        return message + \": ...\";\n    }\n    const cause = getErrorCause(err);\n    if (cause) {\n        seen.add(err);\n        const skipIfVErrorStyleCause = \"cause\" in err && typeof err.cause === \"function\";\n        return message + (skipIfVErrorStyleCause ? \"\" : \": \") + _messageWithCauses(cause, seen, skipIfVErrorStyleCause);\n    } else {\n        return message;\n    }\n};\n/**\n * @param {Error} err\n * @returns {string}\n */ const messageWithCauses = (err)=>_messageWithCauses(err, new Set()); // linemod-prefix-with: export\nmodule.exports = {\n    findCauseByReference,\n    getErrorCause,\n    stackWithCauses,\n    messageWithCauses\n}; // linemod-remove\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pony-cause/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pony-cause/index.mjs":
/*!*******************************************!*\
  !*** ./node_modules/pony-cause/index.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorWithCause: () => (/* reexport safe */ _lib_error_with_cause_mjs__WEBPACK_IMPORTED_MODULE_0__.ErrorWithCause),\n/* harmony export */   findCauseByReference: () => (/* reexport safe */ _lib_helpers_mjs__WEBPACK_IMPORTED_MODULE_1__.findCauseByReference),\n/* harmony export */   getErrorCause: () => (/* reexport safe */ _lib_helpers_mjs__WEBPACK_IMPORTED_MODULE_1__.getErrorCause),\n/* harmony export */   messageWithCauses: () => (/* reexport safe */ _lib_helpers_mjs__WEBPACK_IMPORTED_MODULE_1__.messageWithCauses),\n/* harmony export */   stackWithCauses: () => (/* reexport safe */ _lib_helpers_mjs__WEBPACK_IMPORTED_MODULE_1__.stackWithCauses)\n/* harmony export */ });\n/* harmony import */ var _lib_error_with_cause_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/error-with-cause.mjs */ \"(ssr)/./node_modules/pony-cause/lib/error-with-cause.mjs\");\n/* harmony import */ var _lib_helpers_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/helpers.mjs */ \"(ssr)/./node_modules/pony-cause/lib/helpers.mjs\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG9ueS1jYXVzZS9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBO0FBRTREO0FBT2pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktd2ViMy1jcmVhdG9yLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL3BvbnktY2F1c2UvaW5kZXgubWpzPzcwYzIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5leHBvcnQgeyBFcnJvcldpdGhDYXVzZSB9IGZyb20gJy4vbGliL2Vycm9yLXdpdGgtY2F1c2UubWpzJztcblxuZXhwb3J0IHtcbiAgZmluZENhdXNlQnlSZWZlcmVuY2UsXG4gIGdldEVycm9yQ2F1c2UsXG4gIG1lc3NhZ2VXaXRoQ2F1c2VzLFxuICBzdGFja1dpdGhDYXVzZXMsXG59IGZyb20gJy4vbGliL2hlbHBlcnMubWpzJztcbiJdLCJuYW1lcyI6WyJFcnJvcldpdGhDYXVzZSIsImZpbmRDYXVzZUJ5UmVmZXJlbmNlIiwiZ2V0RXJyb3JDYXVzZSIsIm1lc3NhZ2VXaXRoQ2F1c2VzIiwic3RhY2tXaXRoQ2F1c2VzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pony-cause/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/pony-cause/lib/error-with-cause.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/pony-cause/lib/error-with-cause.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorWithCause: () => (/* binding */ ErrorWithCause)\n/* harmony export */ });\n\n/** @template [T=undefined] */ class ErrorWithCause extends Error {\n    /**\n   * @param {string} message\n   * @param {{ cause?: T }} options\n   */ constructor(message, { cause } = {}){\n        super(message);\n        /** @type {string} */ this.name = ErrorWithCause.name;\n        if (cause) {\n            /** @type {T} */ this.cause = cause;\n        }\n        /** @type {string} */ this.message = message;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG9ueS1jYXVzZS9saWIvZXJyb3Itd2l0aC1jYXVzZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBRUEsNEJBQTRCLEdBQ3JCLE1BQU1BLHVCQUF1QkM7SUFDbEM7OztHQUdDLEdBQ0RDLFlBQWFDLE9BQU8sRUFBRSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxDQUFDLENBQUMsQ0FBRTtRQUNwQyxLQUFLLENBQUNEO1FBRU4sbUJBQW1CLEdBQ25CLElBQUksQ0FBQ0UsSUFBSSxHQUFHTCxlQUFlSyxJQUFJO1FBQy9CLElBQUlELE9BQU87WUFDVCxjQUFjLEdBQ2QsSUFBSSxDQUFDQSxLQUFLLEdBQUdBO1FBQ2Y7UUFDQSxtQkFBbUIsR0FDbkIsSUFBSSxDQUFDRCxPQUFPLEdBQUdBO0lBQ2pCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvcG9ueS1jYXVzZS9saWIvZXJyb3Itd2l0aC1jYXVzZS5tanM/ODkzMSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbi8qKiBAdGVtcGxhdGUgW1Q9dW5kZWZpbmVkXSAqL1xuZXhwb3J0IGNsYXNzIEVycm9yV2l0aENhdXNlIGV4dGVuZHMgRXJyb3Ige1xuICAvKipcbiAgICogQHBhcmFtIHtzdHJpbmd9IG1lc3NhZ2VcbiAgICogQHBhcmFtIHt7IGNhdXNlPzogVCB9fSBvcHRpb25zXG4gICAqL1xuICBjb25zdHJ1Y3RvciAobWVzc2FnZSwgeyBjYXVzZSB9ID0ge30pIHtcbiAgICBzdXBlcihtZXNzYWdlKTtcblxuICAgIC8qKiBAdHlwZSB7c3RyaW5nfSAqL1xuICAgIHRoaXMubmFtZSA9IEVycm9yV2l0aENhdXNlLm5hbWU7XG4gICAgaWYgKGNhdXNlKSB7XG4gICAgICAvKiogQHR5cGUge1R9ICovXG4gICAgICB0aGlzLmNhdXNlID0gY2F1c2U7XG4gICAgfVxuICAgIC8qKiBAdHlwZSB7c3RyaW5nfSAqL1xuICAgIHRoaXMubWVzc2FnZSA9IG1lc3NhZ2U7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJFcnJvcldpdGhDYXVzZSIsIkVycm9yIiwiY29uc3RydWN0b3IiLCJtZXNzYWdlIiwiY2F1c2UiLCJuYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pony-cause/lib/error-with-cause.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/pony-cause/lib/helpers.mjs":
/*!*************************************************!*\
  !*** ./node_modules/pony-cause/lib/helpers.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findCauseByReference: () => (/* binding */ findCauseByReference),\n/* harmony export */   getErrorCause: () => (/* binding */ getErrorCause),\n/* harmony export */   messageWithCauses: () => (/* binding */ messageWithCauses),\n/* harmony export */   stackWithCauses: () => (/* binding */ stackWithCauses)\n/* harmony export */ });\n\n/**\n * @template {Error} T\n * @param {unknown} err\n * @param {new(...args: any[]) => T} reference\n * @returns {T|undefined}\n */ const findCauseByReference = (err, reference)=>{\n    if (!err || !reference) return;\n    if (!(err instanceof Error)) return;\n    if (!(reference.prototype instanceof Error) && // @ts-ignore\n    reference !== Error) return;\n    /**\n   * Ensures we don't go circular\n   *\n   * @type {Set<Error>}\n   */ const seen = new Set();\n    /** @type {Error|undefined} */ let currentErr = err;\n    while(currentErr && !seen.has(currentErr)){\n        seen.add(currentErr);\n        if (currentErr instanceof reference) {\n            return currentErr;\n        }\n        currentErr = getErrorCause(currentErr);\n    }\n};\n/**\n * @param {Error|{ cause?: unknown|(()=>err)}} err\n * @returns {Error|undefined}\n */ const getErrorCause = (err)=>{\n    if (!err || typeof err !== \"object\" || !(\"cause\" in err)) {\n        return;\n    }\n    // VError / NError style causes\n    if (typeof err.cause === \"function\") {\n        const causeResult = err.cause();\n        return causeResult instanceof Error ? causeResult : undefined;\n    } else {\n        return err.cause instanceof Error ? err.cause : undefined;\n    }\n};\n/**\n * Internal method that keeps a track of which error we have already added, to avoid circular recursion\n *\n * @private\n * @param {Error} err\n * @param {Set<Error>} seen\n * @returns {string}\n */ const _stackWithCauses = (err, seen)=>{\n    if (!(err instanceof Error)) return \"\";\n    const stack = err.stack || \"\";\n    // Ensure we don't go circular or crazily deep\n    if (seen.has(err)) {\n        return stack + \"\\ncauses have become circular...\";\n    }\n    const cause = getErrorCause(err);\n    // TODO: Follow up in https://github.com/nodejs/node/issues/38725#issuecomment-920309092 on how to log stuff\n    if (cause) {\n        seen.add(err);\n        return stack + \"\\ncaused by: \" + _stackWithCauses(cause, seen);\n    } else {\n        return stack;\n    }\n};\n/**\n * @param {Error} err\n * @returns {string}\n */ const stackWithCauses = (err)=>_stackWithCauses(err, new Set());\n/**\n * Internal method that keeps a track of which error we have already added, to avoid circular recursion\n *\n * @private\n * @param {Error} err\n * @param {Set<Error>} seen\n * @param {boolean} [skip]\n * @returns {string}\n */ const _messageWithCauses = (err, seen, skip)=>{\n    if (!(err instanceof Error)) return \"\";\n    const message = skip ? \"\" : err.message || \"\";\n    // Ensure we don't go circular or crazily deep\n    if (seen.has(err)) {\n        return message + \": ...\";\n    }\n    const cause = getErrorCause(err);\n    if (cause) {\n        seen.add(err);\n        const skipIfVErrorStyleCause = \"cause\" in err && typeof err.cause === \"function\";\n        return message + (skipIfVErrorStyleCause ? \"\" : \": \") + _messageWithCauses(cause, seen, skipIfVErrorStyleCause);\n    } else {\n        return message;\n    }\n};\n/**\n * @param {Error} err\n * @returns {string}\n */ const messageWithCauses = (err)=>_messageWithCauses(err, new Set());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG9ueS1jYXVzZS9saWIvaGVscGVycy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBRUE7Ozs7O0NBS0MsR0FDTSxNQUFNQSx1QkFBdUIsQ0FBQ0MsS0FBS0M7SUFDeEMsSUFBSSxDQUFDRCxPQUFPLENBQUNDLFdBQVc7SUFDeEIsSUFBSSxDQUFFRCxDQUFBQSxlQUFlRSxLQUFJLEdBQUk7SUFDN0IsSUFDRSxDQUFFRCxDQUFBQSxVQUFVRSxTQUFTLFlBQVlELEtBQUksS0FDckMsYUFBYTtJQUNiRCxjQUFjQyxPQUNkO0lBRUY7Ozs7R0FJQyxHQUNELE1BQU1FLE9BQU8sSUFBSUM7SUFFakIsNEJBQTRCLEdBQzVCLElBQUlDLGFBQWFOO0lBRWpCLE1BQU9NLGNBQWMsQ0FBQ0YsS0FBS0csR0FBRyxDQUFDRCxZQUFhO1FBQzFDRixLQUFLSSxHQUFHLENBQUNGO1FBRVQsSUFBSUEsc0JBQXNCTCxXQUFXO1lBQ25DLE9BQU9LO1FBQ1Q7UUFFQUEsYUFBYUcsY0FBY0g7SUFDN0I7QUFDRixFQUFFO0FBRUY7OztDQUdDLEdBQ00sTUFBTUcsZ0JBQWdCLENBQUNUO0lBQzVCLElBQUksQ0FBQ0EsT0FBTyxPQUFPQSxRQUFRLFlBQVksQ0FBRSxZQUFXQSxHQUFFLEdBQUk7UUFDeEQ7SUFDRjtJQUVBLCtCQUErQjtJQUMvQixJQUFJLE9BQU9BLElBQUlVLEtBQUssS0FBSyxZQUFZO1FBQ25DLE1BQU1DLGNBQWNYLElBQUlVLEtBQUs7UUFFN0IsT0FBT0MsdUJBQXVCVCxRQUMxQlMsY0FDQUM7SUFDTixPQUFPO1FBQ0wsT0FBT1osSUFBSVUsS0FBSyxZQUFZUixRQUN4QkYsSUFBSVUsS0FBSyxHQUNURTtJQUNOO0FBQ0YsRUFBRTtBQUVGOzs7Ozs7O0NBT0MsR0FDRCxNQUFNQyxtQkFBbUIsQ0FBQ2IsS0FBS0k7SUFDN0IsSUFBSSxDQUFFSixDQUFBQSxlQUFlRSxLQUFJLEdBQUksT0FBTztJQUVwQyxNQUFNWSxRQUFRZCxJQUFJYyxLQUFLLElBQUk7SUFFM0IsOENBQThDO0lBQzlDLElBQUlWLEtBQUtHLEdBQUcsQ0FBQ1AsTUFBTTtRQUNqQixPQUFPYyxRQUFRO0lBQ2pCO0lBRUEsTUFBTUosUUFBUUQsY0FBY1Q7SUFFNUIsNEdBQTRHO0lBRTVHLElBQUlVLE9BQU87UUFDVE4sS0FBS0ksR0FBRyxDQUFDUjtRQUNULE9BQVFjLFFBQVEsa0JBQWtCRCxpQkFBaUJILE9BQU9OO0lBQzVELE9BQU87UUFDTCxPQUFPVTtJQUNUO0FBQ0Y7QUFFQTs7O0NBR0MsR0FDTSxNQUFNQyxrQkFBa0IsQ0FBQ2YsTUFBUWEsaUJBQWlCYixLQUFLLElBQUlLLE9BQU87QUFFekU7Ozs7Ozs7O0NBUUMsR0FDRCxNQUFNVyxxQkFBcUIsQ0FBQ2hCLEtBQUtJLE1BQU1hO0lBQ3JDLElBQUksQ0FBRWpCLENBQUFBLGVBQWVFLEtBQUksR0FBSSxPQUFPO0lBRXBDLE1BQU1nQixVQUFVRCxPQUFPLEtBQU1qQixJQUFJa0IsT0FBTyxJQUFJO0lBRTVDLDhDQUE4QztJQUM5QyxJQUFJZCxLQUFLRyxHQUFHLENBQUNQLE1BQU07UUFDakIsT0FBT2tCLFVBQVU7SUFDbkI7SUFFQSxNQUFNUixRQUFRRCxjQUFjVDtJQUU1QixJQUFJVSxPQUFPO1FBQ1ROLEtBQUtJLEdBQUcsQ0FBQ1I7UUFFVCxNQUFNbUIseUJBQXlCLFdBQVduQixPQUFPLE9BQU9BLElBQUlVLEtBQUssS0FBSztRQUV0RSxPQUFRUSxVQUNMQyxDQUFBQSx5QkFBeUIsS0FBSyxJQUFHLElBQ2xDSCxtQkFBbUJOLE9BQU9OLE1BQU1lO0lBQ3BDLE9BQU87UUFDTCxPQUFPRDtJQUNUO0FBQ0Y7QUFFQTs7O0NBR0MsR0FDTSxNQUFNRSxvQkFBb0IsQ0FBQ3BCLE1BQVFnQixtQkFBbUJoQixLQUFLLElBQUlLLE9BQU8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvcG9ueS1jYXVzZS9saWIvaGVscGVycy5tanM/Yzg4MyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbi8qKlxuICogQHRlbXBsYXRlIHtFcnJvcn0gVFxuICogQHBhcmFtIHt1bmtub3dufSBlcnJcbiAqIEBwYXJhbSB7bmV3KC4uLmFyZ3M6IGFueVtdKSA9PiBUfSByZWZlcmVuY2VcbiAqIEByZXR1cm5zIHtUfHVuZGVmaW5lZH1cbiAqL1xuZXhwb3J0IGNvbnN0IGZpbmRDYXVzZUJ5UmVmZXJlbmNlID0gKGVyciwgcmVmZXJlbmNlKSA9PiB7XG4gIGlmICghZXJyIHx8ICFyZWZlcmVuY2UpIHJldHVybjtcbiAgaWYgKCEoZXJyIGluc3RhbmNlb2YgRXJyb3IpKSByZXR1cm47XG4gIGlmIChcbiAgICAhKHJlZmVyZW5jZS5wcm90b3R5cGUgaW5zdGFuY2VvZiBFcnJvcikgJiZcbiAgICAvLyBAdHMtaWdub3JlXG4gICAgcmVmZXJlbmNlICE9PSBFcnJvclxuICApIHJldHVybjtcblxuICAvKipcbiAgICogRW5zdXJlcyB3ZSBkb24ndCBnbyBjaXJjdWxhclxuICAgKlxuICAgKiBAdHlwZSB7U2V0PEVycm9yPn1cbiAgICovXG4gIGNvbnN0IHNlZW4gPSBuZXcgU2V0KCk7XG5cbiAgLyoqIEB0eXBlIHtFcnJvcnx1bmRlZmluZWR9ICovXG4gIGxldCBjdXJyZW50RXJyID0gZXJyO1xuXG4gIHdoaWxlIChjdXJyZW50RXJyICYmICFzZWVuLmhhcyhjdXJyZW50RXJyKSkge1xuICAgIHNlZW4uYWRkKGN1cnJlbnRFcnIpO1xuXG4gICAgaWYgKGN1cnJlbnRFcnIgaW5zdGFuY2VvZiByZWZlcmVuY2UpIHtcbiAgICAgIHJldHVybiBjdXJyZW50RXJyO1xuICAgIH1cblxuICAgIGN1cnJlbnRFcnIgPSBnZXRFcnJvckNhdXNlKGN1cnJlbnRFcnIpO1xuICB9XG59O1xuXG4vKipcbiAqIEBwYXJhbSB7RXJyb3J8eyBjYXVzZT86IHVua25vd258KCgpPT5lcnIpfX0gZXJyXG4gKiBAcmV0dXJucyB7RXJyb3J8dW5kZWZpbmVkfVxuICovXG5leHBvcnQgY29uc3QgZ2V0RXJyb3JDYXVzZSA9IChlcnIpID0+IHtcbiAgaWYgKCFlcnIgfHwgdHlwZW9mIGVyciAhPT0gJ29iamVjdCcgfHwgISgnY2F1c2UnIGluIGVycikpIHtcbiAgICByZXR1cm47XG4gIH1cblxuICAvLyBWRXJyb3IgLyBORXJyb3Igc3R5bGUgY2F1c2VzXG4gIGlmICh0eXBlb2YgZXJyLmNhdXNlID09PSAnZnVuY3Rpb24nKSB7XG4gICAgY29uc3QgY2F1c2VSZXN1bHQgPSBlcnIuY2F1c2UoKTtcblxuICAgIHJldHVybiBjYXVzZVJlc3VsdCBpbnN0YW5jZW9mIEVycm9yXG4gICAgICA/IGNhdXNlUmVzdWx0XG4gICAgICA6IHVuZGVmaW5lZDtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gZXJyLmNhdXNlIGluc3RhbmNlb2YgRXJyb3JcbiAgICAgID8gZXJyLmNhdXNlXG4gICAgICA6IHVuZGVmaW5lZDtcbiAgfVxufTtcblxuLyoqXG4gKiBJbnRlcm5hbCBtZXRob2QgdGhhdCBrZWVwcyBhIHRyYWNrIG9mIHdoaWNoIGVycm9yIHdlIGhhdmUgYWxyZWFkeSBhZGRlZCwgdG8gYXZvaWQgY2lyY3VsYXIgcmVjdXJzaW9uXG4gKlxuICogQHByaXZhdGVcbiAqIEBwYXJhbSB7RXJyb3J9IGVyclxuICogQHBhcmFtIHtTZXQ8RXJyb3I+fSBzZWVuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5jb25zdCBfc3RhY2tXaXRoQ2F1c2VzID0gKGVyciwgc2VlbikgPT4ge1xuICBpZiAoIShlcnIgaW5zdGFuY2VvZiBFcnJvcikpIHJldHVybiAnJztcblxuICBjb25zdCBzdGFjayA9IGVyci5zdGFjayB8fCAnJztcblxuICAvLyBFbnN1cmUgd2UgZG9uJ3QgZ28gY2lyY3VsYXIgb3IgY3JhemlseSBkZWVwXG4gIGlmIChzZWVuLmhhcyhlcnIpKSB7XG4gICAgcmV0dXJuIHN0YWNrICsgJ1xcbmNhdXNlcyBoYXZlIGJlY29tZSBjaXJjdWxhci4uLic7XG4gIH1cblxuICBjb25zdCBjYXVzZSA9IGdldEVycm9yQ2F1c2UoZXJyKTtcblxuICAvLyBUT0RPOiBGb2xsb3cgdXAgaW4gaHR0cHM6Ly9naXRodWIuY29tL25vZGVqcy9ub2RlL2lzc3Vlcy8zODcyNSNpc3N1ZWNvbW1lbnQtOTIwMzA5MDkyIG9uIGhvdyB0byBsb2cgc3R1ZmZcblxuICBpZiAoY2F1c2UpIHtcbiAgICBzZWVuLmFkZChlcnIpO1xuICAgIHJldHVybiAoc3RhY2sgKyAnXFxuY2F1c2VkIGJ5OiAnICsgX3N0YWNrV2l0aENhdXNlcyhjYXVzZSwgc2VlbikpO1xuICB9IGVsc2Uge1xuICAgIHJldHVybiBzdGFjaztcbiAgfVxufTtcblxuLyoqXG4gKiBAcGFyYW0ge0Vycm9yfSBlcnJcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBjb25zdCBzdGFja1dpdGhDYXVzZXMgPSAoZXJyKSA9PiBfc3RhY2tXaXRoQ2F1c2VzKGVyciwgbmV3IFNldCgpKTtcblxuLyoqXG4gKiBJbnRlcm5hbCBtZXRob2QgdGhhdCBrZWVwcyBhIHRyYWNrIG9mIHdoaWNoIGVycm9yIHdlIGhhdmUgYWxyZWFkeSBhZGRlZCwgdG8gYXZvaWQgY2lyY3VsYXIgcmVjdXJzaW9uXG4gKlxuICogQHByaXZhdGVcbiAqIEBwYXJhbSB7RXJyb3J9IGVyclxuICogQHBhcmFtIHtTZXQ8RXJyb3I+fSBzZWVuXG4gKiBAcGFyYW0ge2Jvb2xlYW59IFtza2lwXVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuY29uc3QgX21lc3NhZ2VXaXRoQ2F1c2VzID0gKGVyciwgc2Vlbiwgc2tpcCkgPT4ge1xuICBpZiAoIShlcnIgaW5zdGFuY2VvZiBFcnJvcikpIHJldHVybiAnJztcblxuICBjb25zdCBtZXNzYWdlID0gc2tpcCA/ICcnIDogKGVyci5tZXNzYWdlIHx8ICcnKTtcblxuICAvLyBFbnN1cmUgd2UgZG9uJ3QgZ28gY2lyY3VsYXIgb3IgY3JhemlseSBkZWVwXG4gIGlmIChzZWVuLmhhcyhlcnIpKSB7XG4gICAgcmV0dXJuIG1lc3NhZ2UgKyAnOiAuLi4nO1xuICB9XG5cbiAgY29uc3QgY2F1c2UgPSBnZXRFcnJvckNhdXNlKGVycik7XG5cbiAgaWYgKGNhdXNlKSB7XG4gICAgc2Vlbi5hZGQoZXJyKTtcblxuICAgIGNvbnN0IHNraXBJZlZFcnJvclN0eWxlQ2F1c2UgPSAnY2F1c2UnIGluIGVyciAmJiB0eXBlb2YgZXJyLmNhdXNlID09PSAnZnVuY3Rpb24nO1xuXG4gICAgcmV0dXJuIChtZXNzYWdlICtcbiAgICAgIChza2lwSWZWRXJyb3JTdHlsZUNhdXNlID8gJycgOiAnOiAnKSArXG4gICAgICBfbWVzc2FnZVdpdGhDYXVzZXMoY2F1c2UsIHNlZW4sIHNraXBJZlZFcnJvclN0eWxlQ2F1c2UpKTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gbWVzc2FnZTtcbiAgfVxufTtcblxuLyoqXG4gKiBAcGFyYW0ge0Vycm9yfSBlcnJcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBjb25zdCBtZXNzYWdlV2l0aENhdXNlcyA9IChlcnIpID0+IF9tZXNzYWdlV2l0aENhdXNlcyhlcnIsIG5ldyBTZXQoKSk7XG4iXSwibmFtZXMiOlsiZmluZENhdXNlQnlSZWZlcmVuY2UiLCJlcnIiLCJyZWZlcmVuY2UiLCJFcnJvciIsInByb3RvdHlwZSIsInNlZW4iLCJTZXQiLCJjdXJyZW50RXJyIiwiaGFzIiwiYWRkIiwiZ2V0RXJyb3JDYXVzZSIsImNhdXNlIiwiY2F1c2VSZXN1bHQiLCJ1bmRlZmluZWQiLCJfc3RhY2tXaXRoQ2F1c2VzIiwic3RhY2siLCJzdGFja1dpdGhDYXVzZXMiLCJfbWVzc2FnZVdpdGhDYXVzZXMiLCJza2lwIiwibWVzc2FnZSIsInNraXBJZlZFcnJvclN0eWxlQ2F1c2UiLCJtZXNzYWdlV2l0aENhdXNlcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pony-cause/lib/helpers.mjs\n");

/***/ })

};
;