/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/end-of-stream";
exports.ids = ["vendor-chunks/end-of-stream"];
exports.modules = {

/***/ "(ssr)/./node_modules/end-of-stream/index.js":
/*!*********************************************!*\
  !*** ./node_modules/end-of-stream/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var once = __webpack_require__(/*! once */ \"(ssr)/./node_modules/once/once.js\");\nvar noop = function() {};\nvar qnt = global.Bare ? queueMicrotask : process.nextTick.bind(process);\nvar isRequest = function(stream) {\n    return stream.setHeader && typeof stream.abort === \"function\";\n};\nvar isChildProcess = function(stream) {\n    return stream.stdio && Array.isArray(stream.stdio) && stream.stdio.length === 3;\n};\nvar eos = function(stream, opts, callback) {\n    if (typeof opts === \"function\") return eos(stream, null, opts);\n    if (!opts) opts = {};\n    callback = once(callback || noop);\n    var ws = stream._writableState;\n    var rs = stream._readableState;\n    var readable = opts.readable || opts.readable !== false && stream.readable;\n    var writable = opts.writable || opts.writable !== false && stream.writable;\n    var cancelled = false;\n    var onlegacyfinish = function() {\n        if (!stream.writable) onfinish();\n    };\n    var onfinish = function() {\n        writable = false;\n        if (!readable) callback.call(stream);\n    };\n    var onend = function() {\n        readable = false;\n        if (!writable) callback.call(stream);\n    };\n    var onexit = function(exitCode) {\n        callback.call(stream, exitCode ? new Error(\"exited with error code: \" + exitCode) : null);\n    };\n    var onerror = function(err) {\n        callback.call(stream, err);\n    };\n    var onclose = function() {\n        qnt(onclosenexttick);\n    };\n    var onclosenexttick = function() {\n        if (cancelled) return;\n        if (readable && !(rs && rs.ended && !rs.destroyed)) return callback.call(stream, new Error(\"premature close\"));\n        if (writable && !(ws && ws.ended && !ws.destroyed)) return callback.call(stream, new Error(\"premature close\"));\n    };\n    var onrequest = function() {\n        stream.req.on(\"finish\", onfinish);\n    };\n    if (isRequest(stream)) {\n        stream.on(\"complete\", onfinish);\n        stream.on(\"abort\", onclose);\n        if (stream.req) onrequest();\n        else stream.on(\"request\", onrequest);\n    } else if (writable && !ws) {\n        stream.on(\"end\", onlegacyfinish);\n        stream.on(\"close\", onlegacyfinish);\n    }\n    if (isChildProcess(stream)) stream.on(\"exit\", onexit);\n    stream.on(\"end\", onend);\n    stream.on(\"finish\", onfinish);\n    if (opts.error !== false) stream.on(\"error\", onerror);\n    stream.on(\"close\", onclose);\n    return function() {\n        cancelled = true;\n        stream.removeListener(\"complete\", onfinish);\n        stream.removeListener(\"abort\", onclose);\n        stream.removeListener(\"request\", onrequest);\n        if (stream.req) stream.req.removeListener(\"finish\", onfinish);\n        stream.removeListener(\"end\", onlegacyfinish);\n        stream.removeListener(\"close\", onlegacyfinish);\n        stream.removeListener(\"finish\", onfinish);\n        stream.removeListener(\"exit\", onexit);\n        stream.removeListener(\"end\", onend);\n        stream.removeListener(\"error\", onerror);\n        stream.removeListener(\"close\", onclose);\n    };\n};\nmodule.exports = eos;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5kLW9mLXN0cmVhbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxPQUFPQyxtQkFBT0EsQ0FBQztBQUVuQixJQUFJQyxPQUFPLFlBQVk7QUFFdkIsSUFBSUMsTUFBTUMsT0FBT0MsSUFBSSxHQUFHQyxpQkFBaUJDLFFBQVFDLFFBQVEsQ0FBQ0MsSUFBSSxDQUFDRjtBQUUvRCxJQUFJRyxZQUFZLFNBQVNDLE1BQU07SUFDOUIsT0FBT0EsT0FBT0MsU0FBUyxJQUFJLE9BQU9ELE9BQU9FLEtBQUssS0FBSztBQUNwRDtBQUVBLElBQUlDLGlCQUFpQixTQUFTSCxNQUFNO0lBQ25DLE9BQU9BLE9BQU9JLEtBQUssSUFBSUMsTUFBTUMsT0FBTyxDQUFDTixPQUFPSSxLQUFLLEtBQUtKLE9BQU9JLEtBQUssQ0FBQ0csTUFBTSxLQUFLO0FBQy9FO0FBRUEsSUFBSUMsTUFBTSxTQUFTUixNQUFNLEVBQUVTLElBQUksRUFBRUMsUUFBUTtJQUN4QyxJQUFJLE9BQU9ELFNBQVMsWUFBWSxPQUFPRCxJQUFJUixRQUFRLE1BQU1TO0lBQ3pELElBQUksQ0FBQ0EsTUFBTUEsT0FBTyxDQUFDO0lBRW5CQyxXQUFXckIsS0FBS3FCLFlBQVluQjtJQUU1QixJQUFJb0IsS0FBS1gsT0FBT1ksY0FBYztJQUM5QixJQUFJQyxLQUFLYixPQUFPYyxjQUFjO0lBQzlCLElBQUlDLFdBQVdOLEtBQUtNLFFBQVEsSUFBS04sS0FBS00sUUFBUSxLQUFLLFNBQVNmLE9BQU9lLFFBQVE7SUFDM0UsSUFBSUMsV0FBV1AsS0FBS08sUUFBUSxJQUFLUCxLQUFLTyxRQUFRLEtBQUssU0FBU2hCLE9BQU9nQixRQUFRO0lBQzNFLElBQUlDLFlBQVk7SUFFaEIsSUFBSUMsaUJBQWlCO1FBQ3BCLElBQUksQ0FBQ2xCLE9BQU9nQixRQUFRLEVBQUVHO0lBQ3ZCO0lBRUEsSUFBSUEsV0FBVztRQUNkSCxXQUFXO1FBQ1gsSUFBSSxDQUFDRCxVQUFVTCxTQUFTVSxJQUFJLENBQUNwQjtJQUM5QjtJQUVBLElBQUlxQixRQUFRO1FBQ1hOLFdBQVc7UUFDWCxJQUFJLENBQUNDLFVBQVVOLFNBQVNVLElBQUksQ0FBQ3BCO0lBQzlCO0lBRUEsSUFBSXNCLFNBQVMsU0FBU0MsUUFBUTtRQUM3QmIsU0FBU1UsSUFBSSxDQUFDcEIsUUFBUXVCLFdBQVcsSUFBSUMsTUFBTSw2QkFBNkJELFlBQVk7SUFDckY7SUFFQSxJQUFJRSxVQUFVLFNBQVNDLEdBQUc7UUFDekJoQixTQUFTVSxJQUFJLENBQUNwQixRQUFRMEI7SUFDdkI7SUFFQSxJQUFJQyxVQUFVO1FBQ2JuQyxJQUFJb0M7SUFDTDtJQUVBLElBQUlBLGtCQUFrQjtRQUNyQixJQUFJWCxXQUFXO1FBQ2YsSUFBSUYsWUFBWSxDQUFFRixDQUFBQSxNQUFPQSxHQUFHZ0IsS0FBSyxJQUFJLENBQUNoQixHQUFHaUIsU0FBUyxHQUFJLE9BQU9wQixTQUFTVSxJQUFJLENBQUNwQixRQUFRLElBQUl3QixNQUFNO1FBQzdGLElBQUlSLFlBQVksQ0FBRUwsQ0FBQUEsTUFBT0EsR0FBR2tCLEtBQUssSUFBSSxDQUFDbEIsR0FBR21CLFNBQVMsR0FBSSxPQUFPcEIsU0FBU1UsSUFBSSxDQUFDcEIsUUFBUSxJQUFJd0IsTUFBTTtJQUM5RjtJQUVBLElBQUlPLFlBQVk7UUFDZi9CLE9BQU9nQyxHQUFHLENBQUNDLEVBQUUsQ0FBQyxVQUFVZDtJQUN6QjtJQUVBLElBQUlwQixVQUFVQyxTQUFTO1FBQ3RCQSxPQUFPaUMsRUFBRSxDQUFDLFlBQVlkO1FBQ3RCbkIsT0FBT2lDLEVBQUUsQ0FBQyxTQUFTTjtRQUNuQixJQUFJM0IsT0FBT2dDLEdBQUcsRUFBRUQ7YUFDWC9CLE9BQU9pQyxFQUFFLENBQUMsV0FBV0Y7SUFDM0IsT0FBTyxJQUFJZixZQUFZLENBQUNMLElBQUk7UUFDM0JYLE9BQU9pQyxFQUFFLENBQUMsT0FBT2Y7UUFDakJsQixPQUFPaUMsRUFBRSxDQUFDLFNBQVNmO0lBQ3BCO0lBRUEsSUFBSWYsZUFBZUgsU0FBU0EsT0FBT2lDLEVBQUUsQ0FBQyxRQUFRWDtJQUU5Q3RCLE9BQU9pQyxFQUFFLENBQUMsT0FBT1o7SUFDakJyQixPQUFPaUMsRUFBRSxDQUFDLFVBQVVkO0lBQ3BCLElBQUlWLEtBQUt5QixLQUFLLEtBQUssT0FBT2xDLE9BQU9pQyxFQUFFLENBQUMsU0FBU1I7SUFDN0N6QixPQUFPaUMsRUFBRSxDQUFDLFNBQVNOO0lBRW5CLE9BQU87UUFDTlYsWUFBWTtRQUNaakIsT0FBT21DLGNBQWMsQ0FBQyxZQUFZaEI7UUFDbENuQixPQUFPbUMsY0FBYyxDQUFDLFNBQVNSO1FBQy9CM0IsT0FBT21DLGNBQWMsQ0FBQyxXQUFXSjtRQUNqQyxJQUFJL0IsT0FBT2dDLEdBQUcsRUFBRWhDLE9BQU9nQyxHQUFHLENBQUNHLGNBQWMsQ0FBQyxVQUFVaEI7UUFDcERuQixPQUFPbUMsY0FBYyxDQUFDLE9BQU9qQjtRQUM3QmxCLE9BQU9tQyxjQUFjLENBQUMsU0FBU2pCO1FBQy9CbEIsT0FBT21DLGNBQWMsQ0FBQyxVQUFVaEI7UUFDaENuQixPQUFPbUMsY0FBYyxDQUFDLFFBQVFiO1FBQzlCdEIsT0FBT21DLGNBQWMsQ0FBQyxPQUFPZDtRQUM3QnJCLE9BQU9tQyxjQUFjLENBQUMsU0FBU1Y7UUFDL0J6QixPQUFPbUMsY0FBYyxDQUFDLFNBQVNSO0lBQ2hDO0FBQ0Q7QUFFQVMsT0FBT0MsT0FBTyxHQUFHN0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS13ZWIzLWNyZWF0b3ItcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvZW5kLW9mLXN0cmVhbS9pbmRleC5qcz9jNDBkIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBvbmNlID0gcmVxdWlyZSgnb25jZScpO1xuXG52YXIgbm9vcCA9IGZ1bmN0aW9uKCkge307XG5cbnZhciBxbnQgPSBnbG9iYWwuQmFyZSA/IHF1ZXVlTWljcm90YXNrIDogcHJvY2Vzcy5uZXh0VGljay5iaW5kKHByb2Nlc3MpO1xuXG52YXIgaXNSZXF1ZXN0ID0gZnVuY3Rpb24oc3RyZWFtKSB7XG5cdHJldHVybiBzdHJlYW0uc2V0SGVhZGVyICYmIHR5cGVvZiBzdHJlYW0uYWJvcnQgPT09ICdmdW5jdGlvbic7XG59O1xuXG52YXIgaXNDaGlsZFByb2Nlc3MgPSBmdW5jdGlvbihzdHJlYW0pIHtcblx0cmV0dXJuIHN0cmVhbS5zdGRpbyAmJiBBcnJheS5pc0FycmF5KHN0cmVhbS5zdGRpbykgJiYgc3RyZWFtLnN0ZGlvLmxlbmd0aCA9PT0gM1xufTtcblxudmFyIGVvcyA9IGZ1bmN0aW9uKHN0cmVhbSwgb3B0cywgY2FsbGJhY2spIHtcblx0aWYgKHR5cGVvZiBvcHRzID09PSAnZnVuY3Rpb24nKSByZXR1cm4gZW9zKHN0cmVhbSwgbnVsbCwgb3B0cyk7XG5cdGlmICghb3B0cykgb3B0cyA9IHt9O1xuXG5cdGNhbGxiYWNrID0gb25jZShjYWxsYmFjayB8fCBub29wKTtcblxuXHR2YXIgd3MgPSBzdHJlYW0uX3dyaXRhYmxlU3RhdGU7XG5cdHZhciBycyA9IHN0cmVhbS5fcmVhZGFibGVTdGF0ZTtcblx0dmFyIHJlYWRhYmxlID0gb3B0cy5yZWFkYWJsZSB8fCAob3B0cy5yZWFkYWJsZSAhPT0gZmFsc2UgJiYgc3RyZWFtLnJlYWRhYmxlKTtcblx0dmFyIHdyaXRhYmxlID0gb3B0cy53cml0YWJsZSB8fCAob3B0cy53cml0YWJsZSAhPT0gZmFsc2UgJiYgc3RyZWFtLndyaXRhYmxlKTtcblx0dmFyIGNhbmNlbGxlZCA9IGZhbHNlO1xuXG5cdHZhciBvbmxlZ2FjeWZpbmlzaCA9IGZ1bmN0aW9uKCkge1xuXHRcdGlmICghc3RyZWFtLndyaXRhYmxlKSBvbmZpbmlzaCgpO1xuXHR9O1xuXG5cdHZhciBvbmZpbmlzaCA9IGZ1bmN0aW9uKCkge1xuXHRcdHdyaXRhYmxlID0gZmFsc2U7XG5cdFx0aWYgKCFyZWFkYWJsZSkgY2FsbGJhY2suY2FsbChzdHJlYW0pO1xuXHR9O1xuXG5cdHZhciBvbmVuZCA9IGZ1bmN0aW9uKCkge1xuXHRcdHJlYWRhYmxlID0gZmFsc2U7XG5cdFx0aWYgKCF3cml0YWJsZSkgY2FsbGJhY2suY2FsbChzdHJlYW0pO1xuXHR9O1xuXG5cdHZhciBvbmV4aXQgPSBmdW5jdGlvbihleGl0Q29kZSkge1xuXHRcdGNhbGxiYWNrLmNhbGwoc3RyZWFtLCBleGl0Q29kZSA/IG5ldyBFcnJvcignZXhpdGVkIHdpdGggZXJyb3IgY29kZTogJyArIGV4aXRDb2RlKSA6IG51bGwpO1xuXHR9O1xuXG5cdHZhciBvbmVycm9yID0gZnVuY3Rpb24oZXJyKSB7XG5cdFx0Y2FsbGJhY2suY2FsbChzdHJlYW0sIGVycik7XG5cdH07XG5cblx0dmFyIG9uY2xvc2UgPSBmdW5jdGlvbigpIHtcblx0XHRxbnQob25jbG9zZW5leHR0aWNrKTtcblx0fTtcblxuXHR2YXIgb25jbG9zZW5leHR0aWNrID0gZnVuY3Rpb24oKSB7XG5cdFx0aWYgKGNhbmNlbGxlZCkgcmV0dXJuO1xuXHRcdGlmIChyZWFkYWJsZSAmJiAhKHJzICYmIChycy5lbmRlZCAmJiAhcnMuZGVzdHJveWVkKSkpIHJldHVybiBjYWxsYmFjay5jYWxsKHN0cmVhbSwgbmV3IEVycm9yKCdwcmVtYXR1cmUgY2xvc2UnKSk7XG5cdFx0aWYgKHdyaXRhYmxlICYmICEod3MgJiYgKHdzLmVuZGVkICYmICF3cy5kZXN0cm95ZWQpKSkgcmV0dXJuIGNhbGxiYWNrLmNhbGwoc3RyZWFtLCBuZXcgRXJyb3IoJ3ByZW1hdHVyZSBjbG9zZScpKTtcblx0fTtcblxuXHR2YXIgb25yZXF1ZXN0ID0gZnVuY3Rpb24oKSB7XG5cdFx0c3RyZWFtLnJlcS5vbignZmluaXNoJywgb25maW5pc2gpO1xuXHR9O1xuXG5cdGlmIChpc1JlcXVlc3Qoc3RyZWFtKSkge1xuXHRcdHN0cmVhbS5vbignY29tcGxldGUnLCBvbmZpbmlzaCk7XG5cdFx0c3RyZWFtLm9uKCdhYm9ydCcsIG9uY2xvc2UpO1xuXHRcdGlmIChzdHJlYW0ucmVxKSBvbnJlcXVlc3QoKTtcblx0XHRlbHNlIHN0cmVhbS5vbigncmVxdWVzdCcsIG9ucmVxdWVzdCk7XG5cdH0gZWxzZSBpZiAod3JpdGFibGUgJiYgIXdzKSB7IC8vIGxlZ2FjeSBzdHJlYW1zXG5cdFx0c3RyZWFtLm9uKCdlbmQnLCBvbmxlZ2FjeWZpbmlzaCk7XG5cdFx0c3RyZWFtLm9uKCdjbG9zZScsIG9ubGVnYWN5ZmluaXNoKTtcblx0fVxuXG5cdGlmIChpc0NoaWxkUHJvY2VzcyhzdHJlYW0pKSBzdHJlYW0ub24oJ2V4aXQnLCBvbmV4aXQpO1xuXG5cdHN0cmVhbS5vbignZW5kJywgb25lbmQpO1xuXHRzdHJlYW0ub24oJ2ZpbmlzaCcsIG9uZmluaXNoKTtcblx0aWYgKG9wdHMuZXJyb3IgIT09IGZhbHNlKSBzdHJlYW0ub24oJ2Vycm9yJywgb25lcnJvcik7XG5cdHN0cmVhbS5vbignY2xvc2UnLCBvbmNsb3NlKTtcblxuXHRyZXR1cm4gZnVuY3Rpb24oKSB7XG5cdFx0Y2FuY2VsbGVkID0gdHJ1ZTtcblx0XHRzdHJlYW0ucmVtb3ZlTGlzdGVuZXIoJ2NvbXBsZXRlJywgb25maW5pc2gpO1xuXHRcdHN0cmVhbS5yZW1vdmVMaXN0ZW5lcignYWJvcnQnLCBvbmNsb3NlKTtcblx0XHRzdHJlYW0ucmVtb3ZlTGlzdGVuZXIoJ3JlcXVlc3QnLCBvbnJlcXVlc3QpO1xuXHRcdGlmIChzdHJlYW0ucmVxKSBzdHJlYW0ucmVxLnJlbW92ZUxpc3RlbmVyKCdmaW5pc2gnLCBvbmZpbmlzaCk7XG5cdFx0c3RyZWFtLnJlbW92ZUxpc3RlbmVyKCdlbmQnLCBvbmxlZ2FjeWZpbmlzaCk7XG5cdFx0c3RyZWFtLnJlbW92ZUxpc3RlbmVyKCdjbG9zZScsIG9ubGVnYWN5ZmluaXNoKTtcblx0XHRzdHJlYW0ucmVtb3ZlTGlzdGVuZXIoJ2ZpbmlzaCcsIG9uZmluaXNoKTtcblx0XHRzdHJlYW0ucmVtb3ZlTGlzdGVuZXIoJ2V4aXQnLCBvbmV4aXQpO1xuXHRcdHN0cmVhbS5yZW1vdmVMaXN0ZW5lcignZW5kJywgb25lbmQpO1xuXHRcdHN0cmVhbS5yZW1vdmVMaXN0ZW5lcignZXJyb3InLCBvbmVycm9yKTtcblx0XHRzdHJlYW0ucmVtb3ZlTGlzdGVuZXIoJ2Nsb3NlJywgb25jbG9zZSk7XG5cdH07XG59O1xuXG5tb2R1bGUuZXhwb3J0cyA9IGVvcztcbiJdLCJuYW1lcyI6WyJvbmNlIiwicmVxdWlyZSIsIm5vb3AiLCJxbnQiLCJnbG9iYWwiLCJCYXJlIiwicXVldWVNaWNyb3Rhc2siLCJwcm9jZXNzIiwibmV4dFRpY2siLCJiaW5kIiwiaXNSZXF1ZXN0Iiwic3RyZWFtIiwic2V0SGVhZGVyIiwiYWJvcnQiLCJpc0NoaWxkUHJvY2VzcyIsInN0ZGlvIiwiQXJyYXkiLCJpc0FycmF5IiwibGVuZ3RoIiwiZW9zIiwib3B0cyIsImNhbGxiYWNrIiwid3MiLCJfd3JpdGFibGVTdGF0ZSIsInJzIiwiX3JlYWRhYmxlU3RhdGUiLCJyZWFkYWJsZSIsIndyaXRhYmxlIiwiY2FuY2VsbGVkIiwib25sZWdhY3lmaW5pc2giLCJvbmZpbmlzaCIsImNhbGwiLCJvbmVuZCIsIm9uZXhpdCIsImV4aXRDb2RlIiwiRXJyb3IiLCJvbmVycm9yIiwiZXJyIiwib25jbG9zZSIsIm9uY2xvc2VuZXh0dGljayIsImVuZGVkIiwiZGVzdHJveWVkIiwib25yZXF1ZXN0IiwicmVxIiwib24iLCJlcnJvciIsInJlbW92ZUxpc3RlbmVyIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/end-of-stream/index.js\n");

/***/ })

};
;